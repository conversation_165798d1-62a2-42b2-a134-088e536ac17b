using System.ComponentModel.DataAnnotations;
using CrmApi.Models.Person;

namespace CrmApi.Models.ImportExport
{
    public class PersonExportRequest
    {
        [Required(ErrorMessage = "Export format is required")]
        public FileFormat Format { get; set; } = FileFormat.CSV;

        // Filtering Options
        public int? DivisionId { get; set; }
        public int? CategoryId { get; set; }
        public int? FirmNatureId { get; set; }
        public PersonNature? Nature { get; set; }
        public Gender? Gender { get; set; }
        public string? WorkingState { get; set; }
        public string? District { get; set; }
        public DateTime? CreatedAfter { get; set; }
        public DateTime? CreatedBefore { get; set; }
        public bool IncludeDeleted { get; set; } = false;

        // Column Selection
        public List<string> SelectedColumns { get; set; } = new List<string>();
        public bool IncludeAllColumns { get; set; } = true;

        // Export Options
        public bool IncludeHeaders { get; set; } = true;
        public bool IncludeRelatedData { get; set; } = true;
        public string? FileName { get; set; }
        public int? MaxRecords { get; set; }

        // Sorting
        public string SortBy { get; set; } = "CreatedAt";
        public string SortDirection { get; set; } = "desc";
    }

    public class PersonExportResponse
    {
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public byte[] FileContent { get; set; } = Array.Empty<byte>();
        public int RecordCount { get; set; }
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public long FileSizeBytes { get; set; }
    }

    public class ExportTemplateRequest
    {
        public FileFormat Format { get; set; } = FileFormat.CSV;
        public bool IncludeSampleData { get; set; } = false;
        public bool IncludeValidationRules { get; set; } = true;
    }
}
