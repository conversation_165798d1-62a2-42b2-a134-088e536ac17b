-- =====================================================
-- Rollback Script: Firm Nature to Subcategory (MySQL)
-- Description: Rollback the migration from firm_natures back to subcategories
-- Database: MySQL
-- Author: CRM System Migration
-- Date: 2025-01-01
-- =====================================================

USE data_crm;

SELECT 'Starting rollback from Firm Nature to Subcategory...' AS Status;

-- =====================================================
-- STEP 1: VERIFY BACKUP TABLES EXIST
-- =====================================================

SELECT 'Step 1: Verifying backup tables exist...' AS Status;

-- Check if backup table exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'subcategories_backup table found'
        ELSE 'ERROR: subcategories_backup table does not exist! Cannot proceed with rollback.'
    END AS verification_result
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'subcategories_backup';

-- =====================================================
-- STEP 2: DROP FOREIGN KEY CONSTRAINTS
-- =====================================================

SELECT 'Step 2: Dropping foreign key constraints...' AS Status;

-- Drop all foreign key constraints from persons table
ALTER TABLE persons DROP FOREIGN KEY fk_persons_divisions;
ALTER TABLE persons DROP FOREIGN KEY fk_persons_categories;
ALTER TABLE persons DROP FOREIGN KEY fk_persons_firm_natures;

SELECT 'Dropped all foreign key constraints from persons table' AS Status;

-- =====================================================
-- STEP 3: BACKUP CURRENT PERSONS DATA
-- =====================================================

SELECT 'Step 3: Backing up current persons data...' AS Status;

-- Create temporary table to hold current persons data
DROP TABLE IF EXISTS persons_rollback_temp;
CREATE TABLE persons_rollback_temp AS SELECT * FROM persons;
SELECT 'Created temporary backup of current persons data' AS Status;

-- =====================================================
-- STEP 4: DROP PARTITIONED PERSONS TABLE
-- =====================================================

SELECT 'Step 4: Dropping partitioned persons table...' AS Status;

-- Drop the partitioned persons table
DROP TABLE persons;
SELECT 'Dropped partitioned persons table' AS Status;

-- =====================================================
-- STEP 5: RECREATE ORIGINAL PERSONS TABLE STRUCTURE
-- =====================================================

SELECT 'Step 5: Recreating original persons table structure...' AS Status;

-- Recreate persons table without partitioning (original structure)
CREATE TABLE persons (
    id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    division_id INT NOT NULL,
    category_id INT NOT NULL,
    subcategory_id INT NULL,
    name VARCHAR(255) NOT NULL,
    mobile_number VARCHAR(15) NOT NULL,
    nature INT NOT NULL,
    gender INT NULL,
    alternate_numbers JSON NULL,
    primary_email_id VARCHAR(255) NULL,
    alternate_email_ids JSON NULL,
    website VARCHAR(500) NULL,
    date_of_birth DATETIME NULL,
    is_married BOOLEAN NULL,
    date_of_marriage DATETIME NULL,
    working_state INT NULL,
    district VARCHAR(100) NULL,
    pin_code VARCHAR(10) NULL,
    address_line_1 VARCHAR(500) NULL,
    address_line_2 VARCHAR(500) NULL,
    landmark VARCHAR(255) NULL,
    star_rating INT NULL,
    remarks TEXT NULL,
    firm_name VARCHAR(255) NULL,
    number_of_offices INT NULL,
    number_of_branches INT NULL,
    total_employee_strength INT NULL,
    authorized_person_name VARCHAR(255) NULL,
    authorized_person_email VARCHAR(255) NULL,
    designation VARCHAR(100) NULL,
    marketing_contact VARCHAR(255) NULL,
    marketing_designation VARCHAR(100) NULL,
    place_of_posting VARCHAR(255) NULL,
    department VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP NULL
);

SELECT 'Recreated original persons table structure' AS Status;

-- =====================================================
-- STEP 6: RESTORE PERSONS DATA WITH RENAMED COLUMN
-- =====================================================

SELECT 'Step 6: Restoring persons data with renamed column...' AS Status;

-- Insert data back with column name change
INSERT INTO persons (
    id, division_id, category_id, subcategory_id, name, mobile_number, nature, gender,
    alternate_numbers, primary_email_id, alternate_email_ids, website, date_of_birth,
    is_married, date_of_marriage, working_state, district, pin_code, address_line_1,
    address_line_2, landmark, star_rating, remarks, firm_name, number_of_offices,
    number_of_branches, total_employee_strength, authorized_person_name,
    authorized_person_email, designation, marketing_contact, marketing_designation,
    place_of_posting, department, created_at, updated_at, is_deleted, deleted_at
)
SELECT 
    id, division_id, category_id, firm_nature_id, name, mobile_number, nature, gender,
    alternate_numbers, primary_email_id, alternate_email_ids, website, date_of_birth,
    is_married, date_of_marriage, working_state, district, pin_code, address_line_1,
    address_line_2, landmark, star_rating, remarks, firm_name, number_of_offices,
    number_of_branches, total_employee_strength, authorized_person_name,
    authorized_person_email, designation, marketing_contact, marketing_designation,
    place_of_posting, department, created_at, updated_at, is_deleted, deleted_at
FROM persons_rollback_temp;

SELECT 'Restored persons data with subcategory_id column' AS Status;

-- Drop temporary table
DROP TABLE persons_rollback_temp;
SELECT 'Dropped temporary backup table' AS Status;

-- =====================================================
-- STEP 7: RENAME FIRM_NATURES TABLE BACK TO SUBCATEGORIES
-- =====================================================

SELECT 'Step 7: Renaming firm_natures table back to subcategories...' AS Status;

-- Rename the table back
RENAME TABLE firm_natures TO subcategories;
SELECT 'Renamed firm_natures table back to subcategories' AS Status;

-- =====================================================
-- STEP 8: RESTORE DATA FROM BACKUP (IF NEEDED)
-- =====================================================

SELECT 'Step 8: Checking if data restoration is needed...' AS Status;

-- Compare record counts
SET @current_count = (SELECT COUNT(*) FROM subcategories);
SET @backup_count = (SELECT COUNT(*) FROM subcategories_backup);

SELECT 
    CASE 
        WHEN @current_count = @backup_count THEN 'Data counts match, no restoration needed'
        ELSE 'Data count mismatch detected. Restoring from backup...'
    END AS restoration_status;

-- Restore from backup if counts don't match
-- Note: This is a simplified check. In production, you might want more sophisticated comparison
SET @restore_needed = @current_count != @backup_count;

-- If restoration is needed, truncate and restore
-- (This would be done conditionally in a stored procedure, but MySQL script limitations require manual execution if needed)

-- =====================================================
-- STEP 9: RECREATE ORIGINAL INDEXES
-- =====================================================

SELECT 'Step 9: Recreating original indexes...' AS Status;

-- Create indexes on subcategories table
CREATE INDEX idx_subcategories_category_id ON subcategories(category_id);
CREATE INDEX idx_subcategories_name_category_id ON subcategories(name, category_id);
SELECT 'Created indexes on subcategories table' AS Status;

-- Create indexes on persons table
CREATE INDEX idx_persons_division_id ON persons(division_id);
CREATE INDEX idx_persons_category_id ON persons(category_id);
CREATE INDEX idx_persons_subcategory_id ON persons(subcategory_id);
CREATE INDEX idx_persons_mobile_number ON persons(mobile_number);
CREATE INDEX idx_persons_name ON persons(name);
CREATE INDEX idx_persons_primary_email_id ON persons(primary_email_id);
CREATE INDEX idx_persons_is_deleted ON persons(is_deleted);
CREATE INDEX idx_persons_created_at ON persons(created_at);
CREATE INDEX idx_persons_composite_division_category ON persons(division_id, category_id);
CREATE INDEX idx_persons_composite_mobile_division_category ON persons(mobile_number, division_id, category_id);

SELECT 'Created all original indexes on persons table' AS Status;

-- =====================================================
-- STEP 10: RECREATE ORIGINAL FOREIGN KEY CONSTRAINTS
-- =====================================================

SELECT 'Step 10: Recreating original foreign key constraints...' AS Status;

-- Add original foreign key constraints
ALTER TABLE persons 
ADD CONSTRAINT fk_persons_divisions 
FOREIGN KEY (division_id) REFERENCES divisions(id);

ALTER TABLE persons 
ADD CONSTRAINT fk_persons_categories 
FOREIGN KEY (category_id) REFERENCES categories(id);

ALTER TABLE persons 
ADD CONSTRAINT fk_persons_subcategories 
FOREIGN KEY (subcategory_id) REFERENCES subcategories(id)
ON DELETE SET NULL ON UPDATE CASCADE;

SELECT 'Created all original foreign key constraints' AS Status;

-- =====================================================
-- STEP 11: RECREATE ORIGINAL UNIQUE CONSTRAINTS
-- =====================================================

SELECT 'Step 11: Recreating original unique constraints...' AS Status;

-- Recreate original unique constraint for mobile number
ALTER TABLE persons 
ADD CONSTRAINT uk_mobile_division_category 
UNIQUE (mobile_number, division_id, category_id, is_deleted);

SELECT 'Created original unique constraint for mobile number' AS Status;

-- =====================================================
-- STEP 12: UPDATE STATISTICS
-- =====================================================

SELECT 'Step 12: Updating statistics...' AS Status;

-- Analyze tables to update statistics
ANALYZE TABLE subcategories;
ANALYZE TABLE persons;

SELECT 'Updated table statistics' AS Status;

-- =====================================================
-- STEP 13: VERIFICATION
-- =====================================================

SELECT 'Step 13: Verifying rollback...' AS Status;

-- Verify table exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'subcategories table exists'
        ELSE 'ERROR: subcategories table does not exist'
    END AS verification_result
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'subcategories';

-- Verify column exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'subcategory_id column exists in persons table'
        ELSE 'ERROR: subcategory_id column does not exist in persons table'
    END AS verification_result
FROM information_schema.columns 
WHERE table_schema = 'data_crm' AND table_name = 'persons' AND column_name = 'subcategory_id';

-- Verify foreign key constraint
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'fk_persons_subcategories constraint exists'
        ELSE 'ERROR: fk_persons_subcategories constraint does not exist'
    END AS verification_result
FROM information_schema.key_column_usage 
WHERE table_schema = 'data_crm' 
AND table_name = 'persons' 
AND column_name = 'subcategory_id' 
AND referenced_table_name = 'subcategories';

-- Verify partitioning is removed
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'Partitioning successfully removed from persons table'
        ELSE 'WARNING: Persons table still appears to be partitioned'
    END AS verification_result
FROM information_schema.partitions 
WHERE table_schema = 'data_crm' AND table_name = 'persons' AND partition_name IS NOT NULL;

-- Show record counts
SELECT 
    (SELECT COUNT(*) FROM subcategories) AS subcategory_count,
    (SELECT COUNT(*) FROM persons) AS persons_count;

-- =====================================================
-- FINAL STATUS
-- =====================================================

SELECT '==================================================' AS Status;
SELECT 'ROLLBACK COMPLETED SUCCESSFULLY!' AS Status;
SELECT '==================================================' AS Status;
SELECT '' AS Status;
SELECT 'Summary of rollback:' AS Status;
SELECT '✓ Renamed firm_natures table back to subcategories' AS Status;
SELECT '✓ Renamed firm_nature_id column back to subcategory_id' AS Status;
SELECT '✓ Restored all original foreign key constraints' AS Status;
SELECT '✓ Recreated all original indexes' AS Status;
SELECT '✓ Removed partitioning from persons table' AS Status;
SELECT '✓ Restored original table structure' AS Status;
SELECT '✓ Updated statistics' AS Status;
SELECT '' AS Status;
SELECT 'Next steps:' AS Status;
SELECT '1. Update your application code to use original table/column names' AS Status;
SELECT '2. Test the application thoroughly' AS Status;
SELECT '3. Remove backup tables when confident rollback is successful' AS Status;
SELECT '' AS Status;
