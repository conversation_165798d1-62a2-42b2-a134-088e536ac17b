using Microsoft.EntityFrameworkCore;
using CrmApi.Models.Admin;
using CrmApi.Models.Division;
using CrmApi.Models.Category;
using CrmApi.Models.FirmNature;
using CrmApi.Models.State;
using CrmApi.Models.Person;

namespace CrmApi.Data
{
    public class CrmDbContext : DbContext
    {
        public CrmDbContext(DbContextOptions<CrmDbContext> options) : base(options) { }

        public DbSet<Division> Divisions { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<FirmNature> FirmNatures { get; set; }
        public DbSet<State> States { get; set; }
        public DbSet<Admin> Admins { get; set; }
        public DbSet<Models.Person.Person> Persons { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Division configuration
            modelBuilder.Entity<Division>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Name).IsRequired().HasMaxLength(255).HasColumnName("name");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.HasIndex(e => e.Name).IsUnique();
                entity.ToTable("divisions");
            });

            // Category configuration
            modelBuilder.Entity<Category>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Name).IsRequired().HasMaxLength(255).HasColumnName("name");
                entity.Property(e => e.DivisionId).HasColumnName("division_id");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.HasOne(e => e.Division)
                      .WithMany(e => e.Categories)
                      .HasForeignKey(e => e.DivisionId);
                entity.HasIndex(e => new { e.DivisionId, e.Name }).IsUnique();
                entity.ToTable("categories");
            });

            // FirmNature configuration
            modelBuilder.Entity<FirmNature>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Name).IsRequired().HasMaxLength(255).HasColumnName("name");
                entity.Property(e => e.CategoryId).HasColumnName("category_id");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.HasOne(e => e.Category)
                      .WithMany(c => c.FirmNatures)
                      .HasForeignKey(e => e.CategoryId);
                entity.HasIndex(e => new { e.CategoryId, e.Name }).IsUnique();
                entity.ToTable("firm_natures");
            });

            // State configuration
            modelBuilder.Entity<State>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100).HasColumnName("name");
                entity.Property(e => e.Code).IsRequired().HasMaxLength(10).HasColumnName("code");
                entity.Property(e => e.Capital).HasMaxLength(100).HasColumnName("capital");
                entity.Property(e => e.Region).HasMaxLength(50).HasColumnName("region");
                entity.Property(e => e.IsActive).HasColumnName("is_active");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.HasIndex(e => e.Name).IsUnique();
                entity.HasIndex(e => e.Code).IsUnique();
                entity.ToTable("states");
            });



            // Admin configuration
            modelBuilder.Entity<Admin>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Username).IsRequired().HasMaxLength(50).HasColumnName("username");
                entity.Property(e => e.Email).IsRequired().HasMaxLength(255).HasColumnName("email");
                entity.Property(e => e.PasswordHash).IsRequired().HasColumnName("password_hash");
                entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100).HasColumnName("first_name");
                entity.Property(e => e.LastName).IsRequired().HasMaxLength(100).HasColumnName("last_name");
                entity.Property(e => e.IsActive).HasColumnName("is_active");
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.Property(e => e.LastLoginAt).HasColumnName("last_login_at");
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
                entity.ToTable("admins");
            });

            // Person configuration
            modelBuilder.Entity<Models.Person.Person>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id");

                // Foreign Keys
                entity.Property(e => e.DivisionId).IsRequired().HasColumnName("division_id");
                entity.Property(e => e.CategoryId).IsRequired().HasColumnName("category_id");
                entity.Property(e => e.FirmNatureId).IsRequired().HasColumnName("firm_nature_id");

                // Required Fields
                entity.Property(e => e.Name).IsRequired().HasMaxLength(255).HasColumnName("name");
                entity.Property(e => e.MobileNumber).IsRequired().HasMaxLength(15).HasColumnName("mobile_number");

                // Enum Fields
                entity.Property(e => e.Nature).IsRequired().HasColumnName("nature");
                entity.Property(e => e.Gender).HasColumnName("gender");

                // Contact Information
                entity.Property(e => e.AlternateNumbers)
                    .HasColumnName("alternate_numbers")
                    .HasConversion(
                        v => string.Join(',', v),
                        v => v.Split(',', StringSplitOptions.RemoveEmptyEntries));

                entity.Property(e => e.PrimaryEmailId).HasMaxLength(255).HasColumnName("primary_email_id");

                entity.Property(e => e.AlternateEmailIds)
                    .HasColumnName("alternate_email_ids")
                    .HasConversion(
                        v => string.Join(',', v),
                        v => v.Split(',', StringSplitOptions.RemoveEmptyEntries));

                entity.Property(e => e.Website).HasMaxLength(500).HasColumnName("website");

                // Personal Information
                entity.Property(e => e.DateOfBirth).HasColumnName("date_of_birth");
                entity.Property(e => e.IsMarried).HasColumnName("is_married");
                entity.Property(e => e.DateOfMarriage).HasColumnName("date_of_marriage");

                // Location Information
                entity.Property(e => e.WorkingState).HasMaxLength(100).HasColumnName("working_state");
                entity.Property(e => e.DomesticState).HasMaxLength(100).HasColumnName("domestic_state");
                entity.Property(e => e.District).HasMaxLength(100).HasColumnName("district");
                entity.Property(e => e.Address).HasMaxLength(500).HasColumnName("address");
                entity.Property(e => e.WorkingArea).HasMaxLength(200).HasColumnName("working_area");

                // Associate Information
                entity.Property(e => e.HasAssociate).HasColumnName("has_associate");
                entity.Property(e => e.AssociateName).HasMaxLength(255).HasColumnName("associate_name");
                entity.Property(e => e.AssociateRelation).HasMaxLength(100).HasColumnName("associate_relation");
                entity.Property(e => e.AssociateMobile).HasMaxLength(15).HasColumnName("associate_mobile");

                // Digital Presence
                entity.Property(e => e.UsingWebsite).HasColumnName("using_website");
                entity.Property(e => e.WebsiteLink).HasMaxLength(500).HasColumnName("website_link");
                entity.Property(e => e.UsingCRMApp).HasColumnName("using_crm_app");
                entity.Property(e => e.CRMAppLink).HasMaxLength(500).HasColumnName("crm_app_link");

                // Business Information
                entity.Property(e => e.TransactionValue).HasColumnType("decimal(18,2)").HasColumnName("transaction_value");
                entity.Property(e => e.RERARegistrationNumber).HasMaxLength(50).HasColumnName("rera_registration_number");

                entity.Property(e => e.WorkingProfiles)
                    .HasColumnName("working_profiles")
                    .HasConversion(
                        v => string.Join(',', v.Select(wp => (int)wp)),
                        v => v.Split(',', StringSplitOptions.RemoveEmptyEntries)
                              .Select(s => (WorkingProfile)int.Parse(s)).ToArray());

                entity.Property(e => e.StarRating).HasColumnName("star_rating");
                entity.Property(e => e.Source).HasMaxLength(200).HasColumnName("source");
                entity.Property(e => e.Remarks).HasMaxLength(1000).HasColumnName("remarks");

                // Company Information
                entity.Property(e => e.FirmName).HasMaxLength(255).HasColumnName("firm_name");
                entity.Property(e => e.NumberOfOffices).HasColumnName("number_of_offices");
                entity.Property(e => e.NumberOfBranches).HasColumnName("number_of_branches");
                entity.Property(e => e.TotalEmployeeStrength).HasColumnName("total_employee_strength");

                // Authorized Person Information
                entity.Property(e => e.AuthorizedPersonName).HasMaxLength(255).HasColumnName("authorized_person_name");
                entity.Property(e => e.AuthorizedPersonEmail).HasMaxLength(255).HasColumnName("authorized_person_email");
                entity.Property(e => e.Designation).HasMaxLength(100).HasColumnName("designation");

                // Marketing Information
                entity.Property(e => e.MarketingContact).HasMaxLength(255).HasColumnName("marketing_contact");
                entity.Property(e => e.MarketingDesignation).HasMaxLength(100).HasColumnName("marketing_designation");
                entity.Property(e => e.PlaceOfPosting).HasMaxLength(200).HasColumnName("place_of_posting");
                entity.Property(e => e.Department).HasMaxLength(100).HasColumnName("department");

                // Audit Fields
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.Property(e => e.IsDeleted).HasColumnName("is_deleted");
                entity.Property(e => e.DeletedAt).HasColumnName("deleted_at");

                // Foreign Key Relationships
                entity.HasOne(e => e.Division)
                      .WithMany()
                      .HasForeignKey(e => e.DivisionId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Category)
                      .WithMany()
                      .HasForeignKey(e => e.CategoryId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.FirmNature)
                      .WithMany()
                      .HasForeignKey(e => e.FirmNatureId)
                      .OnDelete(DeleteBehavior.Restrict);

                // Indexes for Performance
                entity.HasIndex(e => e.DivisionId).HasDatabaseName("IX_Persons_DivisionId");
                entity.HasIndex(e => e.CategoryId).HasDatabaseName("IX_Persons_CategoryId");
                entity.HasIndex(e => e.FirmNatureId).HasDatabaseName("IX_Persons_FirmNatureId");
                entity.HasIndex(e => e.MobileNumber).HasDatabaseName("IX_Persons_MobileNumber");
                entity.HasIndex(e => e.PrimaryEmailId).HasDatabaseName("IX_Persons_PrimaryEmailId");
                entity.HasIndex(e => e.Name).HasDatabaseName("IX_Persons_Name");
                entity.HasIndex(e => e.Nature).HasDatabaseName("IX_Persons_Nature");
                entity.HasIndex(e => e.WorkingState).HasDatabaseName("IX_Persons_WorkingState");
                entity.HasIndex(e => e.District).HasDatabaseName("IX_Persons_District");
                entity.HasIndex(e => e.CreatedAt).HasDatabaseName("IX_Persons_CreatedAt");
                entity.HasIndex(e => e.IsDeleted).HasDatabaseName("IX_Persons_IsDeleted");
                entity.HasIndex(e => e.StarRating).HasDatabaseName("IX_Persons_StarRating");

                // Composite Indexes for Common Queries
                entity.HasIndex(e => new { e.DivisionId, e.CategoryId }).HasDatabaseName("IX_Persons_Division_Category");
                entity.HasIndex(e => new { e.DivisionId, e.CategoryId, e.MobileNumber }).HasDatabaseName("IX_Persons_Division_Category_Mobile");
                entity.HasIndex(e => new { e.IsDeleted, e.CreatedAt }).HasDatabaseName("IX_Persons_IsDeleted_CreatedAt");
                entity.HasIndex(e => new { e.Nature, e.IsDeleted }).HasDatabaseName("IX_Persons_Nature_IsDeleted");

                // Unique Constraint for Mobile Number within Division/Category
                entity.HasIndex(e => new { e.MobileNumber, e.DivisionId, e.CategoryId, e.IsDeleted })
                      .IsUnique()
                      .HasDatabaseName("UX_Persons_Mobile_Division_Category_NotDeleted")
                      .HasFilter("is_deleted = 0");

                entity.ToTable("persons");
            });
        }
    }
}