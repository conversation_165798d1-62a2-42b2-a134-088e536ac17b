using Microsoft.AspNetCore.Mvc;
using CrmApi.Services.Person;
using CrmApi.Models.Person;
using CrmApi.Exceptions;

namespace CrmApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PartitionedPersonsController : ControllerBase
    {
        private readonly IPartitionAwarePersonService _partitionService;
        private readonly ILogger<PartitionedPersonsController> _logger;

        public PartitionedPersonsController(
            IPartitionAwarePersonService partitionService,
            ILogger<PartitionedPersonsController> logger)
        {
            _partitionService = partitionService;
            _logger = logger;
        }

        /// <summary>
        /// Get persons from a specific partition (Division/Category/FirmNature)
        /// </summary>
        [HttpGet("partition/{divisionId}")]
        public async Task<ActionResult<IEnumerable<PersonResponse>>> GetPersonsByPartition(
            int divisionId,
            [FromQuery] int? categoryId = null,
            [FromQuery] int? firmNatureId = null,
            [FromQuery] bool includeDeleted = false)
        {
            try
            {
                _logger.LogInformation("Getting persons from partition: Division={DivisionId}, Category={CategoryId}, FirmNature={FirmNatureId}",
                    divisionId, categoryId, firmNatureId);

                // Validate partition exists
                if (categoryId.HasValue)
                {
                    var partitionExists = await _partitionService.ValidatePartitionExistsAsync(divisionId, categoryId.Value, firmNatureId);
                    if (!partitionExists)
                    {
                        return BadRequest("Invalid partition: Division, Category, or FirmNature does not exist or they don't belong together.");
                    }
                }

                var persons = await _partitionService.GetPersonsByPartitionAsync(divisionId, categoryId, firmNatureId, includeDeleted);
                
                var response = persons.Select(p => new PersonResponse
                {
                    Id = p.Id,
                    DivisionId = p.DivisionId,
                    DivisionName = p.Division?.Name ?? "",
                    CategoryId = p.CategoryId,
                    CategoryName = p.Category?.Name ?? "",
                    FirmNatureId = p.FirmNatureId,
                    FirmNatureName = p.FirmNature?.Name ?? "",
                    Name = p.Name,
                    MobileNumber = p.MobileNumber,
                    Nature = p.Nature,
                    Gender = p.Gender,
                    AlternateNumbers = p.AlternateNumbers,
                    PrimaryEmailId = p.PrimaryEmailId,
                    AlternateEmailIds = p.AlternateEmailIds,
                    Website = p.Website,
                    DateOfBirth = p.DateOfBirth,
                    IsMarried = p.IsMarried,
                    DateOfMarriage = p.DateOfMarriage,
                    WorkingState = p.WorkingState,
                    DomesticState = p.DomesticState,
                    District = p.District,
                    Address = p.Address,
                    WorkingArea = p.WorkingArea,
                    HasAssociate = p.HasAssociate,
                    AssociateName = p.AssociateName,
                    AssociateRelation = p.AssociateRelation,
                    AssociateMobile = p.AssociateMobile,
                    UsingWebsite = p.UsingWebsite,
                    WebsiteLink = p.WebsiteLink,
                    UsingCRMApp = p.UsingCRMApp,
                    CRMAppLink = p.CRMAppLink,
                    TransactionValue = p.TransactionValue,
                    RERARegistrationNumber = p.RERARegistrationNumber,
                    WorkingProfiles = p.WorkingProfiles,
                    StarRating = p.StarRating,
                    Source = p.Source,
                    Remarks = p.Remarks,
                    FirmName = p.FirmName,
                    NumberOfOffices = p.NumberOfOffices,
                    NumberOfBranches = p.NumberOfBranches,
                    TotalEmployeeStrength = p.TotalEmployeeStrength,
                    AuthorizedPersonName = p.AuthorizedPersonName,
                    AuthorizedPersonEmail = p.AuthorizedPersonEmail,
                    Designation = p.Designation,
                    MarketingContact = p.MarketingContact,
                    MarketingDesignation = p.MarketingDesignation,
                    PlaceOfPosting = p.PlaceOfPosting,
                    Department = p.Department,
                    CreatedAt = p.CreatedAt,
                    UpdatedAt = p.UpdatedAt,
                    IsDeleted = p.IsDeleted,
                    DeletedAt = p.DeletedAt
                });

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting persons from partition");
                return StatusCode(500, "An error occurred while retrieving persons from partition");
            }
        }

        /// <summary>
        /// Get a specific person by ID within a partition
        /// </summary>
        [HttpGet("partition/{divisionId}/category/{categoryId}/person/{id}")]
        public async Task<ActionResult<PersonResponse>> GetPersonByIdInPartition(
            int divisionId,
            int categoryId,
            int id,
            [FromQuery] int? firmNatureId = null)
        {
            try
            {
                var person = await _partitionService.GetPersonByIdWithPartitionAsync(id, divisionId, categoryId, firmNatureId);
                
                if (person == null)
                {
                    return NotFound($"Person with ID {id} not found in the specified partition");
                }

                var response = new PersonResponse
                {
                    Id = person.Id,
                    DivisionId = person.DivisionId,
                    DivisionName = person.Division?.Name ?? "",
                    CategoryId = person.CategoryId,
                    CategoryName = person.Category?.Name ?? "",
                    FirmNatureId = person.FirmNatureId,
                    FirmNatureName = person.FirmNature?.Name ?? "",
                    Name = person.Name,
                    MobileNumber = person.MobileNumber,
                    Nature = person.Nature,
                    Gender = person.Gender,
                    AlternateNumbers = person.AlternateNumbers,
                    PrimaryEmailId = person.PrimaryEmailId,
                    AlternateEmailIds = person.AlternateEmailIds,
                    Website = person.Website,
                    DateOfBirth = person.DateOfBirth,
                    IsMarried = person.IsMarried,
                    DateOfMarriage = person.DateOfMarriage,
                    WorkingState = person.WorkingState,
                    DomesticState = person.DomesticState,
                    District = person.District,
                    Address = person.Address,
                    WorkingArea = person.WorkingArea,
                    HasAssociate = person.HasAssociate,
                    AssociateName = person.AssociateName,
                    AssociateRelation = person.AssociateRelation,
                    AssociateMobile = person.AssociateMobile,
                    UsingWebsite = person.UsingWebsite,
                    WebsiteLink = person.WebsiteLink,
                    UsingCRMApp = person.UsingCRMApp,
                    CRMAppLink = person.CRMAppLink,
                    TransactionValue = person.TransactionValue,
                    RERARegistrationNumber = person.RERARegistrationNumber,
                    WorkingProfiles = person.WorkingProfiles,
                    StarRating = person.StarRating,
                    Source = person.Source,
                    Remarks = person.Remarks,
                    FirmName = person.FirmName,
                    NumberOfOffices = person.NumberOfOffices,
                    NumberOfBranches = person.NumberOfBranches,
                    TotalEmployeeStrength = person.TotalEmployeeStrength,
                    AuthorizedPersonName = person.AuthorizedPersonName,
                    AuthorizedPersonEmail = person.AuthorizedPersonEmail,
                    Designation = person.Designation,
                    MarketingContact = person.MarketingContact,
                    MarketingDesignation = person.MarketingDesignation,
                    PlaceOfPosting = person.PlaceOfPosting,
                    Department = person.Department,
                    CreatedAt = person.CreatedAt,
                    UpdatedAt = person.UpdatedAt,
                    IsDeleted = person.IsDeleted,
                    DeletedAt = person.DeletedAt
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting person {PersonId} from partition", id);
                return StatusCode(500, "An error occurred while retrieving the person");
            }
        }

        /// <summary>
        /// Search persons within partitions with advanced filtering
        /// </summary>
        [HttpPost("search")]
        public async Task<ActionResult<IEnumerable<PersonResponse>>> SearchPersonsInPartition(
            [FromBody] PersonSearchRequest request)
        {
            try
            {
                _logger.LogInformation("Searching persons in partition with filters");

                var persons = await _partitionService.SearchPersonsInPartitionAsync(request);
                
                var response = persons.Select(p => new PersonResponse
                {
                    Id = p.Id,
                    DivisionId = p.DivisionId,
                    DivisionName = p.Division?.Name ?? "",
                    CategoryId = p.CategoryId,
                    CategoryName = p.Category?.Name ?? "",
                    FirmNatureId = p.FirmNatureId,
                    FirmNatureName = p.FirmNature?.Name ?? "",
                    Name = p.Name,
                    MobileNumber = p.MobileNumber,
                    Nature = p.Nature,
                    Gender = p.Gender,
                    PrimaryEmailId = p.PrimaryEmailId,
                    WorkingState = p.WorkingState,
                    District = p.District,
                    StarRating = p.StarRating,
                    TransactionValue = p.TransactionValue,
                    CreatedAt = p.CreatedAt,
                    UpdatedAt = p.UpdatedAt,
                    IsDeleted = p.IsDeleted
                });

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching persons in partition");
                return StatusCode(500, "An error occurred while searching persons");
            }
        }

        /// <summary>
        /// Get partition information and statistics
        /// </summary>
        [HttpGet("partition/{divisionId}/info")]
        public async Task<ActionResult<PersonPartitionInfo>> GetPartitionInfo(
            int divisionId,
            [FromQuery] int? categoryId = null,
            [FromQuery] int? firmNatureId = null)
        {
            try
            {
                var info = await _partitionService.GetPartitionInfoAsync(divisionId, categoryId, firmNatureId);
                return Ok(info);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting partition info");
                return StatusCode(500, "An error occurred while retrieving partition information");
            }
        }

        /// <summary>
        /// Get statistics for all partitions
        /// </summary>
        [HttpGet("partitions/statistics")]
        public async Task<ActionResult<IEnumerable<PartitionStatistics>>> GetPartitionStatistics()
        {
            try
            {
                var statistics = await _partitionService.GetPartitionStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting partition statistics");
                return StatusCode(500, "An error occurred while retrieving partition statistics");
            }
        }

        /// <summary>
        /// Validate if a partition exists
        /// </summary>
        [HttpGet("partition/{divisionId}/category/{categoryId}/validate")]
        public async Task<ActionResult<bool>> ValidatePartition(
            int divisionId,
            int categoryId,
            [FromQuery] int? firmNatureId = null)
        {
            try
            {
                var exists = await _partitionService.ValidatePartitionExistsAsync(divisionId, categoryId, firmNatureId);
                return Ok(new { exists, divisionId, categoryId, firmNatureId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating partition");
                return StatusCode(500, "An error occurred while validating partition");
            }
        }
    }
}
