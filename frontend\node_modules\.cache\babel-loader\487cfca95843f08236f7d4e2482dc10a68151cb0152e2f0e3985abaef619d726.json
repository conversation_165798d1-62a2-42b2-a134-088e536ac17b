{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FiPieChart, FiBarChart2, FiGrid, FiLayers, FiRefreshCw } from 'react-icons/fi';\nimport axios from 'axios';\nimport { Bar } from 'react-chartjs-2';\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';\n\n// Register Chart.js components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);\nconst API_BASE_URL = 'http://localhost:5000/api';\nconst Dashboard = () => {\n  _s();\n  const [dashboardData, setDashboardData] = useState({\n    totalDivisions: 0,\n    totalCategories: 0,\n    totalFirmNatures: 0,\n    totalStates: 0,\n    divisions: [],\n    categories: [],\n    firmNatures: [],\n    states: []\n  });\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    setLoading(true);\n    try {\n      const [divisionsRes, categoriesRes, firmNaturesRes, statesRes] = await Promise.all([axios.get(`${API_BASE_URL}/divisions`), axios.get(`${API_BASE_URL}/categories`), axios.get(`${API_BASE_URL}/firmnatures`), axios.get(`${API_BASE_URL}/states`)]);\n      const divisions = divisionsRes.data;\n      const categories = categoriesRes.data;\n      const firmNatures = firmNaturesRes.data;\n      const states = statesRes.data;\n      setDashboardData({\n        totalDivisions: divisions.length,\n        totalCategories: categories.length,\n        totalFirmNatures: firmNatures.length,\n        totalStates: states.length,\n        divisions,\n        categories,\n        firmNatures,\n        states\n      });\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      setDashboardData({\n        totalDivisions: 0,\n        totalCategories: 0,\n        totalFirmNatures: 0,\n        totalStates: 0,\n        divisions: [],\n        categories: [],\n        firmNatures: [],\n        states: []\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getRandomColor = () => {\n    const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe', '#43e97b', '#38f9d7', '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'];\n    return colors[Math.floor(Math.random() * colors.length)];\n  };\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await fetchDashboardData();\n    setRefreshing(false);\n  };\n\n  // Chart configurations\n  const divisionsChartData = {\n    labels: dashboardData.divisions.map(item => item.name),\n    datasets: [{\n      label: 'Divisions',\n      data: dashboardData.divisions.map((_, index) => index + 1),\n      backgroundColor: dashboardData.divisions.map(() => getRandomColor()),\n      borderRadius: 8\n    }]\n  };\n  const categoriesChartData = {\n    labels: dashboardData.categories.map(item => item.name),\n    datasets: [{\n      label: 'Categories',\n      data: dashboardData.categories.map((_, index) => index + 1),\n      backgroundColor: dashboardData.categories.map(() => getRandomColor()),\n      borderRadius: 8\n    }]\n  };\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'bottom',\n        labels: {\n          padding: 20,\n          usePointStyle: true\n        }\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner-large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"dashboard-header\",\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.5\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83D\\uDCCA System Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Overview of your CRM system configuration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-controls\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary dashboard-btn\",\n          onClick: handleRefresh,\n          disabled: refreshing,\n          children: [/*#__PURE__*/_jsxDEV(FiRefreshCw, {\n            className: refreshing ? 'spinning' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), \"Refresh\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"metrics-grid\",\n      children: [{\n        title: 'Divisions',\n        value: dashboardData.totalDivisions,\n        icon: FiGrid,\n        color: '#22c55e'\n      }, {\n        title: 'Categories',\n        value: dashboardData.totalCategories,\n        icon: FiLayers,\n        color: '#f59e0b'\n      }, {\n        title: 'Sub Categories',\n        value: dashboardData.totalSubCategories,\n        icon: FiBarChart2,\n        color: '#8b5cf6'\n      }, {\n        title: 'States',\n        value: dashboardData.totalStates,\n        icon: FiPieChart,\n        color: '#ef4444'\n      }].map((metric, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"metric-card\",\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          duration: 0.5,\n          delay: index * 0.1\n        },\n        whileHover: {\n          scale: 1.05\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-icon\",\n          style: {\n            backgroundColor: metric.color\n          },\n          children: /*#__PURE__*/_jsxDEV(metric.icon, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: metric.value.toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: metric.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this)]\n      }, metric.title, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"charts-grid\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"chart-card\",\n        initial: {\n          opacity: 0,\n          x: -20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [/*#__PURE__*/_jsxDEV(FiGrid, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this), \" Divisions Overview\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-container\",\n          children: /*#__PURE__*/_jsxDEV(Bar, {\n            data: divisionsChartData,\n            options: chartOptions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"chart-card\",\n        initial: {\n          opacity: 0,\n          x: 20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.1\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [/*#__PURE__*/_jsxDEV(FiLayers, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), \" Categories Overview\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-container\",\n          children: /*#__PURE__*/_jsxDEV(Bar, {\n            data: categoriesChartData,\n            options: chartOptions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tables-grid\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"table-card\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [/*#__PURE__*/_jsxDEV(FiGrid, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this), \" Divisions\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-container\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"dashboard-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: dashboardData.divisions.slice(0, 10).map(division => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: division.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: new Date(division.createdAt).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this)]\n              }, division.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"table-card\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [/*#__PURE__*/_jsxDEV(FiLayers, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this), \" Categories\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-container\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"dashboard-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Division\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Created\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: dashboardData.categories.slice(0, 10).map(category => {\n                var _category$division;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: category.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: ((_category$division = category.division) === null || _category$division === void 0 ? void 0 : _category$division.name) || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: new Date(category.createdAt).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 21\n                  }, this)]\n                }, category.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"f7nDiOM1z9HDEITlVMxEsCtr1Uo=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FiBarChart2", "<PERSON><PERSON><PERSON>", "FiLayers", "FiRefreshCw", "axios", "Bar", "Chart", "ChartJS", "CategoryScale", "LinearScale", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "jsxDEV", "_jsxDEV", "register", "API_BASE_URL", "Dashboard", "_s", "dashboardData", "setDashboardData", "totalDivisions", "totalCategories", "totalFirmNatures", "totalStates", "divisions", "categories", "firmNatures", "states", "loading", "setLoading", "refreshing", "setRefreshing", "fetchDashboardData", "divisionsRes", "categoriesRes", "firmNaturesRes", "statesRes", "Promise", "all", "get", "data", "length", "error", "console", "getRandomColor", "colors", "Math", "floor", "random", "handleRefresh", "divisionsChartData", "labels", "map", "item", "name", "datasets", "label", "_", "index", "backgroundColor", "borderRadius", "categoriesChartData", "chartOptions", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "padding", "usePointStyle", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "transition", "duration", "onClick", "disabled", "title", "value", "icon", "color", "totalSubCategories", "metric", "scale", "delay", "whileHover", "style", "size", "toLocaleString", "x", "options", "slice", "division", "Date", "createdAt", "toLocaleDateString", "id", "category", "_category$division", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport {\r\n  <PERSON><PERSON>ie<PERSON>hart,\r\n  FiBarChart2,\r\n  FiGrid,\r\n  FiLayers,\r\n  FiRefreshCw\r\n} from 'react-icons/fi';\r\nimport axios from 'axios';\r\nimport { Bar } from 'react-chartjs-2';\r\nimport {\r\n  Chart as ChartJS,\r\n  CategoryScale,\r\n  LinearScale,\r\n  BarElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n} from 'chart.js';\r\n\r\n// Register Chart.js components\r\nChartJS.register(\r\n  CategoryScale,\r\n  LinearScale,\r\n  BarElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend\r\n);\r\n\r\nconst API_BASE_URL = 'http://localhost:5000/api';\r\n\r\nconst Dashboard = () => {\r\n  const [dashboardData, setDashboardData] = useState({\r\n    totalDivisions: 0,\r\n    totalCategories: 0,\r\n    totalFirmNatures: 0,\r\n    totalStates: 0,\r\n    divisions: [],\r\n    categories: [],\r\n    firmNatures: [],\r\n    states: []\r\n  });\r\n\r\n  const [loading, setLoading] = useState(true);\r\n  const [refreshing, setRefreshing] = useState(false);\r\n\r\n  useEffect(() => {\r\n    fetchDashboardData();\r\n  }, []);\r\n\r\n  const fetchDashboardData = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const [\r\n        divisionsRes,\r\n        categoriesRes,\r\n        firmNaturesRes,\r\n        statesRes\r\n      ] = await Promise.all([\r\n        axios.get(`${API_BASE_URL}/divisions`),\r\n        axios.get(`${API_BASE_URL}/categories`),\r\n        axios.get(`${API_BASE_URL}/firmnatures`),\r\n        axios.get(`${API_BASE_URL}/states`)\r\n      ]);\r\n\r\n      const divisions = divisionsRes.data;\r\n      const categories = categoriesRes.data;\r\n      const firmNatures = firmNaturesRes.data;\r\n      const states = statesRes.data;\r\n\r\n      setDashboardData({\r\n        totalDivisions: divisions.length,\r\n        totalCategories: categories.length,\r\n        totalFirmNatures: firmNatures.length,\r\n        totalStates: states.length,\r\n        divisions,\r\n        categories,\r\n        firmNatures,\r\n        states\r\n      });\r\n    } catch (error) {\r\n      console.error('Error fetching dashboard data:', error);\r\n      setDashboardData({\r\n        totalDivisions: 0,\r\n        totalCategories: 0,\r\n        totalFirmNatures: 0,\r\n        totalStates: 0,\r\n        divisions: [],\r\n        categories: [],\r\n        firmNatures: [],\r\n        states: []\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getRandomColor = () => {\r\n    const colors = [\r\n      '#667eea', '#764ba2', '#f093fb', '#f5576c',\r\n      '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',\r\n      '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'\r\n    ];\r\n    return colors[Math.floor(Math.random() * colors.length)];\r\n  };\r\n\r\n  const handleRefresh = async () => {\r\n    setRefreshing(true);\r\n    await fetchDashboardData();\r\n    setRefreshing(false);\r\n  };\r\n\r\n  // Chart configurations\r\n  const divisionsChartData = {\r\n    labels: dashboardData.divisions.map(item => item.name),\r\n    datasets: [\r\n      {\r\n        label: 'Divisions',\r\n        data: dashboardData.divisions.map((_, index) => index + 1),\r\n        backgroundColor: dashboardData.divisions.map(() => getRandomColor()),\r\n        borderRadius: 8\r\n      }\r\n    ]\r\n  };\r\n\r\n  const categoriesChartData = {\r\n    labels: dashboardData.categories.map(item => item.name),\r\n    datasets: [\r\n      {\r\n        label: 'Categories',\r\n        data: dashboardData.categories.map((_, index) => index + 1),\r\n        backgroundColor: dashboardData.categories.map(() => getRandomColor()),\r\n        borderRadius: 8\r\n      }\r\n    ]\r\n  };\r\n\r\n  const chartOptions = {\r\n    responsive: true,\r\n    maintainAspectRatio: false,\r\n    plugins: {\r\n      legend: {\r\n        position: 'bottom',\r\n        labels: {\r\n          padding: 20,\r\n          usePointStyle: true\r\n        }\r\n      }\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"dashboard-loading\">\r\n        <div className=\"loading-spinner-large\"></div>\r\n        <p>Loading dashboard...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"dashboard\">\r\n      {/* Dashboard Header */}\r\n      <motion.div\r\n        className=\"dashboard-header\"\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n      >\r\n        <div className=\"dashboard-title\">\r\n          <h1>📊 System Overview</h1>\r\n          <p>Overview of your CRM system configuration</p>\r\n        </div>\r\n\r\n        <div className=\"dashboard-controls\">\r\n          <button\r\n            className=\"btn btn-secondary dashboard-btn\"\r\n            onClick={handleRefresh}\r\n            disabled={refreshing}\r\n          >\r\n            <FiRefreshCw className={refreshing ? 'spinning' : ''} />\r\n            Refresh\r\n          </button>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Key Metrics Cards */}\r\n      <div className=\"metrics-grid\">\r\n        {[\r\n          { title: 'Divisions', value: dashboardData.totalDivisions, icon: FiGrid, color: '#22c55e' },\r\n          { title: 'Categories', value: dashboardData.totalCategories, icon: FiLayers, color: '#f59e0b' },\r\n          { title: 'Sub Categories', value: dashboardData.totalSubCategories, icon: FiBarChart2, color: '#8b5cf6' },\r\n          { title: 'States', value: dashboardData.totalStates, icon: FiPieChart, color: '#ef4444' }\r\n        ].map((metric, index) => (\r\n          <motion.div\r\n            key={metric.title}\r\n            className=\"metric-card\"\r\n            initial={{ opacity: 0, scale: 0.9 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 0.5, delay: index * 0.1 }}\r\n            whileHover={{ scale: 1.05 }}\r\n          >\r\n            <div className=\"metric-icon\" style={{ backgroundColor: metric.color }}>\r\n              <metric.icon size={24} />\r\n            </div>\r\n            <div className=\"metric-content\">\r\n              <h3>{metric.value.toLocaleString()}</h3>\r\n              <p>{metric.title}</p>\r\n            </div>\r\n          </motion.div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Charts Section */}\r\n      <div className=\"charts-grid\">\r\n        {/* Division Overview */}\r\n        <motion.div\r\n          className=\"chart-card\"\r\n          initial={{ opacity: 0, x: -20 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n        >\r\n          <div className=\"chart-header\">\r\n            <h3><FiGrid /> Divisions Overview</h3>\r\n          </div>\r\n          <div className=\"chart-container\">\r\n            <Bar data={divisionsChartData} options={chartOptions} />\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Categories Overview */}\r\n        <motion.div\r\n          className=\"chart-card\"\r\n          initial={{ opacity: 0, x: 20 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ duration: 0.6, delay: 0.1 }}\r\n        >\r\n          <div className=\"chart-header\">\r\n            <h3><FiLayers /> Categories Overview</h3>\r\n          </div>\r\n          <div className=\"chart-container\">\r\n            <Bar data={categoriesChartData} options={chartOptions} />\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* Data Tables Section */}\r\n      <div className=\"tables-grid\">\r\n        {/* Divisions List */}\r\n        <motion.div\r\n          className=\"table-card\"\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6, delay: 0.4 }}\r\n        >\r\n          <div className=\"table-header\">\r\n            <h3><FiGrid /> Divisions</h3>\r\n          </div>\r\n          <div className=\"table-container\">\r\n            <table className=\"dashboard-table\">\r\n              <thead>\r\n                <tr>\r\n                  <th>Name</th>\r\n                  <th>Created</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {dashboardData.divisions.slice(0, 10).map(division => (\r\n                  <tr key={division.id}>\r\n                    <td>{division.name}</td>\r\n                    <td>{new Date(division.createdAt).toLocaleDateString()}</td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Categories List */}\r\n        <motion.div\r\n          className=\"table-card\"\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6, delay: 0.5 }}\r\n        >\r\n          <div className=\"table-header\">\r\n            <h3><FiLayers /> Categories</h3>\r\n          </div>\r\n          <div className=\"table-container\">\r\n            <table className=\"dashboard-table\">\r\n              <thead>\r\n                <tr>\r\n                  <th>Name</th>\r\n                  <th>Division</th>\r\n                  <th>Created</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {dashboardData.categories.slice(0, 10).map(category => (\r\n                  <tr key={category.id}>\r\n                    <td>{category.name}</td>\r\n                    <td>{category.division?.name || '-'}</td>\r\n                    <td>{new Date(category.createdAt).toLocaleDateString()}</td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Dashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,UAAU,EACVC,WAAW,EACXC,MAAM,EACNC,QAAQ,EACRC,WAAW,QACN,gBAAgB;AACvB,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,QAAQ,iBAAiB;AACrC,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,QACD,UAAU;;AAEjB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAR,OAAO,CAACS,QAAQ,CACdR,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MACF,CAAC;AAED,MAAMI,YAAY,GAAG,2BAA2B;AAEhD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC;IACjD0B,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,WAAW,EAAE,CAAC;IACdC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACdqC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM,CACJI,YAAY,EACZC,aAAa,EACbC,cAAc,EACdC,SAAS,CACV,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpBpC,KAAK,CAACqC,GAAG,CAAC,GAAGxB,YAAY,YAAY,CAAC,EACtCb,KAAK,CAACqC,GAAG,CAAC,GAAGxB,YAAY,aAAa,CAAC,EACvCb,KAAK,CAACqC,GAAG,CAAC,GAAGxB,YAAY,cAAc,CAAC,EACxCb,KAAK,CAACqC,GAAG,CAAC,GAAGxB,YAAY,SAAS,CAAC,CACpC,CAAC;MAEF,MAAMS,SAAS,GAAGS,YAAY,CAACO,IAAI;MACnC,MAAMf,UAAU,GAAGS,aAAa,CAACM,IAAI;MACrC,MAAMd,WAAW,GAAGS,cAAc,CAACK,IAAI;MACvC,MAAMb,MAAM,GAAGS,SAAS,CAACI,IAAI;MAE7BrB,gBAAgB,CAAC;QACfC,cAAc,EAAEI,SAAS,CAACiB,MAAM;QAChCpB,eAAe,EAAEI,UAAU,CAACgB,MAAM;QAClCnB,gBAAgB,EAAEI,WAAW,CAACe,MAAM;QACpClB,WAAW,EAAEI,MAAM,CAACc,MAAM;QAC1BjB,SAAS;QACTC,UAAU;QACVC,WAAW;QACXC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDvB,gBAAgB,CAAC;QACfC,cAAc,EAAE,CAAC;QACjBC,eAAe,EAAE,CAAC;QAClBC,gBAAgB,EAAE,CAAC;QACnBC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE,EAAE;QACbC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,EAAE;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMe,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,MAAM,GAAG,CACb,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC3C;IACD,OAAOA,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGH,MAAM,CAACJ,MAAM,CAAC,CAAC;EAC1D,CAAC;EAED,MAAMQ,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChClB,aAAa,CAAC,IAAI,CAAC;IACnB,MAAMC,kBAAkB,CAAC,CAAC;IAC1BD,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAMmB,kBAAkB,GAAG;IACzBC,MAAM,EAAEjC,aAAa,CAACM,SAAS,CAAC4B,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC;IACtDC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,WAAW;MAClBhB,IAAI,EAAEtB,aAAa,CAACM,SAAS,CAAC4B,GAAG,CAAC,CAACK,CAAC,EAAEC,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC;MAC1DC,eAAe,EAAEzC,aAAa,CAACM,SAAS,CAAC4B,GAAG,CAAC,MAAMR,cAAc,CAAC,CAAC,CAAC;MACpEgB,YAAY,EAAE;IAChB,CAAC;EAEL,CAAC;EAED,MAAMC,mBAAmB,GAAG;IAC1BV,MAAM,EAAEjC,aAAa,CAACO,UAAU,CAAC2B,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC;IACvDC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,YAAY;MACnBhB,IAAI,EAAEtB,aAAa,CAACO,UAAU,CAAC2B,GAAG,CAAC,CAACK,CAAC,EAAEC,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC;MAC3DC,eAAe,EAAEzC,aAAa,CAACO,UAAU,CAAC2B,GAAG,CAAC,MAAMR,cAAc,CAAC,CAAC,CAAC;MACrEgB,YAAY,EAAE;IAChB,CAAC;EAEL,CAAC;EAED,MAAME,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClBhB,MAAM,EAAE;UACNiB,OAAO,EAAE,EAAE;UACXC,aAAa,EAAE;QACjB;MACF;IACF;EACF,CAAC;EAED,IAAIzC,OAAO,EAAE;IACX,oBACEf,OAAA;MAAKyD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC1D,OAAA;QAAKyD,SAAS,EAAC;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7C9D,OAAA;QAAA0D,QAAA,EAAG;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAEV;EAEA,oBACE9D,OAAA;IAAKyD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB1D,OAAA,CAACjB,MAAM,CAACgF,GAAG;MACTN,SAAS,EAAC,kBAAkB;MAC5BO,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAX,QAAA,gBAE9B1D,OAAA;QAAKyD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B1D,OAAA;UAAA0D,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B9D,OAAA;UAAA0D,QAAA,EAAG;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAEN9D,OAAA;QAAKyD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjC1D,OAAA;UACEyD,SAAS,EAAC,iCAAiC;UAC3Ca,OAAO,EAAElC,aAAc;UACvBmC,QAAQ,EAAEtD,UAAW;UAAAyC,QAAA,gBAErB1D,OAAA,CAACZ,WAAW;YAACqE,SAAS,EAAExC,UAAU,GAAG,UAAU,GAAG;UAAG;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAE1D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGb9D,OAAA;MAAKyD,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1B,CACC;QAAEc,KAAK,EAAE,WAAW;QAAEC,KAAK,EAAEpE,aAAa,CAACE,cAAc;QAAEmE,IAAI,EAAExF,MAAM;QAAEyF,KAAK,EAAE;MAAU,CAAC,EAC3F;QAAEH,KAAK,EAAE,YAAY;QAAEC,KAAK,EAAEpE,aAAa,CAACG,eAAe;QAAEkE,IAAI,EAAEvF,QAAQ;QAAEwF,KAAK,EAAE;MAAU,CAAC,EAC/F;QAAEH,KAAK,EAAE,gBAAgB;QAAEC,KAAK,EAAEpE,aAAa,CAACuE,kBAAkB;QAAEF,IAAI,EAAEzF,WAAW;QAAE0F,KAAK,EAAE;MAAU,CAAC,EACzG;QAAEH,KAAK,EAAE,QAAQ;QAAEC,KAAK,EAAEpE,aAAa,CAACK,WAAW;QAAEgE,IAAI,EAAE1F,UAAU;QAAE2F,KAAK,EAAE;MAAU,CAAC,CAC1F,CAACpC,GAAG,CAAC,CAACsC,MAAM,EAAEhC,KAAK,kBAClB7C,OAAA,CAACjB,MAAM,CAACgF,GAAG;QAETN,SAAS,EAAC,aAAa;QACvBO,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEa,KAAK,EAAE;QAAI,CAAE;QACpCX,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEa,KAAK,EAAE;QAAE,CAAE;QAClCV,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEU,KAAK,EAAElC,KAAK,GAAG;QAAI,CAAE;QAClDmC,UAAU,EAAE;UAAEF,KAAK,EAAE;QAAK,CAAE;QAAApB,QAAA,gBAE5B1D,OAAA;UAAKyD,SAAS,EAAC,aAAa;UAACwB,KAAK,EAAE;YAAEnC,eAAe,EAAE+B,MAAM,CAACF;UAAM,CAAE;UAAAjB,QAAA,eACpE1D,OAAA,CAAC6E,MAAM,CAACH,IAAI;YAACQ,IAAI,EAAE;UAAG;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACN9D,OAAA;UAAKyD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B1D,OAAA;YAAA0D,QAAA,EAAKmB,MAAM,CAACJ,KAAK,CAACU,cAAc,CAAC;UAAC;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxC9D,OAAA;YAAA0D,QAAA,EAAImB,MAAM,CAACL;UAAK;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA,GAbDe,MAAM,CAACL,KAAK;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAcP,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAE1B1D,OAAA,CAACjB,MAAM,CAACgF,GAAG;QACTN,SAAS,EAAC,YAAY;QACtBO,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEmB,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCjB,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEmB,CAAC,EAAE;QAAE,CAAE;QAC9BhB,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAX,QAAA,gBAE9B1D,OAAA;UAAKyD,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B1D,OAAA;YAAA0D,QAAA,gBAAI1D,OAAA,CAACd,MAAM;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,uBAAmB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACN9D,OAAA;UAAKyD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1D,OAAA,CAACV,GAAG;YAACqC,IAAI,EAAEU,kBAAmB;YAACgD,OAAO,EAAEpC;UAAa;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb9D,OAAA,CAACjB,MAAM,CAACgF,GAAG;QACTN,SAAS,EAAC,YAAY;QACtBO,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEmB,CAAC,EAAE;QAAG,CAAE;QAC/BjB,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEmB,CAAC,EAAE;QAAE,CAAE;QAC9BhB,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEU,KAAK,EAAE;QAAI,CAAE;QAAArB,QAAA,gBAE1C1D,OAAA;UAAKyD,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B1D,OAAA;YAAA0D,QAAA,gBAAI1D,OAAA,CAACb,QAAQ;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wBAAoB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACN9D,OAAA;UAAKyD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1D,OAAA,CAACV,GAAG;YAACqC,IAAI,EAAEqB,mBAAoB;YAACqC,OAAO,EAAEpC;UAAa;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAE1B1D,OAAA,CAACjB,MAAM,CAACgF,GAAG;QACTN,SAAS,EAAC,YAAY;QACtBO,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEU,KAAK,EAAE;QAAI,CAAE;QAAArB,QAAA,gBAE1C1D,OAAA;UAAKyD,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B1D,OAAA;YAAA0D,QAAA,gBAAI1D,OAAA,CAACd,MAAM;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAAU;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACN9D,OAAA;UAAKyD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1D,OAAA;YAAOyD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAChC1D,OAAA;cAAA0D,QAAA,eACE1D,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBAAA0D,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACb9D,OAAA;kBAAA0D,QAAA,EAAI;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR9D,OAAA;cAAA0D,QAAA,EACGrD,aAAa,CAACM,SAAS,CAAC2E,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC/C,GAAG,CAACgD,QAAQ,iBAChDvF,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBAAA0D,QAAA,EAAK6B,QAAQ,CAAC9C;gBAAI;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxB9D,OAAA;kBAAA0D,QAAA,EAAK,IAAI8B,IAAI,CAACD,QAAQ,CAACE,SAAS,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA,GAFrDyB,QAAQ,CAACI,EAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGhB,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb9D,OAAA,CAACjB,MAAM,CAACgF,GAAG;QACTN,SAAS,EAAC,YAAY;QACtBO,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEU,KAAK,EAAE;QAAI,CAAE;QAAArB,QAAA,gBAE1C1D,OAAA;UAAKyD,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B1D,OAAA;YAAA0D,QAAA,gBAAI1D,OAAA,CAACb,QAAQ;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAAW;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACN9D,OAAA;UAAKyD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1D,OAAA;YAAOyD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAChC1D,OAAA;cAAA0D,QAAA,eACE1D,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA;kBAAA0D,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACb9D,OAAA;kBAAA0D,QAAA,EAAI;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjB9D,OAAA;kBAAA0D,QAAA,EAAI;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR9D,OAAA;cAAA0D,QAAA,EACGrD,aAAa,CAACO,UAAU,CAAC0E,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC/C,GAAG,CAACqD,QAAQ;gBAAA,IAAAC,kBAAA;gBAAA,oBACjD7F,OAAA;kBAAA0D,QAAA,gBACE1D,OAAA;oBAAA0D,QAAA,EAAKkC,QAAQ,CAACnD;kBAAI;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxB9D,OAAA;oBAAA0D,QAAA,EAAK,EAAAmC,kBAAA,GAAAD,QAAQ,CAACL,QAAQ,cAAAM,kBAAA,uBAAjBA,kBAAA,CAAmBpD,IAAI,KAAI;kBAAG;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzC9D,OAAA;oBAAA0D,QAAA,EAAK,IAAI8B,IAAI,CAACI,QAAQ,CAACH,SAAS,CAAC,CAACC,kBAAkB,CAAC;kBAAC;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA,GAHrD8B,QAAQ,CAACD,EAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIhB,CAAC;cAAA,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAzRID,SAAS;AAAA2F,EAAA,GAAT3F,SAAS;AA2Rf,eAAeA,SAAS;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}