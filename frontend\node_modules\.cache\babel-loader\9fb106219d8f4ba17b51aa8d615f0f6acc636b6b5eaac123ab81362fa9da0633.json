{"ast": null, "code": "import { complex } from '../../../value/types/complex/index.mjs';\nimport { filter } from '../../../value/types/complex/filter.mjs';\nimport { getDefaultValueType } from './defaults.mjs';\nfunction getAnimatableNone(key, value) {\n  let defaultValueType = getDefaultValueType(key);\n  if (defaultValueType !== filter) defaultValueType = complex;\n  // If value is not recognised as animatable, ie \"none\", create an animatable version origin based on the target\n  return defaultValueType.getAnimatableNone ? defaultValueType.getAnimatableNone(value) : undefined;\n}\nexport { getAnimatableNone };", "map": {"version": 3, "names": ["complex", "filter", "getDefaultValueType", "getAnimatableNone", "key", "value", "defaultValueType", "undefined"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/render/dom/value-types/animatable-none.mjs"], "sourcesContent": ["import { complex } from '../../../value/types/complex/index.mjs';\nimport { filter } from '../../../value/types/complex/filter.mjs';\nimport { getDefaultValueType } from './defaults.mjs';\n\nfunction getAnimatableNone(key, value) {\n    let defaultValueType = getDefaultValueType(key);\n    if (defaultValueType !== filter)\n        defaultValueType = complex;\n    // If value is not recognised as animatable, ie \"none\", create an animatable version origin based on the target\n    return defaultValueType.getAnimatableNone\n        ? defaultValueType.getAnimatableNone(value)\n        : undefined;\n}\n\nexport { getAnimatableNone };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wCAAwC;AAChE,SAASC,MAAM,QAAQ,yCAAyC;AAChE,SAASC,mBAAmB,QAAQ,gBAAgB;AAEpD,SAASC,iBAAiBA,CAACC,GAAG,EAAEC,KAAK,EAAE;EACnC,IAAIC,gBAAgB,GAAGJ,mBAAmB,CAACE,GAAG,CAAC;EAC/C,IAAIE,gBAAgB,KAAKL,MAAM,EAC3BK,gBAAgB,GAAGN,OAAO;EAC9B;EACA,OAAOM,gBAAgB,CAACH,iBAAiB,GACnCG,gBAAgB,CAACH,iBAAiB,CAACE,KAAK,CAAC,GACzCE,SAAS;AACnB;AAEA,SAASJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}