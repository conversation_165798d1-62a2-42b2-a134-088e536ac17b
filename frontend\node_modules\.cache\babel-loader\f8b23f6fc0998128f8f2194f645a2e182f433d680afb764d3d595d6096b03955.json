{"ast": null, "code": "/**\n * React Router v6.30.1\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { UNSAFE_invariant, joinPaths, matchPath, UNSAFE_decodePath, UNSAFE_getResolveToMatches, UNSAFE_warning, resolveTo, parsePath, matchRoutes, Action, UNSAFE_convertRouteMatchToUiMatch, stripBasename, IDLE_BLOCKER, isRouteErrorResponse, createMemoryHistory, AbortedDeferredError, createRouter } from '@remix-run/router';\nexport { AbortedDeferredError, Action as NavigationType, createPath, defer, generatePath, isRouteErrorResponse, json, matchPath, matchRoutes, parsePath, redirect, redirectDocument, replace, resolvePath } from '@remix-run/router';\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nconst DataRouterContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  DataRouterContext.displayName = \"DataRouter\";\n}\nconst DataRouterStateContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\nconst AwaitContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  AwaitContext.displayName = \"Await\";\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level `<Router>` API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\n\nconst NavigationContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  NavigationContext.displayName = \"Navigation\";\n}\nconst LocationContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  LocationContext.displayName = \"Location\";\n}\nconst RouteContext = /*#__PURE__*/React.createContext({\n  outlet: null,\n  matches: [],\n  isDataRoute: false\n});\nif (process.env.NODE_ENV !== \"production\") {\n  RouteContext.displayName = \"Route\";\n}\nconst RouteErrorContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/v6/hooks/use-href\n */\nfunction useHref(to, _temp) {\n  let {\n    relative\n  } = _temp === void 0 ? {} : _temp;\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useHref() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    basename,\n    navigator\n  } = React.useContext(NavigationContext);\n  let {\n    hash,\n    pathname,\n    search\n  } = useResolvedPath(to, {\n    relative\n  });\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname = pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n  return navigator.createHref({\n    pathname: joinedPathname,\n    search,\n    hash\n  });\n}\n\n/**\n * Returns true if this component is a descendant of a `<Router>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-in-router-context\n */\nfunction useInRouterContext() {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/v6/hooks/use-location\n */\nfunction useLocation() {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useLocation() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigation-type\n */\nfunction useNavigationType() {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * `<NavLink>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-match\n */\nfunction useMatch(pattern) {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useMatch() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    pathname\n  } = useLocation();\n  return React.useMemo(() => matchPath(pattern, UNSAFE_decodePath(pathname)), [pathname, pattern]);\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\n\nconst navigateEffectWarning = \"You should call navigate() in a React.useEffect(), not when \" + \"your component is first rendered.\";\n\n// Mute warnings for calls to useNavigate in SSR environments\nfunction useIsomorphicLayoutEffect(cb) {\n  let isStatic = React.useContext(NavigationContext).static;\n  if (!isStatic) {\n    // We should be able to get rid of this once react 18.3 is released\n    // See: https://github.com/facebook/react/pull/26395\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(cb);\n  }\n}\n\n/**\n * Returns an imperative method for changing the location. Used by `<Link>`s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigate\n */\nfunction useNavigate() {\n  let {\n    isDataRoute\n  } = React.useContext(RouteContext);\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\nfunction useNavigateUnstable() {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useNavigate() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let dataRouterContext = React.useContext(DataRouterContext);\n  let {\n    basename,\n    future,\n    navigator\n  } = React.useContext(NavigationContext);\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify(UNSAFE_getResolveToMatches(matches, future.v7_relativeSplatPath));\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n  let navigate = React.useCallback(function (to, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(activeRef.current, navigateEffectWarning) : void 0;\n\n    // Short circuit here since if this happens on first render the navigate\n    // is useless because we haven't wired up our history listener yet\n    if (!activeRef.current) return;\n    if (typeof to === \"number\") {\n      navigator.go(to);\n      return;\n    }\n    let path = resolveTo(to, JSON.parse(routePathnamesJson), locationPathname, options.relative === \"path\");\n\n    // If we're operating within a basename, prepend it to the pathname prior\n    // to handing off to history (but only if we're not in a data router,\n    // otherwise it'll prepend the basename inside of the router).\n    // If this is a root navigation, then we navigate to the raw basename\n    // which allows the basename to have full control over the presence of a\n    // trailing slash on root links\n    if (dataRouterContext == null && basename !== \"/\") {\n      path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n    }\n    (!!options.replace ? navigator.replace : navigator.push)(path, options.state, options);\n  }, [basename, navigator, routePathnamesJson, locationPathname, dataRouterContext]);\n  return navigate;\n}\nconst OutletContext = /*#__PURE__*/React.createContext(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/v6/hooks/use-outlet-context\n */\nfunction useOutletContext() {\n  return React.useContext(OutletContext);\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by `<Outlet>` to render child routes.\n *\n * @see https://reactrouter.com/v6/hooks/use-outlet\n */\nfunction useOutlet(context) {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return /*#__PURE__*/React.createElement(OutletContext.Provider, {\n      value: context\n    }, outlet);\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/v6/hooks/use-params\n */\nfunction useParams() {\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? routeMatch.params : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/v6/hooks/use-resolved-path\n */\nfunction useResolvedPath(to, _temp2) {\n  let {\n    relative\n  } = _temp2 === void 0 ? {} : _temp2;\n  let {\n    future\n  } = React.useContext(NavigationContext);\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify(UNSAFE_getResolveToMatches(matches, future.v7_relativeSplatPath));\n  return React.useMemo(() => resolveTo(to, JSON.parse(routePathnamesJson), locationPathname, relative === \"path\"), [to, routePathnamesJson, locationPathname, relative]);\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an `<Outlet>` to render their child route's\n * element.\n *\n * @see https://reactrouter.com/v6/hooks/use-routes\n */\nfunction useRoutes(routes, locationArg) {\n  return useRoutesImpl(routes, locationArg);\n}\n\n// Internal implementation with accept optional param for RouterProvider usage\nfunction useRoutesImpl(routes, locationArg, dataRouterState, future) {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useRoutes() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    navigator\n  } = React.useContext(NavigationContext);\n  let {\n    matches: parentMatches\n  } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n  if (process.env.NODE_ENV !== \"production\") {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = parentRoute && parentRoute.path || \"\";\n    warningOnce(parentPathname, !parentRoute || parentPath.endsWith(\"*\"), \"You rendered descendant <Routes> (or called `useRoutes()`) at \" + (\"\\\"\" + parentPathname + \"\\\" (under <Route path=\\\"\" + parentPath + \"\\\">) but the \") + \"parent route path has no trailing \\\"*\\\". This means if you navigate \" + \"deeper, the parent won't match anymore and therefore the child \" + \"routes will never render.\\n\\n\" + (\"Please change the parent <Route path=\\\"\" + parentPath + \"\\\"> to <Route \") + (\"path=\\\"\" + (parentPath === \"/\" ? \"*\" : parentPath + \"/*\") + \"\\\">.\"));\n  }\n  let locationFromContext = useLocation();\n  let location;\n  if (locationArg) {\n    var _parsedLocationArg$pa;\n    let parsedLocationArg = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n    !(parentPathnameBase === \"/\" || ((_parsedLocationArg$pa = parsedLocationArg.pathname) == null ? void 0 : _parsedLocationArg$pa.startsWith(parentPathnameBase))) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, \" + \"the location pathname must begin with the portion of the URL pathname that was \" + (\"matched by all parent routes. The current pathname base is \\\"\" + parentPathnameBase + \"\\\" \") + (\"but pathname \\\"\" + parsedLocationArg.pathname + \"\\\" was given in the `location` prop.\")) : UNSAFE_invariant(false) : void 0;\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n  let pathname = location.pathname || \"/\";\n  let remainingPathname = pathname;\n  if (parentPathnameBase !== \"/\") {\n    // Determine the remaining pathname by removing the # of URL segments the\n    // parentPathnameBase has, instead of removing based on character count.\n    // This is because we can't guarantee that incoming/outgoing encodings/\n    // decodings will match exactly.\n    // We decode paths before matching on a per-segment basis with\n    // decodeURIComponent(), but we re-encode pathnames via `new URL()` so they\n    // match what `window.location.pathname` would reflect.  Those don't 100%\n    // align when it comes to encoded URI characters such as % and &.\n    //\n    // So we may end up with:\n    //   pathname:           \"/descendant/a%25b/match\"\n    //   parentPathnameBase: \"/descendant/a%b\"\n    //\n    // And the direct substring removal approach won't work :/\n    let parentSegments = parentPathnameBase.replace(/^\\//, \"\").split(\"/\");\n    let segments = pathname.replace(/^\\//, \"\").split(\"/\");\n    remainingPathname = \"/\" + segments.slice(parentSegments.length).join(\"/\");\n  }\n  let matches = matchRoutes(routes, {\n    pathname: remainingPathname\n  });\n  if (process.env.NODE_ENV !== \"production\") {\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(parentRoute || matches != null, \"No routes matched location \\\"\" + location.pathname + location.search + location.hash + \"\\\" \") : void 0;\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(matches == null || matches[matches.length - 1].route.element !== undefined || matches[matches.length - 1].route.Component !== undefined || matches[matches.length - 1].route.lazy !== undefined, \"Matched leaf route at location \\\"\" + location.pathname + location.search + location.hash + \"\\\" \" + \"does not have an element or Component. This means it will render an <Outlet /> with a \" + \"null value by default resulting in an \\\"empty\\\" page.\") : void 0;\n  }\n  let renderedMatches = _renderMatches(matches && matches.map(match => Object.assign({}, match, {\n    params: Object.assign({}, parentParams, match.params),\n    pathname: joinPaths([parentPathnameBase,\n    // Re-encode pathnames that were decoded inside matchRoutes\n    navigator.encodeLocation ? navigator.encodeLocation(match.pathname).pathname : match.pathname]),\n    pathnameBase: match.pathnameBase === \"/\" ? parentPathnameBase : joinPaths([parentPathnameBase,\n    // Re-encode pathnames that were decoded inside matchRoutes\n    navigator.encodeLocation ? navigator.encodeLocation(match.pathnameBase).pathname : match.pathnameBase])\n  })), parentMatches, dataRouterState, future);\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return /*#__PURE__*/React.createElement(LocationContext.Provider, {\n      value: {\n        location: _extends({\n          pathname: \"/\",\n          search: \"\",\n          hash: \"\",\n          state: null,\n          key: \"default\"\n        }, location),\n        navigationType: Action.Pop\n      }\n    }, renderedMatches);\n  }\n  return renderedMatches;\n}\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error) ? error.status + \" \" + error.statusText : error instanceof Error ? error.message : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = {\n    padding: \"0.5rem\",\n    backgroundColor: lightgrey\n  };\n  let codeStyles = {\n    padding: \"2px 4px\",\n    backgroundColor: lightgrey\n  };\n  let devInfo = null;\n  if (process.env.NODE_ENV !== \"production\") {\n    console.error(\"Error handled by React Router default ErrorBoundary:\", error);\n    devInfo = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"p\", null, \"\\uD83D\\uDCBF Hey developer \\uD83D\\uDC4B\"), /*#__PURE__*/React.createElement(\"p\", null, \"You can provide a way better UX than this when your app throws errors by providing your own \", /*#__PURE__*/React.createElement(\"code\", {\n      style: codeStyles\n    }, \"ErrorBoundary\"), \" or\", \" \", /*#__PURE__*/React.createElement(\"code\", {\n      style: codeStyles\n    }, \"errorElement\"), \" prop on your route.\"));\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"h2\", null, \"Unexpected Application Error!\"), /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      fontStyle: \"italic\"\n    }\n  }, message), stack ? /*#__PURE__*/React.createElement(\"pre\", {\n    style: preStyles\n  }, stack) : null, devInfo);\n}\nconst defaultErrorElement = /*#__PURE__*/React.createElement(DefaultErrorComponent, null);\nclass RenderErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error: error\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (state.location !== props.location || state.revalidation !== \"idle\" && props.revalidation === \"idle\") {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error !== undefined ? props.error : state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\"React Router caught the following error during render\", error, errorInfo);\n  }\n  render() {\n    return this.state.error !== undefined ? /*#__PURE__*/React.createElement(RouteContext.Provider, {\n      value: this.props.routeContext\n    }, /*#__PURE__*/React.createElement(RouteErrorContext.Provider, {\n      value: this.state.error,\n      children: this.props.component\n    })) : this.props.children;\n  }\n}\nfunction RenderedRoute(_ref) {\n  let {\n    routeContext,\n    match,\n    children\n  } = _ref;\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (dataRouterContext && dataRouterContext.static && dataRouterContext.staticContext && (match.route.errorElement || match.route.ErrorBoundary)) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n  return /*#__PURE__*/React.createElement(RouteContext.Provider, {\n    value: routeContext\n  }, children);\n}\nfunction _renderMatches(matches, parentMatches, dataRouterState, future) {\n  var _dataRouterState;\n  if (parentMatches === void 0) {\n    parentMatches = [];\n  }\n  if (dataRouterState === void 0) {\n    dataRouterState = null;\n  }\n  if (future === void 0) {\n    future = null;\n  }\n  if (matches == null) {\n    var _future;\n    if (!dataRouterState) {\n      return null;\n    }\n    if (dataRouterState.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches;\n    } else if ((_future = future) != null && _future.v7_partialHydration && parentMatches.length === 0 && !dataRouterState.initialized && dataRouterState.matches.length > 0) {\n      // Don't bail if we're initializing with partial hydration and we have\n      // router matches.  That means we're actively running `patchRoutesOnNavigation`\n      // so we should render down the partial matches to the appropriate\n      // `HydrateFallback`.  We only do this if `parentMatches` is empty so it\n      // only impacts the root matches for `RouterProvider` and no descendant\n      // `<Routes>`\n      matches = dataRouterState.matches;\n    } else {\n      return null;\n    }\n  }\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = (_dataRouterState = dataRouterState) == null ? void 0 : _dataRouterState.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(m => m.route.id && (errors == null ? void 0 : errors[m.route.id]) !== undefined);\n    !(errorIndex >= 0) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"Could not find a matching route for errors on route IDs: \" + Object.keys(errors).join(\",\")) : UNSAFE_invariant(false) : void 0;\n    renderedMatches = renderedMatches.slice(0, Math.min(renderedMatches.length, errorIndex + 1));\n  }\n\n  // If we're in a partial hydration mode, detect if we need to render down to\n  // a given HydrateFallback while we load the rest of the hydration data\n  let renderFallback = false;\n  let fallbackIndex = -1;\n  if (dataRouterState && future && future.v7_partialHydration) {\n    for (let i = 0; i < renderedMatches.length; i++) {\n      let match = renderedMatches[i];\n      // Track the deepest fallback up until the first route without data\n      if (match.route.HydrateFallback || match.route.hydrateFallbackElement) {\n        fallbackIndex = i;\n      }\n      if (match.route.id) {\n        let {\n          loaderData,\n          errors\n        } = dataRouterState;\n        let needsToRunLoader = match.route.loader && loaderData[match.route.id] === undefined && (!errors || errors[match.route.id] === undefined);\n        if (match.route.lazy || needsToRunLoader) {\n          // We found the first route that's not ready to render (waiting on\n          // lazy, or has a loader that hasn't run yet).  Flag that we need to\n          // render a fallback and render up until the appropriate fallback\n          renderFallback = true;\n          if (fallbackIndex >= 0) {\n            renderedMatches = renderedMatches.slice(0, fallbackIndex + 1);\n          } else {\n            renderedMatches = [renderedMatches[0]];\n          }\n          break;\n        }\n      }\n    }\n  }\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    // Only data routers handle errors/fallbacks\n    let error;\n    let shouldRenderHydrateFallback = false;\n    let errorElement = null;\n    let hydrateFallbackElement = null;\n    if (dataRouterState) {\n      error = errors && match.route.id ? errors[match.route.id] : undefined;\n      errorElement = match.route.errorElement || defaultErrorElement;\n      if (renderFallback) {\n        if (fallbackIndex < 0 && index === 0) {\n          warningOnce(\"route-fallback\", false, \"No `HydrateFallback` element provided to render during initial hydration\");\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = null;\n        } else if (fallbackIndex === index) {\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = match.route.hydrateFallbackElement || null;\n        }\n      }\n    }\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children;\n      if (error) {\n        children = errorElement;\n      } else if (shouldRenderHydrateFallback) {\n        children = hydrateFallbackElement;\n      } else if (match.route.Component) {\n        // Note: This is a de-optimized path since React won't re-use the\n        // ReactElement since it's identity changes with each new\n        // React.createElement call.  We keep this so folks can use\n        // `<Route Component={...}>` in `<Routes>` but generally `Component`\n        // usage is only advised in `RouterProvider` when we can convert it to\n        // `element` ahead of time.\n        children = /*#__PURE__*/React.createElement(match.route.Component, null);\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return /*#__PURE__*/React.createElement(RenderedRoute, {\n        match: match,\n        routeContext: {\n          outlet,\n          matches,\n          isDataRoute: dataRouterState != null\n        },\n        children: children\n      });\n    };\n    // Only wrap in an error boundary within data router usages when we have an\n    // ErrorBoundary/errorElement on this route.  Otherwise let it bubble up to\n    // an ancestor ErrorBoundary/errorElement\n    return dataRouterState && (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? /*#__PURE__*/React.createElement(RenderErrorBoundary, {\n      location: dataRouterState.location,\n      revalidation: dataRouterState.revalidation,\n      component: errorElement,\n      error: error,\n      children: getChildren(),\n      routeContext: {\n        outlet: null,\n        matches,\n        isDataRoute: true\n      }\n    }) : getChildren();\n  }, null);\n}\nvar DataRouterHook = /*#__PURE__*/function (DataRouterHook) {\n  DataRouterHook[\"UseBlocker\"] = \"useBlocker\";\n  DataRouterHook[\"UseRevalidator\"] = \"useRevalidator\";\n  DataRouterHook[\"UseNavigateStable\"] = \"useNavigate\";\n  return DataRouterHook;\n}(DataRouterHook || {});\nvar DataRouterStateHook = /*#__PURE__*/function (DataRouterStateHook) {\n  DataRouterStateHook[\"UseBlocker\"] = \"useBlocker\";\n  DataRouterStateHook[\"UseLoaderData\"] = \"useLoaderData\";\n  DataRouterStateHook[\"UseActionData\"] = \"useActionData\";\n  DataRouterStateHook[\"UseRouteError\"] = \"useRouteError\";\n  DataRouterStateHook[\"UseNavigation\"] = \"useNavigation\";\n  DataRouterStateHook[\"UseRouteLoaderData\"] = \"useRouteLoaderData\";\n  DataRouterStateHook[\"UseMatches\"] = \"useMatches\";\n  DataRouterStateHook[\"UseRevalidator\"] = \"useRevalidator\";\n  DataRouterStateHook[\"UseNavigateStable\"] = \"useNavigate\";\n  DataRouterStateHook[\"UseRouteId\"] = \"useRouteId\";\n  return DataRouterStateHook;\n}(DataRouterStateHook || {});\nfunction getDataRouterConsoleError(hookName) {\n  return hookName + \" must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.\";\n}\nfunction useDataRouterContext(hookName) {\n  let ctx = React.useContext(DataRouterContext);\n  !ctx ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return ctx;\n}\nfunction useDataRouterState(hookName) {\n  let state = React.useContext(DataRouterStateContext);\n  !state ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return state;\n}\nfunction useRouteContext(hookName) {\n  let route = React.useContext(RouteContext);\n  !route ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return route;\n}\n\n// Internal version with hookName-aware debugging\nfunction useCurrentRouteId(hookName) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  !thisRoute.route.id ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, hookName + \" can only be used on routes that contain a unique \\\"id\\\"\") : UNSAFE_invariant(false) : void 0;\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the ID for the nearest contextual route\n */\nfunction useRouteId() {\n  return useCurrentRouteId(DataRouterStateHook.UseRouteId);\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nfunction useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nfunction useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return React.useMemo(() => ({\n    revalidate: dataRouterContext.router.revalidate,\n    state: state.revalidation\n  }), [dataRouterContext.router.revalidate, state.revalidation]);\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nfunction useMatches() {\n  let {\n    matches,\n    loaderData\n  } = useDataRouterState(DataRouterStateHook.UseMatches);\n  return React.useMemo(() => matches.map(m => UNSAFE_convertRouteMatchToUiMatch(m, loaderData)), [matches, loaderData]);\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nfunction useLoaderData() {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\"You cannot `useLoaderData` in an errorElement (routeId: \" + routeId + \")\");\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nfunction useRouteLoaderData(routeId) {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nfunction useActionData() {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  return state.actionData ? state.actionData[routeId] : undefined;\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * ErrorBoundary/errorElement to display a proper error message.\n */\nfunction useRouteError() {\n  var _state$errors;\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error !== undefined) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return (_state$errors = state.errors) == null ? void 0 : _state$errors[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor `<Await />` value\n */\nfunction useAsyncValue() {\n  let value = React.useContext(AwaitContext);\n  return value == null ? void 0 : value._data;\n}\n\n/**\n * Returns the error from the nearest ancestor `<Await />` value\n */\nfunction useAsyncError() {\n  let value = React.useContext(AwaitContext);\n  return value == null ? void 0 : value._error;\n}\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nfunction useBlocker(shouldBlock) {\n  let {\n    router,\n    basename\n  } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let state = useDataRouterState(DataRouterStateHook.UseBlocker);\n  let [blockerKey, setBlockerKey] = React.useState(\"\");\n  let blockerFunction = React.useCallback(arg => {\n    if (typeof shouldBlock !== \"function\") {\n      return !!shouldBlock;\n    }\n    if (basename === \"/\") {\n      return shouldBlock(arg);\n    }\n\n    // If they provided us a function and we've got an active basename, strip\n    // it from the locations we expose to the user to match the behavior of\n    // useLocation\n    let {\n      currentLocation,\n      nextLocation,\n      historyAction\n    } = arg;\n    return shouldBlock({\n      currentLocation: _extends({}, currentLocation, {\n        pathname: stripBasename(currentLocation.pathname, basename) || currentLocation.pathname\n      }),\n      nextLocation: _extends({}, nextLocation, {\n        pathname: stripBasename(nextLocation.pathname, basename) || nextLocation.pathname\n      }),\n      historyAction\n    });\n  }, [basename, shouldBlock]);\n\n  // This effect is in charge of blocker key assignment and deletion (which is\n  // tightly coupled to the key)\n  React.useEffect(() => {\n    let key = String(++blockerId);\n    setBlockerKey(key);\n    return () => router.deleteBlocker(key);\n  }, [router]);\n\n  // This effect handles assigning the blockerFunction.  This is to handle\n  // unstable blocker function identities, and happens only after the prior\n  // effect so we don't get an orphaned blockerFunction in the router with a\n  // key of \"\".  Until then we just have the IDLE_BLOCKER.\n  React.useEffect(() => {\n    if (blockerKey !== \"\") {\n      router.getBlocker(blockerKey, blockerFunction);\n    }\n  }, [router, blockerKey, blockerFunction]);\n\n  // Prefer the blocker from `state` not `router.state` since DataRouterContext\n  // is memoized so this ensures we update on blocker state updates\n  return blockerKey && state.blockers.has(blockerKey) ? state.blockers.get(blockerKey) : IDLE_BLOCKER;\n}\n\n/**\n * Stable version of useNavigate that is used when we are in the context of\n * a RouterProvider.\n */\nfunction useNavigateStable() {\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseNavigateStable);\n  let id = useCurrentRouteId(DataRouterStateHook.UseNavigateStable);\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n  let navigate = React.useCallback(function (to, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(activeRef.current, navigateEffectWarning) : void 0;\n\n    // Short circuit here since if this happens on first render the navigate\n    // is useless because we haven't wired up our router subscriber yet\n    if (!activeRef.current) return;\n    if (typeof to === \"number\") {\n      router.navigate(to);\n    } else {\n      router.navigate(to, _extends({\n        fromRouteId: id\n      }, options));\n    }\n  }, [router, id]);\n  return navigate;\n}\nconst alreadyWarned$1 = {};\nfunction warningOnce(key, cond, message) {\n  if (!cond && !alreadyWarned$1[key]) {\n    alreadyWarned$1[key] = true;\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, message) : void 0;\n  }\n}\nconst alreadyWarned = {};\nfunction warnOnce(key, message) {\n  if (process.env.NODE_ENV !== \"production\" && !alreadyWarned[message]) {\n    alreadyWarned[message] = true;\n    console.warn(message);\n  }\n}\nconst logDeprecation = (flag, msg, link) => warnOnce(flag, \"\\u26A0\\uFE0F React Router Future Flag Warning: \" + msg + \". \" + (\"You can use the `\" + flag + \"` future flag to opt-in early. \") + (\"For more information, see \" + link + \".\"));\nfunction logV6DeprecationWarnings(renderFuture, routerFuture) {\n  if ((renderFuture == null ? void 0 : renderFuture.v7_startTransition) === undefined) {\n    logDeprecation(\"v7_startTransition\", \"React Router will begin wrapping state updates in `React.startTransition` in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_starttransition\");\n  }\n  if ((renderFuture == null ? void 0 : renderFuture.v7_relativeSplatPath) === undefined && (!routerFuture || routerFuture.v7_relativeSplatPath === undefined)) {\n    logDeprecation(\"v7_relativeSplatPath\", \"Relative route resolution within Splat routes is changing in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath\");\n  }\n  if (routerFuture) {\n    if (routerFuture.v7_fetcherPersist === undefined) {\n      logDeprecation(\"v7_fetcherPersist\", \"The persistence behavior of fetchers is changing in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist\");\n    }\n    if (routerFuture.v7_normalizeFormMethod === undefined) {\n      logDeprecation(\"v7_normalizeFormMethod\", \"Casing of `formMethod` fields is being normalized to uppercase in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod\");\n    }\n    if (routerFuture.v7_partialHydration === undefined) {\n      logDeprecation(\"v7_partialHydration\", \"`RouterProvider` hydration behavior is changing in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_partialhydration\");\n    }\n    if (routerFuture.v7_skipActionErrorRevalidation === undefined) {\n      logDeprecation(\"v7_skipActionErrorRevalidation\", \"The revalidation behavior after 4xx/5xx `action` responses is changing in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation\");\n    }\n  }\n}\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nfunction RouterProvider(_ref) {\n  let {\n    fallbackElement,\n    router,\n    future\n  } = _ref;\n  let [state, setStateImpl] = React.useState(router.state);\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    if (v7_startTransition && startTransitionImpl) {\n      startTransitionImpl(() => setStateImpl(newState));\n    } else {\n      setStateImpl(newState);\n    }\n  }, [setStateImpl, v7_startTransition]);\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n  React.useEffect(() => {\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(fallbackElement == null || !router.future.v7_partialHydration, \"`<RouterProvider fallbackElement>` is deprecated when using \" + \"`v7_partialHydration`, use a `HydrateFallback` component instead\") : void 0;\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  let navigator = React.useMemo(() => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: n => router.navigate(n),\n      push: (to, state, opts) => router.navigate(to, {\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      }),\n      replace: (to, state, opts) => router.navigate(to, {\n        replace: true,\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      })\n    };\n  }, [router]);\n  let basename = router.basename || \"/\";\n  let dataRouterContext = React.useMemo(() => ({\n    router,\n    navigator,\n    static: false,\n    basename\n  }), [router, navigator, basename]);\n  React.useEffect(() => logV6DeprecationWarnings(future, router.future), [router, future]);\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(DataRouterContext.Provider, {\n    value: dataRouterContext\n  }, /*#__PURE__*/React.createElement(DataRouterStateContext.Provider, {\n    value: state\n  }, /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    location: state.location,\n    navigationType: state.historyAction,\n    navigator: navigator,\n    future: {\n      v7_relativeSplatPath: router.future.v7_relativeSplatPath\n    }\n  }, state.initialized || router.future.v7_partialHydration ? /*#__PURE__*/React.createElement(DataRoutes, {\n    routes: router.routes,\n    future: router.future,\n    state: state\n  }) : fallbackElement))), null);\n}\nfunction DataRoutes(_ref2) {\n  let {\n    routes,\n    future,\n    state\n  } = _ref2;\n  return useRoutesImpl(routes, undefined, state, future);\n}\n/**\n * A `<Router>` that stores all entries in memory.\n *\n * @see https://reactrouter.com/v6/router-components/memory-router\n */\nfunction MemoryRouter(_ref3) {\n  let {\n    basename,\n    children,\n    initialEntries,\n    initialIndex,\n    future\n  } = _ref3;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history,\n    future: future\n  });\n}\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/v6/components/navigate\n */\nfunction Navigate(_ref4) {\n  let {\n    to,\n    replace,\n    state,\n    relative\n  } = _ref4;\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of\n  // the router loaded. We can help them understand how to avoid that.\n  \"<Navigate> may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    future,\n    static: isStatic\n  } = React.useContext(NavigationContext);\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(!isStatic, \"<Navigate> must not be used on the initial render in a <StaticRouter>. \" + \"This is a no-op, but you should modify your code so the <Navigate> is \" + \"only ever rendered in response to some user interaction or state change.\") : void 0;\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let navigate = useNavigate();\n\n  // Resolve the path outside of the effect so that when effects run twice in\n  // StrictMode they navigate to the same place\n  let path = resolveTo(to, UNSAFE_getResolveToMatches(matches, future.v7_relativeSplatPath), locationPathname, relative === \"path\");\n  let jsonPath = JSON.stringify(path);\n  React.useEffect(() => navigate(JSON.parse(jsonPath), {\n    replace,\n    state,\n    relative\n  }), [navigate, jsonPath, relative, replace, state]);\n  return null;\n}\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/v6/components/outlet\n */\nfunction Outlet(props) {\n  return useOutlet(props.context);\n}\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/v6/components/route\n */\nfunction Route(_props) {\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"A <Route> is only ever to be used as the child of <Routes> element, \" + \"never rendered directly. Please wrap your <Route> in a <Routes>.\") : UNSAFE_invariant(false);\n}\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a `<Router>` directly. Instead, you'll render a\n * router that is more specific to your environment such as a `<BrowserRouter>`\n * in web browsers or a `<StaticRouter>` for server rendering.\n *\n * @see https://reactrouter.com/v6/router-components/router\n */\nfunction Router(_ref5) {\n  let {\n    basename: basenameProp = \"/\",\n    children = null,\n    location: locationProp,\n    navigationType = Action.Pop,\n    navigator,\n    static: staticProp = false,\n    future\n  } = _ref5;\n  !!useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"You cannot render a <Router> inside another <Router>.\" + \" You should never have more than one in your app.\") : UNSAFE_invariant(false) : void 0;\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(() => ({\n    basename,\n    navigator,\n    static: staticProp,\n    future: _extends({\n      v7_relativeSplatPath: false\n    }, future)\n  }), [basename, future, navigator, staticProp]);\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\"\n  } = locationProp;\n  let locationContext = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n    if (trailingPathname == null) {\n      return null;\n    }\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key\n      },\n      navigationType\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(locationContext != null, \"<Router basename=\\\"\" + basename + \"\\\"> is not able to match the URL \" + (\"\\\"\" + pathname + search + hash + \"\\\" because it does not start with the \") + \"basename, so the <Router> won't render anything.\") : void 0;\n  if (locationContext == null) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(NavigationContext.Provider, {\n    value: navigationContext\n  }, /*#__PURE__*/React.createElement(LocationContext.Provider, {\n    children: children,\n    value: locationContext\n  }));\n}\n/**\n * A container for a nested tree of `<Route>` elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/v6/components/routes\n */\nfunction Routes(_ref6) {\n  let {\n    children,\n    location\n  } = _ref6;\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nfunction Await(_ref7) {\n  let {\n    children,\n    errorElement,\n    resolve\n  } = _ref7;\n  return /*#__PURE__*/React.createElement(AwaitErrorBoundary, {\n    resolve: resolve,\n    errorElement: errorElement\n  }, /*#__PURE__*/React.createElement(ResolveAwait, null, children));\n}\nvar AwaitRenderStatus = /*#__PURE__*/function (AwaitRenderStatus) {\n  AwaitRenderStatus[AwaitRenderStatus[\"pending\"] = 0] = \"pending\";\n  AwaitRenderStatus[AwaitRenderStatus[\"success\"] = 1] = \"success\";\n  AwaitRenderStatus[AwaitRenderStatus[\"error\"] = 2] = \"error\";\n  return AwaitRenderStatus;\n}(AwaitRenderStatus || {});\nconst neverSettledPromise = new Promise(() => {});\nclass AwaitErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      error: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\"<Await> caught the following error during render\", error, errorInfo);\n  }\n  render() {\n    let {\n      children,\n      errorElement,\n      resolve\n    } = this.props;\n    let promise = null;\n    let status = AwaitRenderStatus.pending;\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", {\n        get: () => true\n      });\n      Object.defineProperty(promise, \"_data\", {\n        get: () => resolve\n      });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", {\n        get: () => true\n      });\n      Object.defineProperty(promise, \"_error\", {\n        get: () => renderError\n      });\n    } else if (resolve._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status = \"_error\" in promise ? AwaitRenderStatus.error : \"_data\" in promise ? AwaitRenderStatus.success : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", {\n        get: () => true\n      });\n      promise = resolve.then(data => Object.defineProperty(resolve, \"_data\", {\n        get: () => data\n      }), error => Object.defineProperty(resolve, \"_error\", {\n        get: () => error\n      }));\n    }\n    if (status === AwaitRenderStatus.error && promise._error instanceof AbortedDeferredError) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return /*#__PURE__*/React.createElement(AwaitContext.Provider, {\n        value: promise,\n        children: errorElement\n      });\n    }\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return /*#__PURE__*/React.createElement(AwaitContext.Provider, {\n        value: promise,\n        children: children\n      });\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on `<Await>`\n */\nfunction ResolveAwait(_ref8) {\n  let {\n    children\n  } = _ref8;\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, toRender);\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/v6/utils/create-routes-from-children\n */\nfunction createRoutesFromChildren(children, parentPath) {\n  if (parentPath === void 0) {\n    parentPath = [];\n  }\n  let routes = [];\n  React.Children.forEach(children, (element, index) => {\n    if (! /*#__PURE__*/React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n    let treePath = [...parentPath, index];\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(routes, createRoutesFromChildren(element.props.children, treePath));\n      return;\n    }\n    !(element.type === Route) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"[\" + (typeof element.type === \"string\" ? element.type : element.type.name) + \"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>\") : UNSAFE_invariant(false) : void 0;\n    !(!element.props.index || !element.props.children) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"An index route cannot have child routes.\") : UNSAFE_invariant(false) : void 0;\n    let route = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary: element.props.ErrorBoundary != null || element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy\n    };\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(element.props.children, treePath);\n    }\n    routes.push(route);\n  });\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nfunction renderMatches(matches) {\n  return _renderMatches(matches);\n}\nfunction mapRouteProperties(route) {\n  let updates = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.ErrorBoundary != null || route.errorElement != null\n  };\n  if (route.Component) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (route.element) {\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"You should not include both `Component` and `element` on your route - \" + \"`Component` will be used.\") : void 0;\n      }\n    }\n    Object.assign(updates, {\n      element: /*#__PURE__*/React.createElement(route.Component),\n      Component: undefined\n    });\n  }\n  if (route.HydrateFallback) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (route.hydrateFallbackElement) {\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - \" + \"`HydrateFallback` will be used.\") : void 0;\n      }\n    }\n    Object.assign(updates, {\n      hydrateFallbackElement: /*#__PURE__*/React.createElement(route.HydrateFallback),\n      HydrateFallback: undefined\n    });\n  }\n  if (route.ErrorBoundary) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (route.errorElement) {\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"You should not include both `ErrorBoundary` and `errorElement` on your route - \" + \"`ErrorBoundary` will be used.\") : void 0;\n      }\n    }\n    Object.assign(updates, {\n      errorElement: /*#__PURE__*/React.createElement(route.ErrorBoundary),\n      ErrorBoundary: undefined\n    });\n  }\n  return updates;\n}\nfunction createMemoryRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    future: _extends({}, opts == null ? void 0 : opts.future, {\n      v7_prependBasename: true\n    }),\n    history: createMemoryHistory({\n      initialEntries: opts == null ? void 0 : opts.initialEntries,\n      initialIndex: opts == null ? void 0 : opts.initialIndex\n    }),\n    hydrationData: opts == null ? void 0 : opts.hydrationData,\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts == null ? void 0 : opts.dataStrategy,\n    patchRoutesOnNavigation: opts == null ? void 0 : opts.patchRoutesOnNavigation\n  }).initialize();\n}\nexport { Await, MemoryRouter, Navigate, Outlet, Route, Router, RouterProvider, Routes, DataRouterContext as UNSAFE_DataRouterContext, DataRouterStateContext as UNSAFE_DataRouterStateContext, LocationContext as UNSAFE_LocationContext, NavigationContext as UNSAFE_NavigationContext, RouteContext as UNSAFE_RouteContext, logV6DeprecationWarnings as UNSAFE_logV6DeprecationWarnings, mapRouteProperties as UNSAFE_mapRouteProperties, useRouteId as UNSAFE_useRouteId, useRoutesImpl as UNSAFE_useRoutesImpl, createMemoryRouter, createRoutesFromChildren, createRoutesFromChildren as createRoutesFromElements, renderMatches, useActionData, useAsyncError, useAsyncValue, useBlocker, useHref, useInRouterContext, useLoaderData, useLocation, useMatch, useMatches, useNavigate, useNavigation, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRevalidator, useRouteError, useRouteLoaderData, useRoutes };", "map": {"version": 3, "names": ["DataRouterContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName", "DataRouterStateContext", "AwaitContext", "NavigationContext", "LocationContext", "RouteContext", "outlet", "matches", "isDataRoute", "RouteErrorContext", "useHref", "to", "_temp", "relative", "useInRouterContext", "UNSAFE_invariant", "basename", "navigator", "useContext", "hash", "pathname", "search", "useResolvedPath", "joinedPathname", "joinPaths", "createHref", "useLocation", "location", "useNavigationType", "navigationType", "useMatch", "pattern", "useMemo", "matchPath", "UNSAFE_decodePath", "navigateEffectWarning", "useIsomorphicLayoutEffect", "cb", "isStatic", "static", "useLayoutEffect", "useNavigate", "useNavigateStable", "useNavigateUnstable", "dataRouterContext", "future", "locationPathname", "routePathnamesJson", "JSON", "stringify", "UNSAFE_getResolveToMatches", "v7_relativeSplatPath", "activeRef", "useRef", "current", "navigate", "useCallback", "options", "UNSAFE_warning", "go", "path", "resolveTo", "parse", "replace", "push", "state", "OutletContext", "useOutletContext", "useOutlet", "context", "createElement", "Provider", "value", "useParams", "routeMatch", "length", "params", "_temp2", "useRoutes", "routes", "locationArg", "useRoutesImpl", "dataRouterState", "parentMatches", "parentParams", "parentPathname", "parentPathnameBase", "pathnameBase", "parentRoute", "route", "parentPath", "warningOnce", "endsWith", "locationFromContext", "_parsedLocationArg$pa", "parsedLocationArg", "parsePath", "startsWith", "remainingPathname", "parentSegments", "split", "segments", "slice", "join", "matchRoutes", "element", "undefined", "Component", "lazy", "renderedMatches", "_renderMatches", "map", "match", "Object", "assign", "encodeLocation", "_extends", "key", "Action", "Pop", "DefaultErrorComponent", "error", "useRouteError", "message", "isRouteErrorResponse", "status", "statusText", "Error", "stack", "<PERSON><PERSON>rey", "preStyles", "padding", "backgroundColor", "codeStyles", "devInfo", "console", "Fragment", "style", "fontStyle", "defaultErrorElement", "RenderErrorBoundary", "constructor", "props", "revalidation", "getDerivedStateFromError", "getDerivedStateFromProps", "componentDidCatch", "errorInfo", "render", "routeContext", "children", "component", "RenderedRoute", "_ref", "staticContext", "errorElement", "Error<PERSON>ou<PERSON><PERSON>", "_deepestRenderedBoundaryId", "id", "_dataRouterState", "_future", "errors", "v7_partialHydration", "initialized", "errorIndex", "findIndex", "m", "keys", "Math", "min", "renderFallback", "fallbackIndex", "i", "HydrateFallback", "hydrateFallbackElement", "loaderData", "needsToRunLoader", "loader", "reduceRight", "index", "shouldRenderHydrateFallback", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "useDataRouterState", "useRouteContext", "useCurrentRouteId", "thisRoute", "useRouteId", "UseRouteId", "useNavigation", "UseNavigation", "navigation", "useRevalidator", "UseRevalidator", "revalidate", "router", "useMatches", "UseMatches", "UNSAFE_convertRouteMatchToUiMatch", "useLoaderData", "UseLoaderData", "routeId", "useRouteLoaderData", "UseRouteLoaderData", "useActionData", "UseActionData", "actionData", "_state$errors", "UseRouteError", "useAsyncValue", "_data", "useAsyncError", "_error", "blockerId", "useBlocker", "shouldBlock", "UseBlocker", "blockerKey", "set<PERSON><PERSON>er<PERSON>ey", "useState", "blockerFunction", "arg", "currentLocation", "nextLocation", "historyAction", "stripBasename", "useEffect", "String", "deleteBlocker", "get<PERSON><PERSON>er", "blockers", "has", "get", "IDLE_BLOCKER", "UseNavigateStable", "fromRouteId", "alreadyWarned$1", "cond", "alreadyWarned", "warnOnce", "warn", "logDeprecation", "flag", "msg", "link", "logV6DeprecationWarnings", "renderFuture", "routerFuture", "v7_startTransition", "v7_fetcherPersist", "v7_normalizeFormMethod", "v7_skipActionErrorRevalidation", "START_TRANSITION", "startTransitionImpl", "RouterProvider", "fallbackElement", "setStateImpl", "setState", "newState", "subscribe", "n", "opts", "preventScrollReset", "Router", "DataRoutes", "_ref2", "MemoryRouter", "_ref3", "initialEntries", "initialIndex", "historyRef", "createMemoryHistory", "v5Compat", "history", "action", "listen", "Navigate", "_ref4", "jsonPath", "Outlet", "Route", "_props", "_ref5", "basenameProp", "locationProp", "staticProp", "navigationContext", "locationContext", "trailingPathname", "Routes", "_ref6", "createRoutesFromChildren", "Await", "_ref7", "resolve", "Await<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "ResolveAwait", "AwaitRenderStatus", "neverSettledPromise", "Promise", "promise", "pending", "success", "defineProperty", "renderError", "reject", "catch", "_tracked", "then", "data", "Aborted<PERSON>eferredError", "_ref8", "to<PERSON><PERSON>", "Children", "for<PERSON>ach", "isValidElement", "treePath", "type", "apply", "name", "caseSensitive", "hasErrorBou<PERSON>ry", "shouldRevalidate", "handle", "renderMatches", "mapRouteProperties", "updates", "createMemoryRouter", "createRouter", "v7_prependBasename", "hydrationData", "dataStrategy", "patchRoutesOnNavigation", "initialize"], "sources": ["C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\node_modules\\react-router\\lib\\context.ts", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\node_modules\\react-router\\lib\\hooks.tsx", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\node_modules\\react-router\\lib\\deprecations.ts", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\node_modules\\react-router\\lib\\components.tsx", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\node_modules\\react-router\\index.ts"], "sourcesContent": ["import * as React from \"react\";\nimport type {\n  AgnosticIndexRouteObject,\n  AgnosticNonIndexRouteObject,\n  AgnosticRouteMatch,\n  History,\n  LazyRouteFunction,\n  Location,\n  Action as NavigationType,\n  RelativeRoutingType,\n  Router,\n  StaticHandlerContext,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nexport interface IndexRouteObject {\n  caseSensitive?: AgnosticIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticIndexRouteObject[\"path\"];\n  id?: AgnosticIndexRouteObject[\"id\"];\n  loader?: AgnosticIndexRouteObject[\"loader\"];\n  action?: AgnosticIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticIndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport interface NonIndexRouteObject {\n  caseSensitive?: AgnosticNonIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticNonIndexRouteObject[\"path\"];\n  id?: AgnosticNonIndexRouteObject[\"id\"];\n  loader?: AgnosticNonIndexRouteObject[\"loader\"];\n  action?: AgnosticNonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticNonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticNonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticNonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: RouteObject[];\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport type RouteObject = IndexRouteObject | NonIndexRouteObject;\n\nexport type DataRouteObject = RouteObject & {\n  children?: DataRouteObject[];\n  id: string;\n};\n\nexport interface RouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends RouteObject = RouteObject\n> extends AgnosticRouteMatch<ParamKey, RouteObjectType> {}\n\nexport interface DataRouteMatch extends RouteMatch<string, DataRouteObject> {}\n\nexport interface DataRouterContextObject\n  // Omit `future` since those can be pulled from the `router`\n  // `NavigationContext` needs future since it doesn't have a `router` in all cases\n  extends Omit<NavigationContextObject, \"future\"> {\n  router: Router;\n  staticContext?: StaticHandlerContext;\n}\n\nexport const DataRouterContext =\n  React.createContext<DataRouterContextObject | null>(null);\nif (__DEV__) {\n  DataRouterContext.displayName = \"DataRouter\";\n}\n\nexport const DataRouterStateContext = React.createContext<\n  Router[\"state\"] | null\n>(null);\nif (__DEV__) {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\n\nexport const AwaitContext = React.createContext<TrackedPromise | null>(null);\nif (__DEV__) {\n  AwaitContext.displayName = \"Await\";\n}\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  flushSync?: boolean;\n  viewTransition?: boolean;\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level `<Router>` API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport interface Navigator {\n  createHref: History[\"createHref\"];\n  // Optional for backwards-compat with Router/HistoryRouter usage (edge case)\n  encodeLocation?: History[\"encodeLocation\"];\n  go: History[\"go\"];\n  push(to: To, state?: any, opts?: NavigateOptions): void;\n  replace(to: To, state?: any, opts?: NavigateOptions): void;\n}\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n  future: {\n    v7_relativeSplatPath: boolean;\n  };\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\nexport interface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n  isDataRoute: boolean;\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n  isDataRoute: false,\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n\nexport const RouteErrorContext = React.createContext<any>(null);\n\nif (__DEV__) {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n", "import * as React from \"react\";\nimport type {\n  Blocker,\n  BlockerFunction,\n  Location,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathPattern,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RevalidationState,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  IDLE_BLOCKER,\n  Action as NavigationType,\n  UNSAFE_convertRouteMatchToUiMatch as convertRouteMatchToUiMatch,\n  UNSAFE_decodePath as decodePath,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  isRouteErrorResponse,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  DataRouteMatch,\n  NavigateOptions,\n  RouteContextObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n  RouteErrorContext,\n} from \"./context\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/v6/hooks/use-href\n */\nexport function useHref(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to, { relative });\n\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname =\n      pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a `<Router>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-in-router-context\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/v6/hooks/use-location\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigation-type\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * `<NavLink>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-match\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, decodePath(pathname)),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\nconst navigateEffectWarning =\n  `You should call navigate() in a React.useEffect(), not when ` +\n  `your component is first rendered.`;\n\n// Mute warnings for calls to useNavigate in SSR environments\nfunction useIsomorphicLayoutEffect(\n  cb: Parameters<typeof React.useLayoutEffect>[0]\n) {\n  let isStatic = React.useContext(NavigationContext).static;\n  if (!isStatic) {\n    // We should be able to get rid of this once react 18.3 is released\n    // See: https://github.com/facebook/react/pull/26395\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(cb);\n  }\n}\n\n/**\n * Returns an imperative method for changing the location. Used by `<Link>`s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigate\n */\nexport function useNavigate(): NavigateFunction {\n  let { isDataRoute } = React.useContext(RouteContext);\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\n\nfunction useNavigateUnstable(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let dataRouterContext = React.useContext(DataRouterContext);\n  let { basename, future, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our history listener yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        options.relative === \"path\"\n      );\n\n      // If we're operating within a basename, prepend it to the pathname prior\n      // to handing off to history (but only if we're not in a data router,\n      // otherwise it'll prepend the basename inside of the router).\n      // If this is a root navigation, then we navigate to the raw basename\n      // which allows the basename to have full control over the presence of a\n      // trailing slash on root links\n      if (dataRouterContext == null && basename !== \"/\") {\n        path.pathname =\n          path.pathname === \"/\"\n            ? basename\n            : joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state,\n        options\n      );\n    },\n    [\n      basename,\n      navigator,\n      routePathnamesJson,\n      locationPathname,\n      dataRouterContext,\n    ]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/v6/hooks/use-outlet-context\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by `<Outlet>` to render child routes.\n *\n * @see https://reactrouter.com/v6/hooks/use-outlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/v6/hooks/use-params\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/v6/hooks/use-resolved-path\n */\nexport function useResolvedPath(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): Path {\n  let { future } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  return React.useMemo(\n    () =>\n      resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        relative === \"path\"\n      ),\n    [to, routePathnamesJson, locationPathname, relative]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an `<Outlet>` to render their child route's\n * element.\n *\n * @see https://reactrouter.com/v6/hooks/use-routes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  return useRoutesImpl(routes, locationArg);\n}\n\n// Internal implementation with accept optional param for RouterProvider usage\nexport function useRoutesImpl(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string,\n  dataRouterState?: RemixRouter[\"state\"],\n  future?: RemixRouter[\"future\"]\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { navigator } = React.useContext(NavigationContext);\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n\n  let remainingPathname = pathname;\n  if (parentPathnameBase !== \"/\") {\n    // Determine the remaining pathname by removing the # of URL segments the\n    // parentPathnameBase has, instead of removing based on character count.\n    // This is because we can't guarantee that incoming/outgoing encodings/\n    // decodings will match exactly.\n    // We decode paths before matching on a per-segment basis with\n    // decodeURIComponent(), but we re-encode pathnames via `new URL()` so they\n    // match what `window.location.pathname` would reflect.  Those don't 100%\n    // align when it comes to encoded URI characters such as % and &.\n    //\n    // So we may end up with:\n    //   pathname:           \"/descendant/a%25b/match\"\n    //   parentPathnameBase: \"/descendant/a%b\"\n    //\n    // And the direct substring removal approach won't work :/\n    let parentSegments = parentPathnameBase.replace(/^\\//, \"\").split(\"/\");\n    let segments = pathname.replace(/^\\//, \"\").split(\"/\");\n    remainingPathname = \"/\" + segments.slice(parentSegments.length).join(\"/\");\n  }\n\n  let matches = matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined ||\n        matches[matches.length - 1].route.Component !== undefined ||\n        matches[matches.length - 1].route.lazy !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" ` +\n        `does not have an element or Component. This means it will render an <Outlet /> with a ` +\n        `null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  let renderedMatches = _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([\n            parentPathnameBase,\n            // Re-encode pathnames that were decoded inside matchRoutes\n            navigator.encodeLocation\n              ? navigator.encodeLocation(match.pathname).pathname\n              : match.pathname,\n          ]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([\n                  parentPathnameBase,\n                  // Re-encode pathnames that were decoded inside matchRoutes\n                  navigator.encodeLocation\n                    ? navigator.encodeLocation(match.pathnameBase).pathname\n                    : match.pathnameBase,\n                ]),\n        })\n      ),\n    parentMatches,\n    dataRouterState,\n    future\n  );\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return (\n      <LocationContext.Provider\n        value={{\n          location: {\n            pathname: \"/\",\n            search: \"\",\n            hash: \"\",\n            state: null,\n            key: \"default\",\n            ...location,\n          },\n          navigationType: NavigationType.Pop,\n        }}\n      >\n        {renderedMatches}\n      </LocationContext.Provider>\n    );\n  }\n\n  return renderedMatches;\n}\n\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error)\n    ? `${error.status} ${error.statusText}`\n    : error instanceof Error\n    ? error.message\n    : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = { padding: \"0.5rem\", backgroundColor: lightgrey };\n  let codeStyles = { padding: \"2px 4px\", backgroundColor: lightgrey };\n\n  let devInfo = null;\n  if (__DEV__) {\n    console.error(\n      \"Error handled by React Router default ErrorBoundary:\",\n      error\n    );\n\n    devInfo = (\n      <>\n        <p>💿 Hey developer 👋</p>\n        <p>\n          You can provide a way better UX than this when your app throws errors\n          by providing your own <code style={codeStyles}>ErrorBoundary</code> or{\" \"}\n          <code style={codeStyles}>errorElement</code> prop on your route.\n        </p>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <h2>Unexpected Application Error!</h2>\n      <h3 style={{ fontStyle: \"italic\" }}>{message}</h3>\n      {stack ? <pre style={preStyles}>{stack}</pre> : null}\n      {devInfo}\n    </>\n  );\n}\n\nconst defaultErrorElement = <DefaultErrorComponent />;\n\ntype RenderErrorBoundaryProps = React.PropsWithChildren<{\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n  component: React.ReactNode;\n  routeContext: RouteContextObject;\n}>;\n\ntype RenderErrorBoundaryState = {\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n};\n\nexport class RenderErrorBoundary extends React.Component<\n  RenderErrorBoundaryProps,\n  RenderErrorBoundaryState\n> {\n  constructor(props: RenderErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error,\n    };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error: error };\n  }\n\n  static getDerivedStateFromProps(\n    props: RenderErrorBoundaryProps,\n    state: RenderErrorBoundaryState\n  ) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (\n      state.location !== props.location ||\n      (state.revalidation !== \"idle\" && props.revalidation === \"idle\")\n    ) {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation,\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error !== undefined ? props.error : state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation,\n    };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"React Router caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    return this.state.error !== undefined ? (\n      <RouteContext.Provider value={this.props.routeContext}>\n        <RouteErrorContext.Provider\n          value={this.state.error}\n          children={this.props.component}\n        />\n      </RouteContext.Provider>\n    ) : (\n      this.props.children\n    );\n  }\n}\n\ninterface RenderedRouteProps {\n  routeContext: RouteContextObject;\n  match: RouteMatch<string, RouteObject>;\n  children: React.ReactNode | null;\n}\n\nfunction RenderedRoute({ routeContext, match, children }: RenderedRouteProps) {\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (\n    dataRouterContext &&\n    dataRouterContext.static &&\n    dataRouterContext.staticContext &&\n    (match.route.errorElement || match.route.ErrorBoundary)\n  ) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n\n  return (\n    <RouteContext.Provider value={routeContext}>\n      {children}\n    </RouteContext.Provider>\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = [],\n  dataRouterState: RemixRouter[\"state\"] | null = null,\n  future: RemixRouter[\"future\"] | null = null\n): React.ReactElement | null {\n  if (matches == null) {\n    if (!dataRouterState) {\n      return null;\n    }\n\n    if (dataRouterState.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else if (\n      future?.v7_partialHydration &&\n      parentMatches.length === 0 &&\n      !dataRouterState.initialized &&\n      dataRouterState.matches.length > 0\n    ) {\n      // Don't bail if we're initializing with partial hydration and we have\n      // router matches.  That means we're actively running `patchRoutesOnNavigation`\n      // so we should render down the partial matches to the appropriate\n      // `HydrateFallback`.  We only do this if `parentMatches` is empty so it\n      // only impacts the root matches for `RouterProvider` and no descendant\n      // `<Routes>`\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else {\n      return null;\n    }\n  }\n\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = dataRouterState?.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(\n      (m) => m.route.id && errors?.[m.route.id] !== undefined\n    );\n    invariant(\n      errorIndex >= 0,\n      `Could not find a matching route for errors on route IDs: ${Object.keys(\n        errors\n      ).join(\",\")}`\n    );\n    renderedMatches = renderedMatches.slice(\n      0,\n      Math.min(renderedMatches.length, errorIndex + 1)\n    );\n  }\n\n  // If we're in a partial hydration mode, detect if we need to render down to\n  // a given HydrateFallback while we load the rest of the hydration data\n  let renderFallback = false;\n  let fallbackIndex = -1;\n  if (dataRouterState && future && future.v7_partialHydration) {\n    for (let i = 0; i < renderedMatches.length; i++) {\n      let match = renderedMatches[i];\n      // Track the deepest fallback up until the first route without data\n      if (match.route.HydrateFallback || match.route.hydrateFallbackElement) {\n        fallbackIndex = i;\n      }\n\n      if (match.route.id) {\n        let { loaderData, errors } = dataRouterState;\n        let needsToRunLoader =\n          match.route.loader &&\n          loaderData[match.route.id] === undefined &&\n          (!errors || errors[match.route.id] === undefined);\n        if (match.route.lazy || needsToRunLoader) {\n          // We found the first route that's not ready to render (waiting on\n          // lazy, or has a loader that hasn't run yet).  Flag that we need to\n          // render a fallback and render up until the appropriate fallback\n          renderFallback = true;\n          if (fallbackIndex >= 0) {\n            renderedMatches = renderedMatches.slice(0, fallbackIndex + 1);\n          } else {\n            renderedMatches = [renderedMatches[0]];\n          }\n          break;\n        }\n      }\n    }\n  }\n\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    // Only data routers handle errors/fallbacks\n    let error: any;\n    let shouldRenderHydrateFallback = false;\n    let errorElement: React.ReactNode | null = null;\n    let hydrateFallbackElement: React.ReactNode | null = null;\n    if (dataRouterState) {\n      error = errors && match.route.id ? errors[match.route.id] : undefined;\n      errorElement = match.route.errorElement || defaultErrorElement;\n\n      if (renderFallback) {\n        if (fallbackIndex < 0 && index === 0) {\n          warningOnce(\n            \"route-fallback\",\n            false,\n            \"No `HydrateFallback` element provided to render during initial hydration\"\n          );\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = null;\n        } else if (fallbackIndex === index) {\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = match.route.hydrateFallbackElement || null;\n        }\n      }\n    }\n\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children: React.ReactNode;\n      if (error) {\n        children = errorElement;\n      } else if (shouldRenderHydrateFallback) {\n        children = hydrateFallbackElement;\n      } else if (match.route.Component) {\n        // Note: This is a de-optimized path since React won't re-use the\n        // ReactElement since it's identity changes with each new\n        // React.createElement call.  We keep this so folks can use\n        // `<Route Component={...}>` in `<Routes>` but generally `Component`\n        // usage is only advised in `RouterProvider` when we can convert it to\n        // `element` ahead of time.\n        children = <match.route.Component />;\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return (\n        <RenderedRoute\n          match={match}\n          routeContext={{\n            outlet,\n            matches,\n            isDataRoute: dataRouterState != null,\n          }}\n          children={children}\n        />\n      );\n    };\n    // Only wrap in an error boundary within data router usages when we have an\n    // ErrorBoundary/errorElement on this route.  Otherwise let it bubble up to\n    // an ancestor ErrorBoundary/errorElement\n    return dataRouterState &&\n      (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? (\n      <RenderErrorBoundary\n        location={dataRouterState.location}\n        revalidation={dataRouterState.revalidation}\n        component={errorElement}\n        error={error}\n        children={getChildren()}\n        routeContext={{ outlet: null, matches, isDataRoute: true }}\n      />\n    ) : (\n      getChildren()\n    );\n  }, null as React.ReactElement | null);\n}\n\nenum DataRouterHook {\n  UseBlocker = \"useBlocker\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n}\n\nenum DataRouterStateHook {\n  UseBlocker = \"useBlocker\",\n  UseLoaderData = \"useLoaderData\",\n  UseActionData = \"useActionData\",\n  UseRouteError = \"useRouteError\",\n  UseNavigation = \"useNavigation\",\n  UseRouteLoaderData = \"useRouteLoaderData\",\n  UseMatches = \"useMatches\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n  UseRouteId = \"useRouteId\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\nfunction useRouteContext(hookName: DataRouterStateHook) {\n  let route = React.useContext(RouteContext);\n  invariant(route, getDataRouterConsoleError(hookName));\n  return route;\n}\n\n// Internal version with hookName-aware debugging\nfunction useCurrentRouteId(hookName: DataRouterStateHook) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  invariant(\n    thisRoute.route.id,\n    `${hookName} can only be used on routes that contain a unique \"id\"`\n  );\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the ID for the nearest contextual route\n */\nexport function useRouteId() {\n  return useCurrentRouteId(DataRouterStateHook.UseRouteId);\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nexport function useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nexport function useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return React.useMemo(\n    () => ({\n      revalidate: dataRouterContext.router.revalidate,\n      state: state.revalidation,\n    }),\n    [dataRouterContext.router.revalidate, state.revalidation]\n  );\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nexport function useMatches(): UIMatch[] {\n  let { matches, loaderData } = useDataRouterState(\n    DataRouterStateHook.UseMatches\n  );\n  return React.useMemo(\n    () => matches.map((m) => convertRouteMatchToUiMatch(m, loaderData)),\n    [matches, loaderData]\n  );\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nexport function useLoaderData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\n      `You cannot \\`useLoaderData\\` in an errorElement (routeId: ${routeId})`\n    );\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nexport function useRouteLoaderData(routeId: string): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nexport function useActionData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  return state.actionData ? state.actionData[routeId] : undefined;\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * ErrorBoundary/errorElement to display a proper error message.\n */\nexport function useRouteError(): unknown {\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error !== undefined) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return state.errors?.[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor `<Await />` value\n */\nexport function useAsyncValue(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._data;\n}\n\n/**\n * Returns the error from the nearest ancestor `<Await />` value\n */\nexport function useAsyncError(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._error;\n}\n\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nexport function useBlocker(shouldBlock: boolean | BlockerFunction): Blocker {\n  let { router, basename } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let state = useDataRouterState(DataRouterStateHook.UseBlocker);\n\n  let [blockerKey, setBlockerKey] = React.useState(\"\");\n  let blockerFunction = React.useCallback<BlockerFunction>(\n    (arg) => {\n      if (typeof shouldBlock !== \"function\") {\n        return !!shouldBlock;\n      }\n      if (basename === \"/\") {\n        return shouldBlock(arg);\n      }\n\n      // If they provided us a function and we've got an active basename, strip\n      // it from the locations we expose to the user to match the behavior of\n      // useLocation\n      let { currentLocation, nextLocation, historyAction } = arg;\n      return shouldBlock({\n        currentLocation: {\n          ...currentLocation,\n          pathname:\n            stripBasename(currentLocation.pathname, basename) ||\n            currentLocation.pathname,\n        },\n        nextLocation: {\n          ...nextLocation,\n          pathname:\n            stripBasename(nextLocation.pathname, basename) ||\n            nextLocation.pathname,\n        },\n        historyAction,\n      });\n    },\n    [basename, shouldBlock]\n  );\n\n  // This effect is in charge of blocker key assignment and deletion (which is\n  // tightly coupled to the key)\n  React.useEffect(() => {\n    let key = String(++blockerId);\n    setBlockerKey(key);\n    return () => router.deleteBlocker(key);\n  }, [router]);\n\n  // This effect handles assigning the blockerFunction.  This is to handle\n  // unstable blocker function identities, and happens only after the prior\n  // effect so we don't get an orphaned blockerFunction in the router with a\n  // key of \"\".  Until then we just have the IDLE_BLOCKER.\n  React.useEffect(() => {\n    if (blockerKey !== \"\") {\n      router.getBlocker(blockerKey, blockerFunction);\n    }\n  }, [router, blockerKey, blockerFunction]);\n\n  // Prefer the blocker from `state` not `router.state` since DataRouterContext\n  // is memoized so this ensures we update on blocker state updates\n  return blockerKey && state.blockers.has(blockerKey)\n    ? state.blockers.get(blockerKey)!\n    : IDLE_BLOCKER;\n}\n\n/**\n * Stable version of useNavigate that is used when we are in the context of\n * a RouterProvider.\n */\nfunction useNavigateStable(): NavigateFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseNavigateStable);\n  let id = useCurrentRouteId(DataRouterStateHook.UseNavigateStable);\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our router subscriber yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        router.navigate(to);\n      } else {\n        router.navigate(to, { fromRouteId: id, ...options });\n      }\n    },\n    [router, id]\n  );\n\n  return navigate;\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\n\nfunction warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n", "import type { FutureConfig as RouterFutureConfig } from \"@remix-run/router\";\nimport type { FutureConfig as RenderFutureConfig } from \"./components\";\n\nconst alreadyWarned: { [key: string]: boolean } = {};\n\nexport function warnOnce(key: string, message: string): void {\n  if (__DEV__ && !alreadyWarned[message]) {\n    alreadyWarned[message] = true;\n    console.warn(message);\n  }\n}\n\nconst logDeprecation = (flag: string, msg: string, link: string) =>\n  warnOnce(\n    flag,\n    `⚠️ React Router Future Flag Warning: ${msg}. ` +\n      `You can use the \\`${flag}\\` future flag to opt-in early. ` +\n      `For more information, see ${link}.`\n  );\n\nexport function logV6DeprecationWarnings(\n  renderFuture: Partial<RenderFutureConfig> | undefined,\n  routerFuture?: Omit<RouterFutureConfig, \"v7_prependBasename\">\n) {\n  if (renderFuture?.v7_startTransition === undefined) {\n    logDeprecation(\n      \"v7_startTransition\",\n      \"React Router will begin wrapping state updates in `React.startTransition` in v7\",\n      \"https://reactrouter.com/v6/upgrading/future#v7_starttransition\"\n    );\n  }\n\n  if (\n    renderFuture?.v7_relativeSplatPath === undefined &&\n    (!routerFuture || routerFuture.v7_relativeSplatPath === undefined)\n  ) {\n    logDeprecation(\n      \"v7_relativeSplatPath\",\n      \"Relative route resolution within Splat routes is changing in v7\",\n      \"https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath\"\n    );\n  }\n\n  if (routerFuture) {\n    if (routerFuture.v7_fetcherPersist === undefined) {\n      logDeprecation(\n        \"v7_fetcherPersist\",\n        \"The persistence behavior of fetchers is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist\"\n      );\n    }\n\n    if (routerFuture.v7_normalizeFormMethod === undefined) {\n      logDeprecation(\n        \"v7_normalizeFormMethod\",\n        \"Casing of `formMethod` fields is being normalized to uppercase in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod\"\n      );\n    }\n\n    if (routerFuture.v7_partialHydration === undefined) {\n      logDeprecation(\n        \"v7_partialHydration\",\n        \"`RouterProvider` hydration behavior is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_partialhydration\"\n      );\n    }\n\n    if (routerFuture.v7_skipActionErrorRevalidation === undefined) {\n      logDeprecation(\n        \"v7_skipActionErrorRevalidation\",\n        \"The revalidation behavior after 4xx/5xx `action` responses is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation\"\n      );\n    }\n  }\n}\n", "import type {\n  InitialEntry,\n  LazyRouteFunction,\n  Location,\n  MemoryHistory,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RouterState,\n  RouterSubscriber,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\nimport * as React from \"react\";\n\nimport type {\n  DataRouteObject,\n  IndexRouteObject,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./context\";\nimport {\n  _renderMatches,\n  useAsyncValue,\n  useInRouterContext,\n  useLocation,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  useRoutesImpl,\n} from \"./hooks\";\nimport { logV6DeprecationWarnings } from \"./deprecations\";\n\nexport interface FutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_startTransition: boolean;\n}\n\nexport interface RouterProviderProps {\n  fallbackElement?: React.ReactNode;\n  router: RemixRouter;\n  // Only accept future flags relevant to rendering behavior\n  // routing flags should be accessed via router.future\n  future?: Partial<Pick<FutureConfig, \"v7_startTransition\">>;\n}\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let { v7_startTransition } = future || {};\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (newState: RouterState) => {\n      if (v7_startTransition && startTransitionImpl) {\n        startTransitionImpl(() => setStateImpl(newState));\n      } else {\n        setStateImpl(newState);\n      }\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  React.useEffect(\n    () => logV6DeprecationWarnings(future, router.future),\n    [router, future]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <Router\n            basename={basename}\n            location={state.location}\n            navigationType={state.historyAction}\n            navigator={navigator}\n            future={{\n              v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n            }}\n          >\n            {state.initialized || router.future.v7_partialHydration ? (\n              <DataRoutes\n                routes={router.routes}\n                future={router.future}\n                state={state}\n              />\n            ) : (\n              fallbackElement\n            )}\n          </Router>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  future?: Partial<FutureConfig>;\n}\n\n/**\n * A `<Router>` that stores all entries in memory.\n *\n * @see https://reactrouter.com/v6/router-components/memory-router\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n  future,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true,\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/v6/components/navigate\n */\nexport function Navigate({\n  to,\n  replace,\n  state,\n  relative,\n}: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  let { future, static: isStatic } = React.useContext(NavigationContext);\n\n  warning(\n    !isStatic,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let navigate = useNavigate();\n\n  // Resolve the path outside of the effect so that when effects run twice in\n  // StrictMode they navigate to the same place\n  let path = resolveTo(\n    to,\n    getResolveToMatches(matches, future.v7_relativeSplatPath),\n    locationPathname,\n    relative === \"path\"\n  );\n  let jsonPath = JSON.stringify(path);\n\n  React.useEffect(\n    () => navigate(JSON.parse(jsonPath), { replace, state, relative }),\n    [navigate, jsonPath, relative, replace, state]\n  );\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/v6/components/outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: NonIndexRouteObject[\"caseSensitive\"];\n  path?: NonIndexRouteObject[\"path\"];\n  id?: NonIndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<NonIndexRouteObject>;\n  loader?: NonIndexRouteObject[\"loader\"];\n  action?: NonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: NonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: NonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: NonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport interface LayoutRouteProps extends PathRouteProps {}\n\nexport interface IndexRouteProps {\n  caseSensitive?: IndexRouteObject[\"caseSensitive\"];\n  path?: IndexRouteObject[\"path\"];\n  id?: IndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<IndexRouteObject>;\n  loader?: IndexRouteObject[\"loader\"];\n  action?: IndexRouteObject[\"action\"];\n  hasErrorBoundary?: IndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: IndexRouteObject[\"shouldRevalidate\"];\n  handle?: IndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport type RouteProps = PathRouteProps | LayoutRouteProps | IndexRouteProps;\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/v6/components/route\n */\nexport function Route(_props: RouteProps): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n  future?: Partial<Pick<FutureConfig, \"v7_relativeSplatPath\">>;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a `<Router>` directly. Instead, you'll render a\n * router that is more specific to your environment such as a `<BrowserRouter>`\n * in web browsers or a `<StaticRouter>` for server rendering.\n *\n * @see https://reactrouter.com/v6/router-components/router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n  future,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(\n    () => ({\n      basename,\n      navigator,\n      static: staticProp,\n      future: {\n        v7_relativeSplatPath: false,\n        ...future,\n      },\n    }),\n    [basename, future, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let locationContext = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key,\n      },\n      navigationType,\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n\n  warning(\n    locationContext != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (locationContext == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider children={children} value={locationContext} />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of `<Route>` elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/v6/components/routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n\nexport interface AwaitResolveRenderFunction {\n  (data: Awaited<any>): React.ReactNode;\n}\n\nexport interface AwaitProps {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nexport function Await({ children, errorElement, resolve }: AwaitProps) {\n  return (\n    <AwaitErrorBoundary resolve={resolve} errorElement={errorElement}>\n      <ResolveAwait>{children}</ResolveAwait>\n    </AwaitErrorBoundary>\n  );\n}\n\ntype AwaitErrorBoundaryProps = React.PropsWithChildren<{\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}>;\n\ntype AwaitErrorBoundaryState = {\n  error: any;\n};\n\nenum AwaitRenderStatus {\n  pending,\n  success,\n  error,\n}\n\nconst neverSettledPromise = new Promise(() => {});\n\nclass AwaitErrorBoundary extends React.Component<\n  AwaitErrorBoundaryProps,\n  AwaitErrorBoundaryState\n> {\n  constructor(props: AwaitErrorBoundaryProps) {\n    super(props);\n    this.state = { error: null };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"<Await> caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    let { children, errorElement, resolve } = this.props;\n\n    let promise: TrackedPromise | null = null;\n    let status: AwaitRenderStatus = AwaitRenderStatus.pending;\n\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_data\", { get: () => resolve });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_error\", { get: () => renderError });\n    } else if ((resolve as TrackedPromise)._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status =\n        \"_error\" in promise\n          ? AwaitRenderStatus.error\n          : \"_data\" in promise\n          ? AwaitRenderStatus.success\n          : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", { get: () => true });\n      promise = resolve.then(\n        (data: any) =>\n          Object.defineProperty(resolve, \"_data\", { get: () => data }),\n        (error: any) =>\n          Object.defineProperty(resolve, \"_error\", { get: () => error })\n      );\n    }\n\n    if (\n      status === AwaitRenderStatus.error &&\n      promise._error instanceof AbortedDeferredError\n    ) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return <AwaitContext.Provider value={promise} children={errorElement} />;\n    }\n\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return <AwaitContext.Provider value={promise} children={children} />;\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on `<Await>`\n */\nfunction ResolveAwait({\n  children,\n}: {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n}) {\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return <>{toRender}</>;\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/v6/utils/create-routes-from-children\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode,\n  parentPath: number[] = []\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element, index) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    let treePath = [...parentPath, index];\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children, treePath)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    invariant(\n      !element.props.index || !element.props.children,\n      \"An index route cannot have child routes.\"\n    );\n\n    let route: RouteObject = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary:\n        element.props.ErrorBoundary != null ||\n        element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(\n        element.props.children,\n        treePath\n      );\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n", "import * as React from \"react\";\nimport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AgnosticPatchRoutesOnNavigationFunction,\n  AgnosticPatchRoutesOnNavigationFunctionArgs,\n  Blocker,\n  BlockerFunction,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  HydrationState,\n  InitialEntry,\n  JsonFunction,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  Navigation,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  RedirectFunction,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  FutureConfig as RouterFutureConfig,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  createPath,\n  createRouter,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  resolvePath,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  AwaitProps,\n  FutureConfig,\n  IndexRouteProps,\n  LayoutRouteProps,\n  MemoryRouterProps,\n  NavigateProps,\n  OutletProps,\n  PathRouteProps,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n} from \"./lib/components\";\nimport {\n  Await,\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createRoutesFromChildren,\n  renderMatches,\n} from \"./lib/components\";\nimport type {\n  DataRouteMatch,\n  DataRouteObject,\n  IndexRouteObject,\n  NavigateOptions,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./lib/context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./lib/context\";\nimport type { NavigateFunction } from \"./lib/hooks\";\nimport {\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteId,\n  useRouteLoaderData,\n  useRoutes,\n  useRoutesImpl,\n} from \"./lib/hooks\";\nimport { logV6DeprecationWarnings } from \"./lib/deprecations\";\n\n// Exported for backwards compatibility, but not being used internally anymore\ntype Hash = string;\ntype Pathname = string;\ntype Search = string;\n\n// Expose react-router public API\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  DataRouteMatch,\n  DataRouteObject,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LayoutRouteProps,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  Pathname,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n  Blocker,\n  BlockerFunction,\n};\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromChildren as createRoutesFromElements,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useBlocker,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n};\n\nexport type PatchRoutesOnNavigationFunctionArgs =\n  AgnosticPatchRoutesOnNavigationFunctionArgs<RouteObject, RouteMatch>;\n\nexport type PatchRoutesOnNavigationFunction =\n  AgnosticPatchRoutesOnNavigationFunction<RouteObject, RouteMatch>;\n\nfunction mapRouteProperties(route: RouteObject) {\n  let updates: Partial<RouteObject> & { hasErrorBoundary: boolean } = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.ErrorBoundary != null || route.errorElement != null,\n  };\n\n  if (route.Component) {\n    if (__DEV__) {\n      if (route.element) {\n        warning(\n          false,\n          \"You should not include both `Component` and `element` on your route - \" +\n            \"`Component` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      element: React.createElement(route.Component),\n      Component: undefined,\n    });\n  }\n\n  if (route.HydrateFallback) {\n    if (__DEV__) {\n      if (route.hydrateFallbackElement) {\n        warning(\n          false,\n          \"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - \" +\n            \"`HydrateFallback` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      hydrateFallbackElement: React.createElement(route.HydrateFallback),\n      HydrateFallback: undefined,\n    });\n  }\n\n  if (route.ErrorBoundary) {\n    if (__DEV__) {\n      if (route.errorElement) {\n        warning(\n          false,\n          \"You should not include both `ErrorBoundary` and `errorElement` on your route - \" +\n            \"`ErrorBoundary` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      errorElement: React.createElement(route.ErrorBoundary),\n      ErrorBoundary: undefined,\n    });\n  }\n\n  return updates;\n}\n\nexport function createMemoryRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n    hydrationData?: HydrationState;\n    initialEntries?: InitialEntry[];\n    initialIndex?: number;\n    dataStrategy?: DataStrategyFunction;\n    patchRoutesOnNavigation?: PatchRoutesOnNavigationFunction;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createMemoryHistory({\n      initialEntries: opts?.initialEntries,\n      initialIndex: opts?.initialIndex,\n    }),\n    hydrationData: opts?.hydrationData,\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n  }).initialize();\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  DataRouterContext as UNSAFE_DataRouterContext,\n  DataRouterStateContext as UNSAFE_DataRouterStateContext,\n  LocationContext as UNSAFE_LocationContext,\n  NavigationContext as UNSAFE_NavigationContext,\n  RouteContext as UNSAFE_RouteContext,\n  mapRouteProperties as UNSAFE_mapRouteProperties,\n  useRouteId as UNSAFE_useRouteId,\n  useRoutesImpl as UNSAFE_useRoutesImpl,\n  logV6DeprecationWarnings as UNSAFE_logV6DeprecationWarnings,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA;AACA;AA+DO,MAAMA,iBAAiB,gBAC5BC,KAAK,CAACC,aAAa,CAAiC,IAAI;AAC1D,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXL,iBAAiB,CAACM,WAAW,GAAG,YAAY;AAC9C;AAEO,MAAMC,sBAAsB,gBAAGN,KAAK,CAACC,aAAa,CAEvD,IAAI;AACN,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXE,sBAAsB,CAACD,WAAW,GAAG,iBAAiB;AACxD;AAEO,MAAME,YAAY,gBAAGP,KAAK,CAACC,aAAa,CAAwB,IAAI,CAAC;AAC5E,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXG,YAAY,CAACF,WAAW,GAAG,OAAO;AACpC;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAmBO,MAAMG,iBAAiB,gBAAGR,KAAK,CAACC,aAAa,CAClD,IACF;AAEA,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXI,iBAAiB,CAACH,WAAW,GAAG,YAAY;AAC9C;AAOO,MAAMI,eAAe,gBAAGT,KAAK,CAACC,aAAa,CAChD,IACF;AAEA,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXK,eAAe,CAACJ,WAAW,GAAG,UAAU;AAC1C;MAQaK,YAAY,gBAAGV,KAAK,CAACC,aAAa,CAAqB;EAClEU,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,EAAE;EACXC,WAAW,EAAE;AACf,CAAC;AAED,IAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXM,YAAY,CAACL,WAAW,GAAG,OAAO;AACpC;AAEO,MAAMS,iBAAiB,gBAAGd,KAAK,CAACC,aAAa,CAAM,IAAI,CAAC;AAE/D,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXU,iBAAiB,CAACT,WAAW,GAAG,YAAY;AAC9C;;AC7HA;AACA;AACA;AACA;AACA;AACA;AACO,SAASU,OAAOA,CACrBC,EAAM,EAAAC,KAAA,EAEE;EAAA,IADR;IAAEC;EAA6C,CAAC,GAAAD,KAAA,cAAG,EAAE,GAAAA,KAAA;EAErD,CACEE,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtB,eAAAgB,gBAAS,CAEP;EAAA;EACA;EAAA,wEAHFA,gBAAS;EAOT,IAAI;IAAEC,QAAQ;IAAEC;EAAU,CAAC,GAAGtB,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC;EACjE,IAAI;IAAEgB,IAAI;IAAEC,QAAQ;IAAEC;EAAO,CAAC,GAAGC,eAAe,CAACX,EAAE,EAAE;IAAEE;EAAS,CAAC,CAAC;EAElE,IAAIU,cAAc,GAAGH,QAAQ;;EAE7B;EACA;EACA;EACA;EACA,IAAIJ,QAAQ,KAAK,GAAG,EAAE;IACpBO,cAAc,GACZH,QAAQ,KAAK,GAAG,GAAGJ,QAAQ,GAAGQ,SAAS,CAAC,CAACR,QAAQ,EAAEI,QAAQ,CAAC,CAAC;EACjE;EAEA,OAAOH,SAAS,CAACQ,UAAU,CAAC;IAAEL,QAAQ,EAAEG,cAAc;IAAEF,MAAM;IAAEF;EAAK,CAAC,CAAC;AACzE;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASL,kBAAkBA,CAAA,EAAY;EAC5C,OAAOnB,KAAK,CAACuB,UAAU,CAACd,eAAe,CAAC,IAAI,IAAI;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASsB,WAAWA,CAAA,EAAa;EACtC,CACEZ,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtB,eAAAgB,gBAAS,CAEP;EAAA;EACA;EAAA,4EAHFA,gBAAS;EAOT,OAAOpB,KAAK,CAACuB,UAAU,CAACd,eAAe,CAAC,CAACuB,QAAQ;AACnD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,iBAAiBA,CAAA,EAAmB;EAClD,OAAOjC,KAAK,CAACuB,UAAU,CAACd,eAAe,CAAC,CAACyB,cAAc;AACzD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,QAAQA,CAGtBC,OAAiC,EAA8B;EAC/D,CACEjB,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtB,eAAAgB,gBAAS,CAEP;EAAA;EACA;EAAA,yEAHFA,gBAAS;EAOT,IAAI;IAAEK;GAAU,GAAGM,WAAW,EAAE;EAChC,OAAO/B,KAAK,CAACqC,OAAO,CAClB,MAAMC,SAAS,CAAiBF,OAAO,EAAEG,iBAAU,CAACd,QAAQ,CAAC,CAAC,EAC9D,CAACA,QAAQ,EAAEW,OAAO,CACpB,CAAC;AACH;;AAEA;AACA;AACA;;AAMA,MAAMI,qBAAqB,GACzB,8DACmC;;AAErC;AACA,SAASC,yBAAyBA,CAChCC,EAA+C,EAC/C;EACA,IAAIC,QAAQ,GAAG3C,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC,CAACoC,MAAM;EACzD,IAAI,CAACD,QAAQ,EAAE;IACb;IACA;IACA;IACA3C,KAAK,CAAC6C,eAAe,CAACH,EAAE,CAAC;EAC3B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,WAAWA,CAAA,EAAqB;EAC9C,IAAI;IAAEjC;EAAY,CAAC,GAAGb,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC;EACpD;EACA;EACA,OAAOG,WAAW,GAAGkC,iBAAiB,EAAE,GAAGC,mBAAmB,EAAE;AAClE;AAEA,SAASA,mBAAmBA,CAAA,EAAqB;EAC/C,CACE7B,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtB,eAAAgB,gBAAS,CAEP;EAAA;EACA;EAAA,4EAHFA,gBAAS;EAOT,IAAI6B,iBAAiB,GAAGjD,KAAK,CAACuB,UAAU,CAACxB,iBAAiB,CAAC;EAC3D,IAAI;IAAEsB,QAAQ;IAAE6B,MAAM;IAAE5B;EAAU,CAAC,GAAGtB,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC;EACzE,IAAI;IAAEI;EAAQ,CAAC,GAAGZ,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC;EAChD,IAAI;IAAEe,QAAQ,EAAE0B;GAAkB,GAAGpB,WAAW,EAAE;EAElD,IAAIqB,kBAAkB,GAAGC,IAAI,CAACC,SAAS,CACrCC,0BAAmB,CAAC3C,OAAO,EAAEsC,MAAM,CAACM,oBAAoB,CAC1D,CAAC;EAED,IAAIC,SAAS,GAAGzD,KAAK,CAAC0D,MAAM,CAAC,KAAK,CAAC;EACnCjB,yBAAyB,CAAC,MAAM;IAC9BgB,SAAS,CAACE,OAAO,GAAG,IAAI;EAC1B,CAAC,CAAC;EAEF,IAAIC,QAA0B,GAAG5D,KAAK,CAAC6D,WAAW,CAChD,UAAC7C,EAAe,EAAE8C,OAAwB,EAAU;IAAA,IAAlCA,OAAwB;MAAxBA,OAAwB,GAAG,EAAE;IAAA;IAC7C5D,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA2D,cAAO,CAACN,SAAS,CAACE,OAAO,EAAEnB,qBAAqB,CAAC;;IAEjD;IACA;IACA,IAAI,CAACiB,SAAS,CAACE,OAAO,EAAE;IAExB,IAAI,OAAO3C,EAAE,KAAK,QAAQ,EAAE;MAC1BM,SAAS,CAAC0C,EAAE,CAAChD,EAAE,CAAC;MAChB;IACF;IAEA,IAAIiD,IAAI,GAAGC,SAAS,CAClBlD,EAAE,EACFqC,IAAI,CAACc,KAAK,CAACf,kBAAkB,CAAC,EAC9BD,gBAAgB,EAChBW,OAAO,CAAC5C,QAAQ,KAAK,MACvB,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA,IAAI+B,iBAAiB,IAAI,IAAI,IAAI5B,QAAQ,KAAK,GAAG,EAAE;MACjD4C,IAAI,CAACxC,QAAQ,GACXwC,IAAI,CAACxC,QAAQ,KAAK,GAAG,GACjBJ,QAAQ,GACRQ,SAAS,CAAC,CAACR,QAAQ,EAAE4C,IAAI,CAACxC,QAAQ,CAAC,CAAC;IAC5C;IAEA,CAAC,CAAC,CAACqC,OAAO,CAACM,OAAO,GAAG9C,SAAS,CAAC8C,OAAO,GAAG9C,SAAS,CAAC+C,IAAI,EACrDJ,IAAI,EACJH,OAAO,CAACQ,KAAK,EACbR,OACF,CAAC;EACH,CAAC,EACD,CACEzC,QAAQ,EACRC,SAAS,EACT8B,kBAAkB,EAClBD,gBAAgB,EAChBF,iBAAiB,CAErB,CAAC;EAED,OAAOW,QAAQ;AACjB;AAEA,MAAMW,aAAa,gBAAGvE,KAAK,CAACC,aAAa,CAAU,IAAI,CAAC;;AAExD;AACA;AACA;AACA;AACA;AACO,SAASuE,gBAAgBA,CAAA,EAA+B;EAC7D,OAAOxE,KAAK,CAACuB,UAAU,CAACgD,aAAa,CAAC;AACxC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,SAASA,CAACC,OAAiB,EAA6B;EACtE,IAAI/D,MAAM,GAAGX,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC,CAACC,MAAM;EAClD,IAAIA,MAAM,EAAE;IACV,oBACEX,KAAA,CAAA2E,aAAA,CAACJ,aAAa,CAACK,QAAQ;MAACC,KAAK,EAAEH;IAAQ,GAAE/D,MAA+B,CAAC;EAE7E;EACA,OAAOA,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASmE,SAASA,CAAA,EAIvB;EACA,IAAI;IAAElE;EAAQ,CAAC,GAAGZ,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC;EAChD,IAAIqE,UAAU,GAAGnE,OAAO,CAACA,OAAO,CAACoE,MAAM,GAAG,CAAC,CAAC;EAC5C,OAAOD,UAAU,GAAIA,UAAU,CAACE,MAAM,GAAW,EAAE;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAStD,eAAeA,CAC7BX,EAAM,EAAAkE,MAAA,EAEA;EAAA,IADN;IAAEhE;EAA6C,CAAC,GAAAgE,MAAA,cAAG,EAAE,GAAAA,MAAA;EAErD,IAAI;IAAEhC;EAAO,CAAC,GAAGlD,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC;EACpD,IAAI;IAAEI;EAAQ,CAAC,GAAGZ,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC;EAChD,IAAI;IAAEe,QAAQ,EAAE0B;GAAkB,GAAGpB,WAAW,EAAE;EAClD,IAAIqB,kBAAkB,GAAGC,IAAI,CAACC,SAAS,CACrCC,0BAAmB,CAAC3C,OAAO,EAAEsC,MAAM,CAACM,oBAAoB,CAC1D,CAAC;EAED,OAAOxD,KAAK,CAACqC,OAAO,CAClB,MACE6B,SAAS,CACPlD,EAAE,EACFqC,IAAI,CAACc,KAAK,CAACf,kBAAkB,CAAC,EAC9BD,gBAAgB,EAChBjC,QAAQ,KAAK,MACf,CAAC,EACH,CAACF,EAAE,EAAEoC,kBAAkB,EAAED,gBAAgB,EAAEjC,QAAQ,CACrD,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASiE,SAASA,CACvBC,MAAqB,EACrBC,WAAwC,EACb;EAC3B,OAAOC,aAAa,CAACF,MAAM,EAAEC,WAAW,CAAC;AAC3C;;AAEA;AACO,SAASC,aAAaA,CAC3BF,MAAqB,EACrBC,WAAwC,EACxCE,eAAsC,EACtCrC,MAA8B,EACH;EAC3B,CACE/B,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtB,eAAAgB,gBAAS,CAEP;EAAA;EACA;EAAA,0EAHFA,gBAAS;EAOT,IAAI;IAAEE;EAAU,CAAC,GAAGtB,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC;EACvD,IAAI;IAAEI,OAAO,EAAE4E;EAAc,CAAC,GAAGxF,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC;EAC/D,IAAIqE,UAAU,GAAGS,aAAa,CAACA,aAAa,CAACR,MAAM,GAAG,CAAC,CAAC;EACxD,IAAIS,YAAY,GAAGV,UAAU,GAAGA,UAAU,CAACE,MAAM,GAAG,EAAE;EACtD,IAAIS,cAAc,GAAGX,UAAU,GAAGA,UAAU,CAACtD,QAAQ,GAAG,GAAG;EAC3D,IAAIkE,kBAAkB,GAAGZ,UAAU,GAAGA,UAAU,CAACa,YAAY,GAAG,GAAG;EACnE,IAAIC,WAAW,GAAGd,UAAU,IAAIA,UAAU,CAACe,KAAK;EAEhD,IAAA5F,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;IACX;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI2F,UAAU,GAAIF,WAAW,IAAIA,WAAW,CAAC5B,IAAI,IAAK,EAAE;IACxD+B,WAAW,CACTN,cAAc,EACd,CAACG,WAAW,IAAIE,UAAU,CAACE,QAAQ,CAAC,GAAG,CAAC,EACxC,2EACMP,cAAc,GAAyB,6BAAAK,UAAU,GAAc,yFACC,GACH,mGAClC,IACU,4CAAAA,UAAU,oBAAe,IACzD,aAAAA,UAAU,KAAK,GAAG,GAAG,GAAG,GAAMA,UAAU,OAAI,WACzD,CAAC;EACH;EAEA,IAAIG,mBAAmB,GAAGnE,WAAW,EAAE;EAEvC,IAAIC,QAAQ;EACZ,IAAIqD,WAAW,EAAE;IAAA,IAAAc,qBAAA;IACf,IAAIC,iBAAiB,GACnB,OAAOf,WAAW,KAAK,QAAQ,GAAGgB,SAAS,CAAChB,WAAW,CAAC,GAAGA,WAAW;IAExE,EACEM,kBAAkB,KAAK,GAAG,MAAAQ,qBAAA,GACxBC,iBAAiB,CAAC3E,QAAQ,qBAA1B0E,qBAAA,CAA4BG,UAAU,CAACX,kBAAkB,CAAC,KAAAzF,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAF9D,eAAAgB,gBAAS,QAGP,2FACmF,0JAClBuE,kBAAkB,SAAI,wBACpES,iBAAiB,CAAC3E,QAAQ,0CAAuC,IANtFL,gBAAS;IASTY,QAAQ,GAAGoE,iBAAiB;EAC9B,CAAC,MAAM;IACLpE,QAAQ,GAAGkE,mBAAmB;EAChC;EAEA,IAAIzE,QAAQ,GAAGO,QAAQ,CAACP,QAAQ,IAAI,GAAG;EAEvC,IAAI8E,iBAAiB,GAAG9E,QAAQ;EAChC,IAAIkE,kBAAkB,KAAK,GAAG,EAAE;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIa,cAAc,GAAGb,kBAAkB,CAACvB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACqC,KAAK,CAAC,GAAG,CAAC;IACrE,IAAIC,QAAQ,GAAGjF,QAAQ,CAAC2C,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACqC,KAAK,CAAC,GAAG,CAAC;IACrDF,iBAAiB,GAAG,GAAG,GAAGG,QAAQ,CAACC,KAAK,CAACH,cAAc,CAACxB,MAAM,CAAC,CAAC4B,IAAI,CAAC,GAAG,CAAC;EAC3E;EAEA,IAAIhG,OAAO,GAAGiG,WAAW,CAACzB,MAAM,EAAE;IAAE3D,QAAQ,EAAE8E;EAAkB,CAAC,CAAC;EAElE,IAAArG,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;IACXF,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA2D,cAAO,CACL8B,WAAW,IAAIjF,OAAO,IAAI,IAAI,oCACCoB,QAAQ,CAACP,QAAQ,GAAGO,QAAQ,CAACN,MAAM,GAAGM,QAAQ,CAACR,IAAI,QACpF,CAAC;IAEDtB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA2D,cAAO,CACLnD,OAAO,IAAI,IAAI,IACbA,OAAO,CAACA,OAAO,CAACoE,MAAM,GAAG,CAAC,CAAC,CAACc,KAAK,CAACgB,OAAO,KAAKC,SAAS,IACvDnG,OAAO,CAACA,OAAO,CAACoE,MAAM,GAAG,CAAC,CAAC,CAACc,KAAK,CAACkB,SAAS,KAAKD,SAAS,IACzDnG,OAAO,CAACA,OAAO,CAACoE,MAAM,GAAG,CAAC,CAAC,CAACc,KAAK,CAACmB,IAAI,KAAKF,SAAS,EACtD,sCAAmC/E,QAAQ,CAACP,QAAQ,GAAGO,QAAQ,CAACN,MAAM,GAAGM,QAAQ,CAACR,IAAI,mGACI,0DAE5F,CAAC;EACH;EAEA,IAAI0F,eAAe,GAAGC,cAAc,CAClCvG,OAAO,IACLA,OAAO,CAACwG,GAAG,CAAEC,KAAK,IAChBC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEF,KAAK,EAAE;IACvBpC,MAAM,EAAEqC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE9B,YAAY,EAAE4B,KAAK,CAACpC,MAAM,CAAC;IACrDxD,QAAQ,EAAEI,SAAS,CAAC,CAClB8D,kBAAkB;IAClB;IACArE,SAAS,CAACkG,cAAc,GACpBlG,SAAS,CAACkG,cAAc,CAACH,KAAK,CAAC5F,QAAQ,CAAC,CAACA,QAAQ,GACjD4F,KAAK,CAAC5F,QAAQ,CACnB,CAAC;IACFmE,YAAY,EACVyB,KAAK,CAACzB,YAAY,KAAK,GAAG,GACtBD,kBAAkB,GAClB9D,SAAS,CAAC,CACR8D,kBAAkB;IAClB;IACArE,SAAS,CAACkG,cAAc,GACpBlG,SAAS,CAACkG,cAAc,CAACH,KAAK,CAACzB,YAAY,CAAC,CAACnE,QAAQ,GACrD4F,KAAK,CAACzB,YAAY,CACvB;GACR,CACH,CAAC,EACHJ,aAAa,EACbD,eAAe,EACfrC,MACF,CAAC;;EAED;EACA;EACA;EACA,IAAImC,WAAW,IAAI6B,eAAe,EAAE;IAClC,oBACElH,KAAA,CAAA2E,aAAA,CAAClE,eAAe,CAACmE,QAAQ;MACvBC,KAAK,EAAE;QACL7C,QAAQ,EAAAyF,QAAA;UACNhG,QAAQ,EAAE,GAAG;UACbC,MAAM,EAAE,EAAE;UACVF,IAAI,EAAE,EAAE;UACR8C,KAAK,EAAE,IAAI;UACXoD,GAAG,EAAE;QAAS,GACX1F,QAAQ,CACZ;QACDE,cAAc,EAAEyF,MAAc,CAACC;MACjC;IAAE,GAEDV,eACuB,CAAC;EAE/B;EAEA,OAAOA,eAAe;AACxB;AAEA,SAASW,qBAAqBA,CAAA,EAAG;EAC/B,IAAIC,KAAK,GAAGC,aAAa,EAAE;EAC3B,IAAIC,OAAO,GAAGC,oBAAoB,CAACH,KAAK,CAAC,GAClCA,KAAK,CAACI,MAAM,GAAI,MAAAJ,KAAK,CAACK,UAAU,GACnCL,KAAK,YAAYM,KAAK,GACtBN,KAAK,CAACE,OAAO,GACb3E,IAAI,CAACC,SAAS,CAACwE,KAAK,CAAC;EACzB,IAAIO,KAAK,GAAGP,KAAK,YAAYM,KAAK,GAAGN,KAAK,CAACO,KAAK,GAAG,IAAI;EACvD,IAAIC,SAAS,GAAG,wBAAwB;EACxC,IAAIC,SAAS,GAAG;IAAEC,OAAO,EAAE,QAAQ;IAAEC,eAAe,EAAEH;GAAW;EACjE,IAAII,UAAU,GAAG;IAAEF,OAAO,EAAE,SAAS;IAAEC,eAAe,EAAEH;GAAW;EAEnE,IAAIK,OAAO,GAAG,IAAI;EAClB,IAAAzI,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;IACXwI,OAAO,CAACd,KAAK,CACX,sDAAsD,EACtDA,KACF,CAAC;IAEDa,OAAO,gBACL3I,KAAA,CAAA2E,aAAA,CAAA3E,KAAA,CAAA6I,QAAA,EACE,mBAAA7I,KAAA,CAAA2E,aAAA,YAAG,yCAAsB,CAAC,eAC1B3E,KAAA,CAAA2E,aAAA,YAAG,8FAEqB,eAAA3E,KAAA,CAAA2E,aAAA;MAAMmE,KAAK,EAAEJ;KAAY,iBAAmB,CAAC,EAAG,OAAC,GAAG,eAC1E1I,KAAA,CAAA2E,aAAA;MAAMmE,KAAK,EAAEJ;IAAW,GAAC,cAAkB,CAAC,EAC3C,uBACH,CACH;EACH;EAEA,oBACE1I,KAAA,CAAA2E,aAAA,CAAA3E,KAAA,CAAA6I,QAAA,qBACE7I,KAAA,CAAA2E,aAAA,CAAI,2CAAiC,CAAC,eACtC3E,KAAA,CAAA2E,aAAA;IAAImE,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAS;EAAE,GAAEf,OAAY,CAAC,EACjDK,KAAK,gBAAGrI,KAAA,CAAA2E,aAAA;IAAKmE,KAAK,EAAEP;EAAU,GAAEF,KAAW,CAAC,GAAG,IAAI,EACnDM,OACD,CAAC;AAEP;AAEA,MAAMK,mBAAmB,gBAAGhJ,KAAA,CAAA2E,aAAA,CAACkD,qBAAqB,MAAE,CAAC;AAgB9C,MAAMoB,mBAAmB,SAASjJ,KAAK,CAACgH,SAAS,CAGtD;EACAkC,WAAWA,CAACC,KAA+B,EAAE;IAC3C,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAAC7E,KAAK,GAAG;MACXtC,QAAQ,EAAEmH,KAAK,CAACnH,QAAQ;MACxBoH,YAAY,EAAED,KAAK,CAACC,YAAY;MAChCtB,KAAK,EAAEqB,KAAK,CAACrB;KACd;EACH;EAEA,OAAOuB,wBAAwBA,CAACvB,KAAU,EAAE;IAC1C,OAAO;MAAEA,KAAK,EAAEA;KAAO;EACzB;EAEA,OAAOwB,wBAAwBA,CAC7BH,KAA+B,EAC/B7E,KAA+B,EAC/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IACEA,KAAK,CAACtC,QAAQ,KAAKmH,KAAK,CAACnH,QAAQ,IAChCsC,KAAK,CAAC8E,YAAY,KAAK,MAAM,IAAID,KAAK,CAACC,YAAY,KAAK,MAAO,EAChE;MACA,OAAO;QACLtB,KAAK,EAAEqB,KAAK,CAACrB,KAAK;QAClB9F,QAAQ,EAAEmH,KAAK,CAACnH,QAAQ;QACxBoH,YAAY,EAAED,KAAK,CAACC;OACrB;IACH;;IAEA;IACA;IACA;IACA;IACA,OAAO;MACLtB,KAAK,EAAEqB,KAAK,CAACrB,KAAK,KAAKf,SAAS,GAAGoC,KAAK,CAACrB,KAAK,GAAGxD,KAAK,CAACwD,KAAK;MAC5D9F,QAAQ,EAAEsC,KAAK,CAACtC,QAAQ;MACxBoH,YAAY,EAAED,KAAK,CAACC,YAAY,IAAI9E,KAAK,CAAC8E;KAC3C;EACH;EAEAG,iBAAiBA,CAACzB,KAAU,EAAE0B,SAAc,EAAE;IAC5CZ,OAAO,CAACd,KAAK,CACX,uDAAuD,EACvDA,KAAK,EACL0B,SACF,CAAC;EACH;EAEAC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACnF,KAAK,CAACwD,KAAK,KAAKf,SAAS,gBACnC/G,KAAA,CAAA2E,aAAA,CAACjE,YAAY,CAACkE,QAAQ;MAACC,KAAK,EAAE,IAAI,CAACsE,KAAK,CAACO;IAAa,gBACpD1J,KAAA,CAAA2E,aAAA,CAAC7D,iBAAiB,CAAC8D,QAAQ;MACzBC,KAAK,EAAE,IAAI,CAACP,KAAK,CAACwD,KAAM;MACxB6B,QAAQ,EAAE,IAAI,CAACR,KAAK,CAACS;IAAU,CAChC,CACoB,CAAC,GAExB,IAAI,CAACT,KAAK,CAACQ,QACZ;EACH;AACF;AAQA,SAASE,aAAaA,CAAAC,IAAA,EAAwD;EAAA,IAAvD;IAAEJ,YAAY;IAAErC,KAAK;IAAEsC;EAA6B,CAAC,GAAAG,IAAA;EAC1E,IAAI7G,iBAAiB,GAAGjD,KAAK,CAACuB,UAAU,CAACxB,iBAAiB,CAAC;;EAE3D;EACA;EACA,IACEkD,iBAAiB,IACjBA,iBAAiB,CAACL,MAAM,IACxBK,iBAAiB,CAAC8G,aAAa,KAC9B1C,KAAK,CAACvB,KAAK,CAACkE,YAAY,IAAI3C,KAAK,CAACvB,KAAK,CAACmE,aAAa,CAAC,EACvD;IACAhH,iBAAiB,CAAC8G,aAAa,CAACG,0BAA0B,GAAG7C,KAAK,CAACvB,KAAK,CAACqE,EAAE;EAC7E;EAEA,oBACEnK,KAAA,CAAA2E,aAAA,CAACjE,YAAY,CAACkE,QAAQ;IAACC,KAAK,EAAE6E;EAAa,GACxCC,QACoB,CAAC;AAE5B;AAEO,SAASxC,cAAcA,CAC5BvG,OAA4B,EAC5B4E,aAA2B,EAC3BD,eAA4C,EAC5CrC,MAAoC,EACT;EAAA,IAAAkH,gBAAA;EAAA,IAH3B5E,aAA2B;IAA3BA,aAA2B,GAAG,EAAE;EAAA;EAAA,IAChCD,eAA4C;IAA5CA,eAA4C,GAAG,IAAI;EAAA;EAAA,IACnDrC,MAAoC;IAApCA,MAAoC,GAAG,IAAI;EAAA;EAE3C,IAAItC,OAAO,IAAI,IAAI,EAAE;IAAA,IAAAyJ,OAAA;IACnB,IAAI,CAAC9E,eAAe,EAAE;MACpB,OAAO,IAAI;IACb;IAEA,IAAIA,eAAe,CAAC+E,MAAM,EAAE;MAC1B;MACA;MACA1J,OAAO,GAAG2E,eAAe,CAAC3E,OAA2B;IACvD,CAAC,MAAM,IACL,CAAAyJ,OAAA,GAAAnH,MAAM,KAAN,QAAAmH,OAAA,CAAQE,mBAAmB,IAC3B/E,aAAa,CAACR,MAAM,KAAK,CAAC,IAC1B,CAACO,eAAe,CAACiF,WAAW,IAC5BjF,eAAe,CAAC3E,OAAO,CAACoE,MAAM,GAAG,CAAC,EAClC;MACA;MACA;MACA;MACA;MACA;MACA;MACApE,OAAO,GAAG2E,eAAe,CAAC3E,OAA2B;IACvD,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;EAEA,IAAIsG,eAAe,GAAGtG,OAAO;;EAE7B;EACA,IAAI0J,MAAM,IAAAF,gBAAA,GAAG7E,eAAe,KAAf,gBAAA6E,gBAAA,CAAiBE,MAAM;EACpC,IAAIA,MAAM,IAAI,IAAI,EAAE;IAClB,IAAIG,UAAU,GAAGvD,eAAe,CAACwD,SAAS,CACvCC,CAAC,IAAKA,CAAC,CAAC7E,KAAK,CAACqE,EAAE,IAAI,CAAAG,MAAM,oBAANA,MAAM,CAAGK,CAAC,CAAC7E,KAAK,CAACqE,EAAE,CAAC,MAAKpD,SAChD,CAAC;IACD,EACE0D,UAAU,IAAI,CAAC,IAAAvK,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBADjBgB,gBAAS,sEAEqDkG,MAAM,CAACsD,IAAI,CACrEN,MACF,CAAC,CAAC1D,IAAI,CAAC,GAAG,CAAC,IAJbxF,gBAAS;IAMT8F,eAAe,GAAGA,eAAe,CAACP,KAAK,CACrC,CAAC,EACDkE,IAAI,CAACC,GAAG,CAAC5D,eAAe,CAAClC,MAAM,EAAEyF,UAAU,GAAG,CAAC,CACjD,CAAC;EACH;;EAEA;EACA;EACA,IAAIM,cAAc,GAAG,KAAK;EAC1B,IAAIC,aAAa,GAAG,CAAC,CAAC;EACtB,IAAIzF,eAAe,IAAIrC,MAAM,IAAIA,MAAM,CAACqH,mBAAmB,EAAE;IAC3D,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/D,eAAe,CAAClC,MAAM,EAAEiG,CAAC,EAAE,EAAE;MAC/C,IAAI5D,KAAK,GAAGH,eAAe,CAAC+D,CAAC,CAAC;MAC9B;MACA,IAAI5D,KAAK,CAACvB,KAAK,CAACoF,eAAe,IAAI7D,KAAK,CAACvB,KAAK,CAACqF,sBAAsB,EAAE;QACrEH,aAAa,GAAGC,CAAC;MACnB;MAEA,IAAI5D,KAAK,CAACvB,KAAK,CAACqE,EAAE,EAAE;QAClB,IAAI;UAAEiB,UAAU;UAAEd;QAAO,CAAC,GAAG/E,eAAe;QAC5C,IAAI8F,gBAAgB,GAClBhE,KAAK,CAACvB,KAAK,CAACwF,MAAM,IAClBF,UAAU,CAAC/D,KAAK,CAACvB,KAAK,CAACqE,EAAE,CAAC,KAAKpD,SAAS,KACvC,CAACuD,MAAM,IAAIA,MAAM,CAACjD,KAAK,CAACvB,KAAK,CAACqE,EAAE,CAAC,KAAKpD,SAAS,CAAC;QACnD,IAAIM,KAAK,CAACvB,KAAK,CAACmB,IAAI,IAAIoE,gBAAgB,EAAE;UACxC;UACA;UACA;UACAN,cAAc,GAAG,IAAI;UACrB,IAAIC,aAAa,IAAI,CAAC,EAAE;YACtB9D,eAAe,GAAGA,eAAe,CAACP,KAAK,CAAC,CAAC,EAAEqE,aAAa,GAAG,CAAC,CAAC;UAC/D,CAAC,MAAM;YACL9D,eAAe,GAAG,CAACA,eAAe,CAAC,CAAC,CAAC,CAAC;UACxC;UACA;QACF;MACF;IACF;EACF;EAEA,OAAOA,eAAe,CAACqE,WAAW,CAAC,CAAC5K,MAAM,EAAE0G,KAAK,EAAEmE,KAAK,KAAK;IAC3D;IACA,IAAI1D,KAAU;IACd,IAAI2D,2BAA2B,GAAG,KAAK;IACvC,IAAIzB,YAAoC,GAAG,IAAI;IAC/C,IAAImB,sBAA8C,GAAG,IAAI;IACzD,IAAI5F,eAAe,EAAE;MACnBuC,KAAK,GAAGwC,MAAM,IAAIjD,KAAK,CAACvB,KAAK,CAACqE,EAAE,GAAGG,MAAM,CAACjD,KAAK,CAACvB,KAAK,CAACqE,EAAE,CAAC,GAAGpD,SAAS;MACrEiD,YAAY,GAAG3C,KAAK,CAACvB,KAAK,CAACkE,YAAY,IAAIhB,mBAAmB;MAE9D,IAAI+B,cAAc,EAAE;QAClB,IAAIC,aAAa,GAAG,CAAC,IAAIQ,KAAK,KAAK,CAAC,EAAE;UACpCxF,WAAW,CACT,gBAAgB,EAChB,KAAK,EACL,0EACF,CAAC;UACDyF,2BAA2B,GAAG,IAAI;UAClCN,sBAAsB,GAAG,IAAI;QAC/B,CAAC,MAAM,IAAIH,aAAa,KAAKQ,KAAK,EAAE;UAClCC,2BAA2B,GAAG,IAAI;UAClCN,sBAAsB,GAAG9D,KAAK,CAACvB,KAAK,CAACqF,sBAAsB,IAAI,IAAI;QACrE;MACF;IACF;IAEA,IAAIvK,OAAO,GAAG4E,aAAa,CAACkG,MAAM,CAACxE,eAAe,CAACP,KAAK,CAAC,CAAC,EAAE6E,KAAK,GAAG,CAAC,CAAC,CAAC;IACvE,IAAIG,WAAW,GAAGA,CAAA,KAAM;MACtB,IAAIhC,QAAyB;MAC7B,IAAI7B,KAAK,EAAE;QACT6B,QAAQ,GAAGK,YAAY;OACxB,MAAM,IAAIyB,2BAA2B,EAAE;QACtC9B,QAAQ,GAAGwB,sBAAsB;MACnC,CAAC,MAAM,IAAI9D,KAAK,CAACvB,KAAK,CAACkB,SAAS,EAAE;QAChC;QACA;QACA;QACA;QACA;QACA;QACA2C,QAAQ,gBAAG3J,KAAA,CAAA2E,aAAA,CAAC0C,KAAK,CAACvB,KAAK,CAACkB,SAAS,MAAE,CAAC;MACtC,CAAC,MAAM,IAAIK,KAAK,CAACvB,KAAK,CAACgB,OAAO,EAAE;QAC9B6C,QAAQ,GAAGtC,KAAK,CAACvB,KAAK,CAACgB,OAAO;MAChC,CAAC,MAAM;QACL6C,QAAQ,GAAGhJ,MAAM;MACnB;MACA,oBACEX,KAAA,CAAA2E,aAAA,CAACkF,aAAa;QACZxC,KAAK,EAAEA,KAAM;QACbqC,YAAY,EAAE;UACZ/I,MAAM;UACNC,OAAO;UACPC,WAAW,EAAE0E,eAAe,IAAI;SAChC;QACFoE,QAAQ,EAAEA;MAAS,CACpB,CAAC;KAEL;IACD;IACA;IACA;IACA,OAAOpE,eAAe,KACnB8B,KAAK,CAACvB,KAAK,CAACmE,aAAa,IAAI5C,KAAK,CAACvB,KAAK,CAACkE,YAAY,IAAIwB,KAAK,KAAK,CAAC,CAAC,gBACtExL,KAAA,CAAA2E,aAAA,CAACsE,mBAAmB;MAClBjH,QAAQ,EAAEuD,eAAe,CAACvD,QAAS;MACnCoH,YAAY,EAAE7D,eAAe,CAAC6D,YAAa;MAC3CQ,SAAS,EAAEI,YAAa;MACxBlC,KAAK,EAAEA,KAAM;MACb6B,QAAQ,EAAEgC,WAAW,EAAG;MACxBjC,YAAY,EAAE;QAAE/I,MAAM,EAAE,IAAI;QAAEC,OAAO;QAAEC,WAAW,EAAE;MAAK;IAAE,CAC5D,CAAC,GAEF8K,WAAW,EACZ;GACF,EAAE,IAAiC,CAAC;AACvC;AAAC,IAEIC,cAAc,0BAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAA,OAAdA,cAAc;AAAA,EAAdA,cAAc;AAAA,IAMdC,mBAAmB,0BAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAnBA,mBAAmB;EAAA,OAAnBA,mBAAmB;AAAA,EAAnBA,mBAAmB;AAaxB,SAASC,yBAAyBA,CAChCC,QAA8C,EAC9C;EACA,OAAUA,QAAQ;AACpB;AAEA,SAASC,oBAAoBA,CAACD,QAAwB,EAAE;EACtD,IAAIE,GAAG,GAAGjM,KAAK,CAACuB,UAAU,CAACxB,iBAAiB,CAAC;EAC7C,CAAUkM,GAAG,GAAA/L,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAb,eAAAgB,gBAAS,QAAM0K,yBAAyB,CAACC,QAAQ,CAAC,IAAlD3K,gBAAS;EACT,OAAO6K,GAAG;AACZ;AAEA,SAASC,kBAAkBA,CAACH,QAA6B,EAAE;EACzD,IAAIzH,KAAK,GAAGtE,KAAK,CAACuB,UAAU,CAACjB,sBAAsB,CAAC;EACpD,CAAUgE,KAAK,GAAApE,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAf,eAAAgB,gBAAS,QAAQ0K,yBAAyB,CAACC,QAAQ,CAAC,IAApD3K,gBAAS;EACT,OAAOkD,KAAK;AACd;AAEA,SAAS6H,eAAeA,CAACJ,QAA6B,EAAE;EACtD,IAAIjG,KAAK,GAAG9F,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC;EAC1C,CAAUoF,KAAK,GAAA5F,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAf,eAAAgB,gBAAS,QAAQ0K,yBAAyB,CAACC,QAAQ,CAAC,IAApD3K,gBAAS;EACT,OAAO0E,KAAK;AACd;;AAEA;AACA,SAASsG,iBAAiBA,CAACL,QAA6B,EAAE;EACxD,IAAIjG,KAAK,GAAGqG,eAAe,CAACJ,QAAQ,CAAC;EACrC,IAAIM,SAAS,GAAGvG,KAAK,CAAClF,OAAO,CAACkF,KAAK,CAAClF,OAAO,CAACoE,MAAM,GAAG,CAAC,CAAC;EACvD,CACEqH,SAAS,CAACvG,KAAK,CAACqE,EAAE,GAAAjK,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADpB,eAAAgB,gBAAS,CAEJ,OAAA2K,QAAQ,iEAFb3K,gBAAS;EAIT,OAAOiL,SAAS,CAACvG,KAAK,CAACqE,EAAE;AAC3B;;AAEA;AACA;AACA;AACO,SAASmC,UAAUA,CAAA,EAAG;EAC3B,OAAOF,iBAAiB,CAACP,mBAAmB,CAACU,UAAU,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACO,SAASC,aAAaA,CAAA,EAAG;EAC9B,IAAIlI,KAAK,GAAG4H,kBAAkB,CAACL,mBAAmB,CAACY,aAAa,CAAC;EACjE,OAAOnI,KAAK,CAACoI,UAAU;AACzB;;AAEA;AACA;AACA;AACA;AACO,SAASC,cAAcA,CAAA,EAAG;EAC/B,IAAI1J,iBAAiB,GAAG+I,oBAAoB,CAACJ,cAAc,CAACgB,cAAc,CAAC;EAC3E,IAAItI,KAAK,GAAG4H,kBAAkB,CAACL,mBAAmB,CAACe,cAAc,CAAC;EAClE,OAAO5M,KAAK,CAACqC,OAAO,CAClB,OAAO;IACLwK,UAAU,EAAE5J,iBAAiB,CAAC6J,MAAM,CAACD,UAAU;IAC/CvI,KAAK,EAAEA,KAAK,CAAC8E;EACf,CAAC,CAAC,EACF,CAACnG,iBAAiB,CAAC6J,MAAM,CAACD,UAAU,EAAEvI,KAAK,CAAC8E,YAAY,CAC1D,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACO,SAAS2D,UAAUA,CAAA,EAAc;EACtC,IAAI;IAAEnM,OAAO;IAAEwK;EAAW,CAAC,GAAGc,kBAAkB,CAC9CL,mBAAmB,CAACmB,UACtB,CAAC;EACD,OAAOhN,KAAK,CAACqC,OAAO,CAClB,MAAMzB,OAAO,CAACwG,GAAG,CAAEuD,CAAC,IAAKsC,iCAA0B,CAACtC,CAAC,EAAES,UAAU,CAAC,CAAC,EACnE,CAACxK,OAAO,EAAEwK,UAAU,CACtB,CAAC;AACH;;AAEA;AACA;AACA;AACO,SAAS8B,aAAaA,CAAA,EAAY;EACvC,IAAI5I,KAAK,GAAG4H,kBAAkB,CAACL,mBAAmB,CAACsB,aAAa,CAAC;EACjE,IAAIC,OAAO,GAAGhB,iBAAiB,CAACP,mBAAmB,CAACsB,aAAa,CAAC;EAElE,IAAI7I,KAAK,CAACgG,MAAM,IAAIhG,KAAK,CAACgG,MAAM,CAAC8C,OAAO,CAAC,IAAI,IAAI,EAAE;IACjDxE,OAAO,CAACd,KAAK,CACkD,6DAAAsF,OAAO,MACtE,CAAC;IACD,OAAOrG,SAAS;EAClB;EACA,OAAOzC,KAAK,CAAC8G,UAAU,CAACgC,OAAO,CAAC;AAClC;;AAEA;AACA;AACA;AACO,SAASC,kBAAkBA,CAACD,OAAe,EAAW;EAC3D,IAAI9I,KAAK,GAAG4H,kBAAkB,CAACL,mBAAmB,CAACyB,kBAAkB,CAAC;EACtE,OAAOhJ,KAAK,CAAC8G,UAAU,CAACgC,OAAO,CAAC;AAClC;;AAEA;AACA;AACA;AACO,SAASG,aAAaA,CAAA,EAAY;EACvC,IAAIjJ,KAAK,GAAG4H,kBAAkB,CAACL,mBAAmB,CAAC2B,aAAa,CAAC;EACjE,IAAIJ,OAAO,GAAGhB,iBAAiB,CAACP,mBAAmB,CAACsB,aAAa,CAAC;EAClE,OAAO7I,KAAK,CAACmJ,UAAU,GAAGnJ,KAAK,CAACmJ,UAAU,CAACL,OAAO,CAAC,GAAGrG,SAAS;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASgB,aAAaA,CAAA,EAAY;EAAA,IAAA2F,aAAA;EACvC,IAAI5F,KAAK,GAAG9H,KAAK,CAACuB,UAAU,CAACT,iBAAiB,CAAC;EAC/C,IAAIwD,KAAK,GAAG4H,kBAAkB,CAACL,mBAAmB,CAAC8B,aAAa,CAAC;EACjE,IAAIP,OAAO,GAAGhB,iBAAiB,CAACP,mBAAmB,CAAC8B,aAAa,CAAC;;EAElE;EACA;EACA,IAAI7F,KAAK,KAAKf,SAAS,EAAE;IACvB,OAAOe,KAAK;EACd;;EAEA;EACA,QAAA4F,aAAA,GAAOpJ,KAAK,CAACgG,MAAM,KAAZ,gBAAAoD,aAAA,CAAeN,OAAO,CAAC;AAChC;;AAEA;AACA;AACA;AACO,SAASQ,aAAaA,CAAA,EAAY;EACvC,IAAI/I,KAAK,GAAG7E,KAAK,CAACuB,UAAU,CAAChB,YAAY,CAAC;EAC1C,OAAOsE,KAAK,oBAALA,KAAK,CAAEgJ,KAAK;AACrB;;AAEA;AACA;AACA;AACO,SAASC,aAAaA,CAAA,EAAY;EACvC,IAAIjJ,KAAK,GAAG7E,KAAK,CAACuB,UAAU,CAAChB,YAAY,CAAC;EAC1C,OAAOsE,KAAK,oBAALA,KAAK,CAAEkJ,MAAM;AACtB;AAEA,IAAIC,SAAS,GAAG,CAAC;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,UAAUA,CAACC,WAAsC,EAAW;EAC1E,IAAI;IAAEpB,MAAM;IAAEzL;EAAS,CAAC,GAAG2K,oBAAoB,CAACJ,cAAc,CAACuC,UAAU,CAAC;EAC1E,IAAI7J,KAAK,GAAG4H,kBAAkB,CAACL,mBAAmB,CAACsC,UAAU,CAAC;EAE9D,IAAI,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrO,KAAK,CAACsO,QAAQ,CAAC,EAAE,CAAC;EACpD,IAAIC,eAAe,GAAGvO,KAAK,CAAC6D,WAAW,CACpC2K,GAAG,IAAK;IACP,IAAI,OAAON,WAAW,KAAK,UAAU,EAAE;MACrC,OAAO,CAAC,CAACA,WAAW;IACtB;IACA,IAAI7M,QAAQ,KAAK,GAAG,EAAE;MACpB,OAAO6M,WAAW,CAACM,GAAG,CAAC;IACzB;;IAEA;IACA;IACA;IACA,IAAI;MAAEC,eAAe;MAAEC,YAAY;MAAEC;IAAc,CAAC,GAAGH,GAAG;IAC1D,OAAON,WAAW,CAAC;MACjBO,eAAe,EAAAhH,QAAA,KACVgH,eAAe;QAClBhN,QAAQ,EACNmN,aAAa,CAACH,eAAe,CAAChN,QAAQ,EAAEJ,QAAQ,CAAC,IACjDoN,eAAe,CAAChN;OACnB;MACDiN,YAAY,EAAAjH,QAAA,KACPiH,YAAY;QACfjN,QAAQ,EACNmN,aAAa,CAACF,YAAY,CAACjN,QAAQ,EAAEJ,QAAQ,CAAC,IAC9CqN,YAAY,CAACjN;OAChB;MACDkN;IACF,CAAC,CAAC;EACJ,CAAC,EACD,CAACtN,QAAQ,EAAE6M,WAAW,CACxB,CAAC;;EAED;EACA;EACAlO,KAAK,CAAC6O,SAAS,CAAC,MAAM;IACpB,IAAInH,GAAG,GAAGoH,MAAM,CAAC,EAAEd,SAAS,CAAC;IAC7BK,aAAa,CAAC3G,GAAG,CAAC;IAClB,OAAO,MAAMoF,MAAM,CAACiC,aAAa,CAACrH,GAAG,CAAC;EACxC,CAAC,EAAE,CAACoF,MAAM,CAAC,CAAC;;EAEZ;EACA;EACA;EACA;EACA9M,KAAK,CAAC6O,SAAS,CAAC,MAAM;IACpB,IAAIT,UAAU,KAAK,EAAE,EAAE;MACrBtB,MAAM,CAACkC,UAAU,CAACZ,UAAU,EAAEG,eAAe,CAAC;IAChD;GACD,EAAE,CAACzB,MAAM,EAAEsB,UAAU,EAAEG,eAAe,CAAC,CAAC;;EAEzC;EACA;EACA,OAAOH,UAAU,IAAI9J,KAAK,CAAC2K,QAAQ,CAACC,GAAG,CAACd,UAAU,CAAC,GAC/C9J,KAAK,CAAC2K,QAAQ,CAACE,GAAG,CAACf,UAAU,CAAC,GAC9BgB,YAAY;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASrM,iBAAiBA,CAAA,EAAqB;EAC7C,IAAI;IAAE+J;EAAO,CAAC,GAAGd,oBAAoB,CAACJ,cAAc,CAACyD,iBAAiB,CAAC;EACvE,IAAIlF,EAAE,GAAGiC,iBAAiB,CAACP,mBAAmB,CAACwD,iBAAiB,CAAC;EAEjE,IAAI5L,SAAS,GAAGzD,KAAK,CAAC0D,MAAM,CAAC,KAAK,CAAC;EACnCjB,yBAAyB,CAAC,MAAM;IAC9BgB,SAAS,CAACE,OAAO,GAAG,IAAI;EAC1B,CAAC,CAAC;EAEF,IAAIC,QAA0B,GAAG5D,KAAK,CAAC6D,WAAW,CAChD,UAAC7C,EAAe,EAAE8C,OAAwB,EAAU;IAAA,IAAlCA,OAAwB;MAAxBA,OAAwB,GAAG,EAAE;IAAA;IAC7C5D,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA2D,cAAO,CAACN,SAAS,CAACE,OAAO,EAAEnB,qBAAqB,CAAC;;IAEjD;IACA;IACA,IAAI,CAACiB,SAAS,CAACE,OAAO,EAAE;IAExB,IAAI,OAAO3C,EAAE,KAAK,QAAQ,EAAE;MAC1B8L,MAAM,CAAClJ,QAAQ,CAAC5C,EAAE,CAAC;IACrB,CAAC,MAAM;MACL8L,MAAM,CAAClJ,QAAQ,CAAC5C,EAAE,EAAAyG,QAAA;QAAI6H,WAAW,EAAEnF;OAAO,EAAArG,OAAO,CAAE,CAAC;IACtD;EACF,CAAC,EACD,CAACgJ,MAAM,EAAE3C,EAAE,CACb,CAAC;EAED,OAAOvG,QAAQ;AACjB;AAEA,MAAM2L,eAAsC,GAAG,EAAE;AAEjD,SAASvJ,WAAWA,CAAC0B,GAAW,EAAE8H,IAAa,EAAExH,OAAe,EAAE;EAChE,IAAI,CAACwH,IAAI,IAAI,CAACD,eAAa,CAAC7H,GAAG,CAAC,EAAE;IAChC6H,eAAa,CAAC7H,GAAG,CAAC,GAAG,IAAI;IACzBxH,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA2D,cAAO,CAAC,KAAK,EAAEiE,OAAO,CAAC;EACzB;AACF;AC9lCA,MAAMyH,aAAyC,GAAG,EAAE;AAE7C,SAASC,QAAQA,CAAChI,GAAW,EAAEM,OAAe,EAAQ;EAC3D,IAAI9H,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAW,iBAACqP,aAAa,CAACzH,OAAO,CAAC,EAAE;IACtCyH,aAAa,CAACzH,OAAO,CAAC,GAAG,IAAI;IAC7BY,OAAO,CAAC+G,IAAI,CAAC3H,OAAO,CAAC;EACvB;AACF;AAEA,MAAM4H,cAAc,GAAGA,CAACC,IAAY,EAAEC,GAAW,EAAEC,IAAY,KAC7DL,QAAQ,CACNG,IAAI,EACJ,oDAAwCC,GAAG,iCACpBD,IAAI,GAAkC,qEAC9BE,IAAI,OACrC,CAAC;AAEI,SAASC,wBAAwBA,CACtCC,YAAqD,EACrDC,YAA6D,EAC7D;EACA,IAAI,CAAAD,YAAY,IAAZ,gBAAAA,YAAY,CAAEE,kBAAkB,MAAKpJ,SAAS,EAAE;IAClD6I,cAAc,CACZ,oBAAoB,EACpB,iFAAiF,EACjF,gEACF,CAAC;EACH;EAEA,IACE,CAAAK,YAAY,oBAAZA,YAAY,CAAEzM,oBAAoB,MAAKuD,SAAS,KAC/C,CAACmJ,YAAY,IAAIA,YAAY,CAAC1M,oBAAoB,KAAKuD,SAAS,CAAC,EAClE;IACA6I,cAAc,CACZ,sBAAsB,EACtB,iEAAiE,EACjE,kEACF,CAAC;EACH;EAEA,IAAIM,YAAY,EAAE;IAChB,IAAIA,YAAY,CAACE,iBAAiB,KAAKrJ,SAAS,EAAE;MAChD6I,cAAc,CACZ,mBAAmB,EACnB,wDAAwD,EACxD,+DACF,CAAC;IACH;IAEA,IAAIM,YAAY,CAACG,sBAAsB,KAAKtJ,SAAS,EAAE;MACrD6I,cAAc,CACZ,wBAAwB,EACxB,sEAAsE,EACtE,oEACF,CAAC;IACH;IAEA,IAAIM,YAAY,CAAC3F,mBAAmB,KAAKxD,SAAS,EAAE;MAClD6I,cAAc,CACZ,qBAAqB,EACrB,uDAAuD,EACvD,iEACF,CAAC;IACH;IAEA,IAAIM,YAAY,CAACI,8BAA8B,KAAKvJ,SAAS,EAAE;MAC7D6I,cAAc,CACZ,gCAAgC,EAChC,8EAA8E,EAC9E,4EACF,CAAC;IACH;EACF;AACF;;ACVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,gBAAgB,GAAG,iBAAiB;AAC1C,MAAMC,mBAAmB,GAAGxQ,KAAK,CAACuQ,gBAAgB,CAAC;;AAEnD;AACA;AACA;AACO,SAASE,cAAcA,CAAA3G,IAAA,EAIc;EAAA,IAJb;IAC7B4G,eAAe;IACf5D,MAAM;IACN5J;EACmB,CAAC,GAAA4G,IAAA;EACpB,IAAI,CAACxF,KAAK,EAAEqM,YAAY,CAAC,GAAG3Q,KAAK,CAACsO,QAAQ,CAACxB,MAAM,CAACxI,KAAK,CAAC;EACxD,IAAI;IAAE6L;EAAmB,CAAC,GAAGjN,MAAM,IAAI,EAAE;EAEzC,IAAI0N,QAAQ,GAAG5Q,KAAK,CAAC6D,WAAW,CAC7BgN,QAAqB,IAAK;IACzB,IAAIV,kBAAkB,IAAIK,mBAAmB,EAAE;MAC7CA,mBAAmB,CAAC,MAAMG,YAAY,CAACE,QAAQ,CAAC,CAAC;IACnD,CAAC,MAAM;MACLF,YAAY,CAACE,QAAQ,CAAC;IACxB;EACF,CAAC,EACD,CAACF,YAAY,EAAER,kBAAkB,CACnC,CAAC;;EAED;EACA;EACAnQ,KAAK,CAAC6C,eAAe,CAAC,MAAMiK,MAAM,CAACgE,SAAS,CAACF,QAAQ,CAAC,EAAE,CAAC9D,MAAM,EAAE8D,QAAQ,CAAC,CAAC;EAE3E5Q,KAAK,CAAC6O,SAAS,CAAC,MAAM;IACpB3O,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA2D,cAAO,CACL2M,eAAe,IAAI,IAAI,IAAI,CAAC5D,MAAM,CAAC5J,MAAM,CAACqH,mBAAmB,EAC7D,8DAA8D,GAC5D,kEACJ,CAAC;IACD;IACA;GACD,EAAE,EAAE,CAAC;EAEN,IAAIjJ,SAAS,GAAGtB,KAAK,CAACqC,OAAO,CAAC,MAAiB;IAC7C,OAAO;MACLP,UAAU,EAAEgL,MAAM,CAAChL,UAAU;MAC7B0F,cAAc,EAAEsF,MAAM,CAACtF,cAAc;MACrCxD,EAAE,EAAG+M,CAAC,IAAKjE,MAAM,CAAClJ,QAAQ,CAACmN,CAAC,CAAC;MAC7B1M,IAAI,EAAEA,CAACrD,EAAE,EAAEsD,KAAK,EAAE0M,IAAI,KACpBlE,MAAM,CAAClJ,QAAQ,CAAC5C,EAAE,EAAE;QAClBsD,KAAK;QACL2M,kBAAkB,EAAED,IAAI,IAAJ,gBAAAA,IAAI,CAAEC;MAC5B,CAAC,CAAC;MACJ7M,OAAO,EAAEA,CAACpD,EAAE,EAAEsD,KAAK,EAAE0M,IAAI,KACvBlE,MAAM,CAAClJ,QAAQ,CAAC5C,EAAE,EAAE;QAClBoD,OAAO,EAAE,IAAI;QACbE,KAAK;QACL2M,kBAAkB,EAAED,IAAI,IAAJ,gBAAAA,IAAI,CAAEC;OAC3B;KACJ;EACH,CAAC,EAAE,CAACnE,MAAM,CAAC,CAAC;EAEZ,IAAIzL,QAAQ,GAAGyL,MAAM,CAACzL,QAAQ,IAAI,GAAG;EAErC,IAAI4B,iBAAiB,GAAGjD,KAAK,CAACqC,OAAO,CACnC,OAAO;IACLyK,MAAM;IACNxL,SAAS;IACTsB,MAAM,EAAE,KAAK;IACbvB;GACD,CAAC,EACF,CAACyL,MAAM,EAAExL,SAAS,EAAED,QAAQ,CAC9B,CAAC;EAEDrB,KAAK,CAAC6O,SAAS,CACb,MAAMmB,wBAAwB,CAAC9M,MAAM,EAAE4J,MAAM,CAAC5J,MAAM,CAAC,EACrD,CAAC4J,MAAM,EAAE5J,MAAM,CACjB,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA,oBACElD,KAAA,CAAA2E,aAAA,CAAA3E,KAAA,CAAA6I,QAAA,EACE,mBAAA7I,KAAA,CAAA2E,aAAA,CAAC5E,iBAAiB,CAAC6E,QAAQ;IAACC,KAAK,EAAE5B;EAAkB,gBACnDjD,KAAA,CAAA2E,aAAA,CAACrE,sBAAsB,CAACsE,QAAQ;IAACC,KAAK,EAAEP;EAAM,gBAC5CtE,KAAA,CAAA2E,aAAA,CAACuM,MAAM;IACL7P,QAAQ,EAAEA,QAAS;IACnBW,QAAQ,EAAEsC,KAAK,CAACtC,QAAS;IACzBE,cAAc,EAAEoC,KAAK,CAACqK,aAAc;IACpCrN,SAAS,EAAEA,SAAU;IACrB4B,MAAM,EAAE;MACNM,oBAAoB,EAAEsJ,MAAM,CAAC5J,MAAM,CAACM;IACtC;EAAE,GAEDc,KAAK,CAACkG,WAAW,IAAIsC,MAAM,CAAC5J,MAAM,CAACqH,mBAAmB,gBACrDvK,KAAA,CAAA2E,aAAA,CAACwM,UAAU;IACT/L,MAAM,EAAE0H,MAAM,CAAC1H,MAAO;IACtBlC,MAAM,EAAE4J,MAAM,CAAC5J,MAAO;IACtBoB,KAAK,EAAEA;GACR,CAAC,GAEFoM,eAEI,CACuB,CACP,CAAC,EAC5B,IACD,CAAC;AAEP;AAEA,SAASS,UAAUA,CAAAC,KAAA,EAQW;EAAA,IARV;IAClBhM,MAAM;IACNlC,MAAM;IACNoB;EAKF,CAAC,GAAA8M,KAAA;EACC,OAAO9L,aAAa,CAACF,MAAM,EAAE2B,SAAS,EAAEzC,KAAK,EAAEpB,MAAM,CAAC;AACxD;AAUA;AACA;AACA;AACA;AACA;AACO,SAASmO,YAAYA,CAAAC,KAAA,EAMc;EAAA,IANb;IAC3BjQ,QAAQ;IACRsI,QAAQ;IACR4H,cAAc;IACdC,YAAY;IACZtO;EACiB,CAAC,GAAAoO,KAAA;EAClB,IAAIG,UAAU,GAAGzR,KAAK,CAAC0D,MAAM,EAAiB;EAC9C,IAAI+N,UAAU,CAAC9N,OAAO,IAAI,IAAI,EAAE;IAC9B8N,UAAU,CAAC9N,OAAO,GAAG+N,mBAAmB,CAAC;MACvCH,cAAc;MACdC,YAAY;MACZG,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;EAEA,IAAIC,OAAO,GAAGH,UAAU,CAAC9N,OAAO;EAChC,IAAI,CAACW,KAAK,EAAEqM,YAAY,CAAC,GAAG3Q,KAAK,CAACsO,QAAQ,CAAC;IACzCuD,MAAM,EAAED,OAAO,CAACC,MAAM;IACtB7P,QAAQ,EAAE4P,OAAO,CAAC5P;EACpB,CAAC,CAAC;EACF,IAAI;IAAEmO;EAAmB,CAAC,GAAGjN,MAAM,IAAI,EAAE;EACzC,IAAI0N,QAAQ,GAAG5Q,KAAK,CAAC6D,WAAW,CAC7BgN,QAAwD,IAAK;IAC5DV,kBAAkB,IAAIK,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMG,YAAY,CAACE,QAAQ,CAAC,CAAC,GACjDF,YAAY,CAACE,QAAQ,CAAC;EAC5B,CAAC,EACD,CAACF,YAAY,EAAER,kBAAkB,CACnC,CAAC;EAEDnQ,KAAK,CAAC6C,eAAe,CAAC,MAAM+O,OAAO,CAACE,MAAM,CAAClB,QAAQ,CAAC,EAAE,CAACgB,OAAO,EAAEhB,QAAQ,CAAC,CAAC;EAE1E5Q,KAAK,CAAC6O,SAAS,CAAC,MAAMmB,wBAAwB,CAAC9M,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAEjE,oBACElD,KAAA,CAAA2E,aAAA,CAACuM,MAAM;IACL7P,QAAQ,EAAEA,QAAS;IACnBsI,QAAQ,EAAEA,QAAS;IACnB3H,QAAQ,EAAEsC,KAAK,CAACtC,QAAS;IACzBE,cAAc,EAAEoC,KAAK,CAACuN,MAAO;IAC7BvQ,SAAS,EAAEsQ,OAAQ;IACnB1O,MAAM,EAAEA;EAAO,CAChB,CAAC;AAEN;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS6O,QAAQA,CAAAC,KAAA,EAKA;EAAA,IALC;IACvBhR,EAAE;IACFoD,OAAO;IACPE,KAAK;IACLpD;EACa,CAAC,GAAA8Q,KAAA;EACd,CACE7Q,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADtB,eAAAgB,gBAAS,CAEP;EAAA;EACA;EAAA,yEAHFA,gBAAS;EAOT,IAAI;IAAE8B,MAAM;IAAEN,MAAM,EAAED;EAAS,CAAC,GAAG3C,KAAK,CAACuB,UAAU,CAACf,iBAAiB,CAAC;EAEtEN,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA2D,cAAO,CACL,CAACpB,QAAQ,EACT,yEAC0E,wJAE5E,CAAC;EAED,IAAI;IAAE/B;EAAQ,CAAC,GAAGZ,KAAK,CAACuB,UAAU,CAACb,YAAY,CAAC;EAChD,IAAI;IAAEe,QAAQ,EAAE0B;GAAkB,GAAGpB,WAAW,EAAE;EAClD,IAAI6B,QAAQ,GAAGd,WAAW,EAAE;;EAE5B;EACA;EACA,IAAImB,IAAI,GAAGC,SAAS,CAClBlD,EAAE,EACFuC,0BAAmB,CAAC3C,OAAO,EAAEsC,MAAM,CAACM,oBAAoB,CAAC,EACzDL,gBAAgB,EAChBjC,QAAQ,KAAK,MACf,CAAC;EACD,IAAI+Q,QAAQ,GAAG5O,IAAI,CAACC,SAAS,CAACW,IAAI,CAAC;EAEnCjE,KAAK,CAAC6O,SAAS,CACb,MAAMjL,QAAQ,CAACP,IAAI,CAACc,KAAK,CAAC8N,QAAQ,CAAC,EAAE;IAAE7N,OAAO;IAAEE,KAAK;IAAEpD;EAAS,CAAC,CAAC,EAClE,CAAC0C,QAAQ,EAAEqO,QAAQ,EAAE/Q,QAAQ,EAAEkD,OAAO,EAAEE,KAAK,CAC/C,CAAC;EAED,OAAO,IAAI;AACb;AAMA;AACA;AACA;AACA;AACA;AACO,SAAS4N,MAAMA,CAAC/I,KAAkB,EAA6B;EACpE,OAAO1E,SAAS,CAAC0E,KAAK,CAACzE,OAAO,CAAC;AACjC;AA8CA;AACA;AACA;AACA;AACA;AACO,SAASyN,KAAKA,CAACC,MAAkB,EAA6B;EAE5DlS,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBADPgB,gBAAS,QAEP,sEACoE,yEAHtEA,gBAAS;AAKX;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS8P,MAAMA,CAAAmB,KAAA,EAQqB;EAAA,IARpB;IACrBhR,QAAQ,EAAEiR,YAAY,GAAG,GAAG;IAC5B3I,QAAQ,GAAG,IAAI;IACf3H,QAAQ,EAAEuQ,YAAY;IACtBrQ,cAAc,GAAGyF,MAAc,CAACC,GAAG;IACnCtG,SAAS;IACTsB,MAAM,EAAE4P,UAAU,GAAG,KAAK;IAC1BtP;EACW,CAAC,GAAAmP,KAAA;EACZ,CACE,CAAClR,kBAAkB,EAAE,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADvB,eAAAgB,gBAAS,CAEP,oHACqD,IAHvDA,gBAAS;;EAMT;EACA;EACA,IAAIC,QAAQ,GAAGiR,YAAY,CAAClO,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EAChD,IAAIqO,iBAAiB,GAAGzS,KAAK,CAACqC,OAAO,CACnC,OAAO;IACLhB,QAAQ;IACRC,SAAS;IACTsB,MAAM,EAAE4P,UAAU;IAClBtP,MAAM,EAAAuE,QAAA;MACJjE,oBAAoB,EAAE;IAAK,GACxBN,MAAM;GAEZ,CAAC,EACF,CAAC7B,QAAQ,EAAE6B,MAAM,EAAE5B,SAAS,EAAEkR,UAAU,CAC1C,CAAC;EAED,IAAI,OAAOD,YAAY,KAAK,QAAQ,EAAE;IACpCA,YAAY,GAAGlM,SAAS,CAACkM,YAAY,CAAC;EACxC;EAEA,IAAI;IACF9Q,QAAQ,GAAG,GAAG;IACdC,MAAM,GAAG,EAAE;IACXF,IAAI,GAAG,EAAE;IACT8C,KAAK,GAAG,IAAI;IACZoD,GAAG,GAAG;EACR,CAAC,GAAG6K,YAAY;EAEhB,IAAIG,eAAe,GAAG1S,KAAK,CAACqC,OAAO,CAAC,MAAM;IACxC,IAAIsQ,gBAAgB,GAAG/D,aAAa,CAACnN,QAAQ,EAAEJ,QAAQ,CAAC;IAExD,IAAIsR,gBAAgB,IAAI,IAAI,EAAE;MAC5B,OAAO,IAAI;IACb;IAEA,OAAO;MACL3Q,QAAQ,EAAE;QACRP,QAAQ,EAAEkR,gBAAgB;QAC1BjR,MAAM;QACNF,IAAI;QACJ8C,KAAK;QACLoD;OACD;MACDxF;KACD;EACH,CAAC,EAAE,CAACb,QAAQ,EAAEI,QAAQ,EAAEC,MAAM,EAAEF,IAAI,EAAE8C,KAAK,EAAEoD,GAAG,EAAExF,cAAc,CAAC,CAAC;EAElEhC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA2D,cAAO,CACL2O,eAAe,IAAI,IAAI,EACvB,qBAAqB,GAAArR,QAAQ,iDACvBI,QAAQ,GAAGC,MAAM,GAAGF,IAAI,GAAuC,8FAEvE,CAAC;EAED,IAAIkR,eAAe,IAAI,IAAI,EAAE;IAC3B,OAAO,IAAI;EACb;EAEA,oBACE1S,KAAA,CAAA2E,aAAA,CAACnE,iBAAiB,CAACoE,QAAQ;IAACC,KAAK,EAAE4N;EAAkB,gBACnDzS,KAAA,CAAA2E,aAAA,CAAClE,eAAe,CAACmE,QAAQ;IAAC+E,QAAQ,EAAEA,QAAS;IAAC9E,KAAK,EAAE6N;EAAgB,CAAE,CAC7C,CAAC;AAEjC;AAOA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,MAAMA,CAAAC,KAAA,EAGqB;EAAA,IAHpB;IACrBlJ,QAAQ;IACR3H;EACW,CAAC,GAAA6Q,KAAA;EACZ,OAAO1N,SAAS,CAAC2N,wBAAwB,CAACnJ,QAAQ,CAAC,EAAE3H,QAAQ,CAAC;AAChE;AAYA;AACA;AACA;AACA;AACO,SAAS+Q,KAAKA,CAAAC,KAAA,EAAkD;EAAA,IAAjD;IAAErJ,QAAQ;IAAEK,YAAY;IAAEiJ;EAAoB,CAAC,GAAAD,KAAA;EACnE,oBACEhT,KAAA,CAAA2E,aAAA,CAACuO,kBAAkB;IAACD,OAAO,EAAEA,OAAQ;IAACjJ,YAAY,EAAEA;GAClD,eAAAhK,KAAA,CAAA2E,aAAA,CAACwO,YAAY,EAAE,MAAAxJ,QAAuB,CACpB,CAAC;AAEzB;AAAC,IAWIyJ,iBAAiB,0BAAjBA,iBAAiB;EAAjBA,iBAAiB,CAAjBA,iBAAiB;EAAjBA,iBAAiB,CAAjBA,iBAAiB;EAAjBA,iBAAiB,CAAjBA,iBAAiB;EAAA,OAAjBA,iBAAiB;AAAA,EAAjBA,iBAAiB;AAMtB,MAAMC,mBAAmB,GAAG,IAAIC,OAAO,CAAC,MAAM,EAAE,CAAC;AAEjD,MAAMJ,kBAAkB,SAASlT,KAAK,CAACgH,SAAS,CAG9C;EACAkC,WAAWA,CAACC,KAA8B,EAAE;IAC1C,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAAC7E,KAAK,GAAG;MAAEwD,KAAK,EAAE;KAAM;EAC9B;EAEA,OAAOuB,wBAAwBA,CAACvB,KAAU,EAAE;IAC1C,OAAO;MAAEA;KAAO;EAClB;EAEAyB,iBAAiBA,CAACzB,KAAU,EAAE0B,SAAc,EAAE;IAC5CZ,OAAO,CAACd,KAAK,CACX,kDAAkD,EAClDA,KAAK,EACL0B,SACF,CAAC;EACH;EAEAC,MAAMA,CAAA,EAAG;IACP,IAAI;MAAEE,QAAQ;MAAEK,YAAY;MAAEiJ;KAAS,GAAG,IAAI,CAAC9J,KAAK;IAEpD,IAAIoK,OAA8B,GAAG,IAAI;IACzC,IAAIrL,MAAyB,GAAGkL,iBAAiB,CAACI,OAAO;IAEzD,IAAI,EAAEP,OAAO,YAAYK,OAAO,CAAC,EAAE;MACjC;MACApL,MAAM,GAAGkL,iBAAiB,CAACK,OAAO;MAClCF,OAAO,GAAGD,OAAO,CAACL,OAAO,EAAE;MAC3B3L,MAAM,CAACoM,cAAc,CAACH,OAAO,EAAE,UAAU,EAAE;QAAEpE,GAAG,EAAEA,CAAA,KAAM;MAAK,CAAC,CAAC;MAC/D7H,MAAM,CAACoM,cAAc,CAACH,OAAO,EAAE,OAAO,EAAE;QAAEpE,GAAG,EAAEA,CAAA,KAAM8D;MAAQ,CAAC,CAAC;IACjE,CAAC,MAAM,IAAI,IAAI,CAAC3O,KAAK,CAACwD,KAAK,EAAE;MAC3B;MACAI,MAAM,GAAGkL,iBAAiB,CAACtL,KAAK;MAChC,IAAI6L,WAAW,GAAG,IAAI,CAACrP,KAAK,CAACwD,KAAK;MAClCyL,OAAO,GAAGD,OAAO,CAACM,MAAM,EAAE,CAACC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;MAC3CvM,MAAM,CAACoM,cAAc,CAACH,OAAO,EAAE,UAAU,EAAE;QAAEpE,GAAG,EAAEA,CAAA,KAAM;MAAK,CAAC,CAAC;MAC/D7H,MAAM,CAACoM,cAAc,CAACH,OAAO,EAAE,QAAQ,EAAE;QAAEpE,GAAG,EAAEA,CAAA,KAAMwE;MAAY,CAAC,CAAC;IACtE,CAAC,MAAM,IAAKV,OAAO,CAAoBa,QAAQ,EAAE;MAC/C;MACAP,OAAO,GAAGN,OAAO;MACjB/K,MAAM,GACJ,QAAQ,IAAIqL,OAAO,GACfH,iBAAiB,CAACtL,KAAK,GACvB,OAAO,IAAIyL,OAAO,GAClBH,iBAAiB,CAACK,OAAO,GACzBL,iBAAiB,CAACI,OAAO;IACjC,CAAC,MAAM;MACL;MACAtL,MAAM,GAAGkL,iBAAiB,CAACI,OAAO;MAClClM,MAAM,CAACoM,cAAc,CAACT,OAAO,EAAE,UAAU,EAAE;QAAE9D,GAAG,EAAEA,CAAA,KAAM;MAAK,CAAC,CAAC;MAC/DoE,OAAO,GAAGN,OAAO,CAACc,IAAI,CACnBC,IAAS,IACR1M,MAAM,CAACoM,cAAc,CAACT,OAAO,EAAE,OAAO,EAAE;QAAE9D,GAAG,EAAEA,CAAA,KAAM6E;OAAM,CAAC,EAC7DlM,KAAU,IACTR,MAAM,CAACoM,cAAc,CAACT,OAAO,EAAE,QAAQ,EAAE;QAAE9D,GAAG,EAAEA,CAAA,KAAMrH;MAAM,CAAC,CACjE,CAAC;IACH;IAEA,IACEI,MAAM,KAAKkL,iBAAiB,CAACtL,KAAK,IAClCyL,OAAO,CAACxF,MAAM,YAAYkG,oBAAoB,EAC9C;MACA;MACA,MAAMZ,mBAAmB;IAC3B;IAEA,IAAInL,MAAM,KAAKkL,iBAAiB,CAACtL,KAAK,IAAI,CAACkC,YAAY,EAAE;MACvD;MACA,MAAMuJ,OAAO,CAACxF,MAAM;IACtB;IAEA,IAAI7F,MAAM,KAAKkL,iBAAiB,CAACtL,KAAK,EAAE;MACtC;MACA,oBAAO9H,KAAA,CAAA2E,aAAA,CAACpE,YAAY,CAACqE,QAAQ;QAACC,KAAK,EAAE0O,OAAQ;QAAC5J,QAAQ,EAAEK;MAAa,CAAE,CAAC;IAC1E;IAEA,IAAI9B,MAAM,KAAKkL,iBAAiB,CAACK,OAAO,EAAE;MACxC;MACA,oBAAOzT,KAAA,CAAA2E,aAAA,CAACpE,YAAY,CAACqE,QAAQ;QAACC,KAAK,EAAE0O,OAAQ;QAAC5J,QAAQ,EAAEA;MAAS,CAAE,CAAC;IACtE;;IAEA;IACA,MAAM4J,OAAO;EACf;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASJ,YAAYA,CAAAe,KAAA,EAIlB;EAAA,IAJmB;IACpBvK;EAGF,CAAC,GAAAuK,KAAA;EACC,IAAIF,IAAI,GAAGpG,aAAa,EAAE;EAC1B,IAAIuG,QAAQ,GAAG,OAAOxK,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACqK,IAAI,CAAC,GAAGrK,QAAQ;EACzE,oBAAO3J,KAAA,CAAA2E,aAAA,CAAA3E,KAAA,CAAA6I,QAAA,EAAG,MAAAsL,QAAW,CAAC;AACxB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASrB,wBAAwBA,CACtCnJ,QAAyB,EACzB5D,UAAoB,EACL;EAAA,IADfA,UAAoB;IAApBA,UAAoB,GAAG,EAAE;EAAA;EAEzB,IAAIX,MAAqB,GAAG,EAAE;EAE9BpF,KAAK,CAACoU,QAAQ,CAACC,OAAO,CAAC1K,QAAQ,EAAE,CAAC7C,OAAO,EAAE0E,KAAK,KAAK;IACnD,IAAI,eAACxL,KAAK,CAACsU,cAAc,CAACxN,OAAO,CAAC,EAAE;MAClC;MACA;MACA;IACF;IAEA,IAAIyN,QAAQ,GAAG,CAAC,GAAGxO,UAAU,EAAEyF,KAAK,CAAC;IAErC,IAAI1E,OAAO,CAAC0N,IAAI,KAAKxU,KAAK,CAAC6I,QAAQ,EAAE;MACnC;MACAzD,MAAM,CAACf,IAAI,CAACoQ,KAAK,CACfrP,MAAM,EACN0N,wBAAwB,CAAChM,OAAO,CAACqC,KAAK,CAACQ,QAAQ,EAAE4K,QAAQ,CAC3D,CAAC;MACD;IACF;IAEA,EACEzN,OAAO,CAAC0N,IAAI,KAAKrC,KAAK,IAAAjS,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBADxBgB,gBAAS,CAGL,qBAAO0F,OAAO,CAAC0N,IAAI,KAAK,QAAQ,GAAG1N,OAAO,CAAC0N,IAAI,GAAG1N,OAAO,CAAC0N,IAAI,CAACE,IAAI,gHAHvEtT,gBAAS;IAOT,EACE,CAAC0F,OAAO,CAACqC,KAAK,CAACqC,KAAK,IAAI,CAAC1E,OAAO,CAACqC,KAAK,CAACQ,QAAQ,IAAAzJ,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBADjDgB,gBAAS,QAEP,0CAA0C,IAF5CA,gBAAS;IAKT,IAAI0E,KAAkB,GAAG;MACvBqE,EAAE,EAAErD,OAAO,CAACqC,KAAK,CAACgB,EAAE,IAAIoK,QAAQ,CAAC3N,IAAI,CAAC,GAAG,CAAC;MAC1C+N,aAAa,EAAE7N,OAAO,CAACqC,KAAK,CAACwL,aAAa;MAC1C7N,OAAO,EAAEA,OAAO,CAACqC,KAAK,CAACrC,OAAO;MAC9BE,SAAS,EAAEF,OAAO,CAACqC,KAAK,CAACnC,SAAS;MAClCwE,KAAK,EAAE1E,OAAO,CAACqC,KAAK,CAACqC,KAAK;MAC1BvH,IAAI,EAAE6C,OAAO,CAACqC,KAAK,CAAClF,IAAI;MACxBqH,MAAM,EAAExE,OAAO,CAACqC,KAAK,CAACmC,MAAM;MAC5BuG,MAAM,EAAE/K,OAAO,CAACqC,KAAK,CAAC0I,MAAM;MAC5B7H,YAAY,EAAElD,OAAO,CAACqC,KAAK,CAACa,YAAY;MACxCC,aAAa,EAAEnD,OAAO,CAACqC,KAAK,CAACc,aAAa;MAC1C2K,gBAAgB,EACd9N,OAAO,CAACqC,KAAK,CAACc,aAAa,IAAI,IAAI,IACnCnD,OAAO,CAACqC,KAAK,CAACa,YAAY,IAAI,IAAI;MACpC6K,gBAAgB,EAAE/N,OAAO,CAACqC,KAAK,CAAC0L,gBAAgB;MAChDC,MAAM,EAAEhO,OAAO,CAACqC,KAAK,CAAC2L,MAAM;MAC5B7N,IAAI,EAAEH,OAAO,CAACqC,KAAK,CAAClC;KACrB;IAED,IAAIH,OAAO,CAACqC,KAAK,CAACQ,QAAQ,EAAE;MAC1B7D,KAAK,CAAC6D,QAAQ,GAAGmJ,wBAAwB,CACvChM,OAAO,CAACqC,KAAK,CAACQ,QAAQ,EACtB4K,QACF,CAAC;IACH;IAEAnP,MAAM,CAACf,IAAI,CAACyB,KAAK,CAAC;EACpB,CAAC,CAAC;EAEF,OAAOV,MAAM;AACf;;AAEA;AACA;AACA;AACO,SAAS2P,aAAaA,CAC3BnU,OAA4B,EACD;EAC3B,OAAOuG,cAAc,CAACvG,OAAO,CAAC;AAChC;ACtfA,SAASoU,kBAAkBA,CAAClP,KAAkB,EAAE;EAC9C,IAAImP,OAA6D,GAAG;IAClE;IACA;IACAL,gBAAgB,EAAE9O,KAAK,CAACmE,aAAa,IAAI,IAAI,IAAInE,KAAK,CAACkE,YAAY,IAAI;GACxE;EAED,IAAIlE,KAAK,CAACkB,SAAS,EAAE;IACnB,IAAA9G,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;MACX,IAAI0F,KAAK,CAACgB,OAAO,EAAE;QACjB5G,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA2D,cAAO,CACL,KAAK,EACL,wEAAwE,GACtE,2BACJ,CAAC;MACH;IACF;IACAuD,MAAM,CAACC,MAAM,CAAC0N,OAAO,EAAE;MACrBnO,OAAO,eAAE9G,KAAK,CAAC2E,aAAa,CAACmB,KAAK,CAACkB,SAAS,CAAC;MAC7CA,SAAS,EAAED;IACb,CAAC,CAAC;EACJ;EAEA,IAAIjB,KAAK,CAACoF,eAAe,EAAE;IACzB,IAAAhL,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;MACX,IAAI0F,KAAK,CAACqF,sBAAsB,EAAE;QAChCjL,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA2D,cAAO,CACL,KAAK,EACL,6FAA6F,GAC3F,iCACJ,CAAC;MACH;IACF;IACAuD,MAAM,CAACC,MAAM,CAAC0N,OAAO,EAAE;MACrB9J,sBAAsB,eAAEnL,KAAK,CAAC2E,aAAa,CAACmB,KAAK,CAACoF,eAAe,CAAC;MAClEA,eAAe,EAAEnE;IACnB,CAAC,CAAC;EACJ;EAEA,IAAIjB,KAAK,CAACmE,aAAa,EAAE;IACvB,IAAA/J,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;MACX,IAAI0F,KAAK,CAACkE,YAAY,EAAE;QACtB9J,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA2D,cAAO,CACL,KAAK,EACL,iFAAiF,GAC/E,+BACJ,CAAC;MACH;IACF;IACAuD,MAAM,CAACC,MAAM,CAAC0N,OAAO,EAAE;MACrBjL,YAAY,eAAEhK,KAAK,CAAC2E,aAAa,CAACmB,KAAK,CAACmE,aAAa,CAAC;MACtDA,aAAa,EAAElD;IACjB,CAAC,CAAC;EACJ;EAEA,OAAOkO,OAAO;AAChB;AAEO,SAASC,kBAAkBA,CAChC9P,MAAqB,EACrB4L,IAQC,EACY;EACb,OAAOmE,YAAY,CAAC;IAClB9T,QAAQ,EAAE2P,IAAI,IAAJ,gBAAAA,IAAI,CAAE3P,QAAQ;IACxB6B,MAAM,EAAAuE,QAAA,KACDuJ,IAAI,IAAJ,gBAAAA,IAAI,CAAE9N,MAAM;MACfkS,kBAAkB,EAAE;KACrB;IACDxD,OAAO,EAAEF,mBAAmB,CAAC;MAC3BH,cAAc,EAAEP,IAAI,IAAJ,gBAAAA,IAAI,CAAEO,cAAc;MACpCC,YAAY,EAAER,IAAI,IAAJ,gBAAAA,IAAI,CAAEQ;IACtB,CAAC,CAAC;IACF6D,aAAa,EAAErE,IAAI,IAAJ,gBAAAA,IAAI,CAAEqE,aAAa;IAClCjQ,MAAM;IACN4P,kBAAkB;IAClBM,YAAY,EAAEtE,IAAI,IAAJ,gBAAAA,IAAI,CAAEsE,YAAY;IAChCC,uBAAuB,EAAEvE,IAAI,IAAJ,gBAAAA,IAAI,CAAEuE;EACjC,CAAC,CAAC,CAACC,UAAU,EAAE;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}