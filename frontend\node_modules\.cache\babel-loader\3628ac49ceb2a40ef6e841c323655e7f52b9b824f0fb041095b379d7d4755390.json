{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route}from'react-router-dom';import{ToastContainer}from'react-toastify';import'react-toastify/dist/ReactToastify.css';import{AuthProvider}from'./context/AuthContext';import ProtectedRoute from'./components/ProtectedRoute';import Navbar from'./components/Navbar';import Dashboard from'./components/Dashboard';import DivisionSetup from'./components/DivisionSetup';import CategoryManagement from'./components/CategoryManagement';import PersonManagement from'./components/PersonManagement';import PersonsView from'./components/PersonsView';import ImportPersons from'./components/import/ImportPersons';import FormBuilder from'./components/forms/FormBuilder';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"App\",children:[/*#__PURE__*/_jsxs(ProtectedRoute,{children:[/*#__PURE__*/_jsx(Navbar,{}),/*#__PURE__*/_jsx(\"div\",{className:\"main-content\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Dashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard\",element:/*#__PURE__*/_jsx(Dashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/divisions\",element:/*#__PURE__*/_jsx(DivisionSetup,{})}),/*#__PURE__*/_jsx(Route,{path:\"/categories\",element:/*#__PURE__*/_jsx(CategoryManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"/persons-view\",element:/*#__PURE__*/_jsx(PersonsView,{})}),/*#__PURE__*/_jsx(Route,{path:\"/persons\",element:/*#__PURE__*/_jsx(PersonManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"/import\",element:/*#__PURE__*/_jsx(ImportPersons,{})}),/*#__PURE__*/_jsx(Route,{path:\"/form-builder\",element:/*#__PURE__*/_jsx(FormBuilder,{})})]})})]}),/*#__PURE__*/_jsx(ToastContainer,{position:\"top-right\",autoClose:3000,hideProgressBar:false,newestOnTop:false,closeOnClick:true,rtl:false,pauseOnFocusLoss:true,draggable:true,pauseOnHover:true,theme:\"light\"})]})})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "ToastContainer", "<PERSON>th<PERSON><PERSON><PERSON>", "ProtectedRoute", "<PERSON><PERSON><PERSON>", "Dashboard", "DivisionSetup", "CategoryManagement", "PersonManagement", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>rt<PERSON><PERSON><PERSON>", "FormBuilder", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "className", "path", "element", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\r\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\r\nimport { ToastContainer } from 'react-toastify';\r\nimport 'react-toastify/dist/ReactToastify.css';\r\nimport { AuthProvider } from './context/AuthContext';\r\nimport ProtectedRoute from './components/ProtectedRoute';\r\nimport Navbar from './components/Navbar';\r\nimport Dashboard from './components/Dashboard';\r\nimport DivisionSetup from './components/DivisionSetup';\r\nimport CategoryManagement from './components/CategoryManagement';\r\nimport PersonManagement from './components/PersonManagement';\r\nimport PersonsView from './components/PersonsView';\r\nimport ImportPersons from './components/import/ImportPersons';\r\nimport FormBuilder from './components/forms/FormBuilder';\r\n\r\n\r\nfunction App() {\r\n  return (\r\n    <AuthProvider>\r\n      <Router>\r\n        <div className=\"App\">\r\n          <ProtectedRoute>\r\n            <Navbar />\r\n            <div className=\"main-content\">\r\n              <Routes>\r\n                <Route path=\"/\" element={<Dashboard />} />\r\n                <Route path=\"/dashboard\" element={<Dashboard />} />\r\n                <Route path=\"/divisions\" element={<DivisionSetup />} />\r\n                <Route path=\"/categories\" element={<CategoryManagement />} />\r\n                <Route path=\"/persons-view\" element={<PersonsView />} />\r\n                <Route path=\"/persons\" element={<PersonManagement />} />\r\n                <Route path=\"/import\" element={<ImportPersons />} />\r\n                <Route path=\"/form-builder\" element={<FormBuilder />} />\r\n              </Routes>\r\n            </div>\r\n          </ProtectedRoute>\r\n          <ToastContainer\r\n            position=\"top-right\"\r\n            autoClose={3000}\r\n            hideProgressBar={false}\r\n            newestOnTop={false}\r\n            closeOnClick\r\n            rtl={false}\r\n            pauseOnFocusLoss\r\n            draggable\r\n            pauseOnHover\r\n            theme=\"light\"\r\n          />\r\n        </div>\r\n      </Router>\r\n    </AuthProvider>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CACzE,OAASC,cAAc,KAAQ,gBAAgB,CAC/C,MAAO,uCAAuC,CAC9C,OAASC,YAAY,KAAQ,uBAAuB,CACpD,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,SAAS,KAAM,wBAAwB,CAC9C,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,CAAAC,kBAAkB,KAAM,iCAAiC,CAChE,MAAO,CAAAC,gBAAgB,KAAM,+BAA+B,CAC5D,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAClD,MAAO,CAAAC,aAAa,KAAM,mCAAmC,CAC7D,MAAO,CAAAC,WAAW,KAAM,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGzD,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAACX,YAAY,EAAAe,QAAA,cACXJ,IAAA,CAACf,MAAM,EAAAmB,QAAA,cACLF,KAAA,QAAKG,SAAS,CAAC,KAAK,CAAAD,QAAA,eAClBF,KAAA,CAACZ,cAAc,EAAAc,QAAA,eACbJ,IAAA,CAACT,MAAM,GAAE,CAAC,cACVS,IAAA,QAAKK,SAAS,CAAC,cAAc,CAAAD,QAAA,cAC3BF,KAAA,CAAChB,MAAM,EAAAkB,QAAA,eACLJ,IAAA,CAACb,KAAK,EAACmB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACR,SAAS,GAAE,CAAE,CAAE,CAAC,cAC1CQ,IAAA,CAACb,KAAK,EAACmB,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEP,IAAA,CAACR,SAAS,GAAE,CAAE,CAAE,CAAC,cACnDQ,IAAA,CAACb,KAAK,EAACmB,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEP,IAAA,CAACP,aAAa,GAAE,CAAE,CAAE,CAAC,cACvDO,IAAA,CAACb,KAAK,EAACmB,IAAI,CAAC,aAAa,CAACC,OAAO,cAAEP,IAAA,CAACN,kBAAkB,GAAE,CAAE,CAAE,CAAC,cAC7DM,IAAA,CAACb,KAAK,EAACmB,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEP,IAAA,CAACJ,WAAW,GAAE,CAAE,CAAE,CAAC,cACxDI,IAAA,CAACb,KAAK,EAACmB,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEP,IAAA,CAACL,gBAAgB,GAAE,CAAE,CAAE,CAAC,cACxDK,IAAA,CAACb,KAAK,EAACmB,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEP,IAAA,CAACH,aAAa,GAAE,CAAE,CAAE,CAAC,cACpDG,IAAA,CAACb,KAAK,EAACmB,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEP,IAAA,CAACF,WAAW,GAAE,CAAE,CAAE,CAAC,EAClD,CAAC,CACN,CAAC,EACQ,CAAC,cACjBE,IAAA,CAACZ,cAAc,EACboB,QAAQ,CAAC,WAAW,CACpBC,SAAS,CAAE,IAAK,CAChBC,eAAe,CAAE,KAAM,CACvBC,WAAW,CAAE,KAAM,CACnBC,YAAY,MACZC,GAAG,CAAE,KAAM,CACXC,gBAAgB,MAChBC,SAAS,MACTC,YAAY,MACZC,KAAK,CAAC,OAAO,CACd,CAAC,EACC,CAAC,CACA,CAAC,CACG,CAAC,CAEnB,CAEA,cAAe,CAAAd,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}