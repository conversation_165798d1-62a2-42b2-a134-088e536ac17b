{"ast": null, "code": "import React from'react';import'./Pagination.css';import{jsxs as _jsxs,jsx as _jsx}from\"react/jsx-runtime\";const Pagination=_ref=>{let{currentPage,totalItems,itemsPerPage,onPageChange,showInfo=true}=_ref;const totalPages=Math.ceil(totalItems/itemsPerPage);const startItem=(currentPage-1)*itemsPerPage+1;const endItem=Math.min(currentPage*itemsPerPage,totalItems);if(totalPages<=1)return null;const getPageNumbers=()=>{const pages=[];const maxVisiblePages=3;// Reduced for single row\nif(totalPages<=maxVisiblePages){for(let i=1;i<=totalPages;i++){pages.push(i);}}else{// Simplified logic for single row\nif(currentPage===1){pages.push(1,2,'...');}else if(currentPage===totalPages){pages.push('...',totalPages-1,totalPages);}else{pages.push('...',currentPage,'...');}}return pages;};return/*#__PURE__*/_jsx(\"div\",{className:\"pagination-wrapper-single\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"pagination-container\",children:[showInfo&&/*#__PURE__*/_jsx(\"div\",{className:\"pagination-info\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"entries-text\",children:[startItem,\"-\",endItem,\" of \",totalItems]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"pagination-controls\",children:[/*#__PURE__*/_jsx(\"button\",{className:`pagination-btn pagination-prev ${currentPage===1?'disabled':''}`,onClick:()=>onPageChange(currentPage-1),disabled:currentPage===1,title:\"Previous page\",children:\"\\u2039\"}),/*#__PURE__*/_jsx(\"div\",{className:\"pagination-pages\",children:getPageNumbers().map((page,index)=>/*#__PURE__*/_jsx(\"button\",{className:`pagination-btn pagination-page ${page===currentPage?'active':''} ${page==='...'?'dots':''}`,onClick:()=>page!=='...'&&onPageChange(page),disabled:page==='...',title:page==='...'?'':`Go to page ${page}`,children:page},index))}),/*#__PURE__*/_jsx(\"button\",{className:`pagination-btn pagination-next ${currentPage===totalPages?'disabled':''}`,onClick:()=>onPageChange(currentPage+1),disabled:currentPage===totalPages,title:\"Next page\",children:\"\\u203A\"})]})]})});};export default Pagination;", "map": {"version": 3, "names": ["React", "jsxs", "_jsxs", "jsx", "_jsx", "Pagination", "_ref", "currentPage", "totalItems", "itemsPerPage", "onPageChange", "showInfo", "totalPages", "Math", "ceil", "startItem", "endItem", "min", "getPageNumbers", "pages", "maxVisiblePages", "i", "push", "className", "children", "onClick", "disabled", "title", "map", "page", "index"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/Pagination.js"], "sourcesContent": ["import React from 'react';\r\nimport './Pagination.css';\r\n\r\nconst Pagination = ({\r\n    currentPage,\r\n    totalItems,\r\n    itemsPerPage,\r\n    onPageChange,\r\n    showInfo = true\r\n}) => {\r\n    const totalPages = Math.ceil(totalItems / itemsPerPage);\r\n    const startItem = (currentPage - 1) * itemsPerPage + 1;\r\n    const endItem = Math.min(currentPage * itemsPerPage, totalItems);\r\n\r\n    if (totalPages <= 1) return null;\r\n\r\n    const getPageNumbers = () => {\r\n        const pages = [];\r\n        const maxVisiblePages = 3; // Reduced for single row\r\n\r\n        if (totalPages <= maxVisiblePages) {\r\n            for (let i = 1; i <= totalPages; i++) {\r\n                pages.push(i);\r\n            }\r\n        } else {\r\n            // Simplified logic for single row\r\n            if (currentPage === 1) {\r\n                pages.push(1, 2, '...');\r\n            } else if (currentPage === totalPages) {\r\n                pages.push('...', totalPages - 1, totalPages);\r\n            } else {\r\n                pages.push('...', currentPage, '...');\r\n            }\r\n        }\r\n\r\n        return pages;\r\n    };\r\n\r\n    return (\r\n        <div className=\"pagination-wrapper-single\">\r\n            <div className=\"pagination-container\">\r\n                {showInfo && (\r\n                    <div className=\"pagination-info\">\r\n                        <span className=\"entries-text\">\r\n                            {startItem}-{endItem} of {totalItems}\r\n                        </span>\r\n                    </div>\r\n                )}\r\n\r\n                <div className=\"pagination-controls\">\r\n                    <button\r\n                        className={`pagination-btn pagination-prev ${currentPage === 1 ? 'disabled' : ''}`}\r\n                        onClick={() => onPageChange(currentPage - 1)}\r\n                        disabled={currentPage === 1}\r\n                        title=\"Previous page\"\r\n                    >\r\n                        ‹\r\n                    </button>\r\n\r\n                    <div className=\"pagination-pages\">\r\n                        {getPageNumbers().map((page, index) => (\r\n                            <button\r\n                                key={index}\r\n                                className={`pagination-btn pagination-page ${page === currentPage ? 'active' : ''\r\n                                    } ${page === '...' ? 'dots' : ''}`}\r\n                                onClick={() => page !== '...' && onPageChange(page)}\r\n                                disabled={page === '...'}\r\n                                title={page === '...' ? '' : `Go to page ${page}`}\r\n                            >\r\n                                {page}\r\n                            </button>\r\n                        ))}\r\n                    </div>\r\n\r\n                    <button\r\n                        className={`pagination-btn pagination-next ${currentPage === totalPages ? 'disabled' : ''}`}\r\n                        onClick={() => onPageChange(currentPage + 1)}\r\n                        disabled={currentPage === totalPages}\r\n                        title=\"Next page\"\r\n                    >\r\n                        ›\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Pagination;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,kBAAkB,CAAC,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,yBAE1B,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAMb,IANc,CAChBC,WAAW,CACXC,UAAU,CACVC,YAAY,CACZC,YAAY,CACZC,QAAQ,CAAG,IACf,CAAC,CAAAL,IAAA,CACG,KAAM,CAAAM,UAAU,CAAGC,IAAI,CAACC,IAAI,CAACN,UAAU,CAAGC,YAAY,CAAC,CACvD,KAAM,CAAAM,SAAS,CAAG,CAACR,WAAW,CAAG,CAAC,EAAIE,YAAY,CAAG,CAAC,CACtD,KAAM,CAAAO,OAAO,CAAGH,IAAI,CAACI,GAAG,CAACV,WAAW,CAAGE,YAAY,CAAED,UAAU,CAAC,CAEhE,GAAII,UAAU,EAAI,CAAC,CAAE,MAAO,KAAI,CAEhC,KAAM,CAAAM,cAAc,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,KAAK,CAAG,EAAE,CAChB,KAAM,CAAAC,eAAe,CAAG,CAAC,CAAE;AAE3B,GAAIR,UAAU,EAAIQ,eAAe,CAAE,CAC/B,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAIT,UAAU,CAAES,CAAC,EAAE,CAAE,CAClCF,KAAK,CAACG,IAAI,CAACD,CAAC,CAAC,CACjB,CACJ,CAAC,IAAM,CACH;AACA,GAAId,WAAW,GAAK,CAAC,CAAE,CACnBY,KAAK,CAACG,IAAI,CAAC,CAAC,CAAE,CAAC,CAAE,KAAK,CAAC,CAC3B,CAAC,IAAM,IAAIf,WAAW,GAAKK,UAAU,CAAE,CACnCO,KAAK,CAACG,IAAI,CAAC,KAAK,CAAEV,UAAU,CAAG,CAAC,CAAEA,UAAU,CAAC,CACjD,CAAC,IAAM,CACHO,KAAK,CAACG,IAAI,CAAC,KAAK,CAAEf,WAAW,CAAE,KAAK,CAAC,CACzC,CACJ,CAEA,MAAO,CAAAY,KAAK,CAChB,CAAC,CAED,mBACIf,IAAA,QAAKmB,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACtCtB,KAAA,QAAKqB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,EAChCb,QAAQ,eACLP,IAAA,QAAKmB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC5BtB,KAAA,SAAMqB,SAAS,CAAC,cAAc,CAAAC,QAAA,EACzBT,SAAS,CAAC,GAAC,CAACC,OAAO,CAAC,MAAI,CAACR,UAAU,EAClC,CAAC,CACN,CACR,cAEDN,KAAA,QAAKqB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAChCpB,IAAA,WACImB,SAAS,CAAE,kCAAkChB,WAAW,GAAK,CAAC,CAAG,UAAU,CAAG,EAAE,EAAG,CACnFkB,OAAO,CAAEA,CAAA,GAAMf,YAAY,CAACH,WAAW,CAAG,CAAC,CAAE,CAC7CmB,QAAQ,CAAEnB,WAAW,GAAK,CAAE,CAC5BoB,KAAK,CAAC,eAAe,CAAAH,QAAA,CACxB,QAED,CAAQ,CAAC,cAETpB,IAAA,QAAKmB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC5BN,cAAc,CAAC,CAAC,CAACU,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAC9B1B,IAAA,WAEImB,SAAS,CAAE,kCAAkCM,IAAI,GAAKtB,WAAW,CAAG,QAAQ,CAAG,EAAE,IACzEsB,IAAI,GAAK,KAAK,CAAG,MAAM,CAAG,EAAE,EAAG,CACvCJ,OAAO,CAAEA,CAAA,GAAMI,IAAI,GAAK,KAAK,EAAInB,YAAY,CAACmB,IAAI,CAAE,CACpDH,QAAQ,CAAEG,IAAI,GAAK,KAAM,CACzBF,KAAK,CAAEE,IAAI,GAAK,KAAK,CAAG,EAAE,CAAG,cAAcA,IAAI,EAAG,CAAAL,QAAA,CAEjDK,IAAI,EAPAC,KAQD,CACX,CAAC,CACD,CAAC,cAEN1B,IAAA,WACImB,SAAS,CAAE,kCAAkChB,WAAW,GAAKK,UAAU,CAAG,UAAU,CAAG,EAAE,EAAG,CAC5Fa,OAAO,CAAEA,CAAA,GAAMf,YAAY,CAACH,WAAW,CAAG,CAAC,CAAE,CAC7CmB,QAAQ,CAAEnB,WAAW,GAAKK,UAAW,CACrCe,KAAK,CAAC,WAAW,CAAAH,QAAA,CACpB,QAED,CAAQ,CAAC,EACR,CAAC,EACL,CAAC,CACL,CAAC,CAEd,CAAC,CAED,cAAe,CAAAnB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}