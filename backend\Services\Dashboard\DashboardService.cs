using CrmApi.Repositories.Division;
using CrmApi.Repositories.Category;
using CrmApi.Repositories.FirmNature;
using CrmApi.Repositories.State;

namespace CrmApi.Services.Dashboard
{
    public class DashboardService : IDashboardService
    {
        private readonly IDivisionRepository _divisionRepository;
        private readonly ICategoryRepository _categoryRepository;
        private readonly IFirmNatureRepository _firmNatureRepository;
        private readonly IStateRepository _stateRepository;

        public DashboardService(
            IDivisionRepository divisionRepository,
            ICategoryRepository categoryRepository,
            IFirmNatureRepository firmNatureRepository,
            IStateRepository stateRepository)
        {
            _divisionRepository = divisionRepository;
            _categoryRepository = categoryRepository;
            _firmNatureRepository = firmNatureRepository;
            _stateRepository = stateRepository;
        }

        public async Task<DashboardStatsResponse> GetDashboardStatsAsync()
        {
            var divisions = await _divisionRepository.GetAllAsync();
            var categories = await _categoryRepository.GetAllAsync();
            var firmNatures = await _firmNatureRepository.GetAllAsync();
            var states = await _stateRepository.GetAllAsync();

            return new DashboardStatsResponse
            {
                TotalDivisions = divisions.Count(),
                TotalCategories = categories.Count(),
                TotalFirmNatures = firmNatures.Count(),
                TotalStates = states.Count()
            };
        }

        public async Task<DashboardSummaryResponse> GetSummaryAsync()
        {
            var divisions = await _divisionRepository.GetAllAsync();
            var categories = await _categoryRepository.GetAllAsync();
            var firmNatures = await _firmNatureRepository.GetAllAsync();
            var states = await _stateRepository.GetAllAsync();

            return new DashboardSummaryResponse
            {
                TotalDivisions = divisions.Count(),
                TotalCategories = categories.Count(),
                TotalFirmNatures = firmNatures.Count(),
                TotalStates = states.Count()
            };
        }
    }
}
