-- =====================================================
-- SIMPLE ROLLBACK SCRIPT (MySQL)
-- Description: Simple rollback from firm_natures back to sub_categories
-- Database: MySQL
-- Author: CRM System Migration
-- Date: 2025-01-01
-- =====================================================

USE data_crm;

SELECT '==================================================' AS Status;
SELECT 'SIMPLE ROLLBACK STARTING...' AS Status;
SELECT '==================================================' AS Status;

-- =====================================================
-- STEP 1: DISABLE FOREIGN KEY CHECKS
-- =====================================================

SET foreign_key_checks = 0;
SELECT 'Disabled foreign key checks' AS Status;

-- =====================================================
-- STEP 2: CREATE EMERGENCY BACKUP
-- =====================================================

SELECT 'Step 2: Creating emergency backup...' AS Status;

-- Backup current firm_natures table
DROP TABLE IF EXISTS firm_natures_emergency_backup;
CREATE TABLE firm_natures_emergency_backup AS SELECT * FROM firm_natures;

-- Backup current persons table
DROP TABLE IF EXISTS persons_emergency_backup;
CREATE TABLE persons_emergency_backup AS SELECT * FROM persons;

SELECT 'Emergency backup created' AS Status;

-- =====================================================
-- STEP 3: RECREATE ORIGINAL PERSONS TABLE
-- =====================================================

SELECT 'Step 3: Recreating original persons table...' AS Status;

-- Create temporary table to hold current persons data
CREATE TABLE persons_rollback_temp AS SELECT * FROM persons;

-- Drop the current persons table
DROP TABLE persons;

-- Recreate persons table with original structure
CREATE TABLE persons (
    id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    division_id INT NOT NULL,
    category_id INT NOT NULL,
    sub_category_id INT NULL,
    name VARCHAR(255) NOT NULL,
    mobile_number VARCHAR(15) NOT NULL,
    nature INT NOT NULL,
    gender INT NULL,
    alternate_numbers JSON NULL,
    primary_email_id VARCHAR(255) NULL,
    alternate_email_ids JSON NULL,
    website VARCHAR(500) NULL,
    date_of_birth DATETIME NULL,
    is_married BOOLEAN NULL,
    date_of_marriage DATETIME NULL,
    working_state INT NULL,
    district VARCHAR(100) NULL,
    pin_code VARCHAR(10) NULL,
    address_line_1 VARCHAR(500) NULL,
    address_line_2 VARCHAR(500) NULL,
    landmark VARCHAR(255) NULL,
    star_rating INT NULL,
    remarks TEXT NULL,
    firm_name VARCHAR(255) NULL,
    number_of_offices INT NULL,
    number_of_branches INT NULL,
    total_employee_strength INT NULL,
    authorized_person_name VARCHAR(255) NULL,
    authorized_person_email VARCHAR(255) NULL,
    designation VARCHAR(100) NULL,
    marketing_contact VARCHAR(255) NULL,
    marketing_designation VARCHAR(100) NULL,
    place_of_posting VARCHAR(255) NULL,
    department VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP NULL
);

SELECT 'Recreated original persons table structure' AS Status;

-- =====================================================
-- STEP 4: RESTORE PERSONS DATA
-- =====================================================

SELECT 'Step 4: Restoring persons data...' AS Status;

-- Insert data back with column name change (firm_nature_id -> sub_category_id)
INSERT INTO persons (
    id, division_id, category_id, sub_category_id, name, mobile_number, nature, gender,
    alternate_numbers, primary_email_id, alternate_email_ids, website, date_of_birth,
    is_married, date_of_marriage, working_state, district, pin_code, address_line_1,
    address_line_2, landmark, star_rating, remarks, firm_name, number_of_offices,
    number_of_branches, total_employee_strength, authorized_person_name,
    authorized_person_email, designation, marketing_contact, marketing_designation,
    place_of_posting, department, created_at, updated_at, is_deleted, deleted_at
)
SELECT 
    id, division_id, category_id, firm_nature_id, name, mobile_number, nature, gender,
    alternate_numbers, primary_email_id, alternate_email_ids, website, date_of_birth,
    is_married, date_of_marriage, working_state, district, pin_code, address_line_1,
    address_line_2, landmark, star_rating, remarks, firm_name, number_of_offices,
    number_of_branches, total_employee_strength, authorized_person_name,
    authorized_person_email, designation, marketing_contact, marketing_designation,
    place_of_posting, department, created_at, updated_at, is_deleted, deleted_at
FROM persons_rollback_temp;

SELECT 'Restored persons data with sub_category_id column' AS Status;

-- Drop temporary table
DROP TABLE persons_rollback_temp;

-- =====================================================
-- STEP 5: RENAME TABLE BACK
-- =====================================================

SELECT 'Step 5: Renaming firm_natures back to sub_categories...' AS Status;

-- Rename the table back
RENAME TABLE firm_natures TO sub_categories;
SELECT 'Renamed firm_natures table back to sub_categories' AS Status;

-- =====================================================
-- STEP 6: CREATE INDEXES
-- =====================================================

SELECT 'Step 6: Creating indexes...' AS Status;

-- Create indexes on sub_categories table
CREATE INDEX IX_sub_categories_category_id ON sub_categories(category_id);
CREATE INDEX IX_sub_categories_category_id_name ON sub_categories(category_id, name);

-- Create indexes on persons table
CREATE INDEX IX_persons_division_id ON persons(division_id);
CREATE INDEX IX_persons_category_id ON persons(category_id);
CREATE INDEX IX_persons_sub_category_id ON persons(sub_category_id);
CREATE INDEX IX_persons_mobile_number ON persons(mobile_number);
CREATE INDEX IX_persons_name ON persons(name);
CREATE INDEX IX_persons_primary_email_id ON persons(primary_email_id);
CREATE INDEX IX_persons_is_deleted ON persons(is_deleted);
CREATE INDEX IX_persons_created_at ON persons(created_at);

SELECT 'Created all indexes' AS Status;

-- =====================================================
-- STEP 7: CREATE FOREIGN KEY CONSTRAINTS
-- =====================================================

SELECT 'Step 7: Creating foreign key constraints...' AS Status;

-- Add foreign key constraints
ALTER TABLE persons 
ADD CONSTRAINT fk_persons_divisions 
FOREIGN KEY (division_id) REFERENCES divisions(id);

ALTER TABLE persons 
ADD CONSTRAINT fk_persons_categories 
FOREIGN KEY (category_id) REFERENCES categories(id);

ALTER TABLE persons 
ADD CONSTRAINT fk_persons_sub_categories 
FOREIGN KEY (sub_category_id) REFERENCES sub_categories(id)
ON DELETE SET NULL ON UPDATE CASCADE;

SELECT 'Created all foreign key constraints' AS Status;

-- =====================================================
-- STEP 8: RE-ENABLE FOREIGN KEY CHECKS
-- =====================================================

SET foreign_key_checks = 1;
SELECT 'Re-enabled foreign key checks' AS Status;

-- =====================================================
-- STEP 9: VERIFICATION
-- =====================================================

SELECT 'Step 9: Verifying rollback...' AS Status;

-- Show current tables
SELECT 
    TABLE_NAME as 'Current Tables'
FROM information_schema.tables 
WHERE table_schema = 'data_crm' 
AND table_name IN ('sub_categories', 'persons', 'firm_natures');

-- Verify sub_categories table exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'SUCCESS: sub_categories table exists'
        ELSE 'ERROR: sub_categories table missing'
    END AS sub_categories_check
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'sub_categories';

-- Verify sub_category_id column exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'SUCCESS: sub_category_id column exists'
        ELSE 'ERROR: sub_category_id column missing'
    END AS sub_category_id_check
FROM information_schema.columns 
WHERE table_schema = 'data_crm' AND table_name = 'persons' AND column_name = 'sub_category_id';

-- Show record counts
SELECT 
    (SELECT COUNT(*) FROM sub_categories) AS sub_categories_count,
    (SELECT COUNT(*) FROM persons) AS persons_count;

-- =====================================================
-- FINAL STATUS
-- =====================================================

SELECT '==================================================' AS Status;
SELECT 'ROLLBACK COMPLETED!' AS Status;
SELECT '==================================================' AS Status;
SELECT '' AS Status;
SELECT 'What was restored:' AS Status;
SELECT '✓ firm_natures → sub_categories (table renamed back)' AS Status;
SELECT '✓ firm_nature_id → sub_category_id (column renamed back)' AS Status;
SELECT '✓ Removed partitioning from persons table' AS Status;
SELECT '✓ Restored original table structure' AS Status;
SELECT '✓ Recreated all indexes' AS Status;
SELECT '✓ Recreated all foreign key constraints' AS Status;
SELECT '' AS Status;
SELECT 'Emergency backup tables created:' AS Status;
SELECT '- firm_natures_emergency_backup' AS Status;
SELECT '- persons_emergency_backup' AS Status;
SELECT '' AS Status;
SELECT 'Next steps:' AS Status;
SELECT '1. Test your application with restored structure' AS Status;
SELECT '2. Update code to use sub_categories and sub_category_id' AS Status;
SELECT '3. Remove backup tables when confident' AS Status;
SELECT '' AS Status;
