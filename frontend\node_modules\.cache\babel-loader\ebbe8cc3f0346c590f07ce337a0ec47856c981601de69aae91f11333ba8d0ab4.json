{"ast": null, "code": "import React,{useState,useEffect}from'react';import HierarchicalSelector from'../forms/HierarchicalSelector';import'./DivisionCategorySelection.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DivisionCategorySelection=_ref=>{var _selectionData$divisi,_selectionData$catego;let{onSelectionComplete,onBack,error}=_ref;const[selectedDivision,setSelectedDivision]=useState('');const[selectedCategory,setSelectedCategory]=useState('');const[selectedFirmNature,setSelectedFirmNature]=useState('');const[selectionData,setSelectionData]=useState(null);const[validationErrors,setValidationErrors]=useState({});const[isLoading,setIsLoading]=useState(false);const validateSelection=()=>{const errors={};if(!selectedDivision){errors.division='Division is required for import';}if(!selectedCategory){errors.category='Category is required for import';}setValidationErrors(errors);return Object.keys(errors).length===0;};const handleContinue=()=>{if(validateSelection()){const selection={divisionId:parseInt(selectedDivision),categoryId:parseInt(selectedCategory),firmNatureId:selectedFirmNature?parseInt(selectedFirmNature):null};onSelectionComplete(selection);}};const handleSelectionChange=selection=>{setSelectedDivision(selection.divisionId||'');setSelectedCategory(selection.categoryId||'');setSelectedFirmNature(selection.firmNatureId||'');setSelectionData(selection);// Clear validation errors when user makes changes\nif(validationErrors.division&&selection.divisionId){setValidationErrors(prev=>({...prev,division:null}));}if(validationErrors.category&&selection.categoryId){setValidationErrors(prev=>({...prev,category:null}));}};return/*#__PURE__*/_jsxs(\"div\",{className:\"division-category-selection\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"selection-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Select Division and Category for Import\"}),/*#__PURE__*/_jsx(\"p\",{className:\"selection-description\",children:\"All records in your Excel file will be assigned to the division and category you select below. These fields are mandatory for all imported person records.\"})]}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"error-message\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"error-icon\",children:\"\\u26A0\\uFE0F\"}),error]}),/*#__PURE__*/_jsxs(\"div\",{className:\"selection-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"selection-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Required Fields\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"required-notice\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"required-icon\",children:\"*\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Division and Category are mandatory for all imported records\"})]}),/*#__PURE__*/_jsx(HierarchicalSelector,{initialSelection:{divisionId:selectedDivision,categoryId:selectedCategory,firmNatureId:selectedFirmNature},onSelectionChange:handleSelectionChange,required:true,showLabels:true,disabled:isLoading})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"selection-info\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"What this means:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"All persons imported from your Excel file will be assigned to the selected division and category\"}),/*#__PURE__*/_jsx(\"li\",{children:\"If your Excel file contains division/category columns, those values will be validated against your selection\"}),/*#__PURE__*/_jsx(\"li\",{children:\"If your Excel file doesn't contain division/category columns, all records will use your selection\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Firm Nature is required and must be selected\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"selection-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:onBack,className:\"btn btn-secondary\",disabled:isLoading,children:\"\\u2190 Back\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleContinue,className:\"btn btn-primary\",disabled:isLoading||!selectedDivision||!selectedCategory,children:\"Continue to File Upload \\u2192\"})]})]}),selectedDivision&&selectedCategory&&selectionData&&/*#__PURE__*/_jsxs(\"div\",{className:\"selection-summary\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Selected Configuration:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Division:\"}),\" \",((_selectionData$divisi=selectionData.division)===null||_selectionData$divisi===void 0?void 0:_selectionData$divisi.name)||'Loading...']}),/*#__PURE__*/_jsxs(\"div\",{className:\"summary-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Category:\"}),\" \",((_selectionData$catego=selectionData.category)===null||_selectionData$catego===void 0?void 0:_selectionData$catego.name)||'Loading...']}),selectedFirmNature&&selectionData.firmNature&&/*#__PURE__*/_jsxs(\"div\",{className:\"summary-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Firm Nature:\"}),\" \",selectionData.firmNature.name]})]})]});};export default DivisionCategorySelection;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "HierarchicalSelector", "jsx", "_jsx", "jsxs", "_jsxs", "DivisionCategorySelection", "_ref", "_selectionData$divisi", "_selectionData$catego", "onSelectionComplete", "onBack", "error", "selectedDivision", "setSelectedDivision", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedFirmNature", "setSelectedFirmNature", "selectionData", "setSelectionData", "validationErrors", "setValidationErrors", "isLoading", "setIsLoading", "validateSelection", "errors", "division", "category", "Object", "keys", "length", "handleContinue", "selection", "divisionId", "parseInt", "categoryId", "firmNatureId", "handleSelectionChange", "prev", "className", "children", "initialSelection", "onSelectionChange", "required", "showLabels", "disabled", "type", "onClick", "name", "firmNature"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/import/DivisionCategorySelection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport HierarchicalSelector from '../forms/HierarchicalSelector';\nimport './DivisionCategorySelection.css';\n\nconst DivisionCategorySelection = ({ onSelectionComplete, onBack, error }) => {\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedFirmNature, setSelectedFirmNature] = useState('');\n  const [selectionData, setSelectionData] = useState(null);\n  const [validationErrors, setValidationErrors] = useState({});\n  const [isLoading, setIsLoading] = useState(false);\n\n  const validateSelection = () => {\n    const errors = {};\n    \n    if (!selectedDivision) {\n      errors.division = 'Division is required for import';\n    }\n    \n    if (!selectedCategory) {\n      errors.category = 'Category is required for import';\n    }\n    \n    setValidationErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleContinue = () => {\n    if (validateSelection()) {\n      const selection = {\n        divisionId: parseInt(selectedDivision),\n        categoryId: parseInt(selectedCategory),\n        firmNatureId: selectedFirmNature ? parseInt(selectedFirmNature) : null\n      };\n      onSelectionComplete(selection);\n    }\n  };\n\n  const handleSelectionChange = (selection) => {\n    setSelectedDivision(selection.divisionId || '');\n    setSelectedCategory(selection.categoryId || '');\n    setSelectedFirmNature(selection.firmNatureId || '');\n    setSelectionData(selection);\n\n    // Clear validation errors when user makes changes\n    if (validationErrors.division && selection.divisionId) {\n      setValidationErrors(prev => ({ ...prev, division: null }));\n    }\n    if (validationErrors.category && selection.categoryId) {\n      setValidationErrors(prev => ({ ...prev, category: null }));\n    }\n  };\n\n  return (\n    <div className=\"division-category-selection\">\n      <div className=\"selection-header\">\n        <h3>Select Division and Category for Import</h3>\n        <p className=\"selection-description\">\n          All records in your Excel file will be assigned to the division and category you select below. \n          These fields are mandatory for all imported person records.\n        </p>\n      </div>\n\n      {error && (\n        <div className=\"error-message\">\n          <span className=\"error-icon\">⚠️</span>\n          {error}\n        </div>\n      )}\n\n      <div className=\"selection-form\">\n        <div className=\"selection-section\">\n          <h4>Required Fields</h4>\n          <div className=\"required-notice\">\n            <span className=\"required-icon\">*</span>\n            <span>Division and Category are mandatory for all imported records</span>\n          </div>\n          \n          <HierarchicalSelector\n            initialSelection={{\n              divisionId: selectedDivision,\n              categoryId: selectedCategory,\n              firmNatureId: selectedFirmNature\n            }}\n            onSelectionChange={handleSelectionChange}\n            required={true}\n            showLabels={true}\n            disabled={isLoading}\n          />\n        </div>\n\n        <div className=\"selection-info\">\n          <h4>What this means:</h4>\n          <ul>\n            <li>All persons imported from your Excel file will be assigned to the selected division and category</li>\n            <li>If your Excel file contains division/category columns, those values will be validated against your selection</li>\n            <li>If your Excel file doesn't contain division/category columns, all records will use your selection</li>\n            <li>Firm Nature is required and must be selected</li>\n          </ul>\n        </div>\n\n        <div className=\"selection-actions\">\n          <button \n            type=\"button\" \n            onClick={onBack}\n            className=\"btn btn-secondary\"\n            disabled={isLoading}\n          >\n            ← Back\n          </button>\n          \n          <button \n            type=\"button\" \n            onClick={handleContinue}\n            className=\"btn btn-primary\"\n            disabled={isLoading || !selectedDivision || !selectedCategory}\n          >\n            Continue to File Upload →\n          </button>\n        </div>\n      </div>\n\n      {/* Selection Summary */}\n      {selectedDivision && selectedCategory && selectionData && (\n        <div className=\"selection-summary\">\n          <h4>Selected Configuration:</h4>\n          <div className=\"summary-item\">\n            <strong>Division:</strong> {selectionData.division?.name || 'Loading...'}\n          </div>\n          <div className=\"summary-item\">\n            <strong>Category:</strong> {selectionData.category?.name || 'Loading...'}\n          </div>\n          {selectedFirmNature && selectionData.firmNature && (\n            <div className=\"summary-item\">\n              <strong>Firm Nature:</strong> {selectionData.firmNature.name}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default DivisionCategorySelection;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,oBAAoB,KAAM,+BAA+B,CAChE,MAAO,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAAC,yBAAyB,CAAGC,IAAA,EAA4C,KAAAC,qBAAA,CAAAC,qBAAA,IAA3C,CAAEC,mBAAmB,CAAEC,MAAM,CAAEC,KAAM,CAAC,CAAAL,IAAA,CACvE,KAAM,CAACM,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACgB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACkB,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CAACoB,aAAa,CAAEC,gBAAgB,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAACsB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC5D,KAAM,CAACwB,SAAS,CAAEC,YAAY,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAEjD,KAAM,CAAA0B,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAAC,MAAM,CAAG,CAAC,CAAC,CAEjB,GAAI,CAACb,gBAAgB,CAAE,CACrBa,MAAM,CAACC,QAAQ,CAAG,iCAAiC,CACrD,CAEA,GAAI,CAACZ,gBAAgB,CAAE,CACrBW,MAAM,CAACE,QAAQ,CAAG,iCAAiC,CACrD,CAEAN,mBAAmB,CAACI,MAAM,CAAC,CAC3B,MAAO,CAAAG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACK,MAAM,GAAK,CAAC,CACzC,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAIP,iBAAiB,CAAC,CAAC,CAAE,CACvB,KAAM,CAAAQ,SAAS,CAAG,CAChBC,UAAU,CAAEC,QAAQ,CAACtB,gBAAgB,CAAC,CACtCuB,UAAU,CAAED,QAAQ,CAACpB,gBAAgB,CAAC,CACtCsB,YAAY,CAAEpB,kBAAkB,CAAGkB,QAAQ,CAAClB,kBAAkB,CAAC,CAAG,IACpE,CAAC,CACDP,mBAAmB,CAACuB,SAAS,CAAC,CAChC,CACF,CAAC,CAED,KAAM,CAAAK,qBAAqB,CAAIL,SAAS,EAAK,CAC3CnB,mBAAmB,CAACmB,SAAS,CAACC,UAAU,EAAI,EAAE,CAAC,CAC/ClB,mBAAmB,CAACiB,SAAS,CAACG,UAAU,EAAI,EAAE,CAAC,CAC/ClB,qBAAqB,CAACe,SAAS,CAACI,YAAY,EAAI,EAAE,CAAC,CACnDjB,gBAAgB,CAACa,SAAS,CAAC,CAE3B;AACA,GAAIZ,gBAAgB,CAACM,QAAQ,EAAIM,SAAS,CAACC,UAAU,CAAE,CACrDZ,mBAAmB,CAACiB,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEZ,QAAQ,CAAE,IAAK,CAAC,CAAC,CAAC,CAC5D,CACA,GAAIN,gBAAgB,CAACO,QAAQ,EAAIK,SAAS,CAACG,UAAU,CAAE,CACrDd,mBAAmB,CAACiB,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEX,QAAQ,CAAE,IAAK,CAAC,CAAC,CAAC,CAC5D,CACF,CAAC,CAED,mBACEvB,KAAA,QAAKmC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CpC,KAAA,QAAKmC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BtC,IAAA,OAAAsC,QAAA,CAAI,yCAAuC,CAAI,CAAC,cAChDtC,IAAA,MAAGqC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,4JAGrC,CAAG,CAAC,EACD,CAAC,CAEL7B,KAAK,eACJP,KAAA,QAAKmC,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtC,IAAA,SAAMqC,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,CACrC7B,KAAK,EACH,CACN,cAEDP,KAAA,QAAKmC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BpC,KAAA,QAAKmC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCtC,IAAA,OAAAsC,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxBpC,KAAA,QAAKmC,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BtC,IAAA,SAAMqC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,cACxCtC,IAAA,SAAAsC,QAAA,CAAM,8DAA4D,CAAM,CAAC,EACtE,CAAC,cAENtC,IAAA,CAACF,oBAAoB,EACnByC,gBAAgB,CAAE,CAChBR,UAAU,CAAErB,gBAAgB,CAC5BuB,UAAU,CAAErB,gBAAgB,CAC5BsB,YAAY,CAAEpB,kBAChB,CAAE,CACF0B,iBAAiB,CAAEL,qBAAsB,CACzCM,QAAQ,CAAE,IAAK,CACfC,UAAU,CAAE,IAAK,CACjBC,QAAQ,CAAEvB,SAAU,CACrB,CAAC,EACC,CAAC,cAENlB,KAAA,QAAKmC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BtC,IAAA,OAAAsC,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzBpC,KAAA,OAAAoC,QAAA,eACEtC,IAAA,OAAAsC,QAAA,CAAI,kGAAgG,CAAI,CAAC,cACzGtC,IAAA,OAAAsC,QAAA,CAAI,8GAA4G,CAAI,CAAC,cACrHtC,IAAA,OAAAsC,QAAA,CAAI,mGAAiG,CAAI,CAAC,cAC1GtC,IAAA,OAAAsC,QAAA,CAAI,8CAA4C,CAAI,CAAC,EACnD,CAAC,EACF,CAAC,cAENpC,KAAA,QAAKmC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCtC,IAAA,WACE4C,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAErC,MAAO,CAChB6B,SAAS,CAAC,mBAAmB,CAC7BM,QAAQ,CAAEvB,SAAU,CAAAkB,QAAA,CACrB,aAED,CAAQ,CAAC,cAETtC,IAAA,WACE4C,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEhB,cAAe,CACxBQ,SAAS,CAAC,iBAAiB,CAC3BM,QAAQ,CAAEvB,SAAS,EAAI,CAACV,gBAAgB,EAAI,CAACE,gBAAiB,CAAA0B,QAAA,CAC/D,gCAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CAGL5B,gBAAgB,EAAIE,gBAAgB,EAAII,aAAa,eACpDd,KAAA,QAAKmC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCtC,IAAA,OAAAsC,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChCpC,KAAA,QAAKmC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtC,IAAA,WAAAsC,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAAC,EAAAjC,qBAAA,CAAAW,aAAa,CAACQ,QAAQ,UAAAnB,qBAAA,iBAAtBA,qBAAA,CAAwByC,IAAI,GAAI,YAAY,EACrE,CAAC,cACN5C,KAAA,QAAKmC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtC,IAAA,WAAAsC,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAAC,EAAAhC,qBAAA,CAAAU,aAAa,CAACS,QAAQ,UAAAnB,qBAAA,iBAAtBA,qBAAA,CAAwBwC,IAAI,GAAI,YAAY,EACrE,CAAC,CACLhC,kBAAkB,EAAIE,aAAa,CAAC+B,UAAU,eAC7C7C,KAAA,QAAKmC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BtC,IAAA,WAAAsC,QAAA,CAAQ,cAAY,CAAQ,CAAC,IAAC,CAACtB,aAAa,CAAC+B,UAAU,CAACD,IAAI,EACzD,CACN,EACE,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA3C,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}