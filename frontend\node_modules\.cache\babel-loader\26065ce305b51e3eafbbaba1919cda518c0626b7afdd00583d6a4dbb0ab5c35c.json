{"ast": null, "code": "import React from'react';import{useAuth}from'../context/AuthContext';import Login from'./Login';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProtectedRoute=_ref=>{let{children}=_ref;const{isAuthenticated,loading}=useAuth();if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"loading-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner-large\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Loading...\"})]});}return isAuthenticated?children:/*#__PURE__*/_jsx(Login,{});};export default ProtectedRoute;", "map": {"version": 3, "names": ["React", "useAuth", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "ProtectedRoute", "_ref", "children", "isAuthenticated", "loading", "className"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport Login from './Login';\r\n\r\nconst ProtectedRoute = ({ children }) => {\r\n  const { isAuthenticated, loading } = useAuth();\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"loading-container\">\r\n        <div className=\"loading-spinner-large\"></div>\r\n        <p>Loading...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return isAuthenticated ? children : <Login />;\r\n};\r\n\r\nexport default ProtectedRoute;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,OAAO,KAAQ,wBAAwB,CAChD,MAAO,CAAAC,KAAK,KAAM,SAAS,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5B,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAClC,KAAM,CAAEE,eAAe,CAAEC,OAAQ,CAAC,CAAGV,OAAO,CAAC,CAAC,CAE9C,GAAIU,OAAO,CAAE,CACX,mBACEL,KAAA,QAAKM,SAAS,CAAC,mBAAmB,CAAAH,QAAA,eAChCL,IAAA,QAAKQ,SAAS,CAAC,uBAAuB,CAAM,CAAC,cAC7CR,IAAA,MAAAK,QAAA,CAAG,YAAU,CAAG,CAAC,EACd,CAAC,CAEV,CAEA,MAAO,CAAAC,eAAe,CAAGD,QAAQ,cAAGL,IAAA,CAACF,KAAK,GAAE,CAAC,CAC/C,CAAC,CAED,cAAe,CAAAK,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}