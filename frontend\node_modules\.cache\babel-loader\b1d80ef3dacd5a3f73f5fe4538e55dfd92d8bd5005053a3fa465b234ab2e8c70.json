{"ast": null, "code": "import { motionValue } from './index.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { useEffect } from 'react';\nimport { warning } from '../utils/errors.mjs';\nimport { scrollInfo } from '../render/dom/scroll/track.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nfunction refWarning(name, ref) {\n  warning(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n  scrollX: motionValue(0),\n  scrollY: motionValue(0),\n  scrollXProgress: motionValue(0),\n  scrollYProgress: motionValue(0)\n});\nfunction useScroll() {\n  let {\n    container,\n    target,\n    layoutEffect = true,\n    ...options\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const values = useConstant(createScrollMotionValues);\n  const useLifecycleEffect = layoutEffect ? useIsomorphicLayoutEffect : useEffect;\n  useLifecycleEffect(() => {\n    refWarning(\"target\", target);\n    refWarning(\"container\", container);\n    return scrollInfo(_ref => {\n      let {\n        x,\n        y\n      } = _ref;\n      values.scrollX.set(x.current);\n      values.scrollXProgress.set(x.progress);\n      values.scrollY.set(y.current);\n      values.scrollYProgress.set(y.progress);\n    }, {\n      ...options,\n      container: (container === null || container === void 0 ? void 0 : container.current) || undefined,\n      target: (target === null || target === void 0 ? void 0 : target.current) || undefined\n    });\n  }, [container, target, JSON.stringify(options.offset)]);\n  return values;\n}\nexport { useScroll };", "map": {"version": 3, "names": ["motionValue", "useConstant", "useEffect", "warning", "scrollInfo", "useIsomorphicLayoutEffect", "refWarning", "name", "ref", "Boolean", "current", "createScrollMotionValues", "scrollX", "scrollY", "scrollXProgress", "scrollYProgress", "useScroll", "container", "target", "layoutEffect", "options", "arguments", "length", "undefined", "values", "useLifecycleEffect", "_ref", "x", "y", "set", "progress", "JSON", "stringify", "offset"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/value/use-scroll.mjs"], "sourcesContent": ["import { motionValue } from './index.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { useEffect } from 'react';\nimport { warning } from '../utils/errors.mjs';\nimport { scrollInfo } from '../render/dom/scroll/track.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\n\nfunction refWarning(name, ref) {\n    warning(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n    scrollX: motionValue(0),\n    scrollY: motionValue(0),\n    scrollXProgress: motionValue(0),\n    scrollYProgress: motionValue(0),\n});\nfunction useScroll({ container, target, layoutEffect = true, ...options } = {}) {\n    const values = useConstant(createScrollMotionValues);\n    const useLifecycleEffect = layoutEffect\n        ? useIsomorphicLayoutEffect\n        : useEffect;\n    useLifecycleEffect(() => {\n        refWarning(\"target\", target);\n        refWarning(\"container\", container);\n        return scrollInfo(({ x, y }) => {\n            values.scrollX.set(x.current);\n            values.scrollXProgress.set(x.progress);\n            values.scrollY.set(y.current);\n            values.scrollYProgress.set(y.progress);\n        }, {\n            ...options,\n            container: (container === null || container === void 0 ? void 0 : container.current) || undefined,\n            target: (target === null || target === void 0 ? void 0 : target.current) || undefined,\n        });\n    }, [container, target, JSON.stringify(options.offset)]);\n    return values;\n}\n\nexport { useScroll };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,yBAAyB,QAAQ,oCAAoC;AAE9E,SAASC,UAAUA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAC3BL,OAAO,CAACM,OAAO,CAAC,CAACD,GAAG,IAAIA,GAAG,CAACE,OAAO,CAAC,EAAE,sBAAsBH,IAAI,+MAA+M,CAAC;AACpR;AACA,MAAMI,wBAAwB,GAAGA,CAAA,MAAO;EACpCC,OAAO,EAAEZ,WAAW,CAAC,CAAC,CAAC;EACvBa,OAAO,EAAEb,WAAW,CAAC,CAAC,CAAC;EACvBc,eAAe,EAAEd,WAAW,CAAC,CAAC,CAAC;EAC/Be,eAAe,EAAEf,WAAW,CAAC,CAAC;AAClC,CAAC,CAAC;AACF,SAASgB,SAASA,CAAA,EAA8D;EAAA,IAA7D;IAAEC,SAAS;IAAEC,MAAM;IAAEC,YAAY,GAAG,IAAI;IAAE,GAAGC;EAAQ,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC1E,MAAMG,MAAM,GAAGvB,WAAW,CAACU,wBAAwB,CAAC;EACpD,MAAMc,kBAAkB,GAAGN,YAAY,GACjCd,yBAAyB,GACzBH,SAAS;EACfuB,kBAAkB,CAAC,MAAM;IACrBnB,UAAU,CAAC,QAAQ,EAAEY,MAAM,CAAC;IAC5BZ,UAAU,CAAC,WAAW,EAAEW,SAAS,CAAC;IAClC,OAAOb,UAAU,CAACsB,IAAA,IAAc;MAAA,IAAb;QAAEC,CAAC;QAAEC;MAAE,CAAC,GAAAF,IAAA;MACvBF,MAAM,CAACZ,OAAO,CAACiB,GAAG,CAACF,CAAC,CAACjB,OAAO,CAAC;MAC7Bc,MAAM,CAACV,eAAe,CAACe,GAAG,CAACF,CAAC,CAACG,QAAQ,CAAC;MACtCN,MAAM,CAACX,OAAO,CAACgB,GAAG,CAACD,CAAC,CAAClB,OAAO,CAAC;MAC7Bc,MAAM,CAACT,eAAe,CAACc,GAAG,CAACD,CAAC,CAACE,QAAQ,CAAC;IAC1C,CAAC,EAAE;MACC,GAAGV,OAAO;MACVH,SAAS,EAAE,CAACA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACP,OAAO,KAAKa,SAAS;MACjGL,MAAM,EAAE,CAACA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACR,OAAO,KAAKa;IAChF,CAAC,CAAC;EACN,CAAC,EAAE,CAACN,SAAS,EAAEC,MAAM,EAAEa,IAAI,CAACC,SAAS,CAACZ,OAAO,CAACa,MAAM,CAAC,CAAC,CAAC;EACvD,OAAOT,MAAM;AACjB;AAEA,SAASR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}