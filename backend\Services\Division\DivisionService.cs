using CrmApi.Models.Division;
using CrmApi.Models.Category;
using CrmApi.Models.FirmNature;
using CrmApi.Repositories.Division;
using CrmApi.Exceptions;

namespace CrmApi.Services.Division
{
    public class DivisionService : IDivisionService
    {
        private readonly IDivisionRepository _divisionRepository;

        public DivisionService(IDivisionRepository divisionRepository)
        {
            _divisionRepository = divisionRepository;
        }

        public async Task<IEnumerable<DivisionResponse>> GetAllDivisionsAsync()
        {
            var divisions = await _divisionRepository.GetAllAsync();
            return divisions.Select(MapToResponse);
        }

        public async Task<DivisionResponse?> GetDivisionByIdAsync(int id)
        {
            var division = await _divisionRepository.GetByIdAsync(id);
            return division != null ? MapToResponse(division) : null;
        }

        public async Task<DivisionResponse?> GetDivisionWithCategoriesAsync(int id)
        {
            var division = await _divisionRepository.GetByIdWithCategoriesAsync(id);
            return division != null ? MapToResponseWithCategories(division) : null;
        }

        public async Task<DivisionResponse> CreateDivisionAsync(CreateDivisionRequest request)
        {
            // Check if name already exists
            if (await _divisionRepository.NameExistsAsync(request.Name))
                throw new BusinessException($"Division with name '{request.Name}' already exists");

            var division = new Models.Division.Division
            {
                Name = request.Name
            };

            var createdDivision = await _divisionRepository.CreateAsync(division);
            return MapToResponse(createdDivision);
        }

        public async Task<DivisionResponse> UpdateDivisionAsync(int id, UpdateDivisionRequest request)
        {
            var division = await _divisionRepository.GetByIdAsync(id);
            if (division == null)
                throw new NotFoundException($"Division with ID {id} not found");

            // Check name uniqueness
            if (await _divisionRepository.NameExistsAsync(request.Name, id))
                throw new BusinessException($"Division with name '{request.Name}' already exists");

            division.Name = request.Name;
            var updatedDivision = await _divisionRepository.UpdateAsync(division);
            return MapToResponse(updatedDivision);
        }

        public async Task<bool> DeleteDivisionAsync(int id)
        {
            if (!await _divisionRepository.ExistsAsync(id))
                throw new NotFoundException($"Division with ID {id} not found");

            return await _divisionRepository.DeleteAsync(id);
        }

        private DivisionResponse MapToResponse(Models.Division.Division division)
        {
            return new DivisionResponse
            {
                Id = division.Id,
                Name = division.Name,
                CreatedAt = division.CreatedAt,
                UpdatedAt = division.UpdatedAt
            };
        }

        private DivisionResponse MapToResponseWithCategories(Models.Division.Division division)
        {
            return new DivisionResponse
            {
                Id = division.Id,
                Name = division.Name,
                CreatedAt = division.CreatedAt,
                UpdatedAt = division.UpdatedAt,
                Categories = division.Categories?.Select(c => new CategoryResponse
                {
                    Id = c.Id,
                    DivisionId = c.DivisionId,
                    Name = c.Name,
                    CreatedAt = c.CreatedAt,
                    UpdatedAt = c.UpdatedAt,
                    FirmNatures = c.FirmNatures?.Select(fn => new FirmNatureResponse
                    {
                        Id = fn.Id,
                        CategoryId = fn.CategoryId,
                        Name = fn.Name,
                        CreatedAt = fn.CreatedAt,
                        UpdatedAt = fn.UpdatedAt
                    }).ToList()
                }).ToList()
            };
        }
    }
}
