-- =====================================================
-- Verification Script: Firm Nature Migration
-- Description: Comprehensive verification of the subcategory to firm_nature migration
-- Database: SQL Server
-- Author: CRM System Migration
-- Date: 2025-01-01
-- =====================================================

USE [CrmDatabase]
GO

PRINT '=================================================='
PRINT 'FIRM NATURE MIGRATION VERIFICATION REPORT'
PRINT '=================================================='
PRINT ''

-- =====================================================
-- SECTION 1: TABLE STRUCTURE VERIFICATION
-- =====================================================

PRINT '1. TABLE STRUCTURE VERIFICATION'
PRINT '================================'

-- Check if firm_natures table exists
IF EXISTS (SELECT * FROM sysobjects WHERE name='firm_natures' AND xtype='U')
    PRINT '✓ firm_natures table exists'
ELSE
    PRINT '✗ ERROR: firm_natures table does not exist'

-- Check if old subcategories table still exists (should not)
IF EXISTS (SELECT * FROM sysobjects WHERE name='subcategories' AND xtype='U')
    PRINT '✗ WARNING: subcategories table still exists (should be renamed)'
ELSE
    PRINT '✓ subcategories table properly renamed'

-- Check if firm_nature_id column exists in persons table
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('persons') AND name = 'firm_nature_id')
    PRINT '✓ firm_nature_id column exists in persons table'
ELSE
    PRINT '✗ ERROR: firm_nature_id column does not exist in persons table'

-- Check if old subcategory_id column still exists (should not)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('persons') AND name = 'subcategory_id')
    PRINT '✗ WARNING: subcategory_id column still exists (should be renamed)'
ELSE
    PRINT '✓ subcategory_id column properly renamed'

PRINT ''

-- =====================================================
-- SECTION 2: CONSTRAINTS VERIFICATION
-- =====================================================

PRINT '2. CONSTRAINTS VERIFICATION'
PRINT '============================'

-- Check foreign key constraints
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_persons_firm_natures')
    PRINT '✓ FK_persons_firm_natures constraint exists'
ELSE
    PRINT '✗ ERROR: FK_persons_firm_natures constraint missing'

IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_firm_natures_categories')
    PRINT '✓ FK_firm_natures_categories constraint exists'
ELSE
    PRINT '✗ ERROR: FK_firm_natures_categories constraint missing'

-- Check unique constraints
IF EXISTS (SELECT * FROM sys.key_constraints WHERE name = 'UQ_firm_natures_name_category')
    PRINT '✓ UQ_firm_natures_name_category constraint exists'
ELSE
    PRINT '✗ ERROR: UQ_firm_natures_name_category constraint missing'

-- Check if old constraints still exist (should not)
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_persons_subcategories')
    PRINT '✗ WARNING: FK_persons_subcategories constraint still exists'
ELSE
    PRINT '✓ Old FK_persons_subcategories constraint properly removed'

PRINT ''

-- =====================================================
-- SECTION 3: INDEXES VERIFICATION
-- =====================================================

PRINT '3. INDEXES VERIFICATION'
PRINT '======================='

-- Check indexes on firm_natures table
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_firm_natures_category_id')
    PRINT '✓ IX_firm_natures_category_id index exists'
ELSE
    PRINT '✗ ERROR: IX_firm_natures_category_id index missing'

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_firm_natures_name_category_id')
    PRINT '✓ IX_firm_natures_name_category_id index exists'
ELSE
    PRINT '✗ ERROR: IX_firm_natures_name_category_id index missing'

-- Check indexes on persons table
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_persons_firm_nature_id')
    PRINT '✓ IX_persons_firm_nature_id index exists'
ELSE
    PRINT '✗ ERROR: IX_persons_firm_nature_id index missing'

-- Check partitioned indexes
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_persons_firm_nature_id_partitioned')
    PRINT '✓ IX_persons_firm_nature_id_partitioned index exists'
ELSE
    PRINT '✗ ERROR: IX_persons_firm_nature_id_partitioned index missing'

-- Check if old indexes still exist (should not)
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_persons_subcategory_id')
    PRINT '✗ WARNING: IX_persons_subcategory_id index still exists'
ELSE
    PRINT '✓ Old IX_persons_subcategory_id index properly removed'

PRINT ''

-- =====================================================
-- SECTION 4: PARTITIONING VERIFICATION
-- =====================================================

PRINT '4. PARTITIONING VERIFICATION'
PRINT '============================'

-- Check partition function
IF EXISTS (SELECT * FROM sys.partition_functions WHERE name = 'pf_firm_nature_id')
    PRINT '✓ Partition function pf_firm_nature_id exists'
ELSE
    PRINT '✗ ERROR: Partition function pf_firm_nature_id missing'

-- Check partition scheme
IF EXISTS (SELECT * FROM sys.partition_schemes WHERE name = 'ps_firm_nature_id')
    PRINT '✓ Partition scheme ps_firm_nature_id exists'
ELSE
    PRINT '✗ ERROR: Partition scheme ps_firm_nature_id missing'

-- Show partition information
SELECT 
    pf.name AS partition_function_name,
    pf.type_desc AS partition_type,
    ps.name AS partition_scheme_name,
    prv.value AS boundary_value
FROM sys.partition_functions pf
LEFT JOIN sys.partition_schemes ps ON pf.function_id = ps.function_id
LEFT JOIN sys.partition_range_values prv ON pf.function_id = prv.function_id
WHERE pf.name = 'pf_firm_nature_id'
ORDER BY prv.boundary_id;

PRINT ''

-- =====================================================
-- SECTION 5: DATA INTEGRITY VERIFICATION
-- =====================================================

PRINT '5. DATA INTEGRITY VERIFICATION'
PRINT '==============================='

-- Count records in firm_natures table
DECLARE @firm_nature_count INT
SELECT @firm_nature_count = COUNT(*) FROM firm_natures
PRINT 'firm_natures table record count: ' + CAST(@firm_nature_count AS VARCHAR(10))

-- Count records in persons table
DECLARE @persons_count INT
SELECT @persons_count = COUNT(*) FROM persons
PRINT 'persons table record count: ' + CAST(@persons_count AS VARCHAR(10))

-- Count persons with firm_nature_id
DECLARE @persons_with_firm_nature INT
SELECT @persons_with_firm_nature = COUNT(*) FROM persons WHERE firm_nature_id IS NOT NULL
PRINT 'persons with firm_nature_id: ' + CAST(@persons_with_firm_nature AS VARCHAR(10))

-- Check for orphaned records
DECLARE @orphaned_persons INT
SELECT @orphaned_persons = COUNT(*) 
FROM persons p
LEFT JOIN firm_natures fn ON p.firm_nature_id = fn.id
WHERE p.firm_nature_id IS NOT NULL AND fn.id IS NULL

IF @orphaned_persons > 0
    PRINT '✗ ERROR: ' + CAST(@orphaned_persons AS VARCHAR(10)) + ' orphaned person records found'
ELSE
    PRINT '✓ No orphaned person records found'

-- Check referential integrity
DECLARE @integrity_errors INT
SELECT @integrity_errors = COUNT(*)
FROM persons p
WHERE p.firm_nature_id IS NOT NULL 
  AND NOT EXISTS (SELECT 1 FROM firm_natures fn WHERE fn.id = p.firm_nature_id)

IF @integrity_errors > 0
    PRINT '✗ ERROR: ' + CAST(@integrity_errors AS VARCHAR(10)) + ' referential integrity violations found'
ELSE
    PRINT '✓ Referential integrity maintained'

PRINT ''

-- =====================================================
-- SECTION 6: PERFORMANCE VERIFICATION
-- =====================================================

PRINT '6. PERFORMANCE VERIFICATION'
PRINT '==========================='

-- Check index usage statistics
SELECT 
    i.name AS index_name,
    i.type_desc AS index_type,
    ius.user_seeks,
    ius.user_scans,
    ius.user_lookups,
    ius.user_updates
FROM sys.indexes i
LEFT JOIN sys.dm_db_index_usage_stats ius ON i.object_id = ius.object_id AND i.index_id = ius.index_id
WHERE i.object_id = OBJECT_ID('persons')
  AND i.name LIKE '%firm_nature%'
ORDER BY i.name;

-- Test query performance on partitioned table
SET STATISTICS IO ON
SET STATISTICS TIME ON

PRINT 'Testing query performance on firm_nature_id...'
SELECT COUNT(*) FROM persons WHERE firm_nature_id = 1;

SET STATISTICS IO OFF
SET STATISTICS TIME OFF

PRINT ''

-- =====================================================
-- SECTION 7: BACKUP VERIFICATION
-- =====================================================

PRINT '7. BACKUP VERIFICATION'
PRINT '======================'

-- Check if backup tables exist
IF EXISTS (SELECT * FROM sysobjects WHERE name='subcategories_backup' AND xtype='U')
BEGIN
    DECLARE @backup_count INT
    SELECT @backup_count = COUNT(*) FROM subcategories_backup
    PRINT '✓ subcategories_backup table exists with ' + CAST(@backup_count AS VARCHAR(10)) + ' records'
END
ELSE
    PRINT '✗ WARNING: subcategories_backup table does not exist'

IF EXISTS (SELECT * FROM sysobjects WHERE name='persons_structure_backup' AND xtype='U')
    PRINT '✓ persons_structure_backup table exists'
ELSE
    PRINT '✗ WARNING: persons_structure_backup table does not exist'

PRINT ''

-- =====================================================
-- SECTION 8: SUMMARY REPORT
-- =====================================================

PRINT '8. MIGRATION SUMMARY'
PRINT '===================='

DECLARE @total_checks INT = 0
DECLARE @passed_checks INT = 0

-- Count total and passed checks (simplified version)
-- In a real implementation, you would track each check result

PRINT 'Migration Status: '
IF EXISTS (SELECT * FROM sysobjects WHERE name='firm_natures' AND xtype='U')
   AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('persons') AND name = 'firm_nature_id')
   AND EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_persons_firm_natures')
   AND EXISTS (SELECT * FROM sys.partition_functions WHERE name = 'pf_firm_nature_id')
BEGIN
    PRINT '✓ MIGRATION SUCCESSFUL'
    PRINT ''
    PRINT 'Key accomplishments:'
    PRINT '✓ Table renamed: subcategories → firm_natures'
    PRINT '✓ Column renamed: subcategory_id → firm_nature_id'
    PRINT '✓ Foreign key constraints updated'
    PRINT '✓ Indexes recreated and optimized'
    PRINT '✓ Partitioning implemented'
    PRINT '✓ Data integrity maintained'
END
ELSE
BEGIN
    PRINT '✗ MIGRATION INCOMPLETE OR FAILED'
    PRINT ''
    PRINT 'Please review the errors above and:'
    PRINT '1. Check the migration script execution'
    PRINT '2. Verify database permissions'
    PRINT '3. Consider running the rollback script if needed'
END

PRINT ''
PRINT 'Next steps:'
PRINT '1. Update application code to use new table/column names'
PRINT '2. Update stored procedures and views'
PRINT '3. Test application functionality thoroughly'
PRINT '4. Monitor performance with new partitioning'
PRINT '5. Remove backup tables after successful testing'

PRINT ''
PRINT '=================================================='
PRINT 'VERIFICATION REPORT COMPLETED'
PRINT '=================================================='
GO
