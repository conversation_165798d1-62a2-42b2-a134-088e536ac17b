-- =====================================================
-- Check Current Database Tables (MySQL)
-- Description: Verify what tables and columns currently exist in the database
-- Database: MySQL
-- Author: CRM System Migration
-- Date: 2025-01-01
-- =====================================================

USE data_crm;

SELECT '==================================================' AS Status;
SELECT 'CURRENT DATABASE STRUCTURE VERIFICATION' AS Status;
SELECT '==================================================' AS Status;

-- =====================================================
-- SECTION 1: LIST ALL TABLES
-- =====================================================

SELECT 'SECTION 1: ALL TABLES IN DATABASE' AS Status;
SELECT '=====================================' AS Status;

SELECT 
    TABLE_NAME as 'Table Name',
    TABLE_TYPE as 'Type',
    ENGINE as 'Engine',
    TABLE_ROWS as 'Row Count',
    ROUND(DATA_LENGTH/1024/1024, 2) as 'Data Size (MB)'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'data_crm'
ORDER BY TABLE_NAME;

-- =====================================================
-- SECTION 2: CHECK SPECIFIC TABLES
-- =====================================================

SELECT 'SECTION 2: SPECIFIC TABLE CHECKS' AS Status;
SELECT '==================================' AS Status;

-- Check if sub_categories table exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'sub_categories table EXISTS'
        ELSE 'sub_categories table DOES NOT EXIST'
    END AS 'sub_categories_status'
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'sub_categories';

-- Check if subcategories table exists (without underscore)
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'subcategories table EXISTS'
        ELSE 'subcategories table DOES NOT EXIST'
    END AS 'subcategories_status'
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'subcategories';

-- Check if firm_natures table exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'firm_natures table EXISTS'
        ELSE 'firm_natures table DOES NOT EXIST'
    END AS 'firm_natures_status'
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'firm_natures';

-- Check if persons table exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'persons table EXISTS'
        ELSE 'persons table DOES NOT EXIST'
    END AS 'persons_status'
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'persons';

-- =====================================================
-- SECTION 3: CHECK COLUMNS IN PERSONS TABLE
-- =====================================================

SELECT 'SECTION 3: PERSONS TABLE COLUMNS' AS Status;
SELECT '==================================' AS Status;

-- Check if persons table has sub_category_id column
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'persons.sub_category_id column EXISTS'
        ELSE 'persons.sub_category_id column DOES NOT EXIST'
    END AS 'sub_category_id_status'
FROM information_schema.columns 
WHERE table_schema = 'data_crm' AND table_name = 'persons' AND column_name = 'sub_category_id';

-- Check if persons table has subcategory_id column (without underscore)
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'persons.subcategory_id column EXISTS'
        ELSE 'persons.subcategory_id column DOES NOT EXIST'
    END AS 'subcategory_id_status'
FROM information_schema.columns 
WHERE table_schema = 'data_crm' AND table_name = 'persons' AND column_name = 'subcategory_id';

-- Check if persons table has firm_nature_id column
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'persons.firm_nature_id column EXISTS'
        ELSE 'persons.firm_nature_id column DOES NOT EXIST'
    END AS 'firm_nature_id_status'
FROM information_schema.columns 
WHERE table_schema = 'data_crm' AND table_name = 'persons' AND column_name = 'firm_nature_id';

-- =====================================================
-- SECTION 4: LIST ALL COLUMNS IN PERSONS TABLE
-- =====================================================

SELECT 'SECTION 4: ALL COLUMNS IN PERSONS TABLE' AS Status;
SELECT '=========================================' AS Status;

SELECT 
    COLUMN_NAME as 'Column Name',
    DATA_TYPE as 'Data Type',
    IS_NULLABLE as 'Nullable',
    COLUMN_DEFAULT as 'Default Value',
    COLUMN_KEY as 'Key Type'
FROM information_schema.columns 
WHERE table_schema = 'data_crm' AND table_name = 'persons'
ORDER BY ORDINAL_POSITION;

-- =====================================================
-- SECTION 5: CHECK FOREIGN KEY CONSTRAINTS
-- =====================================================

SELECT 'SECTION 5: FOREIGN KEY CONSTRAINTS' AS Status;
SELECT '===================================' AS Status;

SELECT 
    CONSTRAINT_NAME as 'Constraint Name',
    TABLE_NAME as 'Table',
    COLUMN_NAME as 'Column',
    REFERENCED_TABLE_NAME as 'Referenced Table',
    REFERENCED_COLUMN_NAME as 'Referenced Column'
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'data_crm' 
AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY TABLE_NAME, COLUMN_NAME;

-- =====================================================
-- SECTION 6: CHECK INDEXES
-- =====================================================

SELECT 'SECTION 6: INDEXES ON RELEVANT TABLES' AS Status;
SELECT '====================================' AS Status;

-- Indexes on persons table
SELECT 
    INDEX_NAME as 'Index Name',
    COLUMN_NAME as 'Column',
    NON_UNIQUE as 'Non Unique',
    SEQ_IN_INDEX as 'Sequence'
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'data_crm' 
AND TABLE_NAME = 'persons'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- Indexes on sub_categories table (if it exists)
SELECT 
    INDEX_NAME as 'Index Name',
    COLUMN_NAME as 'Column',
    NON_UNIQUE as 'Non Unique',
    SEQ_IN_INDEX as 'Sequence'
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'data_crm' 
AND TABLE_NAME = 'sub_categories'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- =====================================================
-- SECTION 7: SAMPLE DATA COUNTS
-- =====================================================

SELECT 'SECTION 7: RECORD COUNTS' AS Status;
SELECT '=========================' AS Status;

-- Count records in each table (if they exist)
SET @sql = 'SELECT "No sub_categories table found" as result';
SELECT @sql := CONCAT('SELECT COUNT(*) as sub_categories_count FROM ', table_name) 
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'sub_categories';

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'SELECT "No persons table found" as result';
SELECT @sql := CONCAT('SELECT COUNT(*) as persons_count FROM ', table_name) 
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'persons';

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- FINAL SUMMARY
-- =====================================================

SELECT '==================================================' AS Status;
SELECT 'VERIFICATION COMPLETED' AS Status;
SELECT '==================================================' AS Status;
SELECT 'Run this script to understand your current database structure' AS Status;
SELECT 'before running the migration script.' AS Status;
SELECT '' AS Status;
