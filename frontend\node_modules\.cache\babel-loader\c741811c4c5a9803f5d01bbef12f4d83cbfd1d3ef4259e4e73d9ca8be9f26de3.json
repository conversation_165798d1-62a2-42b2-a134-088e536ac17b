{"ast": null, "code": "import{useState}from'react';import FormBuilder from'./forms/FormBuilder';import DynamicPersonForm from'./forms/DynamicPersonForm';import PersonList from'./PersonList';import ImportPersons from'./import/ImportPersons';import AllFormsModal from'./forms/AllFormsModal';import'./PersonManagement.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PersonManagement=()=>{const[currentView,setCurrentView]=useState('list');// 'list', 'create', 'edit', 'formBuilder', 'import', 'allForms'\nconst[selectedPerson,setSelectedPerson]=useState(null);const[selectedForm,setSelectedForm]=useState(null);const[notification,setNotification]=useState(null);const showNotification=function(message){let type=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'success';setNotification({message,type});setTimeout(()=>setNotification(null),5000);};const handleCreatePerson=()=>{setSelectedPerson(null);setCurrentView('create');};const handleEditPerson=person=>{setSelectedPerson(person);setCurrentView('edit');};const handlePersonSubmit=()=>{const action=currentView==='create'?'created':'updated';showNotification(`Person ${action} successfully!`);setCurrentView('list');setSelectedPerson(null);};const handleFormBuilderOpen=()=>{setSelectedForm(null);// Clear any selected form for new form creation\nsetCurrentView('formBuilder');};const handleFormBuilderSave=config=>{const action=selectedForm?'updated':'created';showNotification(`Form configuration \"${config.name}\" ${action} successfully!`);setSelectedForm(null);setCurrentView('list');};const handleImportOpen=()=>{setCurrentView('import');};const handleImportSuccess=results=>{showNotification(`Import completed! ${results.successfulRows} persons imported successfully.`);setCurrentView('list');// Refresh the person list if we're on the list view\n};const handleImportClose=()=>{setCurrentView('list');};const handleViewForms=()=>{setCurrentView('create');// This will trigger the form selection view\n};const handleAllFormsOpen=()=>{setCurrentView('allForms');};const handleAllFormsClose=()=>{setCurrentView('list');};const handleEditFormFromModal=form=>{// Set the selected form for editing\nsetSelectedForm(form);setCurrentView('formBuilder');};const handleDeleteFormFromModal=form=>{showNotification(`Form \"${form.name}\" deleted successfully!`,'success');};const handleCancel=()=>{setCurrentView('list');setSelectedPerson(null);setSelectedForm(null);};const renderCurrentView=()=>{switch(currentView){case'create':return/*#__PURE__*/_jsx(DynamicPersonForm,{mode:\"create\",onSubmit:handlePersonSubmit,onCancel:handleCancel});case'edit':return/*#__PURE__*/_jsx(DynamicPersonForm,{mode:\"edit\",initialData:selectedPerson,onSubmit:handlePersonSubmit,onCancel:handleCancel});case'formBuilder':return/*#__PURE__*/_jsx(FormBuilder,{onSave:handleFormBuilderSave,onCancel:handleCancel,initialConfig:selectedForm});case'import':return/*#__PURE__*/_jsx(ImportPersons,{onClose:handleImportClose,onSuccess:handleImportSuccess,onViewForms:handleViewForms});case'allForms':return/*#__PURE__*/_jsx(AllFormsModal,{onClose:handleAllFormsClose,onEditForm:handleEditFormFromModal,onDeleteForm:handleDeleteFormFromModal,isInline:true});case'list':default:return/*#__PURE__*/_jsx(PersonList,{onEditPerson:handleEditPerson});}};return/*#__PURE__*/_jsxs(\"div\",{className:\"person-management\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"management-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"header-content\",children:/*#__PURE__*/_jsx(\"h1\",{children:\"Person Management System\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"header-nav\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentView('list'),className:`nav-btn ${currentView==='list'?'active':''}`,children:\"\\uD83D\\uDCCB Person List\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleCreatePerson,className:`nav-btn ${currentView==='create'?'active':''}`,children:\"\\u2795 Create Person\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleFormBuilderOpen,className:`nav-btn ${currentView==='formBuilder'?'active':''}`,children:\"\\uD83D\\uDD27 Form Builder\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleImportOpen,className:`nav-btn ${currentView==='import'?'active':''}`,children:\"\\uD83D\\uDCE5 Import Persons\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleAllFormsOpen,className:`nav-btn ${currentView==='allForms'?'active':''}`,children:\"\\uD83D\\uDCCB All Forms\"})]})]}),notification&&/*#__PURE__*/_jsxs(\"div\",{className:`notification ${notification.type}`,children:[/*#__PURE__*/_jsx(\"span\",{children:notification.message}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setNotification(null),className:\"notification-close\",children:\"\\xD7\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"management-content\",children:renderCurrentView()})]});};export default PersonManagement;", "map": {"version": 3, "names": ["useState", "FormBuilder", "DynamicPersonForm", "PersonList", "<PERSON><PERSON>rt<PERSON><PERSON><PERSON>", "AllFormsModal", "jsx", "_jsx", "jsxs", "_jsxs", "PersonManagement", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "<PERSON><PERSON><PERSON>", "setSelected<PERSON><PERSON>", "selectedForm", "setSelectedForm", "notification", "setNotification", "showNotification", "message", "type", "arguments", "length", "undefined", "setTimeout", "handleCreate<PERSON>erson", "handleEditPerson", "person", "handlePersonSubmit", "action", "handleFormBuilderOpen", "handleFormBuilderSave", "config", "name", "handleImportOpen", "handleImportSuccess", "results", "successfulRows", "handleImportClose", "handleViewForms", "handleAllFormsOpen", "handleAllFormsClose", "handleEditFormFromModal", "form", "handleDeleteFormFromModal", "handleCancel", "renderCurrentView", "mode", "onSubmit", "onCancel", "initialData", "onSave", "initialConfig", "onClose", "onSuccess", "onViewForms", "onEditForm", "onDeleteForm", "isInline", "onEdit<PERSON>erson", "className", "children", "onClick"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/PersonManagement.js"], "sourcesContent": ["import { useState } from 'react';\nimport FormBuilder from './forms/FormBuilder';\nimport DynamicPersonForm from './forms/DynamicPersonForm';\nimport PersonList from './PersonList';\nimport ImportPersons from './import/ImportPersons';\nimport AllFormsModal from './forms/AllFormsModal';\n\nimport './PersonManagement.css';\n\nconst PersonManagement = () => {\n  const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'formBuilder', 'import', 'allForms'\n  const [selectedPerson, setSelectedPerson] = useState(null);\n  const [selectedForm, setSelectedForm] = useState(null);\n  const [notification, setNotification] = useState(null);\n\n  const showNotification = (message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification(null), 5000);\n  };\n\n  const handleCreatePerson = () => {\n    setSelectedPerson(null);\n    setCurrentView('create');\n  };\n\n  const handleEditPerson = (person) => {\n    setSelectedPerson(person);\n    setCurrentView('edit');\n  };\n\n  const handlePersonSubmit = () => {\n    const action = currentView === 'create' ? 'created' : 'updated';\n    showNotification(`Person ${action} successfully!`);\n    setCurrentView('list');\n    setSelectedPerson(null);\n  };\n\n  const handleFormBuilderOpen = () => {\n    setSelectedForm(null); // Clear any selected form for new form creation\n    setCurrentView('formBuilder');\n  };\n\n  const handleFormBuilderSave = (config) => {\n    const action = selectedForm ? 'updated' : 'created';\n    showNotification(`Form configuration \"${config.name}\" ${action} successfully!`);\n    setSelectedForm(null);\n    setCurrentView('list');\n  };\n\n  const handleImportOpen = () => {\n    setCurrentView('import');\n  };\n\n  const handleImportSuccess = (results) => {\n    showNotification(`Import completed! ${results.successfulRows} persons imported successfully.`);\n    setCurrentView('list');\n    // Refresh the person list if we're on the list view\n  };\n\n  const handleImportClose = () => {\n    setCurrentView('list');\n  };\n\n  const handleViewForms = () => {\n    setCurrentView('create'); // This will trigger the form selection view\n  };\n\n  const handleAllFormsOpen = () => {\n    setCurrentView('allForms');\n  };\n\n  const handleAllFormsClose = () => {\n    setCurrentView('list');\n  };\n\n  const handleEditFormFromModal = (form) => {\n    // Set the selected form for editing\n    setSelectedForm(form);\n    setCurrentView('formBuilder');\n  };\n\n  const handleDeleteFormFromModal = (form) => {\n    showNotification(`Form \"${form.name}\" deleted successfully!`, 'success');\n  };\n\n  const handleCancel = () => {\n    setCurrentView('list');\n    setSelectedPerson(null);\n    setSelectedForm(null);\n  };\n\n  const renderCurrentView = () => {\n    switch (currentView) {\n      case 'create':\n        return (\n          <DynamicPersonForm\n            mode=\"create\"\n            onSubmit={handlePersonSubmit}\n            onCancel={handleCancel}\n          />\n        );\n      \n      case 'edit':\n        return (\n          <DynamicPersonForm\n            mode=\"edit\"\n            initialData={selectedPerson}\n            onSubmit={handlePersonSubmit}\n            onCancel={handleCancel}\n          />\n        );\n      \n      case 'formBuilder':\n        return (\n          <FormBuilder\n            onSave={handleFormBuilderSave}\n            onCancel={handleCancel}\n            initialConfig={selectedForm}\n          />\n        );\n\n      case 'import':\n        return (\n          <ImportPersons\n            onClose={handleImportClose}\n            onSuccess={handleImportSuccess}\n            onViewForms={handleViewForms}\n          />\n        );\n\n      case 'allForms':\n        return (\n          <AllFormsModal\n            onClose={handleAllFormsClose}\n            onEditForm={handleEditFormFromModal}\n            onDeleteForm={handleDeleteFormFromModal}\n            isInline={true}\n          />\n        );\n\n      case 'list':\n      default:\n        return (\n          <PersonList\n            onEditPerson={handleEditPerson}\n          />\n        );\n    }\n  };\n\n  return (\n    <div className=\"person-management\">\n      {/* Header */}\n      <div className=\"management-header\">\n        <div className=\"header-content\">\n          <h1>Person Management System</h1>\n        </div>\n        \n        {/* Navigation */}\n        <div className=\"header-nav\">\n          <button\n            onClick={() => setCurrentView('list')}\n            className={`nav-btn ${currentView === 'list' ? 'active' : ''}`}\n          >\n            📋 Person List\n          </button>\n          <button\n            onClick={handleCreatePerson}\n            className={`nav-btn ${currentView === 'create' ? 'active' : ''}`}\n          >\n            ➕ Create Person\n          </button>\n          <button\n            onClick={handleFormBuilderOpen}\n            className={`nav-btn ${currentView === 'formBuilder' ? 'active' : ''}`}\n          >\n            🔧 Form Builder\n          </button>\n          <button\n            onClick={handleImportOpen}\n            className={`nav-btn ${currentView === 'import' ? 'active' : ''}`}\n          >\n            📥 Import Persons\n          </button>\n          <button\n            onClick={handleAllFormsOpen}\n            className={`nav-btn ${currentView === 'allForms' ? 'active' : ''}`}\n          >\n            📋 All Forms\n          </button>\n        </div>\n      </div>\n\n      {/* Notification */}\n      {notification && (\n        <div className={`notification ${notification.type}`}>\n          <span>{notification.message}</span>\n          <button onClick={() => setNotification(null)} className=\"notification-close\">\n            ×\n          </button>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className=\"management-content\">\n        {renderCurrentView()}\n      </div>\n    </div>\n  );\n};\n\nexport default PersonManagement;\n"], "mappings": "AAAA,OAASA,QAAQ,KAAQ,OAAO,CAChC,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,iBAAiB,KAAM,2BAA2B,CACzD,MAAO,CAAAC,UAAU,KAAM,cAAc,CACrC,MAAO,CAAAC,aAAa,KAAM,wBAAwB,CAClD,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CAEjD,MAAO,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhC,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGZ,QAAQ,CAAC,MAAM,CAAC,CAAE;AACxD,KAAM,CAACa,cAAc,CAAEC,iBAAiB,CAAC,CAAGd,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACe,YAAY,CAAEC,eAAe,CAAC,CAAGhB,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACiB,YAAY,CAAEC,eAAe,CAAC,CAAGlB,QAAQ,CAAC,IAAI,CAAC,CAEtD,KAAM,CAAAmB,gBAAgB,CAAG,QAAAA,CAACC,OAAO,CAAuB,IAArB,CAAAC,IAAI,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,SAAS,CACjDJ,eAAe,CAAC,CAAEE,OAAO,CAAEC,IAAK,CAAC,CAAC,CAClCI,UAAU,CAAC,IAAMP,eAAe,CAAC,IAAI,CAAC,CAAE,IAAI,CAAC,CAC/C,CAAC,CAED,KAAM,CAAAQ,kBAAkB,CAAGA,CAAA,GAAM,CAC/BZ,iBAAiB,CAAC,IAAI,CAAC,CACvBF,cAAc,CAAC,QAAQ,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAe,gBAAgB,CAAIC,MAAM,EAAK,CACnCd,iBAAiB,CAACc,MAAM,CAAC,CACzBhB,cAAc,CAAC,MAAM,CAAC,CACxB,CAAC,CAED,KAAM,CAAAiB,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,MAAM,CAAGnB,WAAW,GAAK,QAAQ,CAAG,SAAS,CAAG,SAAS,CAC/DQ,gBAAgB,CAAC,UAAUW,MAAM,gBAAgB,CAAC,CAClDlB,cAAc,CAAC,MAAM,CAAC,CACtBE,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED,KAAM,CAAAiB,qBAAqB,CAAGA,CAAA,GAAM,CAClCf,eAAe,CAAC,IAAI,CAAC,CAAE;AACvBJ,cAAc,CAAC,aAAa,CAAC,CAC/B,CAAC,CAED,KAAM,CAAAoB,qBAAqB,CAAIC,MAAM,EAAK,CACxC,KAAM,CAAAH,MAAM,CAAGf,YAAY,CAAG,SAAS,CAAG,SAAS,CACnDI,gBAAgB,CAAC,uBAAuBc,MAAM,CAACC,IAAI,KAAKJ,MAAM,gBAAgB,CAAC,CAC/Ed,eAAe,CAAC,IAAI,CAAC,CACrBJ,cAAc,CAAC,MAAM,CAAC,CACxB,CAAC,CAED,KAAM,CAAAuB,gBAAgB,CAAGA,CAAA,GAAM,CAC7BvB,cAAc,CAAC,QAAQ,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAwB,mBAAmB,CAAIC,OAAO,EAAK,CACvClB,gBAAgB,CAAC,qBAAqBkB,OAAO,CAACC,cAAc,iCAAiC,CAAC,CAC9F1B,cAAc,CAAC,MAAM,CAAC,CACtB;AACF,CAAC,CAED,KAAM,CAAA2B,iBAAiB,CAAGA,CAAA,GAAM,CAC9B3B,cAAc,CAAC,MAAM,CAAC,CACxB,CAAC,CAED,KAAM,CAAA4B,eAAe,CAAGA,CAAA,GAAM,CAC5B5B,cAAc,CAAC,QAAQ,CAAC,CAAE;AAC5B,CAAC,CAED,KAAM,CAAA6B,kBAAkB,CAAGA,CAAA,GAAM,CAC/B7B,cAAc,CAAC,UAAU,CAAC,CAC5B,CAAC,CAED,KAAM,CAAA8B,mBAAmB,CAAGA,CAAA,GAAM,CAChC9B,cAAc,CAAC,MAAM,CAAC,CACxB,CAAC,CAED,KAAM,CAAA+B,uBAAuB,CAAIC,IAAI,EAAK,CACxC;AACA5B,eAAe,CAAC4B,IAAI,CAAC,CACrBhC,cAAc,CAAC,aAAa,CAAC,CAC/B,CAAC,CAED,KAAM,CAAAiC,yBAAyB,CAAID,IAAI,EAAK,CAC1CzB,gBAAgB,CAAC,SAASyB,IAAI,CAACV,IAAI,yBAAyB,CAAE,SAAS,CAAC,CAC1E,CAAC,CAED,KAAM,CAAAY,YAAY,CAAGA,CAAA,GAAM,CACzBlC,cAAc,CAAC,MAAM,CAAC,CACtBE,iBAAiB,CAAC,IAAI,CAAC,CACvBE,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED,KAAM,CAAA+B,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,OAAQpC,WAAW,EACjB,IAAK,QAAQ,CACX,mBACEJ,IAAA,CAACL,iBAAiB,EAChB8C,IAAI,CAAC,QAAQ,CACbC,QAAQ,CAAEpB,kBAAmB,CAC7BqB,QAAQ,CAAEJ,YAAa,CACxB,CAAC,CAGN,IAAK,MAAM,CACT,mBACEvC,IAAA,CAACL,iBAAiB,EAChB8C,IAAI,CAAC,MAAM,CACXG,WAAW,CAAEtC,cAAe,CAC5BoC,QAAQ,CAAEpB,kBAAmB,CAC7BqB,QAAQ,CAAEJ,YAAa,CACxB,CAAC,CAGN,IAAK,aAAa,CAChB,mBACEvC,IAAA,CAACN,WAAW,EACVmD,MAAM,CAAEpB,qBAAsB,CAC9BkB,QAAQ,CAAEJ,YAAa,CACvBO,aAAa,CAAEtC,YAAa,CAC7B,CAAC,CAGN,IAAK,QAAQ,CACX,mBACER,IAAA,CAACH,aAAa,EACZkD,OAAO,CAAEf,iBAAkB,CAC3BgB,SAAS,CAAEnB,mBAAoB,CAC/BoB,WAAW,CAAEhB,eAAgB,CAC9B,CAAC,CAGN,IAAK,UAAU,CACb,mBACEjC,IAAA,CAACF,aAAa,EACZiD,OAAO,CAAEZ,mBAAoB,CAC7Be,UAAU,CAAEd,uBAAwB,CACpCe,YAAY,CAAEb,yBAA0B,CACxCc,QAAQ,CAAE,IAAK,CAChB,CAAC,CAGN,IAAK,MAAM,CACX,QACE,mBACEpD,IAAA,CAACJ,UAAU,EACTyD,YAAY,CAAEjC,gBAAiB,CAChC,CAAC,CAER,CACF,CAAC,CAED,mBACElB,KAAA,QAAKoD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAEhCrD,KAAA,QAAKoD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvD,IAAA,QAAKsD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BvD,IAAA,OAAAuD,QAAA,CAAI,0BAAwB,CAAI,CAAC,CAC9B,CAAC,cAGNrD,KAAA,QAAKoD,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvD,IAAA,WACEwD,OAAO,CAAEA,CAAA,GAAMnD,cAAc,CAAC,MAAM,CAAE,CACtCiD,SAAS,CAAE,WAAWlD,WAAW,GAAK,MAAM,CAAG,QAAQ,CAAG,EAAE,EAAG,CAAAmD,QAAA,CAChE,0BAED,CAAQ,CAAC,cACTvD,IAAA,WACEwD,OAAO,CAAErC,kBAAmB,CAC5BmC,SAAS,CAAE,WAAWlD,WAAW,GAAK,QAAQ,CAAG,QAAQ,CAAG,EAAE,EAAG,CAAAmD,QAAA,CAClE,sBAED,CAAQ,CAAC,cACTvD,IAAA,WACEwD,OAAO,CAAEhC,qBAAsB,CAC/B8B,SAAS,CAAE,WAAWlD,WAAW,GAAK,aAAa,CAAG,QAAQ,CAAG,EAAE,EAAG,CAAAmD,QAAA,CACvE,2BAED,CAAQ,CAAC,cACTvD,IAAA,WACEwD,OAAO,CAAE5B,gBAAiB,CAC1B0B,SAAS,CAAE,WAAWlD,WAAW,GAAK,QAAQ,CAAG,QAAQ,CAAG,EAAE,EAAG,CAAAmD,QAAA,CAClE,6BAED,CAAQ,CAAC,cACTvD,IAAA,WACEwD,OAAO,CAAEtB,kBAAmB,CAC5BoB,SAAS,CAAE,WAAWlD,WAAW,GAAK,UAAU,CAAG,QAAQ,CAAG,EAAE,EAAG,CAAAmD,QAAA,CACpE,wBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CAGL7C,YAAY,eACXR,KAAA,QAAKoD,SAAS,CAAE,gBAAgB5C,YAAY,CAACI,IAAI,EAAG,CAAAyC,QAAA,eAClDvD,IAAA,SAAAuD,QAAA,CAAO7C,YAAY,CAACG,OAAO,CAAO,CAAC,cACnCb,IAAA,WAAQwD,OAAO,CAAEA,CAAA,GAAM7C,eAAe,CAAC,IAAI,CAAE,CAAC2C,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,MAE7E,CAAQ,CAAC,EACN,CACN,cAGDvD,IAAA,QAAKsD,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAChCf,iBAAiB,CAAC,CAAC,CACjB,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAArC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}