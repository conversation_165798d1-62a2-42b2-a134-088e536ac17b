import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON>ie<PERSON>hart,
  FiBarChart2,
  FiGrid,
  FiLayers,
  FiRefreshCw
} from 'react-icons/fi';
import axios from 'axios';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const API_BASE_URL = 'http://localhost:5000/api';

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    totalDivisions: 0,
    totalCategories: 0,
    totalFirmNatures: 0,
    totalStates: 0,
    divisions: [],
    categories: [],
    firmNatures: [],
    states: []
  });

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      const [
        divisionsRes,
        categoriesRes,
        firmNaturesRes,
        statesRes
      ] = await Promise.all([
        axios.get(`${API_BASE_URL}/divisions`),
        axios.get(`${API_BASE_URL}/categories`),
        axios.get(`${API_BASE_URL}/firmnatures`),
        axios.get(`${API_BASE_URL}/states`)
      ]);

      const divisions = divisionsRes.data;
      const categories = categoriesRes.data;
      const firmNatures = firmNaturesRes.data;
      const states = statesRes.data;

      setDashboardData({
        totalDivisions: divisions.length,
        totalCategories: categories.length,
        totalFirmNatures: firmNatures.length,
        totalStates: states.length,
        divisions,
        categories,
        firmNatures,
        states
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setDashboardData({
        totalDivisions: 0,
        totalCategories: 0,
        totalFirmNatures: 0,
        totalStates: 0,
        divisions: [],
        categories: [],
        firmNatures: [],
        states: []
      });
    } finally {
      setLoading(false);
    }
  };

  const getRandomColor = () => {
    const colors = [
      '#667eea', '#764ba2', '#f093fb', '#f5576c',
      '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
      '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
    setRefreshing(false);
  };

  // Chart configurations
  const divisionsChartData = {
    labels: dashboardData.divisions.map(item => item.name),
    datasets: [
      {
        label: 'Divisions',
        data: dashboardData.divisions.map((_, index) => index + 1),
        backgroundColor: dashboardData.divisions.map(() => getRandomColor()),
        borderRadius: 8
      }
    ]
  };

  const categoriesChartData = {
    labels: dashboardData.categories.map(item => item.name),
    datasets: [
      {
        label: 'Categories',
        data: dashboardData.categories.map((_, index) => index + 1),
        backgroundColor: dashboardData.categories.map(() => getRandomColor()),
        borderRadius: 8
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          padding: 20,
          usePointStyle: true
        }
      }
    }
  };

  if (loading) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner-large"></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  return (
    <div className="dashboard">
      {/* Dashboard Header */}
      <motion.div
        className="dashboard-header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="dashboard-title">
          <h1>📊 System Overview</h1>
          <p>Overview of your CRM system configuration</p>
        </div>

        <div className="dashboard-controls">
          <button
            className="btn btn-secondary dashboard-btn"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <FiRefreshCw className={refreshing ? 'spinning' : ''} />
            Refresh
          </button>
        </div>
      </motion.div>

      {/* Key Metrics Cards */}
      <div className="metrics-grid">
        {[
          { title: 'Divisions', value: dashboardData.totalDivisions, icon: FiGrid, color: '#22c55e' },
          { title: 'Categories', value: dashboardData.totalCategories, icon: FiLayers, color: '#f59e0b' },
          { title: 'Firm Natures', value: dashboardData.totalFirmNatures, icon: FiBarChart2, color: '#8b5cf6' },
          { title: 'States', value: dashboardData.totalStates, icon: FiPieChart, color: '#ef4444' }
        ].map((metric, index) => (
          <motion.div
            key={metric.title}
            className="metric-card"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            whileHover={{ scale: 1.05 }}
          >
            <div className="metric-icon" style={{ backgroundColor: metric.color }}>
              <metric.icon size={24} />
            </div>
            <div className="metric-content">
              <h3>{metric.value.toLocaleString()}</h3>
              <p>{metric.title}</p>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="charts-grid">
        {/* Division Overview */}
        <motion.div
          className="chart-card"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="chart-header">
            <h3><FiGrid /> Divisions Overview</h3>
          </div>
          <div className="chart-container">
            <Bar data={divisionsChartData} options={chartOptions} />
          </div>
        </motion.div>

        {/* Categories Overview */}
        <motion.div
          className="chart-card"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <div className="chart-header">
            <h3><FiLayers /> Categories Overview</h3>
          </div>
          <div className="chart-container">
            <Bar data={categoriesChartData} options={chartOptions} />
          </div>
        </motion.div>
      </div>

      {/* Data Tables Section */}
      <div className="tables-grid">
        {/* Divisions List */}
        <motion.div
          className="table-card"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="table-header">
            <h3><FiGrid /> Divisions</h3>
          </div>
          <div className="table-container">
            <table className="dashboard-table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Created</th>
                </tr>
              </thead>
              <tbody>
                {dashboardData.divisions.slice(0, 10).map(division => (
                  <tr key={division.id}>
                    <td>{division.name}</td>
                    <td>{new Date(division.createdAt).toLocaleDateString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </motion.div>

        {/* Categories List */}
        <motion.div
          className="table-card"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <div className="table-header">
            <h3><FiLayers /> Categories</h3>
          </div>
          <div className="table-container">
            <table className="dashboard-table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Division</th>
                  <th>Created</th>
                </tr>
              </thead>
              <tbody>
                {dashboardData.categories.slice(0, 10).map(category => (
                  <tr key={category.id}>
                    <td>{category.name}</td>
                    <td>{category.division?.name || '-'}</td>
                    <td>{new Date(category.createdAt).toLocaleDateString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Dashboard;