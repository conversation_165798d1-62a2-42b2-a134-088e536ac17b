namespace CrmApi.Services.Dashboard
{
    public interface IDashboardService
    {
        Task<DashboardStatsResponse> GetDashboardStatsAsync();
        Task<DashboardSummaryResponse> GetSummaryAsync();
    }

    public class DashboardStatsResponse
    {
        public int TotalDivisions { get; set; }
        public int TotalCategories { get; set; }
        public int TotalFirmNatures { get; set; }
        public int TotalStates { get; set; }
    }

    public class DashboardSummaryResponse
    {
        public int TotalDivisions { get; set; }
        public int TotalCategories { get; set; }
        public int TotalFirmNatures { get; set; }
        public int TotalStates { get; set; }
    }
}
