{"ast": null, "code": "import React,{useState,useRef}from'react';import*as XLSX from'xlsx';import apiService from'../../services/apiService';import'./FileUpload.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FileUpload=_ref=>{let{onFileUpload,importSettings,onSettingsChange,onBack,error}=_ref;const[dragActive,setDragActive]=useState(false);const[selectedFile,setSelectedFile]=useState(null);const[fileInfo,setFileInfo]=useState(null);const[parsing,setParsing]=useState(false);const[parseError,setParseError]=useState(null);const fileInputRef=useRef(null);const handleDrag=e=>{e.preventDefault();e.stopPropagation();if(e.type==='dragenter'||e.type==='dragover'){setDragActive(true);}else if(e.type==='dragleave'){setDragActive(false);}};const handleDrop=e=>{e.preventDefault();e.stopPropagation();setDragActive(false);if(e.dataTransfer.files&&e.dataTransfer.files[0]){handleFileSelect(e.dataTransfer.files[0]);}};const handleFileInputChange=e=>{if(e.target.files&&e.target.files[0]){handleFileSelect(e.target.files[0]);}};const handleFileSelect=file=>{// Validate file type\nconst allowedTypes=['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',// .xlsx\n'application/vnd.ms-excel',// .xls\n'text/csv'// .csv\n];const allowedExtensions=['.xlsx','.xls','.csv'];const fileExtension=file.name.toLowerCase().substring(file.name.lastIndexOf('.'));if(!allowedTypes.includes(file.type)&&!allowedExtensions.includes(fileExtension)){setParseError('Please select a valid Excel (.xlsx, .xls) or CSV file');return;}// Validate file size (10MB limit)\nif(file.size>10*1024*1024){setParseError('File size must be less than 10MB');return;}setSelectedFile(file);setFileInfo({name:file.name,size:formatFileSize(file.size),type:file.type||'Unknown',lastModified:new Date(file.lastModified).toLocaleDateString()});setParseError(null);};const parseFile=async()=>{if(!selectedFile)return;setParsing(true);setParseError(null);try{const headers=await extractHeaders(selectedFile);onFileUpload(selectedFile,headers);}catch(err){console.error('File parsing error:',err);setParseError(err.message||'Failed to parse file');}finally{setParsing(false);}};const extractHeaders=file=>{return new Promise((resolve,reject)=>{const reader=new FileReader();reader.onload=e=>{try{const data=e.target.result;let headers=[];if(file.name.toLowerCase().endsWith('.csv')){// Parse CSV\nconst lines=data.split('\\n');if(lines.length>0){headers=lines[0].split(',').map(header=>header.trim().replace(/\"/g,''));}}else{// Parse Excel with better error handling\nconsole.log('Parsing Excel file:',file.name,'Size:',file.size,'Type:',file.type);try{// Check if the data looks like HTML\nif(typeof data==='string'&&(data.includes('<html>')||data.includes('<table>'))){throw new Error('HTML file detected. Please upload a proper Excel (.xlsx, .xls) file.');}const workbook=XLSX.read(data,{type:'binary'});console.log('Workbook loaded successfully');if(!workbook.SheetNames||workbook.SheetNames.length===0){throw new Error('No sheets found in the Excel file. The file might be corrupted.');}console.log('Available sheets:',workbook.SheetNames);const firstSheetName=workbook.SheetNames[0];const worksheet=workbook.Sheets[firstSheetName];if(!worksheet){throw new Error(`Could not read the sheet \"${firstSheetName}\".`);}const jsonData=XLSX.utils.sheet_to_json(worksheet,{header:1});console.log('Extracted',jsonData.length,'rows from Excel');if(jsonData.length>0){headers=jsonData[0].map(header=>String(header||'').trim());console.log('Headers found:',headers);}else{throw new Error('The Excel file appears to be empty.');}}catch(xlsxError){console.error('Excel parsing error:',xlsxError);if(xlsxError.message&&xlsxError.message.toLowerCase().includes('table')){throw new Error('Invalid file format. The file contains HTML content instead of Excel data. Please upload a proper .xlsx or .xls file.');}if(xlsxError.message&&xlsxError.message.includes('ZIP')){throw new Error('The Excel file is corrupted or not a valid .xlsx file. Please try re-saving the file in Excel.');}throw new Error(`Excel parsing failed: ${xlsxError.message}`);}}if(headers.length===0){reject(new Error('No headers found in file'));return;}resolve(headers.filter(header=>header!==''));}catch(err){reject(new Error('Failed to parse file: '+err.message));}};reader.onerror=()=>{reject(new Error('Failed to read file'));};if(file.name.toLowerCase().endsWith('.csv')){reader.readAsText(file);}else{reader.readAsBinaryString(file);}});};const formatFileSize=bytes=>{if(bytes===0)return'0 Bytes';const k=1024;const sizes=['Bytes','KB','MB','GB'];const i=Math.floor(Math.log(bytes)/Math.log(k));return parseFloat((bytes/Math.pow(k,i)).toFixed(2))+' '+sizes[i];};const downloadTemplate=async()=>{try{const response=await fetch(`${apiService.baseURL}/import-export/persons/template?format=Excel&includeSampleData=true`);if(!response.ok){throw new Error('Failed to download template');}const blob=await response.blob();const url=window.URL.createObjectURL(blob);const a=document.createElement('a');a.href=url;a.download='person_import_template.xlsx';document.body.appendChild(a);a.click();window.URL.revokeObjectURL(url);document.body.removeChild(a);}catch(err){console.error('Template download error:',err);setParseError('Failed to download template');}};return/*#__PURE__*/_jsxs(\"div\",{className:\"file-upload\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"upload-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Upload File\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"template-section\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"Don't have a file? Download our template to get started:\"}),/*#__PURE__*/_jsx(\"button\",{onClick:downloadTemplate,className:\"btn btn-outline\",children:\"\\uD83D\\uDCE5 Download Template\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:`drop-zone ${dragActive?'active':''} ${selectedFile?'has-file':''}`,onDragEnter:handleDrag,onDragLeave:handleDrag,onDragOver:handleDrag,onDrop:handleDrop,onClick:()=>{var _fileInputRef$current;return(_fileInputRef$current=fileInputRef.current)===null||_fileInputRef$current===void 0?void 0:_fileInputRef$current.click();},children:[/*#__PURE__*/_jsx(\"input\",{ref:fileInputRef,type:\"file\",accept:\".xlsx,.xls,.csv\",onChange:handleFileInputChange,style:{display:'none'}}),selectedFile?/*#__PURE__*/_jsxs(\"div\",{className:\"file-selected\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"file-icon\",children:\"\\uD83D\\uDCC4\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"file-details\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"file-name\",children:fileInfo.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"file-meta\",children:[/*#__PURE__*/_jsx(\"span\",{children:fileInfo.size}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u2022\"}),/*#__PURE__*/_jsx(\"span\",{children:fileInfo.lastModified})]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:e=>{e.stopPropagation();setSelectedFile(null);setFileInfo(null);},className:\"remove-file\",children:\"\\u2715\"})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"drop-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"drop-icon\",children:\"\\uD83D\\uDCC1\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"drop-text\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Click to upload\"}),\" or drag and drop\"]}),/*#__PURE__*/_jsx(\"p\",{children:\"Excel (.xlsx, .xls) or CSV files only\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Maximum file size: 10MB\"})]})]})]}),parseError&&/*#__PURE__*/_jsxs(\"div\",{className:\"error-message\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"error-icon\",children:\"\\u26A0\\uFE0F\"}),parseError]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Import Settings\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Import Mode:\"}),/*#__PURE__*/_jsxs(\"select\",{value:importSettings.importMode,onChange:e=>onSettingsChange('importMode',e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"SkipDuplicates\",children:\"Skip Duplicates\"}),/*#__PURE__*/_jsx(\"option\",{value:\"UpdateExisting\",children:\"Update Existing\"}),/*#__PURE__*/_jsx(\"option\",{value:\"FailOnDuplicates\",children:\"Fail on Duplicates\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-help\",children:[importSettings.importMode==='SkipDuplicates'&&'Skip records that already exist (based on mobile number + division + category)',importSettings.importMode==='UpdateExisting'&&'Update existing records with new data',importSettings.importMode==='FailOnDuplicates'&&'Stop import if duplicates are found']})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-group\",children:[/*#__PURE__*/_jsxs(\"label\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:importSettings.validateOnly,onChange:e=>onSettingsChange('validateOnly',e.target.checked)}),\"Validate Only (don't import data)\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"setting-help\",children:\"Check this to validate the file without actually importing the data\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Batch Size:\"}),/*#__PURE__*/_jsxs(\"select\",{value:importSettings.batchSize,onChange:e=>onSettingsChange('batchSize',parseInt(e.target.value)),children:[/*#__PURE__*/_jsx(\"option\",{value:50,children:\"50 records per batch\"}),/*#__PURE__*/_jsx(\"option\",{value:100,children:\"100 records per batch\"}),/*#__PURE__*/_jsx(\"option\",{value:200,children:\"200 records per batch\"}),/*#__PURE__*/_jsx(\"option\",{value:500,children:\"500 records per batch\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"setting-help\",children:\"Smaller batches are slower but more reliable for large files\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"upload-actions\",children:[onBack&&/*#__PURE__*/_jsx(\"button\",{onClick:onBack,disabled:parsing,className:\"btn btn-secondary\",children:\"\\u2190 Back to Division & Category\"}),/*#__PURE__*/_jsx(\"button\",{onClick:parseFile,disabled:!selectedFile||parsing,className:\"btn btn-primary\",children:parsing?'Processing...':'Continue to Field Mapping'})]})]});};export default FileUpload;", "map": {"version": 3, "names": ["React", "useState", "useRef", "XLSX", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "FileUpload", "_ref", "onFileUpload", "importSettings", "onSettingsChange", "onBack", "error", "dragActive", "setDragActive", "selectedFile", "setSelectedFile", "fileInfo", "setFileInfo", "parsing", "setParsing", "parseError", "setParseError", "fileInputRef", "handleDrag", "e", "preventDefault", "stopPropagation", "type", "handleDrop", "dataTransfer", "files", "handleFileSelect", "handleFileInputChange", "target", "file", "allowedTypes", "allowedExtensions", "fileExtension", "name", "toLowerCase", "substring", "lastIndexOf", "includes", "size", "formatFileSize", "lastModified", "Date", "toLocaleDateString", "parseFile", "headers", "extractHeaders", "err", "console", "message", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "data", "result", "endsWith", "lines", "split", "length", "map", "header", "trim", "replace", "log", "Error", "workbook", "read", "SheetNames", "firstSheetName", "worksheet", "Sheets", "jsonData", "utils", "sheet_to_json", "String", "xlsxError", "filter", "onerror", "readAsText", "readAsBinaryString", "bytes", "k", "sizes", "i", "Math", "floor", "parseFloat", "pow", "toFixed", "downloadTemplate", "response", "fetch", "baseURL", "ok", "blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "className", "children", "onClick", "onDragEnter", "onDragLeave", "onDragOver", "onDrop", "_fileInputRef$current", "current", "ref", "accept", "onChange", "style", "display", "value", "importMode", "checked", "validateOnly", "batchSize", "parseInt", "disabled"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/import/FileUpload.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport * as XLSX from 'xlsx';\nimport apiService from '../../services/apiService';\nimport './FileUpload.css';\n\nconst FileUpload = ({ onFileUpload, importSettings, onSettingsChange, onBack, error }) => {\n  const [dragActive, setDragActive] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [fileInfo, setFileInfo] = useState(null);\n  const [parsing, setParsing] = useState(false);\n  const [parseError, setParseError] = useState(null);\n  const fileInputRef = useRef(null);\n\n  const handleDrag = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === 'dragenter' || e.type === 'dragover') {\n      setDragActive(true);\n    } else if (e.type === 'dragleave') {\n      setDragActive(false);\n    }\n  };\n\n  const handleDrop = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n    \n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      handleFileSelect(e.dataTransfer.files[0]);\n    }\n  };\n\n  const handleFileInputChange = (e) => {\n    if (e.target.files && e.target.files[0]) {\n      handleFileSelect(e.target.files[0]);\n    }\n  };\n\n  const handleFileSelect = (file) => {\n    // Validate file type\n    const allowedTypes = [\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx\n      'application/vnd.ms-excel', // .xls\n      'text/csv' // .csv\n    ];\n\n    const allowedExtensions = ['.xlsx', '.xls', '.csv'];\n    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));\n\n    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {\n      setParseError('Please select a valid Excel (.xlsx, .xls) or CSV file');\n      return;\n    }\n\n    // Validate file size (10MB limit)\n    if (file.size > 10 * 1024 * 1024) {\n      setParseError('File size must be less than 10MB');\n      return;\n    }\n\n    setSelectedFile(file);\n    setFileInfo({\n      name: file.name,\n      size: formatFileSize(file.size),\n      type: file.type || 'Unknown',\n      lastModified: new Date(file.lastModified).toLocaleDateString()\n    });\n    setParseError(null);\n  };\n\n  const parseFile = async () => {\n    if (!selectedFile) return;\n\n    setParsing(true);\n    setParseError(null);\n\n    try {\n      const headers = await extractHeaders(selectedFile);\n      onFileUpload(selectedFile, headers);\n    } catch (err) {\n      console.error('File parsing error:', err);\n      setParseError(err.message || 'Failed to parse file');\n    } finally {\n      setParsing(false);\n    }\n  };\n\n  const extractHeaders = (file) => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      \n      reader.onload = (e) => {\n        try {\n          const data = e.target.result;\n          let headers = [];\n\n          if (file.name.toLowerCase().endsWith('.csv')) {\n            // Parse CSV\n            const lines = data.split('\\n');\n            if (lines.length > 0) {\n              headers = lines[0].split(',').map(header => header.trim().replace(/\"/g, ''));\n            }\n          } else {\n            // Parse Excel with better error handling\n            console.log('Parsing Excel file:', file.name, 'Size:', file.size, 'Type:', file.type);\n\n            try {\n              // Check if the data looks like HTML\n              if (typeof data === 'string' && (data.includes('<html>') || data.includes('<table>'))) {\n                throw new Error('HTML file detected. Please upload a proper Excel (.xlsx, .xls) file.');\n              }\n\n              const workbook = XLSX.read(data, { type: 'binary' });\n              console.log('Workbook loaded successfully');\n\n              if (!workbook.SheetNames || workbook.SheetNames.length === 0) {\n                throw new Error('No sheets found in the Excel file. The file might be corrupted.');\n              }\n\n              console.log('Available sheets:', workbook.SheetNames);\n\n              const firstSheetName = workbook.SheetNames[0];\n              const worksheet = workbook.Sheets[firstSheetName];\n\n              if (!worksheet) {\n                throw new Error(`Could not read the sheet \"${firstSheetName}\".`);\n              }\n\n              const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });\n              console.log('Extracted', jsonData.length, 'rows from Excel');\n\n              if (jsonData.length > 0) {\n                headers = jsonData[0].map(header => String(header || '').trim());\n                console.log('Headers found:', headers);\n              } else {\n                throw new Error('The Excel file appears to be empty.');\n              }\n            } catch (xlsxError) {\n              console.error('Excel parsing error:', xlsxError);\n\n              if (xlsxError.message && xlsxError.message.toLowerCase().includes('table')) {\n                throw new Error('Invalid file format. The file contains HTML content instead of Excel data. Please upload a proper .xlsx or .xls file.');\n              }\n\n              if (xlsxError.message && xlsxError.message.includes('ZIP')) {\n                throw new Error('The Excel file is corrupted or not a valid .xlsx file. Please try re-saving the file in Excel.');\n              }\n\n              throw new Error(`Excel parsing failed: ${xlsxError.message}`);\n            }\n          }\n\n          if (headers.length === 0) {\n            reject(new Error('No headers found in file'));\n            return;\n          }\n\n          resolve(headers.filter(header => header !== ''));\n        } catch (err) {\n          reject(new Error('Failed to parse file: ' + err.message));\n        }\n      };\n\n      reader.onerror = () => {\n        reject(new Error('Failed to read file'));\n      };\n\n      if (file.name.toLowerCase().endsWith('.csv')) {\n        reader.readAsText(file);\n      } else {\n        reader.readAsBinaryString(file);\n      }\n    });\n  };\n\n  const formatFileSize = (bytes) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const downloadTemplate = async () => {\n    try {\n      const response = await fetch(`${apiService.baseURL}/import-export/persons/template?format=Excel&includeSampleData=true`);\n      \n      if (!response.ok) {\n        throw new Error('Failed to download template');\n      }\n\n      const blob = await response.blob();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = 'person_import_template.xlsx';\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n    } catch (err) {\n      console.error('Template download error:', err);\n      setParseError('Failed to download template');\n    }\n  };\n\n  return (\n    <div className=\"file-upload\">\n      <div className=\"upload-section\">\n        <h3>Upload File</h3>\n        \n        {/* Template Download */}\n        <div className=\"template-section\">\n          <p>Don't have a file? Download our template to get started:</p>\n          <button onClick={downloadTemplate} className=\"btn btn-outline\">\n            📥 Download Template\n          </button>\n        </div>\n\n        {/* File Drop Zone */}\n        <div\n          className={`drop-zone ${dragActive ? 'active' : ''} ${selectedFile ? 'has-file' : ''}`}\n          onDragEnter={handleDrag}\n          onDragLeave={handleDrag}\n          onDragOver={handleDrag}\n          onDrop={handleDrop}\n          onClick={() => fileInputRef.current?.click()}\n        >\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\".xlsx,.xls,.csv\"\n            onChange={handleFileInputChange}\n            style={{ display: 'none' }}\n          />\n          \n          {selectedFile ? (\n            <div className=\"file-selected\">\n              <div className=\"file-icon\">📄</div>\n              <div className=\"file-details\">\n                <div className=\"file-name\">{fileInfo.name}</div>\n                <div className=\"file-meta\">\n                  <span>{fileInfo.size}</span>\n                  <span>•</span>\n                  <span>{fileInfo.lastModified}</span>\n                </div>\n              </div>\n              <button \n                onClick={(e) => {\n                  e.stopPropagation();\n                  setSelectedFile(null);\n                  setFileInfo(null);\n                }}\n                className=\"remove-file\"\n              >\n                ✕\n              </button>\n            </div>\n          ) : (\n            <div className=\"drop-content\">\n              <div className=\"drop-icon\">📁</div>\n              <div className=\"drop-text\">\n                <p><strong>Click to upload</strong> or drag and drop</p>\n                <p>Excel (.xlsx, .xls) or CSV files only</p>\n                <p>Maximum file size: 10MB</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {parseError && (\n          <div className=\"error-message\">\n            <span className=\"error-icon\">⚠️</span>\n            {parseError}\n          </div>\n        )}\n      </div>\n\n      {/* Import Settings */}\n      <div className=\"settings-section\">\n        <h3>Import Settings</h3>\n        \n        <div className=\"setting-group\">\n          <label>Import Mode:</label>\n          <select\n            value={importSettings.importMode}\n            onChange={(e) => onSettingsChange('importMode', e.target.value)}\n          >\n            <option value=\"SkipDuplicates\">Skip Duplicates</option>\n            <option value=\"UpdateExisting\">Update Existing</option>\n            <option value=\"FailOnDuplicates\">Fail on Duplicates</option>\n          </select>\n          <div className=\"setting-help\">\n            {importSettings.importMode === 'SkipDuplicates' && \n              'Skip records that already exist (based on mobile number + division + category)'}\n            {importSettings.importMode === 'UpdateExisting' && \n              'Update existing records with new data'}\n            {importSettings.importMode === 'FailOnDuplicates' && \n              'Stop import if duplicates are found'}\n          </div>\n        </div>\n\n        <div className=\"setting-group\">\n          <label>\n            <input\n              type=\"checkbox\"\n              checked={importSettings.validateOnly}\n              onChange={(e) => onSettingsChange('validateOnly', e.target.checked)}\n            />\n            Validate Only (don't import data)\n          </label>\n          <div className=\"setting-help\">\n            Check this to validate the file without actually importing the data\n          </div>\n        </div>\n\n        <div className=\"setting-group\">\n          <label>Batch Size:</label>\n          <select\n            value={importSettings.batchSize}\n            onChange={(e) => onSettingsChange('batchSize', parseInt(e.target.value))}\n          >\n            <option value={50}>50 records per batch</option>\n            <option value={100}>100 records per batch</option>\n            <option value={200}>200 records per batch</option>\n            <option value={500}>500 records per batch</option>\n          </select>\n          <div className=\"setting-help\">\n            Smaller batches are slower but more reliable for large files\n          </div>\n        </div>\n      </div>\n\n      {/* Action Buttons */}\n      <div className=\"upload-actions\">\n        {onBack && (\n          <button\n            onClick={onBack}\n            disabled={parsing}\n            className=\"btn btn-secondary\"\n          >\n            ← Back to Division & Category\n          </button>\n        )}\n        <button\n          onClick={parseFile}\n          disabled={!selectedFile || parsing}\n          className=\"btn btn-primary\"\n        >\n          {parsing ? 'Processing...' : 'Continue to Field Mapping'}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default FileUpload;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,KAAQ,OAAO,CAC/C,MAAO,GAAK,CAAAC,IAAI,KAAM,MAAM,CAC5B,MAAO,CAAAC,UAAU,KAAM,2BAA2B,CAClD,MAAO,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAAuE,IAAtE,CAAEC,YAAY,CAAEC,cAAc,CAAEC,gBAAgB,CAAEC,MAAM,CAAEC,KAAM,CAAC,CAAAL,IAAA,CACnF,KAAM,CAACM,UAAU,CAAEC,aAAa,CAAC,CAAGhB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACiB,YAAY,CAAEC,eAAe,CAAC,CAAGlB,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACmB,QAAQ,CAAEC,WAAW,CAAC,CAAGpB,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAACqB,OAAO,CAAEC,UAAU,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACuB,UAAU,CAAEC,aAAa,CAAC,CAAGxB,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAAAyB,YAAY,CAAGxB,MAAM,CAAC,IAAI,CAAC,CAEjC,KAAM,CAAAyB,UAAU,CAAIC,CAAC,EAAK,CACxBA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBD,CAAC,CAACE,eAAe,CAAC,CAAC,CACnB,GAAIF,CAAC,CAACG,IAAI,GAAK,WAAW,EAAIH,CAAC,CAACG,IAAI,GAAK,UAAU,CAAE,CACnDd,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,IAAIW,CAAC,CAACG,IAAI,GAAK,WAAW,CAAE,CACjCd,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAAe,UAAU,CAAIJ,CAAC,EAAK,CACxBA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBD,CAAC,CAACE,eAAe,CAAC,CAAC,CACnBb,aAAa,CAAC,KAAK,CAAC,CAEpB,GAAIW,CAAC,CAACK,YAAY,CAACC,KAAK,EAAIN,CAAC,CAACK,YAAY,CAACC,KAAK,CAAC,CAAC,CAAC,CAAE,CACnDC,gBAAgB,CAACP,CAAC,CAACK,YAAY,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAC3C,CACF,CAAC,CAED,KAAM,CAAAE,qBAAqB,CAAIR,CAAC,EAAK,CACnC,GAAIA,CAAC,CAACS,MAAM,CAACH,KAAK,EAAIN,CAAC,CAACS,MAAM,CAACH,KAAK,CAAC,CAAC,CAAC,CAAE,CACvCC,gBAAgB,CAACP,CAAC,CAACS,MAAM,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CACrC,CACF,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAIG,IAAI,EAAK,CACjC;AACA,KAAM,CAAAC,YAAY,CAAG,CACnB,mEAAmE,CAAE;AACrE,0BAA0B,CAAE;AAC5B,UAAW;AAAA,CACZ,CAED,KAAM,CAAAC,iBAAiB,CAAG,CAAC,OAAO,CAAE,MAAM,CAAE,MAAM,CAAC,CACnD,KAAM,CAAAC,aAAa,CAAGH,IAAI,CAACI,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,SAAS,CAACN,IAAI,CAACI,IAAI,CAACG,WAAW,CAAC,GAAG,CAAC,CAAC,CAEnF,GAAI,CAACN,YAAY,CAACO,QAAQ,CAACR,IAAI,CAACP,IAAI,CAAC,EAAI,CAACS,iBAAiB,CAACM,QAAQ,CAACL,aAAa,CAAC,CAAE,CACnFhB,aAAa,CAAC,uDAAuD,CAAC,CACtE,OACF,CAEA;AACA,GAAIa,IAAI,CAACS,IAAI,CAAG,EAAE,CAAG,IAAI,CAAG,IAAI,CAAE,CAChCtB,aAAa,CAAC,kCAAkC,CAAC,CACjD,OACF,CAEAN,eAAe,CAACmB,IAAI,CAAC,CACrBjB,WAAW,CAAC,CACVqB,IAAI,CAAEJ,IAAI,CAACI,IAAI,CACfK,IAAI,CAAEC,cAAc,CAACV,IAAI,CAACS,IAAI,CAAC,CAC/BhB,IAAI,CAAEO,IAAI,CAACP,IAAI,EAAI,SAAS,CAC5BkB,YAAY,CAAE,GAAI,CAAAC,IAAI,CAACZ,IAAI,CAACW,YAAY,CAAC,CAACE,kBAAkB,CAAC,CAC/D,CAAC,CAAC,CACF1B,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,CAED,KAAM,CAAA2B,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CAAClC,YAAY,CAAE,OAEnBK,UAAU,CAAC,IAAI,CAAC,CAChBE,aAAa,CAAC,IAAI,CAAC,CAEnB,GAAI,CACF,KAAM,CAAA4B,OAAO,CAAG,KAAM,CAAAC,cAAc,CAACpC,YAAY,CAAC,CAClDP,YAAY,CAACO,YAAY,CAAEmC,OAAO,CAAC,CACrC,CAAE,MAAOE,GAAG,CAAE,CACZC,OAAO,CAACzC,KAAK,CAAC,qBAAqB,CAAEwC,GAAG,CAAC,CACzC9B,aAAa,CAAC8B,GAAG,CAACE,OAAO,EAAI,sBAAsB,CAAC,CACtD,CAAC,OAAS,CACRlC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA+B,cAAc,CAAIhB,IAAI,EAAK,CAC/B,MAAO,IAAI,CAAAoB,OAAO,CAAC,CAACC,OAAO,CAAEC,MAAM,GAAK,CACtC,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAE/BD,MAAM,CAACE,MAAM,CAAInC,CAAC,EAAK,CACrB,GAAI,CACF,KAAM,CAAAoC,IAAI,CAAGpC,CAAC,CAACS,MAAM,CAAC4B,MAAM,CAC5B,GAAI,CAAAZ,OAAO,CAAG,EAAE,CAEhB,GAAIf,IAAI,CAACI,IAAI,CAACC,WAAW,CAAC,CAAC,CAACuB,QAAQ,CAAC,MAAM,CAAC,CAAE,CAC5C;AACA,KAAM,CAAAC,KAAK,CAAGH,IAAI,CAACI,KAAK,CAAC,IAAI,CAAC,CAC9B,GAAID,KAAK,CAACE,MAAM,CAAG,CAAC,CAAE,CACpBhB,OAAO,CAAGc,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,CAACC,MAAM,EAAIA,MAAM,CAACC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,CAAE,EAAE,CAAC,CAAC,CAC9E,CACF,CAAC,IAAM,CACL;AACAjB,OAAO,CAACkB,GAAG,CAAC,qBAAqB,CAAEpC,IAAI,CAACI,IAAI,CAAE,OAAO,CAAEJ,IAAI,CAACS,IAAI,CAAE,OAAO,CAAET,IAAI,CAACP,IAAI,CAAC,CAErF,GAAI,CACF;AACA,GAAI,MAAO,CAAAiC,IAAI,GAAK,QAAQ,GAAKA,IAAI,CAAClB,QAAQ,CAAC,QAAQ,CAAC,EAAIkB,IAAI,CAAClB,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAE,CACrF,KAAM,IAAI,CAAA6B,KAAK,CAAC,sEAAsE,CAAC,CACzF,CAEA,KAAM,CAAAC,QAAQ,CAAGzE,IAAI,CAAC0E,IAAI,CAACb,IAAI,CAAE,CAAEjC,IAAI,CAAE,QAAS,CAAC,CAAC,CACpDyB,OAAO,CAACkB,GAAG,CAAC,8BAA8B,CAAC,CAE3C,GAAI,CAACE,QAAQ,CAACE,UAAU,EAAIF,QAAQ,CAACE,UAAU,CAACT,MAAM,GAAK,CAAC,CAAE,CAC5D,KAAM,IAAI,CAAAM,KAAK,CAAC,iEAAiE,CAAC,CACpF,CAEAnB,OAAO,CAACkB,GAAG,CAAC,mBAAmB,CAAEE,QAAQ,CAACE,UAAU,CAAC,CAErD,KAAM,CAAAC,cAAc,CAAGH,QAAQ,CAACE,UAAU,CAAC,CAAC,CAAC,CAC7C,KAAM,CAAAE,SAAS,CAAGJ,QAAQ,CAACK,MAAM,CAACF,cAAc,CAAC,CAEjD,GAAI,CAACC,SAAS,CAAE,CACd,KAAM,IAAI,CAAAL,KAAK,CAAC,6BAA6BI,cAAc,IAAI,CAAC,CAClE,CAEA,KAAM,CAAAG,QAAQ,CAAG/E,IAAI,CAACgF,KAAK,CAACC,aAAa,CAACJ,SAAS,CAAE,CAAET,MAAM,CAAE,CAAE,CAAC,CAAC,CACnEf,OAAO,CAACkB,GAAG,CAAC,WAAW,CAAEQ,QAAQ,CAACb,MAAM,CAAE,iBAAiB,CAAC,CAE5D,GAAIa,QAAQ,CAACb,MAAM,CAAG,CAAC,CAAE,CACvBhB,OAAO,CAAG6B,QAAQ,CAAC,CAAC,CAAC,CAACZ,GAAG,CAACC,MAAM,EAAIc,MAAM,CAACd,MAAM,EAAI,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAChEhB,OAAO,CAACkB,GAAG,CAAC,gBAAgB,CAAErB,OAAO,CAAC,CACxC,CAAC,IAAM,CACL,KAAM,IAAI,CAAAsB,KAAK,CAAC,qCAAqC,CAAC,CACxD,CACF,CAAE,MAAOW,SAAS,CAAE,CAClB9B,OAAO,CAACzC,KAAK,CAAC,sBAAsB,CAAEuE,SAAS,CAAC,CAEhD,GAAIA,SAAS,CAAC7B,OAAO,EAAI6B,SAAS,CAAC7B,OAAO,CAACd,WAAW,CAAC,CAAC,CAACG,QAAQ,CAAC,OAAO,CAAC,CAAE,CAC1E,KAAM,IAAI,CAAA6B,KAAK,CAAC,uHAAuH,CAAC,CAC1I,CAEA,GAAIW,SAAS,CAAC7B,OAAO,EAAI6B,SAAS,CAAC7B,OAAO,CAACX,QAAQ,CAAC,KAAK,CAAC,CAAE,CAC1D,KAAM,IAAI,CAAA6B,KAAK,CAAC,gGAAgG,CAAC,CACnH,CAEA,KAAM,IAAI,CAAAA,KAAK,CAAC,yBAAyBW,SAAS,CAAC7B,OAAO,EAAE,CAAC,CAC/D,CACF,CAEA,GAAIJ,OAAO,CAACgB,MAAM,GAAK,CAAC,CAAE,CACxBT,MAAM,CAAC,GAAI,CAAAe,KAAK,CAAC,0BAA0B,CAAC,CAAC,CAC7C,OACF,CAEAhB,OAAO,CAACN,OAAO,CAACkC,MAAM,CAAChB,MAAM,EAAIA,MAAM,GAAK,EAAE,CAAC,CAAC,CAClD,CAAE,MAAOhB,GAAG,CAAE,CACZK,MAAM,CAAC,GAAI,CAAAe,KAAK,CAAC,wBAAwB,CAAGpB,GAAG,CAACE,OAAO,CAAC,CAAC,CAC3D,CACF,CAAC,CAEDI,MAAM,CAAC2B,OAAO,CAAG,IAAM,CACrB5B,MAAM,CAAC,GAAI,CAAAe,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAC1C,CAAC,CAED,GAAIrC,IAAI,CAACI,IAAI,CAACC,WAAW,CAAC,CAAC,CAACuB,QAAQ,CAAC,MAAM,CAAC,CAAE,CAC5CL,MAAM,CAAC4B,UAAU,CAACnD,IAAI,CAAC,CACzB,CAAC,IAAM,CACLuB,MAAM,CAAC6B,kBAAkB,CAACpD,IAAI,CAAC,CACjC,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAU,cAAc,CAAI2C,KAAK,EAAK,CAChC,GAAIA,KAAK,GAAK,CAAC,CAAE,MAAO,SAAS,CACjC,KAAM,CAAAC,CAAC,CAAG,IAAI,CACd,KAAM,CAAAC,KAAK,CAAG,CAAC,OAAO,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CACzC,KAAM,CAAAC,CAAC,CAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACrB,GAAG,CAACiB,KAAK,CAAC,CAAGI,IAAI,CAACrB,GAAG,CAACkB,CAAC,CAAC,CAAC,CACnD,MAAO,CAAAK,UAAU,CAAC,CAACN,KAAK,CAAGI,IAAI,CAACG,GAAG,CAACN,CAAC,CAAEE,CAAC,CAAC,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,CAAG,GAAG,CAAGN,KAAK,CAACC,CAAC,CAAC,CACzE,CAAC,CAED,KAAM,CAAAM,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAGlG,UAAU,CAACmG,OAAO,qEAAqE,CAAC,CAExH,GAAI,CAACF,QAAQ,CAACG,EAAE,CAAE,CAChB,KAAM,IAAI,CAAA7B,KAAK,CAAC,6BAA6B,CAAC,CAChD,CAEA,KAAM,CAAA8B,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClC,KAAM,CAAAC,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC,CAC5C,KAAM,CAAAK,CAAC,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACrCF,CAAC,CAACG,IAAI,CAAGP,GAAG,CACZI,CAAC,CAACI,QAAQ,CAAG,6BAA6B,CAC1CH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC,CAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC,CACTV,MAAM,CAACC,GAAG,CAACU,eAAe,CAACZ,GAAG,CAAC,CAC/BK,QAAQ,CAACI,IAAI,CAACI,WAAW,CAACT,CAAC,CAAC,CAC9B,CAAE,MAAOvD,GAAG,CAAE,CACZC,OAAO,CAACzC,KAAK,CAAC,0BAA0B,CAAEwC,GAAG,CAAC,CAC9C9B,aAAa,CAAC,6BAA6B,CAAC,CAC9C,CACF,CAAC,CAED,mBACEjB,KAAA,QAAKgH,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjH,KAAA,QAAKgH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnH,IAAA,OAAAmH,QAAA,CAAI,aAAW,CAAI,CAAC,cAGpBjH,KAAA,QAAKgH,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BnH,IAAA,MAAAmH,QAAA,CAAG,0DAAwD,CAAG,CAAC,cAC/DnH,IAAA,WAAQoH,OAAO,CAAEtB,gBAAiB,CAACoB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,gCAE/D,CAAQ,CAAC,EACN,CAAC,cAGNjH,KAAA,QACEgH,SAAS,CAAE,aAAaxG,UAAU,CAAG,QAAQ,CAAG,EAAE,IAAIE,YAAY,CAAG,UAAU,CAAG,EAAE,EAAG,CACvFyG,WAAW,CAAEhG,UAAW,CACxBiG,WAAW,CAAEjG,UAAW,CACxBkG,UAAU,CAAElG,UAAW,CACvBmG,MAAM,CAAE9F,UAAW,CACnB0F,OAAO,CAAEA,CAAA,QAAAK,qBAAA,QAAAA,qBAAA,CAAMrG,YAAY,CAACsG,OAAO,UAAAD,qBAAA,iBAApBA,qBAAA,CAAsBV,KAAK,CAAC,CAAC,EAAC,CAAAI,QAAA,eAE7CnH,IAAA,UACE2H,GAAG,CAAEvG,YAAa,CAClBK,IAAI,CAAC,MAAM,CACXmG,MAAM,CAAC,iBAAiB,CACxBC,QAAQ,CAAE/F,qBAAsB,CAChCgG,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAO,CAAE,CAC5B,CAAC,CAEDnH,YAAY,cACXV,KAAA,QAAKgH,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BnH,IAAA,QAAKkH,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACnCjH,KAAA,QAAKgH,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BnH,IAAA,QAAKkH,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAErG,QAAQ,CAACsB,IAAI,CAAM,CAAC,cAChDlC,KAAA,QAAKgH,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnH,IAAA,SAAAmH,QAAA,CAAOrG,QAAQ,CAAC2B,IAAI,CAAO,CAAC,cAC5BzC,IAAA,SAAAmH,QAAA,CAAM,QAAC,CAAM,CAAC,cACdnH,IAAA,SAAAmH,QAAA,CAAOrG,QAAQ,CAAC6B,YAAY,CAAO,CAAC,EACjC,CAAC,EACH,CAAC,cACN3C,IAAA,WACEoH,OAAO,CAAG9F,CAAC,EAAK,CACdA,CAAC,CAACE,eAAe,CAAC,CAAC,CACnBX,eAAe,CAAC,IAAI,CAAC,CACrBE,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CACFmG,SAAS,CAAC,aAAa,CAAAC,QAAA,CACxB,QAED,CAAQ,CAAC,EACN,CAAC,cAENjH,KAAA,QAAKgH,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BnH,IAAA,QAAKkH,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACnCjH,KAAA,QAAKgH,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjH,KAAA,MAAAiH,QAAA,eAAGnH,IAAA,WAAAmH,QAAA,CAAQ,iBAAe,CAAQ,CAAC,oBAAiB,EAAG,CAAC,cACxDnH,IAAA,MAAAmH,QAAA,CAAG,uCAAqC,CAAG,CAAC,cAC5CnH,IAAA,MAAAmH,QAAA,CAAG,yBAAuB,CAAG,CAAC,EAC3B,CAAC,EACH,CACN,EACE,CAAC,CAELjG,UAAU,eACThB,KAAA,QAAKgH,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BnH,IAAA,SAAMkH,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,CACrCjG,UAAU,EACR,CACN,EACE,CAAC,cAGNhB,KAAA,QAAKgH,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BnH,IAAA,OAAAmH,QAAA,CAAI,iBAAe,CAAI,CAAC,cAExBjH,KAAA,QAAKgH,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BnH,IAAA,UAAAmH,QAAA,CAAO,cAAY,CAAO,CAAC,cAC3BjH,KAAA,WACE8H,KAAK,CAAE1H,cAAc,CAAC2H,UAAW,CACjCJ,QAAQ,CAAGvG,CAAC,EAAKf,gBAAgB,CAAC,YAAY,CAAEe,CAAC,CAACS,MAAM,CAACiG,KAAK,CAAE,CAAAb,QAAA,eAEhEnH,IAAA,WAAQgI,KAAK,CAAC,gBAAgB,CAAAb,QAAA,CAAC,iBAAe,CAAQ,CAAC,cACvDnH,IAAA,WAAQgI,KAAK,CAAC,gBAAgB,CAAAb,QAAA,CAAC,iBAAe,CAAQ,CAAC,cACvDnH,IAAA,WAAQgI,KAAK,CAAC,kBAAkB,CAAAb,QAAA,CAAC,oBAAkB,CAAQ,CAAC,EACtD,CAAC,cACTjH,KAAA,QAAKgH,SAAS,CAAC,cAAc,CAAAC,QAAA,EAC1B7G,cAAc,CAAC2H,UAAU,GAAK,gBAAgB,EAC7C,gFAAgF,CACjF3H,cAAc,CAAC2H,UAAU,GAAK,gBAAgB,EAC7C,uCAAuC,CACxC3H,cAAc,CAAC2H,UAAU,GAAK,kBAAkB,EAC/C,qCAAqC,EACpC,CAAC,EACH,CAAC,cAEN/H,KAAA,QAAKgH,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjH,KAAA,UAAAiH,QAAA,eACEnH,IAAA,UACEyB,IAAI,CAAC,UAAU,CACfyG,OAAO,CAAE5H,cAAc,CAAC6H,YAAa,CACrCN,QAAQ,CAAGvG,CAAC,EAAKf,gBAAgB,CAAC,cAAc,CAAEe,CAAC,CAACS,MAAM,CAACmG,OAAO,CAAE,CACrE,CAAC,oCAEJ,EAAO,CAAC,cACRlI,IAAA,QAAKkH,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,qEAE9B,CAAK,CAAC,EACH,CAAC,cAENjH,KAAA,QAAKgH,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BnH,IAAA,UAAAmH,QAAA,CAAO,aAAW,CAAO,CAAC,cAC1BjH,KAAA,WACE8H,KAAK,CAAE1H,cAAc,CAAC8H,SAAU,CAChCP,QAAQ,CAAGvG,CAAC,EAAKf,gBAAgB,CAAC,WAAW,CAAE8H,QAAQ,CAAC/G,CAAC,CAACS,MAAM,CAACiG,KAAK,CAAC,CAAE,CAAAb,QAAA,eAEzEnH,IAAA,WAAQgI,KAAK,CAAE,EAAG,CAAAb,QAAA,CAAC,sBAAoB,CAAQ,CAAC,cAChDnH,IAAA,WAAQgI,KAAK,CAAE,GAAI,CAAAb,QAAA,CAAC,uBAAqB,CAAQ,CAAC,cAClDnH,IAAA,WAAQgI,KAAK,CAAE,GAAI,CAAAb,QAAA,CAAC,uBAAqB,CAAQ,CAAC,cAClDnH,IAAA,WAAQgI,KAAK,CAAE,GAAI,CAAAb,QAAA,CAAC,uBAAqB,CAAQ,CAAC,EAC5C,CAAC,cACTnH,IAAA,QAAKkH,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,8DAE9B,CAAK,CAAC,EACH,CAAC,EACH,CAAC,cAGNjH,KAAA,QAAKgH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAC5B3G,MAAM,eACLR,IAAA,WACEoH,OAAO,CAAE5G,MAAO,CAChB8H,QAAQ,CAAEtH,OAAQ,CAClBkG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC9B,oCAED,CAAQ,CACT,cACDnH,IAAA,WACEoH,OAAO,CAAEtE,SAAU,CACnBwF,QAAQ,CAAE,CAAC1H,YAAY,EAAII,OAAQ,CACnCkG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAE1BnG,OAAO,CAAG,eAAe,CAAG,2BAA2B,CAClD,CAAC,EACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAb,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}