-- =====================================================
-- Migration Script: Subcategory to Firm Nature
-- Description: Rename subcategories table to firm_natures and update all related references
-- Database: SQL Server
-- Author: CRM System Migration
-- Date: 2025-01-01
-- =====================================================

USE [CrmDatabase]
GO

PRINT 'Starting migration from Subcategory to Firm Nature...'
GO

-- =====================================================
-- STEP 1: BACKUP EXISTING DATA (Optional but recommended)
-- =====================================================

PRINT 'Step 1: Creating backup tables...'
GO

-- Backup subcategories table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='subcategories_backup' AND xtype='U')
BEGIN
    SELECT * INTO subcategories_backup FROM subcategories;
    PRINT '✓ Backup of subcategories table created as subcategories_backup';
END

-- Backup persons table (structure only for reference)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='persons_structure_backup' AND xtype='U')
BEGIN
    SELECT TOP 0 * INTO persons_structure_backup FROM persons;
    PRINT '✓ Backup of persons table structure created as persons_structure_backup';
END
GO

-- =====================================================
-- STEP 2: DROP FOREIGN KEY CONSTRAINTS
-- =====================================================

PRINT 'Step 2: Dropping foreign key constraints...'
GO

-- Drop foreign key constraint from persons table
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_persons_subcategories')
BEGIN
    ALTER TABLE persons DROP CONSTRAINT FK_persons_subcategories;
    PRINT '✓ Dropped FK_persons_subcategories constraint';
END
GO

-- =====================================================
-- STEP 3: DROP EXISTING INDEXES
-- =====================================================

PRINT 'Step 3: Dropping existing indexes...'
GO

-- Drop indexes on subcategories table
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_subcategories_category_id')
BEGIN
    DROP INDEX IX_subcategories_category_id ON subcategories;
    PRINT '✓ Dropped IX_subcategories_category_id index';
END

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_subcategories_name_category_id')
BEGIN
    DROP INDEX IX_subcategories_name_category_id ON subcategories;
    PRINT '✓ Dropped IX_subcategories_name_category_id index';
END

-- Drop indexes on persons table related to subcategory
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_persons_subcategory_id')
BEGIN
    DROP INDEX IX_persons_subcategory_id ON persons;
    PRINT '✓ Dropped IX_persons_subcategory_id index';
END
GO

-- =====================================================
-- STEP 4: RENAME SUBCATEGORIES TABLE TO FIRM_NATURES
-- =====================================================

PRINT 'Step 4: Renaming subcategories table to firm_natures...'
GO

-- Rename the table
EXEC sp_rename 'subcategories', 'firm_natures';
PRINT '✓ Renamed subcategories table to firm_natures';

-- Rename the primary key constraint
EXEC sp_rename 'PK__subcateg__3213E83F', 'PK_firm_natures';
PRINT '✓ Renamed primary key constraint to PK_firm_natures';

-- Rename the foreign key constraint
EXEC sp_rename 'FK_subcategories_categories', 'FK_firm_natures_categories';
PRINT '✓ Renamed foreign key constraint to FK_firm_natures_categories';

-- Rename the unique constraint
EXEC sp_rename 'UQ_subcategories_name_category', 'UQ_firm_natures_name_category';
PRINT '✓ Renamed unique constraint to UQ_firm_natures_name_category';
GO

-- =====================================================
-- STEP 5: RENAME COLUMN IN PERSONS TABLE
-- =====================================================

PRINT 'Step 5: Renaming subcategory_id column to firm_nature_id in persons table...'
GO

-- Rename the column
EXEC sp_rename 'persons.subcategory_id', 'firm_nature_id', 'COLUMN';
PRINT '✓ Renamed subcategory_id column to firm_nature_id in persons table';
GO

-- =====================================================
-- STEP 6: CREATE NEW INDEXES
-- =====================================================

PRINT 'Step 6: Creating new indexes...'
GO

-- Create indexes on firm_natures table
CREATE INDEX IX_firm_natures_category_id ON firm_natures(category_id);
PRINT '✓ Created IX_firm_natures_category_id index';

CREATE INDEX IX_firm_natures_name_category_id ON firm_natures(name, category_id);
PRINT '✓ Created IX_firm_natures_name_category_id index';

-- Create index on persons table for firm_nature_id
CREATE INDEX IX_persons_firm_nature_id ON persons(firm_nature_id);
PRINT '✓ Created IX_persons_firm_nature_id index';
GO

-- =====================================================
-- STEP 7: RECREATE FOREIGN KEY CONSTRAINT
-- =====================================================

PRINT 'Step 7: Creating new foreign key constraint...'
GO

-- Add foreign key constraint from persons to firm_natures
ALTER TABLE persons 
ADD CONSTRAINT FK_persons_firm_natures 
FOREIGN KEY (firm_nature_id) REFERENCES firm_natures(id);
PRINT '✓ Created FK_persons_firm_natures constraint';
GO

-- =====================================================
-- STEP 8: CREATE PARTITIONING ON FIRM_NATURE_ID
-- =====================================================

PRINT 'Step 8: Setting up partitioning on firm_nature_id...'
GO

-- Create partition function for firm_nature_id
-- This creates partitions based on firm_nature_id ranges
CREATE PARTITION FUNCTION pf_firm_nature_id (int)
AS RANGE RIGHT FOR VALUES (1, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100);
PRINT '✓ Created partition function pf_firm_nature_id';

-- Create partition scheme
CREATE PARTITION SCHEME ps_firm_nature_id
AS PARTITION pf_firm_nature_id
ALL TO ([PRIMARY]);
PRINT '✓ Created partition scheme ps_firm_nature_id';
GO

-- =====================================================
-- STEP 9: CREATE PARTITIONED INDEXES
-- =====================================================

PRINT 'Step 9: Creating partitioned indexes...'
GO

-- Create partitioned index on firm_nature_id
CREATE INDEX IX_persons_firm_nature_id_partitioned 
ON persons(firm_nature_id, division_id, category_id)
ON ps_firm_nature_id(firm_nature_id);
PRINT '✓ Created partitioned index IX_persons_firm_nature_id_partitioned';

-- Create partitioned index for mobile number uniqueness
CREATE UNIQUE INDEX IX_persons_mobile_division_category_firm_nature_unique 
ON persons(mobile_number, division_id, category_id, firm_nature_id) 
WHERE is_deleted = 0
ON ps_firm_nature_id(firm_nature_id);
PRINT '✓ Created partitioned unique index for mobile number';

-- Create partitioned index for name searches
CREATE INDEX IX_persons_name_firm_nature_partitioned 
ON persons(name, firm_nature_id, division_id, category_id)
ON ps_firm_nature_id(firm_nature_id);
PRINT '✓ Created partitioned index for name searches';

-- Create partitioned index for date-based queries
CREATE INDEX IX_persons_created_at_firm_nature_partitioned 
ON persons(created_at, firm_nature_id, is_deleted)
ON ps_firm_nature_id(firm_nature_id);
PRINT '✓ Created partitioned index for date-based queries';
GO

-- =====================================================
-- STEP 10: UPDATE STATISTICS
-- =====================================================

PRINT 'Step 10: Updating statistics...'
GO

-- Update statistics on firm_natures table
UPDATE STATISTICS firm_natures;
PRINT '✓ Updated statistics on firm_natures table';

-- Update statistics on persons table
UPDATE STATISTICS persons;
PRINT '✓ Updated statistics on persons table';
GO

-- =====================================================
-- STEP 11: VERIFICATION
-- =====================================================

PRINT 'Step 11: Verifying migration...'
GO

-- Verify table exists
IF EXISTS (SELECT * FROM sysobjects WHERE name='firm_natures' AND xtype='U')
    PRINT '✓ firm_natures table exists'
ELSE
    PRINT '✗ ERROR: firm_natures table does not exist'

-- Verify column exists
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('persons') AND name = 'firm_nature_id')
    PRINT '✓ firm_nature_id column exists in persons table'
ELSE
    PRINT '✗ ERROR: firm_nature_id column does not exist in persons table'

-- Verify foreign key constraint
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_persons_firm_natures')
    PRINT '✓ FK_persons_firm_natures constraint exists'
ELSE
    PRINT '✗ ERROR: FK_persons_firm_natures constraint does not exist'

-- Verify indexes
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_firm_natures_category_id')
    PRINT '✓ IX_firm_natures_category_id index exists'
ELSE
    PRINT '✗ ERROR: IX_firm_natures_category_id index does not exist'

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_persons_firm_nature_id')
    PRINT '✓ IX_persons_firm_nature_id index exists'
ELSE
    PRINT '✗ ERROR: IX_persons_firm_nature_id index does not exist'

-- Verify partition function
IF EXISTS (SELECT * FROM sys.partition_functions WHERE name = 'pf_firm_nature_id')
    PRINT '✓ Partition function pf_firm_nature_id exists'
ELSE
    PRINT '✗ ERROR: Partition function pf_firm_nature_id does not exist'

-- Show record counts
DECLARE @firm_nature_count INT, @persons_count INT
SELECT @firm_nature_count = COUNT(*) FROM firm_natures
SELECT @persons_count = COUNT(*) FROM persons

PRINT '✓ Record counts:'
PRINT '  - firm_natures: ' + CAST(@firm_nature_count AS VARCHAR(10))
PRINT '  - persons: ' + CAST(@persons_count AS VARCHAR(10))
GO

PRINT ''
PRINT '=================================================='
PRINT 'MIGRATION COMPLETED SUCCESSFULLY!'
PRINT '=================================================='
PRINT ''
PRINT 'Summary of changes:'
PRINT '✓ Renamed subcategories table to firm_natures'
PRINT '✓ Renamed subcategory_id column to firm_nature_id'
PRINT '✓ Updated all foreign key constraints'
PRINT '✓ Recreated all indexes with new names'
PRINT '✓ Created partitioning on firm_nature_id'
PRINT '✓ Created partitioned indexes for performance'
PRINT '✓ Updated statistics'
PRINT ''
PRINT 'Next steps:'
PRINT '1. Update your application code to use new table/column names'
PRINT '2. Update any stored procedures or views'
PRINT '3. Test the application thoroughly'
PRINT '4. Remove backup tables when confident migration is successful'
PRINT ''
GO
