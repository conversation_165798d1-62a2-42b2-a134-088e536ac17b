-- =====================================================
-- EMERGENCY ROLLBACK SCRIPT (MySQL)
-- Description: Rollback from firm_natures back to sub_categories
-- Database: MySQL
-- Author: CRM System Migration
-- Date: 2025-01-01
-- =====================================================

USE data_crm;

SELECT '==================================================' AS Status;
SELECT 'EMERGENCY ROLLBACK STARTING...' AS Status;
SELECT '==================================================' AS Status;

-- =====================================================
-- STEP 1: CHECK CURRENT STATE
-- =====================================================

SELECT 'Step 1: Checking current database state...' AS Status;

-- Check what tables currently exist
SELECT 
    TABLE_NAME as 'Current Tables'
FROM information_schema.tables 
WHERE table_schema = 'data_crm' 
AND table_name IN ('firm_natures', 'sub_categories', 'persons', 'persons_temp');

-- Check if backup tables exist
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'sub_categories_backup table EXISTS'
        ELSE 'sub_categories_backup table DOES NOT EXIST'
    END AS backup_status
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'sub_categories_backup';

-- =====================================================
-- STEP 2: DROP FOREIGN KEY CONSTRAINTS
-- =====================================================

SELECT 'Step 2: Dropping foreign key constraints...' AS Status;

-- Drop foreign key constraints manually (safer approach)
-- Drop known foreign key constraints that might exist

-- Try to drop each constraint individually (will ignore errors if constraint doesn't exist)
SET @sql = 'ALTER TABLE persons DROP FOREIGN KEY fk_persons_divisions';
SET @sql_safe = CONCAT('SET @dummy = (SELECT 1 FROM information_schema.KEY_COLUMN_USAGE WHERE TABLE_SCHEMA = "data_crm" AND TABLE_NAME = "persons" AND CONSTRAINT_NAME = "fk_persons_divisions" LIMIT 1)');
PREPARE stmt FROM @sql_safe;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Drop fk_persons_categories if exists
SET foreign_key_checks = 0;
ALTER TABLE persons DROP FOREIGN KEY IF EXISTS fk_persons_categories;
ALTER TABLE persons DROP FOREIGN KEY IF EXISTS fk_persons_divisions;
ALTER TABLE persons DROP FOREIGN KEY IF EXISTS fk_persons_firm_natures;
ALTER TABLE persons DROP FOREIGN KEY IF EXISTS fk_persons_sub_categories;
SET foreign_key_checks = 1;

SELECT 'Attempted to drop foreign key constraints' AS Status;

-- =====================================================
-- STEP 3: BACKUP CURRENT STATE (JUST IN CASE)
-- =====================================================

SELECT 'Step 3: Creating emergency backup of current state...' AS Status;

-- Backup current firm_natures table
DROP TABLE IF EXISTS firm_natures_emergency_backup;
CREATE TABLE firm_natures_emergency_backup AS SELECT * FROM firm_natures;

-- Backup current persons table
DROP TABLE IF EXISTS persons_emergency_backup;
CREATE TABLE persons_emergency_backup AS SELECT * FROM persons;

SELECT 'Emergency backup created' AS Status;

-- =====================================================
-- STEP 4: RECREATE ORIGINAL PERSONS TABLE STRUCTURE
-- =====================================================

SELECT 'Step 4: Recreating original persons table structure...' AS Status;

-- Create temporary table to hold current persons data
CREATE TABLE persons_rollback_temp AS SELECT * FROM persons;

-- Drop the partitioned persons table
DROP TABLE persons;

-- Recreate persons table without partitioning (original structure)
CREATE TABLE persons (
    id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    division_id INT NOT NULL,
    category_id INT NOT NULL,
    sub_category_id INT NULL,
    name VARCHAR(255) NOT NULL,
    mobile_number VARCHAR(15) NOT NULL,
    nature INT NOT NULL,
    gender INT NULL,
    alternate_numbers JSON NULL,
    primary_email_id VARCHAR(255) NULL,
    alternate_email_ids JSON NULL,
    website VARCHAR(500) NULL,
    date_of_birth DATETIME NULL,
    is_married BOOLEAN NULL,
    date_of_marriage DATETIME NULL,
    working_state INT NULL,
    district VARCHAR(100) NULL,
    pin_code VARCHAR(10) NULL,
    address_line_1 VARCHAR(500) NULL,
    address_line_2 VARCHAR(500) NULL,
    landmark VARCHAR(255) NULL,
    star_rating INT NULL,
    remarks TEXT NULL,
    firm_name VARCHAR(255) NULL,
    number_of_offices INT NULL,
    number_of_branches INT NULL,
    total_employee_strength INT NULL,
    authorized_person_name VARCHAR(255) NULL,
    authorized_person_email VARCHAR(255) NULL,
    designation VARCHAR(100) NULL,
    marketing_contact VARCHAR(255) NULL,
    marketing_designation VARCHAR(100) NULL,
    place_of_posting VARCHAR(255) NULL,
    department VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP NULL
);

SELECT 'Recreated original persons table structure' AS Status;

-- =====================================================
-- STEP 5: RESTORE PERSONS DATA WITH RENAMED COLUMN
-- =====================================================

SELECT 'Step 5: Restoring persons data with original column names...' AS Status;

-- Insert data back with column name change (firm_nature_id -> sub_category_id)
INSERT INTO persons (
    id, division_id, category_id, sub_category_id, name, mobile_number, nature, gender,
    alternate_numbers, primary_email_id, alternate_email_ids, website, date_of_birth,
    is_married, date_of_marriage, working_state, district, pin_code, address_line_1,
    address_line_2, landmark, star_rating, remarks, firm_name, number_of_offices,
    number_of_branches, total_employee_strength, authorized_person_name,
    authorized_person_email, designation, marketing_contact, marketing_designation,
    place_of_posting, department, created_at, updated_at, is_deleted, deleted_at
)
SELECT 
    id, division_id, category_id, firm_nature_id, name, mobile_number, nature, gender,
    alternate_numbers, primary_email_id, alternate_email_ids, website, date_of_birth,
    is_married, date_of_marriage, working_state, district, pin_code, address_line_1,
    address_line_2, landmark, star_rating, remarks, firm_name, number_of_offices,
    number_of_branches, total_employee_strength, authorized_person_name,
    authorized_person_email, designation, marketing_contact, marketing_designation,
    place_of_posting, department, created_at, updated_at, is_deleted, deleted_at
FROM persons_rollback_temp;

SELECT 'Restored persons data with sub_category_id column' AS Status;

-- Drop temporary table
DROP TABLE persons_rollback_temp;

-- =====================================================
-- STEP 6: RENAME FIRM_NATURES TABLE BACK TO SUB_CATEGORIES
-- =====================================================

SELECT 'Step 6: Renaming firm_natures table back to sub_categories...' AS Status;

-- Rename the table back
RENAME TABLE firm_natures TO sub_categories;
SELECT 'Renamed firm_natures table back to sub_categories' AS Status;

-- =====================================================
-- STEP 7: RECREATE ORIGINAL INDEXES
-- =====================================================

SELECT 'Step 7: Recreating original indexes...' AS Status;

-- Create indexes on sub_categories table
CREATE INDEX IX_sub_categories_category_id ON sub_categories(category_id);
CREATE INDEX IX_sub_categories_category_id_name ON sub_categories(category_id, name);

-- Create indexes on persons table
CREATE INDEX IX_persons_division_id ON persons(division_id);
CREATE INDEX IX_persons_category_id ON persons(category_id);
CREATE INDEX IX_persons_sub_category_id ON persons(sub_category_id);
CREATE INDEX IX_persons_mobile_number ON persons(mobile_number);
CREATE INDEX IX_persons_name ON persons(name);
CREATE INDEX IX_persons_primary_email_id ON persons(primary_email_id);
CREATE INDEX IX_persons_is_deleted ON persons(is_deleted);
CREATE INDEX IX_persons_created_at ON persons(created_at);

SELECT 'Created all original indexes' AS Status;

-- =====================================================
-- STEP 8: RECREATE ORIGINAL FOREIGN KEY CONSTRAINTS
-- =====================================================

SELECT 'Step 8: Recreating original foreign key constraints...' AS Status;

-- Add original foreign key constraints
ALTER TABLE persons 
ADD CONSTRAINT fk_persons_divisions 
FOREIGN KEY (division_id) REFERENCES divisions(id);

ALTER TABLE persons 
ADD CONSTRAINT fk_persons_categories 
FOREIGN KEY (category_id) REFERENCES categories(id);

ALTER TABLE persons 
ADD CONSTRAINT fk_persons_sub_categories 
FOREIGN KEY (sub_category_id) REFERENCES sub_categories(id)
ON DELETE SET NULL ON UPDATE CASCADE;

SELECT 'Created all original foreign key constraints' AS Status;

-- =====================================================
-- STEP 9: VERIFICATION
-- =====================================================

SELECT 'Step 9: Verifying rollback...' AS Status;

-- Verify tables exist
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'SUCCESS: sub_categories table exists'
        ELSE 'ERROR: sub_categories table missing'
    END AS sub_categories_check
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'sub_categories';

SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'SUCCESS: persons table exists'
        ELSE 'ERROR: persons table missing'
    END AS persons_check
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'persons';

-- Verify column exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'SUCCESS: sub_category_id column exists'
        ELSE 'ERROR: sub_category_id column missing'
    END AS sub_category_id_check
FROM information_schema.columns 
WHERE table_schema = 'data_crm' AND table_name = 'persons' AND column_name = 'sub_category_id';

-- Verify partitioning is removed
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'SUCCESS: Partitioning removed from persons table'
        ELSE 'WARNING: Persons table still appears to be partitioned'
    END AS partitioning_check
FROM information_schema.partitions 
WHERE table_schema = 'data_crm' AND table_name = 'persons' AND partition_name IS NOT NULL;

-- Show record counts
SELECT 
    (SELECT COUNT(*) FROM sub_categories) AS sub_categories_count,
    (SELECT COUNT(*) FROM persons) AS persons_count;

-- =====================================================
-- FINAL STATUS
-- =====================================================

SELECT '==================================================' AS Status;
SELECT 'EMERGENCY ROLLBACK COMPLETED!' AS Status;
SELECT '==================================================' AS Status;
SELECT '' AS Status;
SELECT 'Summary of rollback:' AS Status;
SELECT '✓ Renamed firm_natures table back to sub_categories' AS Status;
SELECT '✓ Renamed firm_nature_id column back to sub_category_id' AS Status;
SELECT '✓ Removed partitioning from persons table' AS Status;
SELECT '✓ Restored original table structure' AS Status;
SELECT '✓ Recreated all original indexes' AS Status;
SELECT '✓ Recreated all original foreign key constraints' AS Status;
SELECT '✓ Created emergency backup tables for safety' AS Status;
SELECT '' AS Status;
SELECT 'Emergency backup tables created:' AS Status;
SELECT '- firm_natures_emergency_backup' AS Status;
SELECT '- persons_emergency_backup' AS Status;
SELECT '' AS Status;
SELECT 'Next steps:' AS Status;
SELECT '1. Verify your application works with restored structure' AS Status;
SELECT '2. Test all functionality thoroughly' AS Status;
SELECT '3. Remove emergency backup tables when confident' AS Status;
SELECT '4. Update application code to use sub_categories and sub_category_id' AS Status;
SELECT '' AS Status;
