using CrmApi.Models.FirmNature;

namespace CrmApi.Repositories.FirmNature
{
    public interface IFirmNatureRepository
    {
        Task<IEnumerable<Models.FirmNature.FirmNature>> GetAllAsync();
        Task<IEnumerable<Models.FirmNature.FirmNature>> GetAllWithRelationsAsync();
        Task<Models.FirmNature.FirmNature?> GetByIdAsync(int id);
        Task<Models.FirmNature.FirmNature?> GetByIdWithRelationsAsync(int id);
        Task<IEnumerable<Models.FirmNature.FirmNature>> GetByCategoryIdAsync(int categoryId);
        Task<Models.FirmNature.FirmNature> CreateAsync(Models.FirmNature.FirmNature firmNature);
        Task<Models.FirmNature.FirmNature> UpdateAsync(Models.FirmNature.FirmNature firmNature);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<bool> NameExistsInCategoryAsync(string name, int categoryId, int? excludeId = null);
    }
}
