{"ast": null, "code": "import React,{useState,useEffect}from'react';import{PersonFieldDefinitions,getAllPersonFields}from'../../constants/personConstants';import'./FieldMapping.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FieldMapping=_ref=>{let{fileHeaders,onMappingComplete,onBack,error}=_ref;const[mapping,setMapping]=useState({});const[unmappedHeaders,setUnmappedHeaders]=useState([]);const[requiredFieldsStatus,setRequiredFieldsStatus]=useState({});const[autoMappingSuggestions,setAutoMappingSuggestions]=useState({});const[defaultValues,setDefaultValues]=useState({});const personFields=getAllPersonFields();const requiredFields=personFields.filter(field=>field.required);useEffect(()=>{// Auto-suggest mappings based on header names\nconst suggestions=autoMapFields();setAutoMappingSuggestions(suggestions);setMapping(suggestions);updateUnmappedHeaders(suggestions);updateRequiredFieldsStatus(suggestions);},[fileHeaders]);// Re-validate when default values change\nuseEffect(()=>{updateRequiredFieldsStatus(mapping);},[defaultValues]);const autoMapFields=()=>{const suggestions={};fileHeaders.forEach(header=>{const normalizedHeader=header.toLowerCase().trim();// Find exact matches first\nconst exactMatch=personFields.find(field=>field.label.toLowerCase()===normalizedHeader||field.key.toLowerCase()===normalizedHeader);if(exactMatch){suggestions[header]=exactMatch.key;return;}// Find partial matches\nconst partialMatch=personFields.find(field=>{const fieldLabel=field.label.toLowerCase();const fieldKey=field.key.toLowerCase();return fieldLabel.includes(normalizedHeader)||normalizedHeader.includes(fieldLabel)||fieldKey.includes(normalizedHeader)||normalizedHeader.includes(fieldKey);});if(partialMatch){suggestions[header]=partialMatch.key;}});return suggestions;};const handleMappingChange=(fileHeader,personFieldKey)=>{const newMapping={...mapping};if(personFieldKey===''){delete newMapping[fileHeader];}else{// Remove any existing mapping to this person field\nObject.keys(newMapping).forEach(key=>{if(newMapping[key]===personFieldKey&&key!==fileHeader){delete newMapping[key];}});newMapping[fileHeader]=personFieldKey;}setMapping(newMapping);updateUnmappedHeaders(newMapping);updateRequiredFieldsStatus(newMapping);};const updateUnmappedHeaders=currentMapping=>{const mapped=Object.keys(currentMapping);const unmapped=fileHeaders.filter(header=>!mapped.includes(header));setUnmappedHeaders(unmapped);};const updateRequiredFieldsStatus=currentMapping=>{const mappedFields=Object.values(currentMapping);const status={};requiredFields.forEach(field=>{// Field is satisfied if it's mapped from Excel OR has a default value\nconst isMapped=mappedFields.includes(field.key);const hasDefaultValue=defaultValues[field.key]&&defaultValues[field.key].trim()!=='';status[field.key]=isMapped||hasDefaultValue;});setRequiredFieldsStatus(status);};const handleDefaultValueChange=(fieldKey,value)=>{const newDefaultValues={...defaultValues,[fieldKey]:value};setDefaultValues(newDefaultValues);// Re-validate required fields status when default values change\nupdateRequiredFieldsStatus(mapping);};const applyAutoMapping=()=>{setMapping(autoMappingSuggestions);updateUnmappedHeaders(autoMappingSuggestions);updateRequiredFieldsStatus(autoMappingSuggestions);};const clearAllMappings=()=>{setMapping({});updateUnmappedHeaders({});updateRequiredFieldsStatus({});};const handleContinue=()=>{// Validate that all required fields are mapped or have default values\nconst missingRequired=requiredFields.filter(field=>!requiredFieldsStatus[field.key]);if(missingRequired.length>0){alert(`Please map the following required fields or provide default values: ${missingRequired.map(f=>f.label).join(', ')}`);return;}// Include both field mapping and default values\nconst completeMapping={fieldMapping:mapping,defaultValues:defaultValues};onMappingComplete(completeMapping);};const getPersonFieldByKey=key=>{return personFields.find(field=>field.key===key);};const getMappedPersonFields=()=>{return Object.values(mapping).map(key=>getPersonFieldByKey(key)).filter(Boolean);};const getAvailablePersonFields=currentHeader=>{const mappedFields=Object.entries(mapping).filter(_ref2=>{let[header,_]=_ref2;return header!==currentHeader;}).map(_ref3=>{let[_,fieldKey]=_ref3;return fieldKey;});return personFields.filter(field=>!mappedFields.includes(field.key));};const groupPersonFieldsBySection=fields=>{const sections={};fields.forEach(field=>{const sectionKey=field.section||'general';if(!sections[sectionKey]){var _PersonFieldDefinitio;sections[sectionKey]={title:((_PersonFieldDefinitio=PersonFieldDefinitions[sectionKey])===null||_PersonFieldDefinitio===void 0?void 0:_PersonFieldDefinitio.title)||sectionKey,fields:[]};}sections[sectionKey].fields.push(field);});return sections;};const renderFieldSelect=fileHeader=>{const availableFields=getAvailablePersonFields(fileHeader);const sections=groupPersonFieldsBySection(availableFields);const currentMapping=mapping[fileHeader]||'';return/*#__PURE__*/_jsxs(\"select\",{value:currentMapping,onChange:e=>handleMappingChange(fileHeader,e.target.value),className:`field-select ${currentMapping?'mapped':'unmapped'}`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"-- Select Field --\"}),Object.entries(sections).map(_ref4=>{let[sectionKey,section]=_ref4;return/*#__PURE__*/_jsx(\"optgroup\",{label:section.title,children:section.fields.map(field=>/*#__PURE__*/_jsxs(\"option\",{value:field.key,children:[field.label,\" \",field.required?'*':'']},field.key))},sectionKey);})]});};const allRequiredMapped=requiredFields.every(field=>requiredFieldsStatus[field.key]);const mappingCount=Object.keys(mapping).length;return/*#__PURE__*/_jsxs(\"div\",{className:\"field-mapping\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mapping-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Map File Columns to Person Fields\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Match your file columns with the corresponding person fields. Required fields are marked with *\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mapping-stats\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:mappingCount}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Mapped Fields\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:unmappedHeaders.length}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Unmapped Columns\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsxs(\"span\",{className:`stat-value ${allRequiredMapped?'success':'error'}`,children:[Object.values(requiredFieldsStatus).filter(Boolean).length,\"/\",requiredFields.length]}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"Required Fields\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mapping-actions\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:applyAutoMapping,className:\"btn btn-outline\",children:\"\\uD83E\\uDD16 Auto Map\"}),/*#__PURE__*/_jsx(\"button\",{onClick:clearAllMappings,className:\"btn btn-outline\",children:\"\\uD83D\\uDDD1\\uFE0F Clear All\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"required-fields-status\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Required Fields Status\"}),/*#__PURE__*/_jsx(\"div\",{className:\"required-fields-grid\",children:requiredFields.map(field=>{const isMapped=Object.values(mapping).includes(field.key);const hasDefaultValue=defaultValues[field.key]&&defaultValues[field.key].trim()!=='';return/*#__PURE__*/_jsxs(\"div\",{className:`required-field ${requiredFieldsStatus[field.key]?'mapped':'unmapped'}`,children:[/*#__PURE__*/_jsx(\"span\",{className:\"field-name\",children:field.label}),/*#__PURE__*/_jsx(\"span\",{className:\"field-status\",children:isMapped?'📋 Mapped':hasDefaultValue?'⚙️ Default':'❌ Missing'})]},field.key);})})]}),requiredFields.some(field=>!Object.values(mapping).includes(field.key))&&/*#__PURE__*/_jsxs(\"div\",{className:\"default-values-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Default Values for Missing Required Fields\"}),/*#__PURE__*/_jsx(\"p\",{className:\"section-description\",children:\"Provide default values for required fields that are not present in your Excel file. These values will be applied to all imported records.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"default-values-grid\",children:requiredFields.filter(field=>!Object.values(mapping).includes(field.key)).map(field=>{var _field$options,_field$validation;return/*#__PURE__*/_jsxs(\"div\",{className:\"default-value-input\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"default-value-label\",children:[field.label,\" \",/*#__PURE__*/_jsx(\"span\",{className:\"required-indicator\",children:\"*\"})]}),field.type==='select'?/*#__PURE__*/_jsxs(\"select\",{value:defaultValues[field.key]||'',onChange:e=>handleDefaultValueChange(field.key,e.target.value),className:\"default-value-select\",children:[/*#__PURE__*/_jsxs(\"option\",{value:\"\",children:[\"Select \",field.label]}),(_field$options=field.options)===null||_field$options===void 0?void 0:_field$options.map(option=>/*#__PURE__*/_jsx(\"option\",{value:option.value,children:option.label},option.value))]}):/*#__PURE__*/_jsx(\"input\",{type:field.type||'text',value:defaultValues[field.key]||'',onChange:e=>handleDefaultValueChange(field.key,e.target.value),placeholder:`Enter default ${field.label.toLowerCase()}`,className:\"default-value-input-field\"}),((_field$validation=field.validation)===null||_field$validation===void 0?void 0:_field$validation.pattern)&&/*#__PURE__*/_jsxs(\"small\",{className:\"field-hint\",children:[\"Format: \",field.validation.pattern]})]},field.key);})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mapping-table-container\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"mapping-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"File Column\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Sample Data\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Maps To\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Field Type\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Required\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:fileHeaders.map(header=>{const mappedField=mapping[header]?getPersonFieldByKey(mapping[header]):null;return/*#__PURE__*/_jsxs(\"tr\",{className:mapping[header]?'mapped-row':'unmapped-row',children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"div\",{className:\"file-header\",children:/*#__PURE__*/_jsx(\"strong\",{children:header})})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"div\",{className:\"sample-data\",children:/*#__PURE__*/_jsx(\"em\",{children:\"Sample data preview\"})})}),/*#__PURE__*/_jsx(\"td\",{children:renderFieldSelect(header)}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"div\",{className:\"field-type\",children:mappedField?/*#__PURE__*/_jsx(\"span\",{className:\"type-badge\",children:mappedField.type}):/*#__PURE__*/_jsx(\"span\",{className:\"no-mapping\",children:\"-\"})})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"div\",{className:\"required-indicator\",children:mappedField!==null&&mappedField!==void 0&&mappedField.required?/*#__PURE__*/_jsx(\"span\",{className:\"required-badge\",children:\"Required\"}):/*#__PURE__*/_jsx(\"span\",{className:\"optional-badge\",children:\"Optional\"})})})]},header);})})]})}),unmappedHeaders.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"unmapped-warning\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u26A0\\uFE0F Unmapped Columns\"}),/*#__PURE__*/_jsx(\"p\",{children:\"The following columns from your file will be ignored during import:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"unmapped-list\",children:unmappedHeaders.map(header=>/*#__PURE__*/_jsx(\"span\",{className:\"unmapped-header\",children:header},header))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mapping-summary\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Mapping Summary\"}),/*#__PURE__*/_jsx(\"div\",{className:\"summary-content\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"summary-section\",children:[/*#__PURE__*/_jsxs(\"h5\",{children:[\"Mapped Fields (\",mappingCount,\")\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"mapped-fields-list\",children:getMappedPersonFields().map(field=>/*#__PURE__*/_jsxs(\"div\",{className:\"mapped-field-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"field-label\",children:field.label}),/*#__PURE__*/_jsx(\"span\",{className:`field-badge ${field.required?'required':'optional'}`,children:field.required?'Required':'Optional'})]},field.key))})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mapping-navigation\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:onBack,className:\"btn btn-outline\",children:\"\\u2190 Back to Upload\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleContinue,disabled:!allRequiredMapped,className:\"btn btn-primary\",children:\"Continue to Import \\u2192\"})]}),!allRequiredMapped&&/*#__PURE__*/_jsxs(\"div\",{className:\"validation-error\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"error-icon\",children:\"\\u26A0\\uFE0F\"}),\"Please map all required fields before continuing\"]})]});};export default FieldMapping;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PersonFieldDefinitions", "<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>s", "jsx", "_jsx", "jsxs", "_jsxs", "FieldMapping", "_ref", "fileHeaders", "onMappingComplete", "onBack", "error", "mapping", "setMapping", "unmappedHeaders", "setUnmappedHeaders", "requiredFields<PERSON><PERSON>us", "setRequiredFieldsStatus", "autoMappingSuggestions", "setAutoMappingSuggestions", "defaultValues", "set<PERSON>efault<PERSON><PERSON><PERSON>", "personFields", "requiredFields", "filter", "field", "required", "suggestions", "autoMapFields", "updateUnmappedHeaders", "updateRequiredFieldsStatus", "for<PERSON>ach", "header", "normalizedHeader", "toLowerCase", "trim", "exactMatch", "find", "label", "key", "partialMatch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "includes", "handleMappingChange", "fileHeader", "person<PERSON><PERSON><PERSON><PERSON>", "newMapping", "Object", "keys", "currentMapping", "mapped", "unmapped", "<PERSON><PERSON><PERSON>s", "values", "status", "isMapped", "hasDefaultValue", "handleDefaultValueChange", "value", "newDefaultValues", "applyAutoMapping", "clearAllMappings", "handleContinue", "missingRequired", "length", "alert", "map", "f", "join", "completeMapping", "fieldMapping", "getPersonFieldByKey", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "Boolean", "getAvailablePersonFields", "<PERSON><PERSON><PERSON><PERSON>", "entries", "_ref2", "_", "_ref3", "groupPersonFieldsBySection", "fields", "sections", "sectionKey", "section", "_PersonFieldDefinitio", "title", "push", "renderFieldSelect", "availableFields", "onChange", "e", "target", "className", "children", "_ref4", "allRequiredMapped", "every", "mappingCount", "onClick", "some", "_field$options", "_field$validation", "type", "options", "option", "placeholder", "validation", "pattern", "mappedField", "disabled"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/import/FieldMapping.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport './FieldMapping.css';\n\nconst FieldMapping = ({ fileHeaders, onMappingComplete, onBack, error }) => {\n  const [mapping, setMapping] = useState({});\n  const [unmappedHeaders, setUnmappedHeaders] = useState([]);\n  const [requiredFieldsStatus, setRequiredFieldsStatus] = useState({});\n  const [autoMappingSuggestions, setAutoMappingSuggestions] = useState({});\n  const [defaultValues, setDefaultValues] = useState({});\n\n  const personFields = getAllPersonFields();\n  const requiredFields = personFields.filter(field => field.required);\n\n  useEffect(() => {\n    // Auto-suggest mappings based on header names\n    const suggestions = autoMapFields();\n    setAutoMappingSuggestions(suggestions);\n    setMapping(suggestions);\n    updateUnmappedHeaders(suggestions);\n    updateRequiredFieldsStatus(suggestions);\n  }, [fileHeaders]);\n\n  // Re-validate when default values change\n  useEffect(() => {\n    updateRequiredFieldsStatus(mapping);\n  }, [defaultValues]);\n\n  const autoMapFields = () => {\n    const suggestions = {};\n    \n    fileHeaders.forEach(header => {\n      const normalizedHeader = header.toLowerCase().trim();\n      \n      // Find exact matches first\n      const exactMatch = personFields.find(field => \n        field.label.toLowerCase() === normalizedHeader ||\n        field.key.toLowerCase() === normalizedHeader\n      );\n      \n      if (exactMatch) {\n        suggestions[header] = exactMatch.key;\n        return;\n      }\n\n      // Find partial matches\n      const partialMatch = personFields.find(field => {\n        const fieldLabel = field.label.toLowerCase();\n        const fieldKey = field.key.toLowerCase();\n        \n        return fieldLabel.includes(normalizedHeader) || \n               normalizedHeader.includes(fieldLabel) ||\n               fieldKey.includes(normalizedHeader) ||\n               normalizedHeader.includes(fieldKey);\n      });\n\n      if (partialMatch) {\n        suggestions[header] = partialMatch.key;\n      }\n    });\n\n    return suggestions;\n  };\n\n  const handleMappingChange = (fileHeader, personFieldKey) => {\n    const newMapping = { ...mapping };\n    \n    if (personFieldKey === '') {\n      delete newMapping[fileHeader];\n    } else {\n      // Remove any existing mapping to this person field\n      Object.keys(newMapping).forEach(key => {\n        if (newMapping[key] === personFieldKey && key !== fileHeader) {\n          delete newMapping[key];\n        }\n      });\n      \n      newMapping[fileHeader] = personFieldKey;\n    }\n    \n    setMapping(newMapping);\n    updateUnmappedHeaders(newMapping);\n    updateRequiredFieldsStatus(newMapping);\n  };\n\n  const updateUnmappedHeaders = (currentMapping) => {\n    const mapped = Object.keys(currentMapping);\n    const unmapped = fileHeaders.filter(header => !mapped.includes(header));\n    setUnmappedHeaders(unmapped);\n  };\n\n  const updateRequiredFieldsStatus = (currentMapping) => {\n    const mappedFields = Object.values(currentMapping);\n    const status = {};\n\n    requiredFields.forEach(field => {\n      // Field is satisfied if it's mapped from Excel OR has a default value\n      const isMapped = mappedFields.includes(field.key);\n      const hasDefaultValue = defaultValues[field.key] && defaultValues[field.key].trim() !== '';\n      status[field.key] = isMapped || hasDefaultValue;\n    });\n\n    setRequiredFieldsStatus(status);\n  };\n\n  const handleDefaultValueChange = (fieldKey, value) => {\n    const newDefaultValues = { ...defaultValues, [fieldKey]: value };\n    setDefaultValues(newDefaultValues);\n    // Re-validate required fields status when default values change\n    updateRequiredFieldsStatus(mapping);\n  };\n\n  const applyAutoMapping = () => {\n    setMapping(autoMappingSuggestions);\n    updateUnmappedHeaders(autoMappingSuggestions);\n    updateRequiredFieldsStatus(autoMappingSuggestions);\n  };\n\n  const clearAllMappings = () => {\n    setMapping({});\n    updateUnmappedHeaders({});\n    updateRequiredFieldsStatus({});\n  };\n\n  const handleContinue = () => {\n    // Validate that all required fields are mapped or have default values\n    const missingRequired = requiredFields.filter(field => !requiredFieldsStatus[field.key]);\n\n    if (missingRequired.length > 0) {\n      alert(`Please map the following required fields or provide default values: ${missingRequired.map(f => f.label).join(', ')}`);\n      return;\n    }\n\n    // Include both field mapping and default values\n    const completeMapping = {\n      fieldMapping: mapping,\n      defaultValues: defaultValues\n    };\n\n    onMappingComplete(completeMapping);\n  };\n\n  const getPersonFieldByKey = (key) => {\n    return personFields.find(field => field.key === key);\n  };\n\n  const getMappedPersonFields = () => {\n    return Object.values(mapping).map(key => getPersonFieldByKey(key)).filter(Boolean);\n  };\n\n  const getAvailablePersonFields = (currentHeader) => {\n    const mappedFields = Object.entries(mapping)\n      .filter(([header, _]) => header !== currentHeader)\n      .map(([_, fieldKey]) => fieldKey);\n    \n    return personFields.filter(field => !mappedFields.includes(field.key));\n  };\n\n  const groupPersonFieldsBySection = (fields) => {\n    const sections = {};\n    fields.forEach(field => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: PersonFieldDefinitions[sectionKey]?.title || sectionKey,\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    return sections;\n  };\n\n  const renderFieldSelect = (fileHeader) => {\n    const availableFields = getAvailablePersonFields(fileHeader);\n    const sections = groupPersonFieldsBySection(availableFields);\n    const currentMapping = mapping[fileHeader] || '';\n\n    return (\n      <select\n        value={currentMapping}\n        onChange={(e) => handleMappingChange(fileHeader, e.target.value)}\n        className={`field-select ${currentMapping ? 'mapped' : 'unmapped'}`}\n      >\n        <option value=\"\">-- Select Field --</option>\n        {Object.entries(sections).map(([sectionKey, section]) => (\n          <optgroup key={sectionKey} label={section.title}>\n            {section.fields.map(field => (\n              <option key={field.key} value={field.key}>\n                {field.label} {field.required ? '*' : ''}\n              </option>\n            ))}\n          </optgroup>\n        ))}\n      </select>\n    );\n  };\n\n  const allRequiredMapped = requiredFields.every(field => requiredFieldsStatus[field.key]);\n  const mappingCount = Object.keys(mapping).length;\n\n  return (\n    <div className=\"field-mapping\">\n      <div className=\"mapping-header\">\n        <h3>Map File Columns to Person Fields</h3>\n        <p>Match your file columns with the corresponding person fields. Required fields are marked with *</p>\n        \n        <div className=\"mapping-stats\">\n          <div className=\"stat\">\n            <span className=\"stat-value\">{mappingCount}</span>\n            <span className=\"stat-label\">Mapped Fields</span>\n          </div>\n          <div className=\"stat\">\n            <span className=\"stat-value\">{unmappedHeaders.length}</span>\n            <span className=\"stat-label\">Unmapped Columns</span>\n          </div>\n          <div className=\"stat\">\n            <span className={`stat-value ${allRequiredMapped ? 'success' : 'error'}`}>\n              {Object.values(requiredFieldsStatus).filter(Boolean).length}/{requiredFields.length}\n            </span>\n            <span className=\"stat-label\">Required Fields</span>\n          </div>\n        </div>\n\n        <div className=\"mapping-actions\">\n          <button onClick={applyAutoMapping} className=\"btn btn-outline\">\n            🤖 Auto Map\n          </button>\n          <button onClick={clearAllMappings} className=\"btn btn-outline\">\n            🗑️ Clear All\n          </button>\n        </div>\n      </div>\n\n      {/* Required Fields Status */}\n      <div className=\"required-fields-status\">\n        <h4>Required Fields Status</h4>\n        <div className=\"required-fields-grid\">\n          {requiredFields.map(field => {\n            const isMapped = Object.values(mapping).includes(field.key);\n            const hasDefaultValue = defaultValues[field.key] && defaultValues[field.key].trim() !== '';\n\n            return (\n              <div\n                key={field.key}\n                className={`required-field ${requiredFieldsStatus[field.key] ? 'mapped' : 'unmapped'}`}\n              >\n                <span className=\"field-name\">{field.label}</span>\n                <span className=\"field-status\">\n                  {isMapped ? '📋 Mapped' : hasDefaultValue ? '⚙️ Default' : '❌ Missing'}\n                </span>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Default Values for Unmapped Required Fields */}\n      {requiredFields.some(field => !Object.values(mapping).includes(field.key)) && (\n        <div className=\"default-values-section\">\n          <h4>Default Values for Missing Required Fields</h4>\n          <p className=\"section-description\">\n            Provide default values for required fields that are not present in your Excel file.\n            These values will be applied to all imported records.\n          </p>\n          <div className=\"default-values-grid\">\n            {requiredFields\n              .filter(field => !Object.values(mapping).includes(field.key))\n              .map(field => (\n                <div key={field.key} className=\"default-value-input\">\n                  <label className=\"default-value-label\">\n                    {field.label} <span className=\"required-indicator\">*</span>\n                  </label>\n                  {field.type === 'select' ? (\n                    <select\n                      value={defaultValues[field.key] || ''}\n                      onChange={(e) => handleDefaultValueChange(field.key, e.target.value)}\n                      className=\"default-value-select\"\n                    >\n                      <option value=\"\">Select {field.label}</option>\n                      {field.options?.map(option => (\n                        <option key={option.value} value={option.value}>\n                          {option.label}\n                        </option>\n                      ))}\n                    </select>\n                  ) : (\n                    <input\n                      type={field.type || 'text'}\n                      value={defaultValues[field.key] || ''}\n                      onChange={(e) => handleDefaultValueChange(field.key, e.target.value)}\n                      placeholder={`Enter default ${field.label.toLowerCase()}`}\n                      className=\"default-value-input-field\"\n                    />\n                  )}\n                  {field.validation?.pattern && (\n                    <small className=\"field-hint\">\n                      Format: {field.validation.pattern}\n                    </small>\n                  )}\n                </div>\n              ))}\n          </div>\n        </div>\n      )}\n\n      {/* Mapping Table */}\n      <div className=\"mapping-table-container\">\n        <table className=\"mapping-table\">\n          <thead>\n            <tr>\n              <th>File Column</th>\n              <th>Sample Data</th>\n              <th>Maps To</th>\n              <th>Field Type</th>\n              <th>Required</th>\n            </tr>\n          </thead>\n          <tbody>\n            {fileHeaders.map(header => {\n              const mappedField = mapping[header] ? getPersonFieldByKey(mapping[header]) : null;\n              return (\n                <tr key={header} className={mapping[header] ? 'mapped-row' : 'unmapped-row'}>\n                  <td>\n                    <div className=\"file-header\">\n                      <strong>{header}</strong>\n                    </div>\n                  </td>\n                  <td>\n                    <div className=\"sample-data\">\n                      {/* This would show sample data from the file */}\n                      <em>Sample data preview</em>\n                    </div>\n                  </td>\n                  <td>\n                    {renderFieldSelect(header)}\n                  </td>\n                  <td>\n                    <div className=\"field-type\">\n                      {mappedField ? (\n                        <span className=\"type-badge\">{mappedField.type}</span>\n                      ) : (\n                        <span className=\"no-mapping\">-</span>\n                      )}\n                    </div>\n                  </td>\n                  <td>\n                    <div className=\"required-indicator\">\n                      {mappedField?.required ? (\n                        <span className=\"required-badge\">Required</span>\n                      ) : (\n                        <span className=\"optional-badge\">Optional</span>\n                      )}\n                    </div>\n                  </td>\n                </tr>\n              );\n            })}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Unmapped Headers Warning */}\n      {unmappedHeaders.length > 0 && (\n        <div className=\"unmapped-warning\">\n          <h4>⚠️ Unmapped Columns</h4>\n          <p>The following columns from your file will be ignored during import:</p>\n          <div className=\"unmapped-list\">\n            {unmappedHeaders.map(header => (\n              <span key={header} className=\"unmapped-header\">{header}</span>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Mapping Summary */}\n      <div className=\"mapping-summary\">\n        <h4>Mapping Summary</h4>\n        <div className=\"summary-content\">\n          <div className=\"summary-section\">\n            <h5>Mapped Fields ({mappingCount})</h5>\n            <div className=\"mapped-fields-list\">\n              {getMappedPersonFields().map(field => (\n                <div key={field.key} className=\"mapped-field-item\">\n                  <span className=\"field-label\">{field.label}</span>\n                  <span className={`field-badge ${field.required ? 'required' : 'optional'}`}>\n                    {field.required ? 'Required' : 'Optional'}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"mapping-navigation\">\n        <button onClick={onBack} className=\"btn btn-outline\">\n          ← Back to Upload\n        </button>\n        <button\n          onClick={handleContinue}\n          disabled={!allRequiredMapped}\n          className=\"btn btn-primary\"\n        >\n          Continue to Import →\n        </button>\n      </div>\n\n      {!allRequiredMapped && (\n        <div className=\"validation-error\">\n          <span className=\"error-icon\">⚠️</span>\n          Please map all required fields before continuing\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FieldMapping;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,sBAAsB,CAAEC,kBAAkB,KAAQ,iCAAiC,CAC5F,MAAO,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5B,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAAuD,IAAtD,CAAEC,WAAW,CAAEC,iBAAiB,CAAEC,MAAM,CAAEC,KAAM,CAAC,CAAAJ,IAAA,CACrE,KAAM,CAACK,OAAO,CAAEC,UAAU,CAAC,CAAGf,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC1C,KAAM,CAACgB,eAAe,CAAEC,kBAAkB,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACkB,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAC,CACpE,KAAM,CAACoB,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC,CACxE,KAAM,CAACsB,aAAa,CAAEC,gBAAgB,CAAC,CAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAEtD,KAAM,CAAAwB,YAAY,CAAGrB,kBAAkB,CAAC,CAAC,CACzC,KAAM,CAAAsB,cAAc,CAAGD,YAAY,CAACE,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACC,QAAQ,CAAC,CAEnE3B,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA4B,WAAW,CAAGC,aAAa,CAAC,CAAC,CACnCT,yBAAyB,CAACQ,WAAW,CAAC,CACtCd,UAAU,CAACc,WAAW,CAAC,CACvBE,qBAAqB,CAACF,WAAW,CAAC,CAClCG,0BAA0B,CAACH,WAAW,CAAC,CACzC,CAAC,CAAE,CAACnB,WAAW,CAAC,CAAC,CAEjB;AACAT,SAAS,CAAC,IAAM,CACd+B,0BAA0B,CAAClB,OAAO,CAAC,CACrC,CAAC,CAAE,CAACQ,aAAa,CAAC,CAAC,CAEnB,KAAM,CAAAQ,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAAD,WAAW,CAAG,CAAC,CAAC,CAEtBnB,WAAW,CAACuB,OAAO,CAACC,MAAM,EAAI,CAC5B,KAAM,CAAAC,gBAAgB,CAAGD,MAAM,CAACE,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAEpD;AACA,KAAM,CAAAC,UAAU,CAAGd,YAAY,CAACe,IAAI,CAACZ,KAAK,EACxCA,KAAK,CAACa,KAAK,CAACJ,WAAW,CAAC,CAAC,GAAKD,gBAAgB,EAC9CR,KAAK,CAACc,GAAG,CAACL,WAAW,CAAC,CAAC,GAAKD,gBAC9B,CAAC,CAED,GAAIG,UAAU,CAAE,CACdT,WAAW,CAACK,MAAM,CAAC,CAAGI,UAAU,CAACG,GAAG,CACpC,OACF,CAEA;AACA,KAAM,CAAAC,YAAY,CAAGlB,YAAY,CAACe,IAAI,CAACZ,KAAK,EAAI,CAC9C,KAAM,CAAAgB,UAAU,CAAGhB,KAAK,CAACa,KAAK,CAACJ,WAAW,CAAC,CAAC,CAC5C,KAAM,CAAAQ,QAAQ,CAAGjB,KAAK,CAACc,GAAG,CAACL,WAAW,CAAC,CAAC,CAExC,MAAO,CAAAO,UAAU,CAACE,QAAQ,CAACV,gBAAgB,CAAC,EACrCA,gBAAgB,CAACU,QAAQ,CAACF,UAAU,CAAC,EACrCC,QAAQ,CAACC,QAAQ,CAACV,gBAAgB,CAAC,EACnCA,gBAAgB,CAACU,QAAQ,CAACD,QAAQ,CAAC,CAC5C,CAAC,CAAC,CAEF,GAAIF,YAAY,CAAE,CAChBb,WAAW,CAACK,MAAM,CAAC,CAAGQ,YAAY,CAACD,GAAG,CACxC,CACF,CAAC,CAAC,CAEF,MAAO,CAAAZ,WAAW,CACpB,CAAC,CAED,KAAM,CAAAiB,mBAAmB,CAAGA,CAACC,UAAU,CAAEC,cAAc,GAAK,CAC1D,KAAM,CAAAC,UAAU,CAAG,CAAE,GAAGnC,OAAQ,CAAC,CAEjC,GAAIkC,cAAc,GAAK,EAAE,CAAE,CACzB,MAAO,CAAAC,UAAU,CAACF,UAAU,CAAC,CAC/B,CAAC,IAAM,CACL;AACAG,MAAM,CAACC,IAAI,CAACF,UAAU,CAAC,CAAChB,OAAO,CAACQ,GAAG,EAAI,CACrC,GAAIQ,UAAU,CAACR,GAAG,CAAC,GAAKO,cAAc,EAAIP,GAAG,GAAKM,UAAU,CAAE,CAC5D,MAAO,CAAAE,UAAU,CAACR,GAAG,CAAC,CACxB,CACF,CAAC,CAAC,CAEFQ,UAAU,CAACF,UAAU,CAAC,CAAGC,cAAc,CACzC,CAEAjC,UAAU,CAACkC,UAAU,CAAC,CACtBlB,qBAAqB,CAACkB,UAAU,CAAC,CACjCjB,0BAA0B,CAACiB,UAAU,CAAC,CACxC,CAAC,CAED,KAAM,CAAAlB,qBAAqB,CAAIqB,cAAc,EAAK,CAChD,KAAM,CAAAC,MAAM,CAAGH,MAAM,CAACC,IAAI,CAACC,cAAc,CAAC,CAC1C,KAAM,CAAAE,QAAQ,CAAG5C,WAAW,CAACgB,MAAM,CAACQ,MAAM,EAAI,CAACmB,MAAM,CAACR,QAAQ,CAACX,MAAM,CAAC,CAAC,CACvEjB,kBAAkB,CAACqC,QAAQ,CAAC,CAC9B,CAAC,CAED,KAAM,CAAAtB,0BAA0B,CAAIoB,cAAc,EAAK,CACrD,KAAM,CAAAG,YAAY,CAAGL,MAAM,CAACM,MAAM,CAACJ,cAAc,CAAC,CAClD,KAAM,CAAAK,MAAM,CAAG,CAAC,CAAC,CAEjBhC,cAAc,CAACQ,OAAO,CAACN,KAAK,EAAI,CAC9B;AACA,KAAM,CAAA+B,QAAQ,CAAGH,YAAY,CAACV,QAAQ,CAAClB,KAAK,CAACc,GAAG,CAAC,CACjD,KAAM,CAAAkB,eAAe,CAAGrC,aAAa,CAACK,KAAK,CAACc,GAAG,CAAC,EAAInB,aAAa,CAACK,KAAK,CAACc,GAAG,CAAC,CAACJ,IAAI,CAAC,CAAC,GAAK,EAAE,CAC1FoB,MAAM,CAAC9B,KAAK,CAACc,GAAG,CAAC,CAAGiB,QAAQ,EAAIC,eAAe,CACjD,CAAC,CAAC,CAEFxC,uBAAuB,CAACsC,MAAM,CAAC,CACjC,CAAC,CAED,KAAM,CAAAG,wBAAwB,CAAGA,CAAChB,QAAQ,CAAEiB,KAAK,GAAK,CACpD,KAAM,CAAAC,gBAAgB,CAAG,CAAE,GAAGxC,aAAa,CAAE,CAACsB,QAAQ,EAAGiB,KAAM,CAAC,CAChEtC,gBAAgB,CAACuC,gBAAgB,CAAC,CAClC;AACA9B,0BAA0B,CAAClB,OAAO,CAAC,CACrC,CAAC,CAED,KAAM,CAAAiD,gBAAgB,CAAGA,CAAA,GAAM,CAC7BhD,UAAU,CAACK,sBAAsB,CAAC,CAClCW,qBAAqB,CAACX,sBAAsB,CAAC,CAC7CY,0BAA0B,CAACZ,sBAAsB,CAAC,CACpD,CAAC,CAED,KAAM,CAAA4C,gBAAgB,CAAGA,CAAA,GAAM,CAC7BjD,UAAU,CAAC,CAAC,CAAC,CAAC,CACdgB,qBAAqB,CAAC,CAAC,CAAC,CAAC,CACzBC,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAChC,CAAC,CAED,KAAM,CAAAiC,cAAc,CAAGA,CAAA,GAAM,CAC3B;AACA,KAAM,CAAAC,eAAe,CAAGzC,cAAc,CAACC,MAAM,CAACC,KAAK,EAAI,CAACT,oBAAoB,CAACS,KAAK,CAACc,GAAG,CAAC,CAAC,CAExF,GAAIyB,eAAe,CAACC,MAAM,CAAG,CAAC,CAAE,CAC9BC,KAAK,CAAC,uEAAuEF,eAAe,CAACG,GAAG,CAACC,CAAC,EAAIA,CAAC,CAAC9B,KAAK,CAAC,CAAC+B,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAC5H,OACF,CAEA;AACA,KAAM,CAAAC,eAAe,CAAG,CACtBC,YAAY,CAAE3D,OAAO,CACrBQ,aAAa,CAAEA,aACjB,CAAC,CAEDX,iBAAiB,CAAC6D,eAAe,CAAC,CACpC,CAAC,CAED,KAAM,CAAAE,mBAAmB,CAAIjC,GAAG,EAAK,CACnC,MAAO,CAAAjB,YAAY,CAACe,IAAI,CAACZ,KAAK,EAAIA,KAAK,CAACc,GAAG,GAAKA,GAAG,CAAC,CACtD,CAAC,CAED,KAAM,CAAAkC,qBAAqB,CAAGA,CAAA,GAAM,CAClC,MAAO,CAAAzB,MAAM,CAACM,MAAM,CAAC1C,OAAO,CAAC,CAACuD,GAAG,CAAC5B,GAAG,EAAIiC,mBAAmB,CAACjC,GAAG,CAAC,CAAC,CAACf,MAAM,CAACkD,OAAO,CAAC,CACpF,CAAC,CAED,KAAM,CAAAC,wBAAwB,CAAIC,aAAa,EAAK,CAClD,KAAM,CAAAvB,YAAY,CAAGL,MAAM,CAAC6B,OAAO,CAACjE,OAAO,CAAC,CACzCY,MAAM,CAACsD,KAAA,MAAC,CAAC9C,MAAM,CAAE+C,CAAC,CAAC,CAAAD,KAAA,OAAK,CAAA9C,MAAM,GAAK4C,aAAa,GAAC,CACjDT,GAAG,CAACa,KAAA,MAAC,CAACD,CAAC,CAAErC,QAAQ,CAAC,CAAAsC,KAAA,OAAK,CAAAtC,QAAQ,GAAC,CAEnC,MAAO,CAAApB,YAAY,CAACE,MAAM,CAACC,KAAK,EAAI,CAAC4B,YAAY,CAACV,QAAQ,CAAClB,KAAK,CAACc,GAAG,CAAC,CAAC,CACxE,CAAC,CAED,KAAM,CAAA0C,0BAA0B,CAAIC,MAAM,EAAK,CAC7C,KAAM,CAAAC,QAAQ,CAAG,CAAC,CAAC,CACnBD,MAAM,CAACnD,OAAO,CAACN,KAAK,EAAI,CACtB,KAAM,CAAA2D,UAAU,CAAG3D,KAAK,CAAC4D,OAAO,EAAI,SAAS,CAC7C,GAAI,CAACF,QAAQ,CAACC,UAAU,CAAC,CAAE,KAAAE,qBAAA,CACzBH,QAAQ,CAACC,UAAU,CAAC,CAAG,CACrBG,KAAK,CAAE,EAAAD,qBAAA,CAAAtF,sBAAsB,CAACoF,UAAU,CAAC,UAAAE,qBAAA,iBAAlCA,qBAAA,CAAoCC,KAAK,GAAIH,UAAU,CAC9DF,MAAM,CAAE,EACV,CAAC,CACH,CACAC,QAAQ,CAACC,UAAU,CAAC,CAACF,MAAM,CAACM,IAAI,CAAC/D,KAAK,CAAC,CACzC,CAAC,CAAC,CACF,MAAO,CAAA0D,QAAQ,CACjB,CAAC,CAED,KAAM,CAAAM,iBAAiB,CAAI5C,UAAU,EAAK,CACxC,KAAM,CAAA6C,eAAe,CAAGf,wBAAwB,CAAC9B,UAAU,CAAC,CAC5D,KAAM,CAAAsC,QAAQ,CAAGF,0BAA0B,CAACS,eAAe,CAAC,CAC5D,KAAM,CAAAxC,cAAc,CAAGtC,OAAO,CAACiC,UAAU,CAAC,EAAI,EAAE,CAEhD,mBACExC,KAAA,WACEsD,KAAK,CAAET,cAAe,CACtByC,QAAQ,CAAGC,CAAC,EAAKhD,mBAAmB,CAACC,UAAU,CAAE+C,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE,CACjEmC,SAAS,CAAE,gBAAgB5C,cAAc,CAAG,QAAQ,CAAG,UAAU,EAAG,CAAA6C,QAAA,eAEpE5F,IAAA,WAAQwD,KAAK,CAAC,EAAE,CAAAoC,QAAA,CAAC,oBAAkB,CAAQ,CAAC,CAC3C/C,MAAM,CAAC6B,OAAO,CAACM,QAAQ,CAAC,CAAChB,GAAG,CAAC6B,KAAA,MAAC,CAACZ,UAAU,CAAEC,OAAO,CAAC,CAAAW,KAAA,oBAClD7F,IAAA,aAA2BmC,KAAK,CAAE+C,OAAO,CAACE,KAAM,CAAAQ,QAAA,CAC7CV,OAAO,CAACH,MAAM,CAACf,GAAG,CAAC1C,KAAK,eACvBpB,KAAA,WAAwBsD,KAAK,CAAElC,KAAK,CAACc,GAAI,CAAAwD,QAAA,EACtCtE,KAAK,CAACa,KAAK,CAAC,GAAC,CAACb,KAAK,CAACC,QAAQ,CAAG,GAAG,CAAG,EAAE,GAD7BD,KAAK,CAACc,GAEX,CACT,CAAC,EALW6C,UAML,CAAC,EACZ,CAAC,EACI,CAAC,CAEb,CAAC,CAED,KAAM,CAAAa,iBAAiB,CAAG1E,cAAc,CAAC2E,KAAK,CAACzE,KAAK,EAAIT,oBAAoB,CAACS,KAAK,CAACc,GAAG,CAAC,CAAC,CACxF,KAAM,CAAA4D,YAAY,CAAGnD,MAAM,CAACC,IAAI,CAACrC,OAAO,CAAC,CAACqD,MAAM,CAEhD,mBACE5D,KAAA,QAAKyF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B1F,KAAA,QAAKyF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B5F,IAAA,OAAA4F,QAAA,CAAI,mCAAiC,CAAI,CAAC,cAC1C5F,IAAA,MAAA4F,QAAA,CAAG,iGAA+F,CAAG,CAAC,cAEtG1F,KAAA,QAAKyF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B1F,KAAA,QAAKyF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB5F,IAAA,SAAM2F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEI,YAAY,CAAO,CAAC,cAClDhG,IAAA,SAAM2F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,eAAa,CAAM,CAAC,EAC9C,CAAC,cACN1F,KAAA,QAAKyF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB5F,IAAA,SAAM2F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEjF,eAAe,CAACmD,MAAM,CAAO,CAAC,cAC5D9D,IAAA,SAAM2F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,EACjD,CAAC,cACN1F,KAAA,QAAKyF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB1F,KAAA,SAAMyF,SAAS,CAAE,cAAcG,iBAAiB,CAAG,SAAS,CAAG,OAAO,EAAG,CAAAF,QAAA,EACtE/C,MAAM,CAACM,MAAM,CAACtC,oBAAoB,CAAC,CAACQ,MAAM,CAACkD,OAAO,CAAC,CAACT,MAAM,CAAC,GAAC,CAAC1C,cAAc,CAAC0C,MAAM,EAC/E,CAAC,cACP9D,IAAA,SAAM2F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,iBAAe,CAAM,CAAC,EAChD,CAAC,EACH,CAAC,cAEN1F,KAAA,QAAKyF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B5F,IAAA,WAAQiG,OAAO,CAAEvC,gBAAiB,CAACiC,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,uBAE/D,CAAQ,CAAC,cACT5F,IAAA,WAAQiG,OAAO,CAAEtC,gBAAiB,CAACgC,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,8BAE/D,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAGN1F,KAAA,QAAKyF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC5F,IAAA,OAAA4F,QAAA,CAAI,wBAAsB,CAAI,CAAC,cAC/B5F,IAAA,QAAK2F,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAClCxE,cAAc,CAAC4C,GAAG,CAAC1C,KAAK,EAAI,CAC3B,KAAM,CAAA+B,QAAQ,CAAGR,MAAM,CAACM,MAAM,CAAC1C,OAAO,CAAC,CAAC+B,QAAQ,CAAClB,KAAK,CAACc,GAAG,CAAC,CAC3D,KAAM,CAAAkB,eAAe,CAAGrC,aAAa,CAACK,KAAK,CAACc,GAAG,CAAC,EAAInB,aAAa,CAACK,KAAK,CAACc,GAAG,CAAC,CAACJ,IAAI,CAAC,CAAC,GAAK,EAAE,CAE1F,mBACE9B,KAAA,QAEEyF,SAAS,CAAE,kBAAkB9E,oBAAoB,CAACS,KAAK,CAACc,GAAG,CAAC,CAAG,QAAQ,CAAG,UAAU,EAAG,CAAAwD,QAAA,eAEvF5F,IAAA,SAAM2F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEtE,KAAK,CAACa,KAAK,CAAO,CAAC,cACjDnC,IAAA,SAAM2F,SAAS,CAAC,cAAc,CAAAC,QAAA,CAC3BvC,QAAQ,CAAG,WAAW,CAAGC,eAAe,CAAG,YAAY,CAAG,WAAW,CAClE,CAAC,GANFhC,KAAK,CAACc,GAOR,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,EACH,CAAC,CAGLhB,cAAc,CAAC8E,IAAI,CAAC5E,KAAK,EAAI,CAACuB,MAAM,CAACM,MAAM,CAAC1C,OAAO,CAAC,CAAC+B,QAAQ,CAAClB,KAAK,CAACc,GAAG,CAAC,CAAC,eACxElC,KAAA,QAAKyF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC5F,IAAA,OAAA4F,QAAA,CAAI,4CAA0C,CAAI,CAAC,cACnD5F,IAAA,MAAG2F,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,2IAGnC,CAAG,CAAC,cACJ5F,IAAA,QAAK2F,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CACjCxE,cAAc,CACZC,MAAM,CAACC,KAAK,EAAI,CAACuB,MAAM,CAACM,MAAM,CAAC1C,OAAO,CAAC,CAAC+B,QAAQ,CAAClB,KAAK,CAACc,GAAG,CAAC,CAAC,CAC5D4B,GAAG,CAAC1C,KAAK,OAAA6E,cAAA,CAAAC,iBAAA,oBACRlG,KAAA,QAAqByF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClD1F,KAAA,UAAOyF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EACnCtE,KAAK,CAACa,KAAK,CAAC,GAAC,cAAAnC,IAAA,SAAM2F,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,EACtD,CAAC,CACPtE,KAAK,CAAC+E,IAAI,GAAK,QAAQ,cACtBnG,KAAA,WACEsD,KAAK,CAAEvC,aAAa,CAACK,KAAK,CAACc,GAAG,CAAC,EAAI,EAAG,CACtCoD,QAAQ,CAAGC,CAAC,EAAKlC,wBAAwB,CAACjC,KAAK,CAACc,GAAG,CAAEqD,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE,CACrEmC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eAEhC1F,KAAA,WAAQsD,KAAK,CAAC,EAAE,CAAAoC,QAAA,EAAC,SAAO,CAACtE,KAAK,CAACa,KAAK,EAAS,CAAC,EAAAgE,cAAA,CAC7C7E,KAAK,CAACgF,OAAO,UAAAH,cAAA,iBAAbA,cAAA,CAAenC,GAAG,CAACuC,MAAM,eACxBvG,IAAA,WAA2BwD,KAAK,CAAE+C,MAAM,CAAC/C,KAAM,CAAAoC,QAAA,CAC5CW,MAAM,CAACpE,KAAK,EADFoE,MAAM,CAAC/C,KAEZ,CACT,CAAC,EACI,CAAC,cAETxD,IAAA,UACEqG,IAAI,CAAE/E,KAAK,CAAC+E,IAAI,EAAI,MAAO,CAC3B7C,KAAK,CAAEvC,aAAa,CAACK,KAAK,CAACc,GAAG,CAAC,EAAI,EAAG,CACtCoD,QAAQ,CAAGC,CAAC,EAAKlC,wBAAwB,CAACjC,KAAK,CAACc,GAAG,CAAEqD,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE,CACrEgD,WAAW,CAAE,iBAAiBlF,KAAK,CAACa,KAAK,CAACJ,WAAW,CAAC,CAAC,EAAG,CAC1D4D,SAAS,CAAC,2BAA2B,CACtC,CACF,CACA,EAAAS,iBAAA,CAAA9E,KAAK,CAACmF,UAAU,UAAAL,iBAAA,iBAAhBA,iBAAA,CAAkBM,OAAO,gBACxBxG,KAAA,UAAOyF,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,UACpB,CAACtE,KAAK,CAACmF,UAAU,CAACC,OAAO,EAC5B,CACR,GA9BOpF,KAAK,CAACc,GA+BX,CAAC,EACP,CAAC,CACD,CAAC,EACH,CACN,cAGDpC,IAAA,QAAK2F,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cACtC1F,KAAA,UAAOyF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC9B5F,IAAA,UAAA4F,QAAA,cACE1F,KAAA,OAAA0F,QAAA,eACE5F,IAAA,OAAA4F,QAAA,CAAI,aAAW,CAAI,CAAC,cACpB5F,IAAA,OAAA4F,QAAA,CAAI,aAAW,CAAI,CAAC,cACpB5F,IAAA,OAAA4F,QAAA,CAAI,SAAO,CAAI,CAAC,cAChB5F,IAAA,OAAA4F,QAAA,CAAI,YAAU,CAAI,CAAC,cACnB5F,IAAA,OAAA4F,QAAA,CAAI,UAAQ,CAAI,CAAC,EACf,CAAC,CACA,CAAC,cACR5F,IAAA,UAAA4F,QAAA,CACGvF,WAAW,CAAC2D,GAAG,CAACnC,MAAM,EAAI,CACzB,KAAM,CAAA8E,WAAW,CAAGlG,OAAO,CAACoB,MAAM,CAAC,CAAGwC,mBAAmB,CAAC5D,OAAO,CAACoB,MAAM,CAAC,CAAC,CAAG,IAAI,CACjF,mBACE3B,KAAA,OAAiByF,SAAS,CAAElF,OAAO,CAACoB,MAAM,CAAC,CAAG,YAAY,CAAG,cAAe,CAAA+D,QAAA,eAC1E5F,IAAA,OAAA4F,QAAA,cACE5F,IAAA,QAAK2F,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B5F,IAAA,WAAA4F,QAAA,CAAS/D,MAAM,CAAS,CAAC,CACtB,CAAC,CACJ,CAAC,cACL7B,IAAA,OAAA4F,QAAA,cACE5F,IAAA,QAAK2F,SAAS,CAAC,aAAa,CAAAC,QAAA,cAE1B5F,IAAA,OAAA4F,QAAA,CAAI,qBAAmB,CAAI,CAAC,CACzB,CAAC,CACJ,CAAC,cACL5F,IAAA,OAAA4F,QAAA,CACGN,iBAAiB,CAACzD,MAAM,CAAC,CACxB,CAAC,cACL7B,IAAA,OAAA4F,QAAA,cACE5F,IAAA,QAAK2F,SAAS,CAAC,YAAY,CAAAC,QAAA,CACxBe,WAAW,cACV3G,IAAA,SAAM2F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEe,WAAW,CAACN,IAAI,CAAO,CAAC,cAEtDrG,IAAA,SAAM2F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,GAAC,CAAM,CACrC,CACE,CAAC,CACJ,CAAC,cACL5F,IAAA,OAAA4F,QAAA,cACE5F,IAAA,QAAK2F,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAChCe,WAAW,SAAXA,WAAW,WAAXA,WAAW,CAAEpF,QAAQ,cACpBvB,IAAA,SAAM2F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,cAEhD5F,IAAA,SAAM2F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAChD,CACE,CAAC,CACJ,CAAC,GAhCE/D,MAiCL,CAAC,CAET,CAAC,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CAGLlB,eAAe,CAACmD,MAAM,CAAG,CAAC,eACzB5D,KAAA,QAAKyF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B5F,IAAA,OAAA4F,QAAA,CAAI,+BAAmB,CAAI,CAAC,cAC5B5F,IAAA,MAAA4F,QAAA,CAAG,qEAAmE,CAAG,CAAC,cAC1E5F,IAAA,QAAK2F,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3BjF,eAAe,CAACqD,GAAG,CAACnC,MAAM,eACzB7B,IAAA,SAAmB2F,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAE/D,MAAM,EAA3CA,MAAkD,CAC9D,CAAC,CACC,CAAC,EACH,CACN,cAGD3B,KAAA,QAAKyF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B5F,IAAA,OAAA4F,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxB5F,IAAA,QAAK2F,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B1F,KAAA,QAAKyF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B1F,KAAA,OAAA0F,QAAA,EAAI,iBAAe,CAACI,YAAY,CAAC,GAAC,EAAI,CAAC,cACvChG,IAAA,QAAK2F,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAChCtB,qBAAqB,CAAC,CAAC,CAACN,GAAG,CAAC1C,KAAK,eAChCpB,KAAA,QAAqByF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChD5F,IAAA,SAAM2F,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEtE,KAAK,CAACa,KAAK,CAAO,CAAC,cAClDnC,IAAA,SAAM2F,SAAS,CAAE,eAAerE,KAAK,CAACC,QAAQ,CAAG,UAAU,CAAG,UAAU,EAAG,CAAAqE,QAAA,CACxEtE,KAAK,CAACC,QAAQ,CAAG,UAAU,CAAG,UAAU,CACrC,CAAC,GAJCD,KAAK,CAACc,GAKX,CACN,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNlC,KAAA,QAAKyF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjC5F,IAAA,WAAQiG,OAAO,CAAE1F,MAAO,CAACoF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,uBAErD,CAAQ,CAAC,cACT5F,IAAA,WACEiG,OAAO,CAAErC,cAAe,CACxBgD,QAAQ,CAAE,CAACd,iBAAkB,CAC7BH,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC5B,2BAED,CAAQ,CAAC,EACN,CAAC,CAEL,CAACE,iBAAiB,eACjB5F,KAAA,QAAKyF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B5F,IAAA,SAAM2F,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,mDAExC,EAAK,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}