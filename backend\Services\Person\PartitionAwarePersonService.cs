using Microsoft.EntityFrameworkCore;
using CrmApi.Data;
using CrmApi.Models.Person;
using CrmApi.Exceptions;

namespace CrmApi.Services.Person
{
    public interface IPartitionAwarePersonService
    {
        Task<IEnumerable<Models.Person.Person>> GetPersonsByPartitionAsync(int divisionId, int? categoryId = null, int? firmNatureId = null, bool includeDeleted = false);
        Task<Models.Person.Person?> GetPersonByIdWithPartitionAsync(int id, int divisionId, int categoryId, int? firmNatureId = null);
        Task<IEnumerable<Models.Person.Person>> SearchPersonsInPartitionAsync(PersonSearchRequest request);
        Task<PersonPartitionInfo> GetPartitionInfoAsync(int divisionId, int? categoryId = null, int? firmNatureId = null);
        Task<IEnumerable<PartitionStatistics>> GetPartitionStatisticsAsync();
        Task<bool> ValidatePartitionExistsAsync(int divisionId, int categoryId, int? firmNatureId = null);
    }

    public class PartitionAwarePersonService : IPartitionAwarePersonService
    {
        private readonly CrmDbContext _context;
        private readonly ILogger<PartitionAwarePersonService> _logger;

        public PartitionAwarePersonService(CrmDbContext context, ILogger<PartitionAwarePersonService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<Models.Person.Person>> GetPersonsByPartitionAsync(
            int divisionId,
            int? categoryId = null,
            int? firmNatureId = null,
            bool includeDeleted = false)
        {
            _logger.LogInformation("Fetching persons from partition: Division={DivisionId}, Category={CategoryId}, FirmNature={FirmNatureId}",
                divisionId, categoryId, firmNatureId);

            var query = _context.Persons
                .Where(p => p.DivisionId == divisionId);

            if (categoryId.HasValue)
            {
                query = query.Where(p => p.CategoryId == categoryId.Value);
            }

            if (firmNatureId.HasValue)
            {
                query = query.Where(p => p.FirmNatureId == firmNatureId.Value);
            }
            else if (categoryId.HasValue)
            {
                // If category is specified but firm nature is not, include all firm natures for that category
                query = query.Where(p => p.FirmNatureId > 0);
            }

            if (!includeDeleted)
            {
                query = query.Where(p => !p.IsDeleted);
            }

            // Include related entities
            query = query
                .Include(p => p.Division)
                .Include(p => p.Category)
                .Include(p => p.FirmNature);

            var persons = await query
                .OrderBy(p => p.CreatedAt)
                .ToListAsync();

            _logger.LogInformation("Retrieved {Count} persons from partition", persons.Count);
            return persons;
        }

        public async Task<Models.Person.Person?> GetPersonByIdWithPartitionAsync(
            int id,
            int divisionId,
            int categoryId,
            int? firmNatureId = null)
        {
            _logger.LogInformation("Fetching person {PersonId} from partition: Division={DivisionId}, Category={CategoryId}, FirmNature={FirmNatureId}",
                id, divisionId, categoryId, firmNatureId);

            var query = _context.Persons
                .Where(p => p.Id == id && p.DivisionId == divisionId && p.CategoryId == categoryId);

            if (firmNatureId.HasValue)
            {
                query = query.Where(p => p.FirmNatureId == firmNatureId.Value);
            }

            return await query
                .Include(p => p.Division)
                .Include(p => p.Category)
                .Include(p => p.FirmNature)
                .FirstOrDefaultAsync();
        }

        public async Task<IEnumerable<Models.Person.Person>> SearchPersonsInPartitionAsync(PersonSearchRequest request)
        {
            _logger.LogInformation("Searching persons in partition with filters");

            var query = _context.Persons.AsQueryable();

            // Partition filtering (most important for performance)
            if (request.DivisionId.HasValue)
            {
                query = query.Where(p => p.DivisionId == request.DivisionId.Value);
            }

            if (request.CategoryId.HasValue)
            {
                query = query.Where(p => p.CategoryId == request.CategoryId.Value);
            }

            if (request.FirmNatureId.HasValue)
            {
                query = query.Where(p => p.FirmNatureId == request.FirmNatureId.Value);
            }

            // Other filters
            if (!string.IsNullOrEmpty(request.Name))
            {
                query = query.Where(p => p.Name.Contains(request.Name));
            }

            if (!string.IsNullOrEmpty(request.MobileNumber))
            {
                query = query.Where(p => p.MobileNumber.Contains(request.MobileNumber));
            }

            if (!string.IsNullOrEmpty(request.Email))
            {
                query = query.Where(p => p.PrimaryEmailId != null && p.PrimaryEmailId.Contains(request.Email));
            }

            if (request.Nature.HasValue)
            {
                query = query.Where(p => p.Nature == request.Nature.Value);
            }

            if (request.Gender.HasValue)
            {
                query = query.Where(p => p.Gender == request.Gender.Value);
            }

            if (!string.IsNullOrEmpty(request.WorkingState))
            {
                query = query.Where(p => p.WorkingState != null && p.WorkingState.Contains(request.WorkingState));
            }

            if (!string.IsNullOrEmpty(request.District))
            {
                query = query.Where(p => p.District != null && p.District.Contains(request.District));
            }

            if (request.MinStarRating.HasValue)
            {
                query = query.Where(p => p.StarRating >= request.MinStarRating.Value);
            }

            if (request.MaxStarRating.HasValue)
            {
                query = query.Where(p => p.StarRating <= request.MaxStarRating.Value);
            }

            if (request.MinTransactionValue.HasValue)
            {
                query = query.Where(p => p.TransactionValue >= request.MinTransactionValue.Value);
            }

            if (request.MaxTransactionValue.HasValue)
            {
                query = query.Where(p => p.TransactionValue <= request.MaxTransactionValue.Value);
            }

            if (request.CreatedAfter.HasValue)
            {
                query = query.Where(p => p.CreatedAt >= request.CreatedAfter.Value);
            }

            if (request.CreatedBefore.HasValue)
            {
                query = query.Where(p => p.CreatedAt <= request.CreatedBefore.Value);
            }

            if (!request.IncludeDeleted)
            {
                query = query.Where(p => !p.IsDeleted);
            }

            // Include related entities
            query = query
                .Include(p => p.Division)
                .Include(p => p.Category)
                .Include(p => p.FirmNature);

            // Apply pagination
            if (request.PageSize > 0)
            {
                var skip = (request.PageNumber - 1) * request.PageSize;
                query = query.Skip(skip).Take(request.PageSize);
            }

            // Apply sorting
            query = request.SortBy?.ToLower() switch
            {
                "name" => request.SortDescending ? query.OrderByDescending(p => p.Name) : query.OrderBy(p => p.Name),
                "createdat" => request.SortDescending ? query.OrderByDescending(p => p.CreatedAt) : query.OrderBy(p => p.CreatedAt),
                "starrating" => request.SortDescending ? query.OrderByDescending(p => p.StarRating) : query.OrderBy(p => p.StarRating),
                "transactionvalue" => request.SortDescending ? query.OrderByDescending(p => p.TransactionValue) : query.OrderBy(p => p.TransactionValue),
                _ => query.OrderBy(p => p.CreatedAt)
            };

            var results = await query.ToListAsync();
            _logger.LogInformation("Search returned {Count} persons", results.Count);
            return results;
        }

        public async Task<PersonPartitionInfo> GetPartitionInfoAsync(int divisionId, int? categoryId = null, int? firmNatureId = null)
        {
            var query = _context.Persons.Where(p => p.DivisionId == divisionId);

            if (categoryId.HasValue)
            {
                query = query.Where(p => p.CategoryId == categoryId.Value);
            }

            if (firmNatureId.HasValue)
            {
                query = query.Where(p => p.FirmNatureId == firmNatureId.Value);
            }

            var totalCount = await query.CountAsync();
            var activeCount = await query.Where(p => !p.IsDeleted).CountAsync();
            var deletedCount = totalCount - activeCount;

            var avgStarRating = await query
                .Where(p => p.StarRating.HasValue && !p.IsDeleted)
                .AverageAsync(p => (double?)p.StarRating) ?? 0;

            var totalTransactionValue = await query
                .Where(p => p.TransactionValue.HasValue && !p.IsDeleted)
                .SumAsync(p => p.TransactionValue) ?? 0;

            return new PersonPartitionInfo
            {
                DivisionId = divisionId,
                CategoryId = categoryId,
                FirmNatureId = firmNatureId,
                TotalPersons = totalCount,
                ActivePersons = activeCount,
                DeletedPersons = deletedCount,
                AverageStarRating = (decimal)avgStarRating,
                TotalTransactionValue = totalTransactionValue,
                LastUpdated = DateTime.UtcNow
            };
        }

        public async Task<IEnumerable<PartitionStatistics>> GetPartitionStatisticsAsync()
        {
            var statistics = await _context.Persons
                .Where(p => !p.IsDeleted)
                .GroupBy(p => new { p.DivisionId, p.CategoryId, p.FirmNatureId })
                .Select(g => new PartitionStatistics
                {
                    DivisionId = g.Key.DivisionId,
                    CategoryId = g.Key.CategoryId,
                    FirmNatureId = g.Key.FirmNatureId,
                    PersonCount = g.Count(),
                    AverageStarRating = g.Where(p => p.StarRating.HasValue).Average(p => (decimal?)p.StarRating) ?? 0,
                    TotalTransactionValue = g.Where(p => p.TransactionValue.HasValue).Sum(p => p.TransactionValue) ?? 0,
                    LastActivity = g.Max(p => p.UpdatedAt)
                })
                .OrderBy(s => s.DivisionId)
                .ThenBy(s => s.CategoryId)
                .ThenBy(s => s.FirmNatureId)
                .ToListAsync();

            return statistics;
        }

        public async Task<bool> ValidatePartitionExistsAsync(int divisionId, int categoryId, int? firmNatureId = null)
        {
            // Check if division exists
            var divisionExists = await _context.Divisions.AnyAsync(d => d.Id == divisionId);
            if (!divisionExists) return false;

            // Check if category exists and belongs to division
            var categoryExists = await _context.Categories
                .AnyAsync(c => c.Id == categoryId && c.DivisionId == divisionId);
            if (!categoryExists) return false;

            // Check if firm nature exists and belongs to category (if specified)
            if (firmNatureId.HasValue)
            {
                var firmNatureExists = await _context.FirmNatures
                    .AnyAsync(fn => fn.Id == firmNatureId.Value && fn.CategoryId == categoryId);
                if (!firmNatureExists) return false;
            }

            return true;
        }
    }

    // Supporting models
    public class PersonPartitionInfo
    {
        public int DivisionId { get; set; }
        public int? CategoryId { get; set; }
        public int? FirmNatureId { get; set; }
        public int TotalPersons { get; set; }
        public int ActivePersons { get; set; }
        public int DeletedPersons { get; set; }
        public decimal AverageStarRating { get; set; }
        public decimal TotalTransactionValue { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    public class PartitionStatistics
    {
        public int DivisionId { get; set; }
        public int CategoryId { get; set; }
        public int? FirmNatureId { get; set; }
        public int PersonCount { get; set; }
        public decimal AverageStarRating { get; set; }
        public decimal TotalTransactionValue { get; set; }
        public DateTime LastActivity { get; set; }
    }
}
