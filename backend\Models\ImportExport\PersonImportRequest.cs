using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;

namespace CrmApi.Models.ImportExport
{
    public class PersonImportRequest
    {
        [Required(ErrorMessage = "File is required")]
        public IFormFile File { get; set; } = null!;

        [Required(ErrorMessage = "Import mode is required")]
        public ImportMode ImportMode { get; set; } = ImportMode.SkipDuplicates;

        public bool ValidateOnly { get; set; } = false;

        public int? BatchSize { get; set; } = 100;

        // Mandatory division and category for all imported records
        [Required(ErrorMessage = "Division ID is required for import")]
        [Range(1, int.MaxValue, ErrorMessage = "Division ID must be a positive number")]
        public int DefaultDivisionId { get; set; }

        [Required(ErrorMessage = "Category ID is required for import")]
        [Range(1, int.MaxValue, ErrorMessage = "Category ID must be a positive number")]
        public int DefaultCategoryId { get; set; }

        // Required firm nature for all imported records
        [Required(ErrorMessage = "Firm Nature ID is required for import")]
        [Range(1, int.MaxValue, ErrorMessage = "Firm Nature ID must be a positive number")]
        public int DefaultFirmNatureId { get; set; }

        // Default values for required fields that are missing from Excel file
        public string? DefaultValues { get; set; }

        // Field mapping from Excel columns to person fields (JSON)
        public string? FieldMapping { get; set; }
    }

    public enum ImportMode
    {
        SkipDuplicates = 1,
        UpdateExisting = 2,
        FailOnDuplicates = 3
    }

    public enum FileFormat
    {
        CSV = 1,
        Excel = 2
    }
}
