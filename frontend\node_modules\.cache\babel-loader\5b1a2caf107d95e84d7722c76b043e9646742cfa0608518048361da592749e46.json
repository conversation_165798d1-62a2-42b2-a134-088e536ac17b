{"ast": null, "code": "function addUniqueItem(arr, item) {\n  if (arr.indexOf(item) === -1) arr.push(item);\n}\nfunction removeItem(arr, item) {\n  const index = arr.indexOf(item);\n  if (index > -1) arr.splice(index, 1);\n}\n// Adapted from array-move\nfunction moveItem(_ref, fromIndex, toIndex) {\n  let [...arr] = _ref;\n  const startIndex = fromIndex < 0 ? arr.length + fromIndex : fromIndex;\n  if (startIndex >= 0 && startIndex < arr.length) {\n    const endIndex = toIndex < 0 ? arr.length + toIndex : toIndex;\n    const [item] = arr.splice(fromIndex, 1);\n    arr.splice(endIndex, 0, item);\n  }\n  return arr;\n}\nexport { addUniqueItem, moveItem, removeItem };", "map": {"version": 3, "names": ["addUniqueItem", "arr", "item", "indexOf", "push", "removeItem", "index", "splice", "moveItem", "_ref", "fromIndex", "toIndex", "startIndex", "length", "endIndex"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/utils/array.mjs"], "sourcesContent": ["function addUniqueItem(arr, item) {\n    if (arr.indexOf(item) === -1)\n        arr.push(item);\n}\nfunction removeItem(arr, item) {\n    const index = arr.indexOf(item);\n    if (index > -1)\n        arr.splice(index, 1);\n}\n// Adapted from array-move\nfunction moveItem([...arr], fromIndex, toIndex) {\n    const startIndex = fromIndex < 0 ? arr.length + fromIndex : fromIndex;\n    if (startIndex >= 0 && startIndex < arr.length) {\n        const endIndex = toIndex < 0 ? arr.length + toIndex : toIndex;\n        const [item] = arr.splice(fromIndex, 1);\n        arr.splice(endIndex, 0, item);\n    }\n    return arr;\n}\n\nexport { addUniqueItem, moveItem, removeItem };\n"], "mappings": "AAAA,SAASA,aAAaA,CAACC,GAAG,EAAEC,IAAI,EAAE;EAC9B,IAAID,GAAG,CAACE,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC,CAAC,EACxBD,GAAG,CAACG,IAAI,CAACF,IAAI,CAAC;AACtB;AACA,SAASG,UAAUA,CAACJ,GAAG,EAAEC,IAAI,EAAE;EAC3B,MAAMI,KAAK,GAAGL,GAAG,CAACE,OAAO,CAACD,IAAI,CAAC;EAC/B,IAAII,KAAK,GAAG,CAAC,CAAC,EACVL,GAAG,CAACM,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;AAC5B;AACA;AACA,SAASE,QAAQA,CAAAC,IAAA,EAAWC,SAAS,EAAEC,OAAO,EAAE;EAAA,IAA9B,CAAC,GAAGV,GAAG,CAAC,GAAAQ,IAAA;EACtB,MAAMG,UAAU,GAAGF,SAAS,GAAG,CAAC,GAAGT,GAAG,CAACY,MAAM,GAAGH,SAAS,GAAGA,SAAS;EACrE,IAAIE,UAAU,IAAI,CAAC,IAAIA,UAAU,GAAGX,GAAG,CAACY,MAAM,EAAE;IAC5C,MAAMC,QAAQ,GAAGH,OAAO,GAAG,CAAC,GAAGV,GAAG,CAACY,MAAM,GAAGF,OAAO,GAAGA,OAAO;IAC7D,MAAM,CAACT,IAAI,CAAC,GAAGD,GAAG,CAACM,MAAM,CAACG,SAAS,EAAE,CAAC,CAAC;IACvCT,GAAG,CAACM,MAAM,CAACO,QAAQ,EAAE,CAAC,EAAEZ,IAAI,CAAC;EACjC;EACA,OAAOD,GAAG;AACd;AAEA,SAASD,aAAa,EAAEQ,QAAQ,EAAEH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}