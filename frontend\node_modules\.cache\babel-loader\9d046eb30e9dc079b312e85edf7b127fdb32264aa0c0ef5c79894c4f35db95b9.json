{"ast": null, "code": "import{PersonFieldDefinitions,getAllPersonFields}from'../constants/personConstants';class FormConfigService{constructor(){this.storagePrefix='crm_form_config_';this.defaultFormKey='default_person_form';}// Generate storage key for form configuration\ngetStorageKey(type,id){return`${this.storagePrefix}${type}_${id}`;}// Save form configuration\nsaveFormConfig(type,id,config){try{const key=this.getStorageKey(type,id);// Deduplicate fields before saving\nconst deduplicatedFields=this.deduplicateFields(config.fields||[]);const formConfig={id:`${type}_${id}`,type,// 'division', 'category', or 'subcategory'\nassociatedId:id,name:config.name||`${type} ${id} Form`,description:config.description||'',fields:deduplicatedFields,// Use deduplicated fields\nsections:config.sections||[],settings:config.settings||{},hierarchy:config.hierarchy||{},createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),version:1,...config,fields:deduplicatedFields// Ensure deduplicated fields override any from ...config\n};localStorage.setItem(key,JSON.stringify(formConfig));return formConfig;}catch(error){console.error('Error saving form configuration:',error);throw new Error('Failed to save form configuration');}}// Load form configuration\nloadFormConfig(type,id){try{const key=this.getStorageKey(type,id);const stored=localStorage.getItem(key);if(stored){return JSON.parse(stored);}return null;}catch(error){console.error('Error loading form configuration:',error);return null;}}// Delete form configuration\ndeleteFormConfig(type,id){try{const key=this.getStorageKey(type,id);localStorage.removeItem(key);return true;}catch(error){console.error('Error deleting form configuration:',error);return false;}}// Clear all form configurations (for testing/debugging)\nclearAllFormConfigs(){try{const keys=Object.keys(localStorage);const formKeys=keys.filter(key=>key.startsWith('form_config_'));formKeys.forEach(key=>localStorage.removeItem(key));return true;}catch(error){console.error('Error clearing form configurations:',error);return false;}}// Check if a form exists for a specific category\nhasFormForCategory(categoryId){try{const key=this.getStorageKey('category',categoryId);return localStorage.getItem(key)!==null;}catch(error){console.error('Error checking category form:',error);return false;}}// Check if a form exists for a specific subcategory\nhasFormForSubCategory(subCategoryId){try{const key=this.getStorageKey('subcategory',subCategoryId);return localStorage.getItem(key)!==null;}catch(error){console.error('Error checking subcategory form:',error);return false;}}// Get all subcategories for a category that have forms\ngetSubCategoriesWithForms(categoryId){try{const allConfigs=this.getAllFormConfigs();return allConfigs.filter(config=>{var _config$hierarchy;return config.type==='subcategory'&&((_config$hierarchy=config.hierarchy)===null||_config$hierarchy===void 0?void 0:_config$hierarchy.categoryId)===categoryId;}).map(config=>{var _config$hierarchy2;return(_config$hierarchy2=config.hierarchy)===null||_config$hierarchy2===void 0?void 0:_config$hierarchy2.subCategoryId;}).filter(id=>id!==null&&id!==undefined);}catch(error){console.error('Error getting subcategories with forms:',error);return[];}}// Get existing form information for a category/subcategory\ngetExistingFormInfo(categoryId){let subCategoryId=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;try{const info={categoryHasForm:false,subCategoryHasForm:false,subCategoriesWithForms:[],existingForms:[]};if(categoryId){info.categoryHasForm=this.hasFormForCategory(categoryId);info.subCategoriesWithForms=this.getSubCategoriesWithForms(categoryId);if(info.categoryHasForm){const categoryForm=this.loadFormConfig('category',categoryId);if(categoryForm){info.existingForms.push({type:'category',name:categoryForm.name,description:categoryForm.description});}}}if(subCategoryId){info.subCategoryHasForm=this.hasFormForSubCategory(subCategoryId);if(info.subCategoryHasForm){const subCategoryForm=this.loadFormConfig('subcategory',subCategoryId);if(subCategoryForm){info.existingForms.push({type:'subcategory',name:subCategoryForm.name,description:subCategoryForm.description});}}}return info;}catch(error){console.error('Error getting existing form info:',error);return{categoryHasForm:false,subCategoryHasForm:false,subCategoriesWithForms:[],existingForms:[]};}}// Validate form creation rules\nvalidateFormCreation(categoryId){let subCategoryId=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;const errors=[];if(subCategoryId){// Rule 1: Check if category already has a form\nif(this.hasFormForCategory(categoryId)){errors.push('Cannot create form for subcategory because a form already exists for the parent category');}// Rule 2: Check if subcategory already has a form\nif(this.hasFormForSubCategory(subCategoryId)){errors.push('A form already exists for this subcategory. Only one form per subcategory is allowed');}}else if(categoryId){// Rule 3: Check if any subcategories have forms\nconst subCategoriesWithForms=this.getSubCategoriesWithForms(categoryId);if(subCategoriesWithForms.length>0){errors.push('Cannot create form for category because forms already exist for its subcategories');}// Rule 4: Check if category already has a form\nif(this.hasFormForCategory(categoryId)){errors.push('A form already exists for this category');}}return{isValid:errors.length===0,errors};}// Get form configuration for category or subcategory\ngetFormConfigForSelection(divisionId,categoryId){let subCategoryId=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;// divisionId is kept for future use and API consistency\n// First try to get subcategory-specific form if subcategory is selected\nif(subCategoryId){const subCategoryForm=this.loadFormConfig('subcategory',subCategoryId);if(subCategoryForm){return subCategoryForm;}}// Then try category-specific form\nconst categoryForm=this.loadFormConfig('category',categoryId);if(categoryForm){return categoryForm;}// Return default form if no custom form found\nreturn this.getDefaultFormConfig();}// Get default form configuration with all fields\ngetDefaultFormConfig(){const allFields=getAllPersonFields();// Deduplicate fields to ensure no duplicates\nconst deduplicatedFields=this.deduplicateFields(allFields);return{id:'default',type:'default',name:'Default Person Form',description:'Default form with all person fields',fields:deduplicatedFields,sections:Object.keys(PersonFieldDefinitions),settings:{showSections:true,allowConditionalFields:true,validateOnChange:true},createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),version:1};}// Create form configuration from field selection\ncreateFormConfigFromFields(selectedFields){let settings=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const config={fields:selectedFields,sections:this.groupFieldsBySections(selectedFields),settings:{showSections:true,allowConditionalFields:true,validateOnChange:true,...settings}};return config;}// Deduplicate fields based on field key\ndeduplicateFields(fields){const seen=new Set();const deduplicated=[];fields.forEach(field=>{if(!seen.has(field.key)){seen.add(field.key);deduplicated.push(field);}});return deduplicated;}// Group fields by their sections\ngroupFieldsBySections(fields){const sections={};fields.forEach(field=>{const sectionKey=field.section;if(!sections[sectionKey]){var _PersonFieldDefinitio;sections[sectionKey]={key:sectionKey,title:((_PersonFieldDefinitio=PersonFieldDefinitions[sectionKey])===null||_PersonFieldDefinitio===void 0?void 0:_PersonFieldDefinitio.title)||sectionKey,fields:[]};}sections[sectionKey].fields.push(field);});return Object.values(sections);}// Validate form configuration\nvalidateFormConfig(config){const errors=[];if(!config.name||config.name.trim()===''){errors.push('Form name is required');}if(!config.fields||config.fields.length===0){errors.push('At least one field must be selected');}// Check if required fields are included\nconst requiredFields=['name','mobileNumber','nature'];const selectedFieldKeys=config.fields.map(f=>f.key);requiredFields.forEach(requiredField=>{if(!selectedFieldKeys.includes(requiredField)){errors.push(`Required field '${requiredField}' must be included`);}});return{isValid:errors.length===0,errors};}// Export form configuration\nexportFormConfig(type,id){const config=this.loadFormConfig(type,id);if(!config){throw new Error('Form configuration not found');}const exportData={...config,exportedAt:new Date().toISOString(),exportVersion:'1.0'};return JSON.stringify(exportData,null,2);}// Import form configuration\nimportFormConfig(configJson,type,id){try{const config=JSON.parse(configJson);// Validate imported configuration\nconst validation=this.validateFormConfig(config);if(!validation.isValid){throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);}// Update metadata for import\nconfig.type=type;config.associatedId=id;config.importedAt=new Date().toISOString();config.updatedAt=new Date().toISOString();return this.saveFormConfig(type,id,config);}catch(error){console.error('Error importing form configuration:',error);throw new Error('Failed to import form configuration');}}// Clone form configuration\ncloneFormConfig(sourceType,sourceId,targetType,targetId,newName){const sourceConfig=this.loadFormConfig(sourceType,sourceId);if(!sourceConfig){throw new Error('Source form configuration not found');}const clonedConfig={...sourceConfig,name:newName||`${sourceConfig.name} (Copy)`,type:targetType,associatedId:targetId,clonedFrom:`${sourceType}_${sourceId}`,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return this.saveFormConfig(targetType,targetId,clonedConfig);}// Get form statistics\ngetFormStatistics(){const allConfigs=this.getAllFormConfigs();return{totalForms:allConfigs.length,categoryForms:allConfigs.filter(c=>c.type==='category').length,subCategoryForms:allConfigs.filter(c=>c.type==='subcategory').length,averageFieldCount:allConfigs.length>0?Math.round(allConfigs.reduce((sum,c)=>{var _c$fields;return sum+(((_c$fields=c.fields)===null||_c$fields===void 0?void 0:_c$fields.length)||0);},0)/allConfigs.length):0,mostUsedFields:this.getMostUsedFields(allConfigs),recentlyModified:allConfigs.sort((a,b)=>new Date(b.updatedAt)-new Date(a.updatedAt)).slice(0,5)};}// Get most used fields across all forms\ngetMostUsedFields(configs){const fieldUsage={};configs.forEach(config=>{if(config.fields){config.fields.forEach(field=>{fieldUsage[field.key]=(fieldUsage[field.key]||0)+1;});}});return Object.entries(fieldUsage).sort((_ref,_ref2)=>{let[,a]=_ref;let[,b]=_ref2;return b-a;}).slice(0,10).map(_ref3=>{let[key,count]=_ref3;return{field:key,usage:count};});}// Get all saved form configurations\ngetAllFormConfigs(){try{const forms=[];for(let i=0;i<localStorage.length;i++){const key=localStorage.key(i);if(key&&key.startsWith(this.storagePrefix)){const stored=localStorage.getItem(key);if(stored){var _config$fields;const config=JSON.parse(stored);forms.push({key,...config,summary:{fieldCount:((_config$fields=config.fields)===null||_config$fields===void 0?void 0:_config$fields.length)||0,type:config.type,associatedId:config.associatedId,createdAt:config.createdAt,updatedAt:config.updatedAt}});}}}// Sort by updated date (most recent first)\nreturn forms.sort((a,b)=>new Date(b.updatedAt)-new Date(a.updatedAt));}catch(error){console.error('Error getting form configurations:',error);return[];}}// Delete a specific form configuration\ndeleteFormConfig(type,id){try{const key=this.getStorageKey(type,id);localStorage.removeItem(key);return true;}catch(error){console.error('Error deleting form configuration:',error);return false;}}// Clear all form configurations (for development/testing)\nclearAllFormConfigs(){try{const keysToRemove=[];for(let i=0;i<localStorage.length;i++){const key=localStorage.key(i);if(key&&key.startsWith(this.storagePrefix)){keysToRemove.push(key);}}keysToRemove.forEach(key=>localStorage.removeItem(key));return true;}catch(error){console.error('Error clearing form configurations:',error);return false;}}}export default new FormConfigService();", "map": {"version": 3, "names": ["PersonFieldDefinitions", "<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>s", "FormConfigService", "constructor", "storagePrefix", "defaultFormKey", "getStorageKey", "type", "id", "saveFormConfig", "config", "key", "deduplicatedFields", "deduplicateFields", "fields", "formConfig", "associatedId", "name", "description", "sections", "settings", "hierarchy", "createdAt", "Date", "toISOString", "updatedAt", "version", "localStorage", "setItem", "JSON", "stringify", "error", "console", "Error", "loadFormConfig", "stored", "getItem", "parse", "deleteFormConfig", "removeItem", "clearAllFormConfigs", "keys", "Object", "formKeys", "filter", "startsWith", "for<PERSON>ach", "hasFormForCategory", "categoryId", "hasFormForSubCategory", "subCategoryId", "getSubCategoriesWithForms", "allConfigs", "getAllFormConfigs", "_config$hierarchy", "map", "_config$hierarchy2", "undefined", "getExistingFormInfo", "arguments", "length", "info", "categoryHasForm", "subCategoryHasForm", "subCategoriesWithForms", "existingForms", "categoryForm", "push", "subCategoryForm", "validateFormCreation", "errors", "<PERSON><PERSON><PERSON><PERSON>", "getFormConfigForSelection", "divisionId", "getDefaultFormConfig", "allFields", "showSections", "allowConditionalFields", "validateOnChange", "createFormConfigFromFields", "<PERSON><PERSON><PERSON>s", "groupFieldsBySections", "seen", "Set", "deduplicated", "field", "has", "add", "sectionKey", "section", "_PersonFieldDefinitio", "title", "values", "validateFormConfig", "trim", "requiredFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f", "requiredField", "includes", "exportFormConfig", "exportData", "exportedAt", "exportVersion", "importFormConfig", "config<PERSON><PERSON>", "validation", "join", "importedAt", "cloneFormConfig", "sourceType", "sourceId", "targetType", "targetId", "newName", "sourceConfig", "clonedConfig", "clonedFrom", "getFormStatistics", "totalForms", "categoryForms", "c", "subCategoryForms", "averageFieldCount", "Math", "round", "reduce", "sum", "_c$fields", "mostUsedFields", "getMostUsedFields", "recentlyModified", "sort", "a", "b", "slice", "configs", "fieldUsage", "entries", "_ref", "_ref2", "_ref3", "count", "usage", "forms", "i", "_config$fields", "summary", "fieldCount", "keysToRemove"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/services/formConfigService.js"], "sourcesContent": ["import { PersonFieldDefinitions, getAllPersonFields } from '../constants/personConstants';\n\nclass FormConfigService {\n  constructor() {\n    this.storagePrefix = 'crm_form_config_';\n    this.defaultFormKey = 'default_person_form';\n  }\n\n  // Generate storage key for form configuration\n  getStorageKey(type, id) {\n    return `${this.storagePrefix}${type}_${id}`;\n  }\n\n  // Save form configuration\n  saveFormConfig(type, id, config) {\n    try {\n      const key = this.getStorageKey(type, id);\n\n      // Deduplicate fields before saving\n      const deduplicatedFields = this.deduplicateFields(config.fields || []);\n\n      const formConfig = {\n        id: `${type}_${id}`,\n        type, // 'division', 'category', or 'subcategory'\n        associatedId: id,\n        name: config.name || `${type} ${id} Form`,\n        description: config.description || '',\n        fields: deduplicatedFields, // Use deduplicated fields\n        sections: config.sections || [],\n        settings: config.settings || {},\n        hierarchy: config.hierarchy || {},\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        version: 1,\n        ...config,\n        fields: deduplicatedFields // Ensure deduplicated fields override any from ...config\n      };\n\n      localStorage.setItem(key, JSON.stringify(formConfig));\n      return formConfig;\n    } catch (error) {\n      console.error('Error saving form configuration:', error);\n      throw new Error('Failed to save form configuration');\n    }\n  }\n\n  // Load form configuration\n  loadFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      const stored = localStorage.getItem(key);\n      \n      if (stored) {\n        return JSON.parse(stored);\n      }\n      \n      return null;\n    } catch (error) {\n      console.error('Error loading form configuration:', error);\n      return null;\n    }\n  }\n\n  // Delete form configuration\n  deleteFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error('Error deleting form configuration:', error);\n      return false;\n    }\n  }\n\n  // Clear all form configurations (for testing/debugging)\n  clearAllFormConfigs() {\n    try {\n      const keys = Object.keys(localStorage);\n      const formKeys = keys.filter(key => key.startsWith('form_config_'));\n      formKeys.forEach(key => localStorage.removeItem(key));\n      return true;\n    } catch (error) {\n      console.error('Error clearing form configurations:', error);\n      return false;\n    }\n  }\n\n\n\n  // Check if a form exists for a specific category\n  hasFormForCategory(categoryId) {\n    try {\n      const key = this.getStorageKey('category', categoryId);\n      return localStorage.getItem(key) !== null;\n    } catch (error) {\n      console.error('Error checking category form:', error);\n      return false;\n    }\n  }\n\n  // Check if a form exists for a specific subcategory\n  hasFormForSubCategory(subCategoryId) {\n    try {\n      const key = this.getStorageKey('subcategory', subCategoryId);\n      return localStorage.getItem(key) !== null;\n    } catch (error) {\n      console.error('Error checking subcategory form:', error);\n      return false;\n    }\n  }\n\n  // Get all subcategories for a category that have forms\n  getSubCategoriesWithForms(categoryId) {\n    try {\n      const allConfigs = this.getAllFormConfigs();\n      return allConfigs\n        .filter(config => config.type === 'subcategory' && config.hierarchy?.categoryId === categoryId)\n        .map(config => config.hierarchy?.subCategoryId)\n        .filter(id => id !== null && id !== undefined);\n    } catch (error) {\n      console.error('Error getting subcategories with forms:', error);\n      return [];\n    }\n  }\n\n  // Get existing form information for a category/subcategory\n  getExistingFormInfo(categoryId, subCategoryId = null) {\n    try {\n      const info = {\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        subCategoriesWithForms: [],\n        existingForms: []\n      };\n\n      if (categoryId) {\n        info.categoryHasForm = this.hasFormForCategory(categoryId);\n        info.subCategoriesWithForms = this.getSubCategoriesWithForms(categoryId);\n\n        if (info.categoryHasForm) {\n          const categoryForm = this.loadFormConfig('category', categoryId);\n          if (categoryForm) {\n            info.existingForms.push({\n              type: 'category',\n              name: categoryForm.name,\n              description: categoryForm.description\n            });\n          }\n        }\n      }\n\n      if (subCategoryId) {\n        info.subCategoryHasForm = this.hasFormForSubCategory(subCategoryId);\n\n        if (info.subCategoryHasForm) {\n          const subCategoryForm = this.loadFormConfig('subcategory', subCategoryId);\n          if (subCategoryForm) {\n            info.existingForms.push({\n              type: 'subcategory',\n              name: subCategoryForm.name,\n              description: subCategoryForm.description\n            });\n          }\n        }\n      }\n\n      return info;\n    } catch (error) {\n      console.error('Error getting existing form info:', error);\n      return {\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        subCategoriesWithForms: [],\n        existingForms: []\n      };\n    }\n  }\n\n  // Validate form creation rules\n  validateFormCreation(categoryId, subCategoryId = null) {\n    const errors = [];\n\n    if (subCategoryId) {\n      // Rule 1: Check if category already has a form\n      if (this.hasFormForCategory(categoryId)) {\n        errors.push('Cannot create form for subcategory because a form already exists for the parent category');\n      }\n\n      // Rule 2: Check if subcategory already has a form\n      if (this.hasFormForSubCategory(subCategoryId)) {\n        errors.push('A form already exists for this subcategory. Only one form per subcategory is allowed');\n      }\n    } else if (categoryId) {\n      // Rule 3: Check if any subcategories have forms\n      const subCategoriesWithForms = this.getSubCategoriesWithForms(categoryId);\n      if (subCategoriesWithForms.length > 0) {\n        errors.push('Cannot create form for category because forms already exist for its subcategories');\n      }\n\n      // Rule 4: Check if category already has a form\n      if (this.hasFormForCategory(categoryId)) {\n        errors.push('A form already exists for this category');\n      }\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  // Get form configuration for category or subcategory\n  getFormConfigForSelection(divisionId, categoryId, subCategoryId = null) {\n    // divisionId is kept for future use and API consistency\n    // First try to get subcategory-specific form if subcategory is selected\n    if (subCategoryId) {\n      const subCategoryForm = this.loadFormConfig('subcategory', subCategoryId);\n      if (subCategoryForm) {\n        return subCategoryForm;\n      }\n    }\n\n    // Then try category-specific form\n    const categoryForm = this.loadFormConfig('category', categoryId);\n    if (categoryForm) {\n      return categoryForm;\n    }\n\n    // Return default form if no custom form found\n    return this.getDefaultFormConfig();\n  }\n\n  // Get default form configuration with all fields\n  getDefaultFormConfig() {\n    const allFields = getAllPersonFields();\n\n    // Deduplicate fields to ensure no duplicates\n    const deduplicatedFields = this.deduplicateFields(allFields);\n\n    return {\n      id: 'default',\n      type: 'default',\n      name: 'Default Person Form',\n      description: 'Default form with all person fields',\n      fields: deduplicatedFields,\n      sections: Object.keys(PersonFieldDefinitions),\n      settings: {\n        showSections: true,\n        allowConditionalFields: true,\n        validateOnChange: true\n      },\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      version: 1\n    };\n  }\n\n  // Create form configuration from field selection\n  createFormConfigFromFields(selectedFields, settings = {}) {\n    const config = {\n      fields: selectedFields,\n      sections: this.groupFieldsBySections(selectedFields),\n      settings: {\n        showSections: true,\n        allowConditionalFields: true,\n        validateOnChange: true,\n        ...settings\n      }\n    };\n\n    return config;\n  }\n\n  // Deduplicate fields based on field key\n  deduplicateFields(fields) {\n    const seen = new Set();\n    const deduplicated = [];\n\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      }\n    });\n\n    return deduplicated;\n  }\n\n  // Group fields by their sections\n  groupFieldsBySections(fields) {\n    const sections = {};\n\n    fields.forEach(field => {\n      const sectionKey = field.section;\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          key: sectionKey,\n          title: PersonFieldDefinitions[sectionKey]?.title || sectionKey,\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n\n    return Object.values(sections);\n  }\n\n  // Validate form configuration\n  validateFormConfig(config) {\n    const errors = [];\n\n    if (!config.name || config.name.trim() === '') {\n      errors.push('Form name is required');\n    }\n\n    if (!config.fields || config.fields.length === 0) {\n      errors.push('At least one field must be selected');\n    }\n\n    // Check if required fields are included\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = config.fields.map(f => f.key);\n    \n    requiredFields.forEach(requiredField => {\n      if (!selectedFieldKeys.includes(requiredField)) {\n        errors.push(`Required field '${requiredField}' must be included`);\n      }\n    });\n\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  // Export form configuration\n  exportFormConfig(type, id) {\n    const config = this.loadFormConfig(type, id);\n    if (!config) {\n      throw new Error('Form configuration not found');\n    }\n\n    const exportData = {\n      ...config,\n      exportedAt: new Date().toISOString(),\n      exportVersion: '1.0'\n    };\n\n    return JSON.stringify(exportData, null, 2);\n  }\n\n  // Import form configuration\n  importFormConfig(configJson, type, id) {\n    try {\n      const config = JSON.parse(configJson);\n      \n      // Validate imported configuration\n      const validation = this.validateFormConfig(config);\n      if (!validation.isValid) {\n        throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);\n      }\n\n      // Update metadata for import\n      config.type = type;\n      config.associatedId = id;\n      config.importedAt = new Date().toISOString();\n      config.updatedAt = new Date().toISOString();\n\n      return this.saveFormConfig(type, id, config);\n    } catch (error) {\n      console.error('Error importing form configuration:', error);\n      throw new Error('Failed to import form configuration');\n    }\n  }\n\n  // Clone form configuration\n  cloneFormConfig(sourceType, sourceId, targetType, targetId, newName) {\n    const sourceConfig = this.loadFormConfig(sourceType, sourceId);\n    if (!sourceConfig) {\n      throw new Error('Source form configuration not found');\n    }\n\n    const clonedConfig = {\n      ...sourceConfig,\n      name: newName || `${sourceConfig.name} (Copy)`,\n      type: targetType,\n      associatedId: targetId,\n      clonedFrom: `${sourceType}_${sourceId}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n\n    return this.saveFormConfig(targetType, targetId, clonedConfig);\n  }\n\n  // Get form statistics\n  getFormStatistics() {\n    const allConfigs = this.getAllFormConfigs();\n    \n    return {\n      totalForms: allConfigs.length,\n      categoryForms: allConfigs.filter(c => c.type === 'category').length,\n      subCategoryForms: allConfigs.filter(c => c.type === 'subcategory').length,\n      averageFieldCount: allConfigs.length > 0 \n        ? Math.round(allConfigs.reduce((sum, c) => sum + (c.fields?.length || 0), 0) / allConfigs.length)\n        : 0,\n      mostUsedFields: this.getMostUsedFields(allConfigs),\n      recentlyModified: allConfigs\n        .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))\n        .slice(0, 5)\n    };\n  }\n\n  // Get most used fields across all forms\n  getMostUsedFields(configs) {\n    const fieldUsage = {};\n    \n    configs.forEach(config => {\n      if (config.fields) {\n        config.fields.forEach(field => {\n          fieldUsage[field.key] = (fieldUsage[field.key] || 0) + 1;\n        });\n      }\n    });\n\n    return Object.entries(fieldUsage)\n      .sort(([,a], [,b]) => b - a)\n      .slice(0, 10)\n      .map(([key, count]) => ({ field: key, usage: count }));\n  }\n\n  // Get all saved form configurations\n  getAllFormConfigs() {\n    try {\n      const forms = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          const stored = localStorage.getItem(key);\n          if (stored) {\n            const config = JSON.parse(stored);\n            forms.push({\n              key,\n              ...config,\n              summary: {\n                fieldCount: config.fields?.length || 0,\n                type: config.type,\n                associatedId: config.associatedId,\n                createdAt: config.createdAt,\n                updatedAt: config.updatedAt\n              }\n            });\n          }\n        }\n      }\n\n      // Sort by updated date (most recent first)\n      return forms.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));\n    } catch (error) {\n      console.error('Error getting form configurations:', error);\n      return [];\n    }\n  }\n\n  // Delete a specific form configuration\n  deleteFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error('Error deleting form configuration:', error);\n      return false;\n    }\n  }\n\n  // Clear all form configurations (for development/testing)\n  clearAllFormConfigs() {\n    try {\n      const keysToRemove = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          keysToRemove.push(key);\n        }\n      }\n\n      keysToRemove.forEach(key => localStorage.removeItem(key));\n      return true;\n    } catch (error) {\n      console.error('Error clearing form configurations:', error);\n      return false;\n    }\n  }\n}\n\nexport default new FormConfigService();\n"], "mappings": "AAAA,OAASA,sBAAsB,CAAEC,kBAAkB,KAAQ,8BAA8B,CAEzF,KAAM,CAAAC,iBAAkB,CACtBC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,aAAa,CAAG,kBAAkB,CACvC,IAAI,CAACC,cAAc,CAAG,qBAAqB,CAC7C,CAEA;AACAC,aAAaA,CAACC,IAAI,CAAEC,EAAE,CAAE,CACtB,MAAO,GAAG,IAAI,CAACJ,aAAa,GAAGG,IAAI,IAAIC,EAAE,EAAE,CAC7C,CAEA;AACAC,cAAcA,CAACF,IAAI,CAAEC,EAAE,CAAEE,MAAM,CAAE,CAC/B,GAAI,CACF,KAAM,CAAAC,GAAG,CAAG,IAAI,CAACL,aAAa,CAACC,IAAI,CAAEC,EAAE,CAAC,CAExC;AACA,KAAM,CAAAI,kBAAkB,CAAG,IAAI,CAACC,iBAAiB,CAACH,MAAM,CAACI,MAAM,EAAI,EAAE,CAAC,CAEtE,KAAM,CAAAC,UAAU,CAAG,CACjBP,EAAE,CAAE,GAAGD,IAAI,IAAIC,EAAE,EAAE,CACnBD,IAAI,CAAE;AACNS,YAAY,CAAER,EAAE,CAChBS,IAAI,CAAEP,MAAM,CAACO,IAAI,EAAI,GAAGV,IAAI,IAAIC,EAAE,OAAO,CACzCU,WAAW,CAAER,MAAM,CAACQ,WAAW,EAAI,EAAE,CACrCJ,MAAM,CAAEF,kBAAkB,CAAE;AAC5BO,QAAQ,CAAET,MAAM,CAACS,QAAQ,EAAI,EAAE,CAC/BC,QAAQ,CAAEV,MAAM,CAACU,QAAQ,EAAI,CAAC,CAAC,CAC/BC,SAAS,CAAEX,MAAM,CAACW,SAAS,EAAI,CAAC,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnCC,SAAS,CAAE,GAAI,CAAAF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnCE,OAAO,CAAE,CAAC,CACV,GAAGhB,MAAM,CACTI,MAAM,CAAEF,kBAAmB;AAC7B,CAAC,CAEDe,YAAY,CAACC,OAAO,CAACjB,GAAG,CAAEkB,IAAI,CAACC,SAAS,CAACf,UAAU,CAAC,CAAC,CACrD,MAAO,CAAAA,UAAU,CACnB,CAAE,MAAOgB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxD,KAAM,IAAI,CAAAE,KAAK,CAAC,mCAAmC,CAAC,CACtD,CACF,CAEA;AACAC,cAAcA,CAAC3B,IAAI,CAAEC,EAAE,CAAE,CACvB,GAAI,CACF,KAAM,CAAAG,GAAG,CAAG,IAAI,CAACL,aAAa,CAACC,IAAI,CAAEC,EAAE,CAAC,CACxC,KAAM,CAAA2B,MAAM,CAAGR,YAAY,CAACS,OAAO,CAACzB,GAAG,CAAC,CAExC,GAAIwB,MAAM,CAAE,CACV,MAAO,CAAAN,IAAI,CAACQ,KAAK,CAACF,MAAM,CAAC,CAC3B,CAEA,MAAO,KAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzD,MAAO,KAAI,CACb,CACF,CAEA;AACAO,gBAAgBA,CAAC/B,IAAI,CAAEC,EAAE,CAAE,CACzB,GAAI,CACF,KAAM,CAAAG,GAAG,CAAG,IAAI,CAACL,aAAa,CAACC,IAAI,CAAEC,EAAE,CAAC,CACxCmB,YAAY,CAACY,UAAU,CAAC5B,GAAG,CAAC,CAC5B,MAAO,KAAI,CACb,CAAE,MAAOoB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1D,MAAO,MAAK,CACd,CACF,CAEA;AACAS,mBAAmBA,CAAA,CAAG,CACpB,GAAI,CACF,KAAM,CAAAC,IAAI,CAAGC,MAAM,CAACD,IAAI,CAACd,YAAY,CAAC,CACtC,KAAM,CAAAgB,QAAQ,CAAGF,IAAI,CAACG,MAAM,CAACjC,GAAG,EAAIA,GAAG,CAACkC,UAAU,CAAC,cAAc,CAAC,CAAC,CACnEF,QAAQ,CAACG,OAAO,CAACnC,GAAG,EAAIgB,YAAY,CAACY,UAAU,CAAC5B,GAAG,CAAC,CAAC,CACrD,MAAO,KAAI,CACb,CAAE,MAAOoB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAC,CAC3D,MAAO,MAAK,CACd,CACF,CAIA;AACAgB,kBAAkBA,CAACC,UAAU,CAAE,CAC7B,GAAI,CACF,KAAM,CAAArC,GAAG,CAAG,IAAI,CAACL,aAAa,CAAC,UAAU,CAAE0C,UAAU,CAAC,CACtD,MAAO,CAAArB,YAAY,CAACS,OAAO,CAACzB,GAAG,CAAC,GAAK,IAAI,CAC3C,CAAE,MAAOoB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,MAAO,MAAK,CACd,CACF,CAEA;AACAkB,qBAAqBA,CAACC,aAAa,CAAE,CACnC,GAAI,CACF,KAAM,CAAAvC,GAAG,CAAG,IAAI,CAACL,aAAa,CAAC,aAAa,CAAE4C,aAAa,CAAC,CAC5D,MAAO,CAAAvB,YAAY,CAACS,OAAO,CAACzB,GAAG,CAAC,GAAK,IAAI,CAC3C,CAAE,MAAOoB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxD,MAAO,MAAK,CACd,CACF,CAEA;AACAoB,yBAAyBA,CAACH,UAAU,CAAE,CACpC,GAAI,CACF,KAAM,CAAAI,UAAU,CAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAC3C,MAAO,CAAAD,UAAU,CACdR,MAAM,CAAClC,MAAM,OAAA4C,iBAAA,OAAI,CAAA5C,MAAM,CAACH,IAAI,GAAK,aAAa,EAAI,EAAA+C,iBAAA,CAAA5C,MAAM,CAACW,SAAS,UAAAiC,iBAAA,iBAAhBA,iBAAA,CAAkBN,UAAU,IAAKA,UAAU,GAAC,CAC9FO,GAAG,CAAC7C,MAAM,OAAA8C,kBAAA,QAAAA,kBAAA,CAAI9C,MAAM,CAACW,SAAS,UAAAmC,kBAAA,iBAAhBA,kBAAA,CAAkBN,aAAa,GAAC,CAC9CN,MAAM,CAACpC,EAAE,EAAIA,EAAE,GAAK,IAAI,EAAIA,EAAE,GAAKiD,SAAS,CAAC,CAClD,CAAE,MAAO1B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,CAAEA,KAAK,CAAC,CAC/D,MAAO,EAAE,CACX,CACF,CAEA;AACA2B,mBAAmBA,CAACV,UAAU,CAAwB,IAAtB,CAAAE,aAAa,CAAAS,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAF,SAAA,CAAAE,SAAA,IAAG,IAAI,CAClD,GAAI,CACF,KAAM,CAAAE,IAAI,CAAG,CACXC,eAAe,CAAE,KAAK,CACtBC,kBAAkB,CAAE,KAAK,CACzBC,sBAAsB,CAAE,EAAE,CAC1BC,aAAa,CAAE,EACjB,CAAC,CAED,GAAIjB,UAAU,CAAE,CACda,IAAI,CAACC,eAAe,CAAG,IAAI,CAACf,kBAAkB,CAACC,UAAU,CAAC,CAC1Da,IAAI,CAACG,sBAAsB,CAAG,IAAI,CAACb,yBAAyB,CAACH,UAAU,CAAC,CAExE,GAAIa,IAAI,CAACC,eAAe,CAAE,CACxB,KAAM,CAAAI,YAAY,CAAG,IAAI,CAAChC,cAAc,CAAC,UAAU,CAAEc,UAAU,CAAC,CAChE,GAAIkB,YAAY,CAAE,CAChBL,IAAI,CAACI,aAAa,CAACE,IAAI,CAAC,CACtB5D,IAAI,CAAE,UAAU,CAChBU,IAAI,CAAEiD,YAAY,CAACjD,IAAI,CACvBC,WAAW,CAAEgD,YAAY,CAAChD,WAC5B,CAAC,CAAC,CACJ,CACF,CACF,CAEA,GAAIgC,aAAa,CAAE,CACjBW,IAAI,CAACE,kBAAkB,CAAG,IAAI,CAACd,qBAAqB,CAACC,aAAa,CAAC,CAEnE,GAAIW,IAAI,CAACE,kBAAkB,CAAE,CAC3B,KAAM,CAAAK,eAAe,CAAG,IAAI,CAAClC,cAAc,CAAC,aAAa,CAAEgB,aAAa,CAAC,CACzE,GAAIkB,eAAe,CAAE,CACnBP,IAAI,CAACI,aAAa,CAACE,IAAI,CAAC,CACtB5D,IAAI,CAAE,aAAa,CACnBU,IAAI,CAAEmD,eAAe,CAACnD,IAAI,CAC1BC,WAAW,CAAEkD,eAAe,CAAClD,WAC/B,CAAC,CAAC,CACJ,CACF,CACF,CAEA,MAAO,CAAA2C,IAAI,CACb,CAAE,MAAO9B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzD,MAAO,CACL+B,eAAe,CAAE,KAAK,CACtBC,kBAAkB,CAAE,KAAK,CACzBC,sBAAsB,CAAE,EAAE,CAC1BC,aAAa,CAAE,EACjB,CAAC,CACH,CACF,CAEA;AACAI,oBAAoBA,CAACrB,UAAU,CAAwB,IAAtB,CAAAE,aAAa,CAAAS,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAF,SAAA,CAAAE,SAAA,IAAG,IAAI,CACnD,KAAM,CAAAW,MAAM,CAAG,EAAE,CAEjB,GAAIpB,aAAa,CAAE,CACjB;AACA,GAAI,IAAI,CAACH,kBAAkB,CAACC,UAAU,CAAC,CAAE,CACvCsB,MAAM,CAACH,IAAI,CAAC,0FAA0F,CAAC,CACzG,CAEA;AACA,GAAI,IAAI,CAAClB,qBAAqB,CAACC,aAAa,CAAC,CAAE,CAC7CoB,MAAM,CAACH,IAAI,CAAC,sFAAsF,CAAC,CACrG,CACF,CAAC,IAAM,IAAInB,UAAU,CAAE,CACrB;AACA,KAAM,CAAAgB,sBAAsB,CAAG,IAAI,CAACb,yBAAyB,CAACH,UAAU,CAAC,CACzE,GAAIgB,sBAAsB,CAACJ,MAAM,CAAG,CAAC,CAAE,CACrCU,MAAM,CAACH,IAAI,CAAC,mFAAmF,CAAC,CAClG,CAEA;AACA,GAAI,IAAI,CAACpB,kBAAkB,CAACC,UAAU,CAAC,CAAE,CACvCsB,MAAM,CAACH,IAAI,CAAC,yCAAyC,CAAC,CACxD,CACF,CAEA,MAAO,CACLI,OAAO,CAAED,MAAM,CAACV,MAAM,GAAK,CAAC,CAC5BU,MACF,CAAC,CACH,CAEA;AACAE,yBAAyBA,CAACC,UAAU,CAAEzB,UAAU,CAAwB,IAAtB,CAAAE,aAAa,CAAAS,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAF,SAAA,CAAAE,SAAA,IAAG,IAAI,CACpE;AACA;AACA,GAAIT,aAAa,CAAE,CACjB,KAAM,CAAAkB,eAAe,CAAG,IAAI,CAAClC,cAAc,CAAC,aAAa,CAAEgB,aAAa,CAAC,CACzE,GAAIkB,eAAe,CAAE,CACnB,MAAO,CAAAA,eAAe,CACxB,CACF,CAEA;AACA,KAAM,CAAAF,YAAY,CAAG,IAAI,CAAChC,cAAc,CAAC,UAAU,CAAEc,UAAU,CAAC,CAChE,GAAIkB,YAAY,CAAE,CAChB,MAAO,CAAAA,YAAY,CACrB,CAEA;AACA,MAAO,KAAI,CAACQ,oBAAoB,CAAC,CAAC,CACpC,CAEA;AACAA,oBAAoBA,CAAA,CAAG,CACrB,KAAM,CAAAC,SAAS,CAAG1E,kBAAkB,CAAC,CAAC,CAEtC;AACA,KAAM,CAAAW,kBAAkB,CAAG,IAAI,CAACC,iBAAiB,CAAC8D,SAAS,CAAC,CAE5D,MAAO,CACLnE,EAAE,CAAE,SAAS,CACbD,IAAI,CAAE,SAAS,CACfU,IAAI,CAAE,qBAAqB,CAC3BC,WAAW,CAAE,qCAAqC,CAClDJ,MAAM,CAAEF,kBAAkB,CAC1BO,QAAQ,CAAEuB,MAAM,CAACD,IAAI,CAACzC,sBAAsB,CAAC,CAC7CoB,QAAQ,CAAE,CACRwD,YAAY,CAAE,IAAI,CAClBC,sBAAsB,CAAE,IAAI,CAC5BC,gBAAgB,CAAE,IACpB,CAAC,CACDxD,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnCC,SAAS,CAAE,GAAI,CAAAF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnCE,OAAO,CAAE,CACX,CAAC,CACH,CAEA;AACAqD,0BAA0BA,CAACC,cAAc,CAAiB,IAAf,CAAA5D,QAAQ,CAAAuC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAF,SAAA,CAAAE,SAAA,IAAG,CAAC,CAAC,CACtD,KAAM,CAAAjD,MAAM,CAAG,CACbI,MAAM,CAAEkE,cAAc,CACtB7D,QAAQ,CAAE,IAAI,CAAC8D,qBAAqB,CAACD,cAAc,CAAC,CACpD5D,QAAQ,CAAE,CACRwD,YAAY,CAAE,IAAI,CAClBC,sBAAsB,CAAE,IAAI,CAC5BC,gBAAgB,CAAE,IAAI,CACtB,GAAG1D,QACL,CACF,CAAC,CAED,MAAO,CAAAV,MAAM,CACf,CAEA;AACAG,iBAAiBA,CAACC,MAAM,CAAE,CACxB,KAAM,CAAAoE,IAAI,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CACtB,KAAM,CAAAC,YAAY,CAAG,EAAE,CAEvBtE,MAAM,CAACgC,OAAO,CAACuC,KAAK,EAAI,CACtB,GAAI,CAACH,IAAI,CAACI,GAAG,CAACD,KAAK,CAAC1E,GAAG,CAAC,CAAE,CACxBuE,IAAI,CAACK,GAAG,CAACF,KAAK,CAAC1E,GAAG,CAAC,CACnByE,YAAY,CAACjB,IAAI,CAACkB,KAAK,CAAC,CAC1B,CACF,CAAC,CAAC,CAEF,MAAO,CAAAD,YAAY,CACrB,CAEA;AACAH,qBAAqBA,CAACnE,MAAM,CAAE,CAC5B,KAAM,CAAAK,QAAQ,CAAG,CAAC,CAAC,CAEnBL,MAAM,CAACgC,OAAO,CAACuC,KAAK,EAAI,CACtB,KAAM,CAAAG,UAAU,CAAGH,KAAK,CAACI,OAAO,CAChC,GAAI,CAACtE,QAAQ,CAACqE,UAAU,CAAC,CAAE,KAAAE,qBAAA,CACzBvE,QAAQ,CAACqE,UAAU,CAAC,CAAG,CACrB7E,GAAG,CAAE6E,UAAU,CACfG,KAAK,CAAE,EAAAD,qBAAA,CAAA1F,sBAAsB,CAACwF,UAAU,CAAC,UAAAE,qBAAA,iBAAlCA,qBAAA,CAAoCC,KAAK,GAAIH,UAAU,CAC9D1E,MAAM,CAAE,EACV,CAAC,CACH,CACAK,QAAQ,CAACqE,UAAU,CAAC,CAAC1E,MAAM,CAACqD,IAAI,CAACkB,KAAK,CAAC,CACzC,CAAC,CAAC,CAEF,MAAO,CAAA3C,MAAM,CAACkD,MAAM,CAACzE,QAAQ,CAAC,CAChC,CAEA;AACA0E,kBAAkBA,CAACnF,MAAM,CAAE,CACzB,KAAM,CAAA4D,MAAM,CAAG,EAAE,CAEjB,GAAI,CAAC5D,MAAM,CAACO,IAAI,EAAIP,MAAM,CAACO,IAAI,CAAC6E,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC7CxB,MAAM,CAACH,IAAI,CAAC,uBAAuB,CAAC,CACtC,CAEA,GAAI,CAACzD,MAAM,CAACI,MAAM,EAAIJ,MAAM,CAACI,MAAM,CAAC8C,MAAM,GAAK,CAAC,CAAE,CAChDU,MAAM,CAACH,IAAI,CAAC,qCAAqC,CAAC,CACpD,CAEA;AACA,KAAM,CAAA4B,cAAc,CAAG,CAAC,MAAM,CAAE,cAAc,CAAE,QAAQ,CAAC,CACzD,KAAM,CAAAC,iBAAiB,CAAGtF,MAAM,CAACI,MAAM,CAACyC,GAAG,CAAC0C,CAAC,EAAIA,CAAC,CAACtF,GAAG,CAAC,CAEvDoF,cAAc,CAACjD,OAAO,CAACoD,aAAa,EAAI,CACtC,GAAI,CAACF,iBAAiB,CAACG,QAAQ,CAACD,aAAa,CAAC,CAAE,CAC9C5B,MAAM,CAACH,IAAI,CAAC,mBAAmB+B,aAAa,oBAAoB,CAAC,CACnE,CACF,CAAC,CAAC,CAEF,MAAO,CACL3B,OAAO,CAAED,MAAM,CAACV,MAAM,GAAK,CAAC,CAC5BU,MACF,CAAC,CACH,CAEA;AACA8B,gBAAgBA,CAAC7F,IAAI,CAAEC,EAAE,CAAE,CACzB,KAAM,CAAAE,MAAM,CAAG,IAAI,CAACwB,cAAc,CAAC3B,IAAI,CAAEC,EAAE,CAAC,CAC5C,GAAI,CAACE,MAAM,CAAE,CACX,KAAM,IAAI,CAAAuB,KAAK,CAAC,8BAA8B,CAAC,CACjD,CAEA,KAAM,CAAAoE,UAAU,CAAG,CACjB,GAAG3F,MAAM,CACT4F,UAAU,CAAE,GAAI,CAAA/E,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACpC+E,aAAa,CAAE,KACjB,CAAC,CAED,MAAO,CAAA1E,IAAI,CAACC,SAAS,CAACuE,UAAU,CAAE,IAAI,CAAE,CAAC,CAAC,CAC5C,CAEA;AACAG,gBAAgBA,CAACC,UAAU,CAAElG,IAAI,CAAEC,EAAE,CAAE,CACrC,GAAI,CACF,KAAM,CAAAE,MAAM,CAAGmB,IAAI,CAACQ,KAAK,CAACoE,UAAU,CAAC,CAErC;AACA,KAAM,CAAAC,UAAU,CAAG,IAAI,CAACb,kBAAkB,CAACnF,MAAM,CAAC,CAClD,GAAI,CAACgG,UAAU,CAACnC,OAAO,CAAE,CACvB,KAAM,IAAI,CAAAtC,KAAK,CAAC,0BAA0ByE,UAAU,CAACpC,MAAM,CAACqC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAC3E,CAEA;AACAjG,MAAM,CAACH,IAAI,CAAGA,IAAI,CAClBG,MAAM,CAACM,YAAY,CAAGR,EAAE,CACxBE,MAAM,CAACkG,UAAU,CAAG,GAAI,CAAArF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC5Cd,MAAM,CAACe,SAAS,CAAG,GAAI,CAAAF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAE3C,MAAO,KAAI,CAACf,cAAc,CAACF,IAAI,CAAEC,EAAE,CAAEE,MAAM,CAAC,CAC9C,CAAE,MAAOqB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAC,CAC3D,KAAM,IAAI,CAAAE,KAAK,CAAC,qCAAqC,CAAC,CACxD,CACF,CAEA;AACA4E,eAAeA,CAACC,UAAU,CAAEC,QAAQ,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,OAAO,CAAE,CACnE,KAAM,CAAAC,YAAY,CAAG,IAAI,CAACjF,cAAc,CAAC4E,UAAU,CAAEC,QAAQ,CAAC,CAC9D,GAAI,CAACI,YAAY,CAAE,CACjB,KAAM,IAAI,CAAAlF,KAAK,CAAC,qCAAqC,CAAC,CACxD,CAEA,KAAM,CAAAmF,YAAY,CAAG,CACnB,GAAGD,YAAY,CACflG,IAAI,CAAEiG,OAAO,EAAI,GAAGC,YAAY,CAAClG,IAAI,SAAS,CAC9CV,IAAI,CAAEyG,UAAU,CAChBhG,YAAY,CAAEiG,QAAQ,CACtBI,UAAU,CAAE,GAAGP,UAAU,IAAIC,QAAQ,EAAE,CACvCzF,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnCC,SAAS,CAAE,GAAI,CAAAF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAED,MAAO,KAAI,CAACf,cAAc,CAACuG,UAAU,CAAEC,QAAQ,CAAEG,YAAY,CAAC,CAChE,CAEA;AACAE,iBAAiBA,CAAA,CAAG,CAClB,KAAM,CAAAlE,UAAU,CAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAE3C,MAAO,CACLkE,UAAU,CAAEnE,UAAU,CAACQ,MAAM,CAC7B4D,aAAa,CAAEpE,UAAU,CAACR,MAAM,CAAC6E,CAAC,EAAIA,CAAC,CAAClH,IAAI,GAAK,UAAU,CAAC,CAACqD,MAAM,CACnE8D,gBAAgB,CAAEtE,UAAU,CAACR,MAAM,CAAC6E,CAAC,EAAIA,CAAC,CAAClH,IAAI,GAAK,aAAa,CAAC,CAACqD,MAAM,CACzE+D,iBAAiB,CAAEvE,UAAU,CAACQ,MAAM,CAAG,CAAC,CACpCgE,IAAI,CAACC,KAAK,CAACzE,UAAU,CAAC0E,MAAM,CAAC,CAACC,GAAG,CAAEN,CAAC,QAAAO,SAAA,OAAK,CAAAD,GAAG,EAAI,EAAAC,SAAA,CAAAP,CAAC,CAAC3G,MAAM,UAAAkH,SAAA,iBAARA,SAAA,CAAUpE,MAAM,GAAI,CAAC,CAAC,GAAE,CAAC,CAAC,CAAGR,UAAU,CAACQ,MAAM,CAAC,CAC/F,CAAC,CACLqE,cAAc,CAAE,IAAI,CAACC,iBAAiB,CAAC9E,UAAU,CAAC,CAClD+E,gBAAgB,CAAE/E,UAAU,CACzBgF,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,GAAI,CAAA/G,IAAI,CAAC+G,CAAC,CAAC7G,SAAS,CAAC,CAAG,GAAI,CAAAF,IAAI,CAAC8G,CAAC,CAAC5G,SAAS,CAAC,CAAC,CAC7D8G,KAAK,CAAC,CAAC,CAAE,CAAC,CACf,CAAC,CACH,CAEA;AACAL,iBAAiBA,CAACM,OAAO,CAAE,CACzB,KAAM,CAAAC,UAAU,CAAG,CAAC,CAAC,CAErBD,OAAO,CAAC1F,OAAO,CAACpC,MAAM,EAAI,CACxB,GAAIA,MAAM,CAACI,MAAM,CAAE,CACjBJ,MAAM,CAACI,MAAM,CAACgC,OAAO,CAACuC,KAAK,EAAI,CAC7BoD,UAAU,CAACpD,KAAK,CAAC1E,GAAG,CAAC,CAAG,CAAC8H,UAAU,CAACpD,KAAK,CAAC1E,GAAG,CAAC,EAAI,CAAC,EAAI,CAAC,CAC1D,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF,MAAO,CAAA+B,MAAM,CAACgG,OAAO,CAACD,UAAU,CAAC,CAC9BL,IAAI,CAAC,CAAAO,IAAA,CAAAC,KAAA,OAAC,EAAEP,CAAC,CAAC,CAAAM,IAAA,IAAE,EAAEL,CAAC,CAAC,CAAAM,KAAA,OAAK,CAAAN,CAAC,CAAGD,CAAC,GAAC,CAC3BE,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CACZhF,GAAG,CAACsF,KAAA,MAAC,CAAClI,GAAG,CAAEmI,KAAK,CAAC,CAAAD,KAAA,OAAM,CAAExD,KAAK,CAAE1E,GAAG,CAAEoI,KAAK,CAAED,KAAM,CAAC,EAAC,CAAC,CAC1D,CAEA;AACAzF,iBAAiBA,CAAA,CAAG,CAClB,GAAI,CACF,KAAM,CAAA2F,KAAK,CAAG,EAAE,CAChB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGtH,YAAY,CAACiC,MAAM,CAAEqF,CAAC,EAAE,CAAE,CAC5C,KAAM,CAAAtI,GAAG,CAAGgB,YAAY,CAAChB,GAAG,CAACsI,CAAC,CAAC,CAC/B,GAAItI,GAAG,EAAIA,GAAG,CAACkC,UAAU,CAAC,IAAI,CAACzC,aAAa,CAAC,CAAE,CAC7C,KAAM,CAAA+B,MAAM,CAAGR,YAAY,CAACS,OAAO,CAACzB,GAAG,CAAC,CACxC,GAAIwB,MAAM,CAAE,KAAA+G,cAAA,CACV,KAAM,CAAAxI,MAAM,CAAGmB,IAAI,CAACQ,KAAK,CAACF,MAAM,CAAC,CACjC6G,KAAK,CAAC7E,IAAI,CAAC,CACTxD,GAAG,CACH,GAAGD,MAAM,CACTyI,OAAO,CAAE,CACPC,UAAU,CAAE,EAAAF,cAAA,CAAAxI,MAAM,CAACI,MAAM,UAAAoI,cAAA,iBAAbA,cAAA,CAAetF,MAAM,GAAI,CAAC,CACtCrD,IAAI,CAAEG,MAAM,CAACH,IAAI,CACjBS,YAAY,CAAEN,MAAM,CAACM,YAAY,CACjCM,SAAS,CAAEZ,MAAM,CAACY,SAAS,CAC3BG,SAAS,CAAEf,MAAM,CAACe,SACpB,CACF,CAAC,CAAC,CACJ,CACF,CACF,CAEA;AACA,MAAO,CAAAuH,KAAK,CAACZ,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,GAAI,CAAA/G,IAAI,CAAC+G,CAAC,CAAC7G,SAAS,CAAC,CAAG,GAAI,CAAAF,IAAI,CAAC8G,CAAC,CAAC5G,SAAS,CAAC,CAAC,CAC5E,CAAE,MAAOM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1D,MAAO,EAAE,CACX,CACF,CAEA;AACAO,gBAAgBA,CAAC/B,IAAI,CAAEC,EAAE,CAAE,CACzB,GAAI,CACF,KAAM,CAAAG,GAAG,CAAG,IAAI,CAACL,aAAa,CAACC,IAAI,CAAEC,EAAE,CAAC,CACxCmB,YAAY,CAACY,UAAU,CAAC5B,GAAG,CAAC,CAC5B,MAAO,KAAI,CACb,CAAE,MAAOoB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1D,MAAO,MAAK,CACd,CACF,CAEA;AACAS,mBAAmBA,CAAA,CAAG,CACpB,GAAI,CACF,KAAM,CAAA6G,YAAY,CAAG,EAAE,CACvB,IAAK,GAAI,CAAAJ,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGtH,YAAY,CAACiC,MAAM,CAAEqF,CAAC,EAAE,CAAE,CAC5C,KAAM,CAAAtI,GAAG,CAAGgB,YAAY,CAAChB,GAAG,CAACsI,CAAC,CAAC,CAC/B,GAAItI,GAAG,EAAIA,GAAG,CAACkC,UAAU,CAAC,IAAI,CAACzC,aAAa,CAAC,CAAE,CAC7CiJ,YAAY,CAAClF,IAAI,CAACxD,GAAG,CAAC,CACxB,CACF,CAEA0I,YAAY,CAACvG,OAAO,CAACnC,GAAG,EAAIgB,YAAY,CAACY,UAAU,CAAC5B,GAAG,CAAC,CAAC,CACzD,MAAO,KAAI,CACb,CAAE,MAAOoB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAC,CAC3D,MAAO,MAAK,CACd,CACF,CACF,CAEA,cAAe,IAAI,CAAA7B,iBAAiB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}