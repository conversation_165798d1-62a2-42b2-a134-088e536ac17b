{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\CategoryManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport Pagination from './Pagination';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = 'http://localhost:5000/api';\nconst CategoryManagement = () => {\n  _s();\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [allCategories, setAllCategories] = useState([]);\n  const [firmNatures, setFirmNatures] = useState([]);\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [newCategory, setNewCategory] = useState('');\n  const [newFirmNature, setNewFirmNature] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  useEffect(() => {\n    fetchDivisions();\n    fetchAllCategories();\n  }, []);\n  useEffect(() => {\n    if (selectedDivision) {\n      fetchCategories(selectedDivision);\n      setSelectedCategory(''); // Reset category selection when division changes\n    } else {\n      setCategories([]);\n      setSelectedCategory('');\n    }\n  }, [selectedDivision]);\n  useEffect(() => {\n    if (selectedCategory) {\n      fetchSubCategories(selectedCategory);\n    }\n  }, [selectedCategory]);\n  const fetchDivisions = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/divisions`);\n      setDivisions(response.data);\n    } catch (error) {\n      console.error('Error fetching divisions:', error);\n    }\n  };\n  const fetchCategories = async divisionId => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/categories/division/${divisionId}`);\n      setCategories(response.data);\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n    }\n  };\n  const fetchAllCategories = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/categories`);\n      setAllCategories(response.data);\n    } catch (error) {\n      console.error('Error fetching all categories:', error);\n    }\n  };\n  const fetchFirmNatures = async categoryId => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/categories/${categoryId}`);\n      setFirmNatures(response.data.firmNatures || []);\n    } catch (error) {\n      console.error('Error fetching firm natures:', error);\n    }\n  };\n  const handleCategorySubmit = async e => {\n    e.preventDefault();\n    if (!newCategory.trim() || !selectedDivision) return;\n    setLoading(true);\n    try {\n      await axios.post(`${API_BASE_URL}/categories`, {\n        Name: newCategory.trim(),\n        DivisionId: parseInt(selectedDivision)\n      });\n      setNewCategory('');\n      fetchCategories(selectedDivision);\n      fetchAllCategories(); // Refresh the overview\n      alert('Category created successfully!');\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data;\n      console.error('Error creating category:', error);\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.errors) {\n        const errorMessages = Object.values(error.response.data.errors).flat().join(', ');\n        alert(`Error creating category: ${errorMessages}`);\n      } else if ((_error$response2 = error.response) !== null && _error$response2 !== void 0 && (_error$response2$data = _error$response2.data) !== null && _error$response2$data !== void 0 && _error$response2$data.message) {\n        alert(`Error creating category: ${error.response.data.message}`);\n      } else {\n        alert('Error creating category. Please try again.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleFirmNatureSubmit = async e => {\n    e.preventDefault();\n    if (!newFirmNature.trim() || !selectedCategory) return;\n    setLoading(true);\n    try {\n      await axios.post(`${API_BASE_URL}/firmnatures`, {\n        Name: newFirmNature.trim(),\n        CategoryId: parseInt(selectedCategory)\n      });\n      setNewFirmNature('');\n      fetchFirmNatures(selectedCategory);\n      fetchAllCategories(); // Refresh the overview\n      alert('Firm nature created successfully!');\n    } catch (error) {\n      var _error$response3, _error$response3$data, _error$response4, _error$response4$data;\n      console.error('Error creating firm nature:', error);\n      if ((_error$response3 = error.response) !== null && _error$response3 !== void 0 && (_error$response3$data = _error$response3.data) !== null && _error$response3$data !== void 0 && _error$response3$data.errors) {\n        const errorMessages = Object.values(error.response.data.errors).flat().join(', ');\n        alert(`Error creating firm nature: ${errorMessages}`);\n      } else if ((_error$response4 = error.response) !== null && _error$response4 !== void 0 && (_error$response4$data = _error$response4.data) !== null && _error$response4$data !== void 0 && _error$response4$data.message) {\n        alert(`Error creating firm nature: ${error.response.data.message}`);\n      } else {\n        alert('Error creating firm nature. Please try again.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Calculate pagination for categories overview\n  const totalItems = allCategories.length;\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  const endIndex = startIndex + itemsPerPage;\n  const currentItems = allCategories.slice(startIndex, endIndex);\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Category Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Add Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"divisionSelect\",\n              children: \"Select Division\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"divisionSelect\",\n              className: \"form-control\",\n              value: selectedDivision,\n              onChange: e => setSelectedDivision(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Division\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: division.id,\n                children: division.name\n              }, division.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleCategorySubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"categoryName\",\n                children: \"Category Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"categoryName\",\n                className: \"form-control\",\n                value: newCategory,\n                onChange: e => setNewCategory(e.target.value),\n                placeholder: \"Enter category name\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary\",\n              disabled: loading || !selectedDivision,\n              children: loading ? 'Adding...' : 'Add Category'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Add Sub-Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"categorySelect\",\n              children: \"Select Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"categorySelect\",\n              className: \"form-control\",\n              value: selectedCategory,\n              onChange: e => setSelectedCategory(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.id,\n                children: category.name\n              }, category.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubCategorySubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"subCategoryName\",\n                children: \"Sub-Category Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"subCategoryName\",\n                className: \"form-control\",\n                value: newSubCategory,\n                onChange: e => setNewSubCategory(e.target.value),\n                placeholder: \"Enter sub-category name\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary\",\n              disabled: loading || !selectedCategory,\n              children: loading ? 'Adding...' : 'Add Sub-Category'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Categories Overview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this), allCategories.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No categories found. Add some categories to see them here.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Division\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Sub-Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: currentItems.map(category => {\n              var _category$division;\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: ((_category$division = category.division) === null || _category$division === void 0 ? void 0 : _category$division.name) || 'Unknown Division'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: category.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: category.subCategories && category.subCategories.length > 0 ? category.subCategories.map(sub => sub.name).join(', ') : 'No sub-categories'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this)]\n              }, category.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n          currentPage: currentPage,\n          totalItems: totalItems,\n          itemsPerPage: itemsPerPage,\n          onPageChange: handlePageChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryManagement, \"4I+7bn1foSEokxHvJQ50gP3c5oM=\");\n_c = CategoryManagement;\nexport default CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Pagination", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_BASE_URL", "CategoryManagement", "_s", "divisions", "setDivisions", "categories", "setCategories", "allCategories", "setAllCategories", "firmNatures", "setFirmNatures", "selectedDivision", "setSelectedDivision", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "newCategory", "setNewCategory", "newFirmNature", "setNewFirmNature", "loading", "setLoading", "currentPage", "setCurrentPage", "itemsPerPage", "fetchDivisions", "fetchAllCategories", "fetchCategories", "fetchSubCategories", "response", "get", "data", "error", "console", "divisionId", "fetchFirmNatures", "categoryId", "handleCategorySubmit", "e", "preventDefault", "trim", "post", "Name", "DivisionId", "parseInt", "alert", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "errors", "errorMessages", "Object", "values", "flat", "join", "message", "handleFirmNatureSubmit", "CategoryId", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data", "totalItems", "length", "startIndex", "endIndex", "currentItems", "slice", "handlePageChange", "page", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "htmlFor", "id", "value", "onChange", "target", "map", "division", "name", "onSubmit", "type", "placeholder", "required", "disabled", "category", "handleSubCategorySubmit", "newSubCategory", "setNewSubCategory", "_category$division", "subCategories", "sub", "onPageChange", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/CategoryManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport Pagination from './Pagination';\r\n\r\nconst API_BASE_URL = 'http://localhost:5000/api';\r\n\r\nconst CategoryManagement = () => {\r\n  const [divisions, setDivisions] = useState([]);\r\n  const [categories, setCategories] = useState([]);\r\n  const [allCategories, setAllCategories] = useState([]);\r\n  const [firmNatures, setFirmNatures] = useState([]);\r\n  const [selectedDivision, setSelectedDivision] = useState('');\r\n  const [selectedCategory, setSelectedCategory] = useState('');\r\n  const [newCategory, setNewCategory] = useState('');\r\n  const [newFirmNature, setNewFirmNature] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [itemsPerPage] = useState(10);\r\n\r\n  useEffect(() => {\r\n    fetchDivisions();\r\n    fetchAllCategories();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (selectedDivision) {\r\n      fetchCategories(selectedDivision);\r\n      setSelectedCategory(''); // Reset category selection when division changes\r\n    } else {\r\n      setCategories([]);\r\n      setSelectedCategory('');\r\n    }\r\n  }, [selectedDivision]);\r\n\r\n  useEffect(() => {\r\n    if (selectedCategory) {\r\n      fetchSubCategories(selectedCategory);\r\n    }\r\n  }, [selectedCategory]);\r\n\r\n  const fetchDivisions = async () => {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/divisions`);\r\n      setDivisions(response.data);\r\n    } catch (error) {\r\n      console.error('Error fetching divisions:', error);\r\n    }\r\n  };\r\n\r\n  const fetchCategories = async (divisionId) => {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/categories/division/${divisionId}`);\r\n      setCategories(response.data);\r\n    } catch (error) {\r\n      console.error('Error fetching categories:', error);\r\n    }\r\n  };\r\n\r\n  const fetchAllCategories = async () => {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/categories`);\r\n      setAllCategories(response.data);\r\n    } catch (error) {\r\n      console.error('Error fetching all categories:', error);\r\n    }\r\n  };\r\n\r\n  const fetchFirmNatures = async (categoryId) => {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/categories/${categoryId}`);\r\n      setFirmNatures(response.data.firmNatures || []);\r\n    } catch (error) {\r\n      console.error('Error fetching firm natures:', error);\r\n    }\r\n  };\r\n\r\n  const handleCategorySubmit = async (e) => {\r\n    e.preventDefault();\r\n    if (!newCategory.trim() || !selectedDivision) return;\r\n\r\n    setLoading(true);\r\n    try {\r\n      await axios.post(`${API_BASE_URL}/categories`, {\r\n        Name: newCategory.trim(),\r\n        DivisionId: parseInt(selectedDivision)\r\n      });\r\n      setNewCategory('');\r\n      fetchCategories(selectedDivision);\r\n      fetchAllCategories(); // Refresh the overview\r\n      alert('Category created successfully!');\r\n    } catch (error) {\r\n      console.error('Error creating category:', error);\r\n      if (error.response?.data?.errors) {\r\n        const errorMessages = Object.values(error.response.data.errors).flat().join(', ');\r\n        alert(`Error creating category: ${errorMessages}`);\r\n      } else if (error.response?.data?.message) {\r\n        alert(`Error creating category: ${error.response.data.message}`);\r\n      } else {\r\n        alert('Error creating category. Please try again.');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleFirmNatureSubmit = async (e) => {\r\n    e.preventDefault();\r\n    if (!newFirmNature.trim() || !selectedCategory) return;\r\n\r\n    setLoading(true);\r\n    try {\r\n      await axios.post(`${API_BASE_URL}/firmnatures`, {\r\n        Name: newFirmNature.trim(),\r\n        CategoryId: parseInt(selectedCategory)\r\n      });\r\n      setNewFirmNature('');\r\n      fetchFirmNatures(selectedCategory);\r\n      fetchAllCategories(); // Refresh the overview\r\n      alert('Firm nature created successfully!');\r\n    } catch (error) {\r\n      console.error('Error creating firm nature:', error);\r\n      if (error.response?.data?.errors) {\r\n        const errorMessages = Object.values(error.response.data.errors).flat().join(', ');\r\n        alert(`Error creating firm nature: ${errorMessages}`);\r\n      } else if (error.response?.data?.message) {\r\n        alert(`Error creating firm nature: ${error.response.data.message}`);\r\n      } else {\r\n        alert('Error creating firm nature. Please try again.');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Calculate pagination for categories overview\r\n  const totalItems = allCategories.length;\r\n  const startIndex = (currentPage - 1) * itemsPerPage;\r\n  const endIndex = startIndex + itemsPerPage;\r\n  const currentItems = allCategories.slice(startIndex, endIndex);\r\n\r\n  const handlePageChange = (page) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <h1>Category Management</h1>\r\n\r\n      <div className=\"row\">\r\n        <div className=\"col\">\r\n          <div className=\"card\">\r\n            <h3>Add Category</h3>\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"divisionSelect\">Select Division</label>\r\n              <select\r\n                id=\"divisionSelect\"\r\n                className=\"form-control\"\r\n                value={selectedDivision}\r\n                onChange={(e) => setSelectedDivision(e.target.value)}\r\n              >\r\n                <option value=\"\">Select Division</option>\r\n                {divisions.map((division) => (\r\n                  <option key={division.id} value={division.id}>\r\n                    {division.name}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            <form onSubmit={handleCategorySubmit}>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"categoryName\">Category Name</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"categoryName\"\r\n                  className=\"form-control\"\r\n                  value={newCategory}\r\n                  onChange={(e) => setNewCategory(e.target.value)}\r\n                  placeholder=\"Enter category name\"\r\n                  required\r\n                />\r\n              </div>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary\"\r\n                disabled={loading || !selectedDivision}\r\n              >\r\n                {loading ? 'Adding...' : 'Add Category'}\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"col\">\r\n          <div className=\"card\">\r\n            <h3>Add Sub-Category</h3>\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"categorySelect\">Select Category</label>\r\n              <select\r\n                id=\"categorySelect\"\r\n                className=\"form-control\"\r\n                value={selectedCategory}\r\n                onChange={(e) => setSelectedCategory(e.target.value)}\r\n              >\r\n                <option value=\"\">Select Category</option>\r\n                {categories.map((category) => (\r\n                  <option key={category.id} value={category.id}>\r\n                    {category.name}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            <form onSubmit={handleSubCategorySubmit}>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"subCategoryName\">Sub-Category Name</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"subCategoryName\"\r\n                  className=\"form-control\"\r\n                  value={newSubCategory}\r\n                  onChange={(e) => setNewSubCategory(e.target.value)}\r\n                  placeholder=\"Enter sub-category name\"\r\n                  required\r\n                />\r\n              </div>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary\"\r\n                disabled={loading || !selectedCategory}\r\n              >\r\n                {loading ? 'Adding...' : 'Add Sub-Category'}\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"card\">\r\n        <h3>Categories Overview</h3>\r\n        {allCategories.length === 0 ? (\r\n          <p>No categories found. Add some categories to see them here.</p>\r\n        ) : (\r\n          <>\r\n            <table className=\"table\">\r\n              <thead>\r\n                <tr>\r\n                  <th>Division</th>\r\n                  <th>Category</th>\r\n                  <th>Sub-Categories</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {currentItems.map((category) => (\r\n                  <tr key={category.id}>\r\n                    <td>{category.division?.name || 'Unknown Division'}</td>\r\n                    <td>{category.name}</td>\r\n                    <td>\r\n                      {category.subCategories && category.subCategories.length > 0\r\n                        ? category.subCategories.map(sub => sub.name).join(', ')\r\n                        : 'No sub-categories'\r\n                      }\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n            <Pagination\r\n              currentPage={currentPage}\r\n              totalItems={totalItems}\r\n              itemsPerPage={itemsPerPage}\r\n              onPageChange={handlePageChange}\r\n            />\r\n          </>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CategoryManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,MAAMC,YAAY,GAAG,2BAA2B;AAEhD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACqB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC+B,YAAY,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAEnCC,SAAS,CAAC,MAAM;IACd+B,cAAc,CAAC,CAAC;IAChBC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAENhC,SAAS,CAAC,MAAM;IACd,IAAIkB,gBAAgB,EAAE;MACpBe,eAAe,CAACf,gBAAgB,CAAC;MACjCG,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,CAAC,MAAM;MACLR,aAAa,CAAC,EAAE,CAAC;MACjBQ,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC,EAAE,CAACH,gBAAgB,CAAC,CAAC;EAEtBlB,SAAS,CAAC,MAAM;IACd,IAAIoB,gBAAgB,EAAE;MACpBc,kBAAkB,CAACd,gBAAgB,CAAC;IACtC;EACF,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;EAEtB,MAAMW,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMlC,KAAK,CAACmC,GAAG,CAAC,GAAG7B,YAAY,YAAY,CAAC;MAC7DI,YAAY,CAACwB,QAAQ,CAACE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED,MAAML,eAAe,GAAG,MAAOO,UAAU,IAAK;IAC5C,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMlC,KAAK,CAACmC,GAAG,CAAC,GAAG7B,YAAY,wBAAwBiC,UAAU,EAAE,CAAC;MACrF3B,aAAa,CAACsB,QAAQ,CAACE,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAMN,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMlC,KAAK,CAACmC,GAAG,CAAC,GAAG7B,YAAY,aAAa,CAAC;MAC9DQ,gBAAgB,CAACoB,QAAQ,CAACE,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAED,MAAMG,gBAAgB,GAAG,MAAOC,UAAU,IAAK;IAC7C,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMlC,KAAK,CAACmC,GAAG,CAAC,GAAG7B,YAAY,eAAemC,UAAU,EAAE,CAAC;MAC5EzB,cAAc,CAACkB,QAAQ,CAACE,IAAI,CAACrB,WAAW,IAAI,EAAE,CAAC;IACjD,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMK,oBAAoB,GAAG,MAAOC,CAAC,IAAK;IACxCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACvB,WAAW,CAACwB,IAAI,CAAC,CAAC,IAAI,CAAC5B,gBAAgB,EAAE;IAE9CS,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM1B,KAAK,CAAC8C,IAAI,CAAC,GAAGxC,YAAY,aAAa,EAAE;QAC7CyC,IAAI,EAAE1B,WAAW,CAACwB,IAAI,CAAC,CAAC;QACxBG,UAAU,EAAEC,QAAQ,CAAChC,gBAAgB;MACvC,CAAC,CAAC;MACFK,cAAc,CAAC,EAAE,CAAC;MAClBU,eAAe,CAACf,gBAAgB,CAAC;MACjCc,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACtBmB,KAAK,CAAC,gCAAgC,CAAC;IACzC,CAAC,CAAC,OAAOb,KAAK,EAAE;MAAA,IAAAc,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdhB,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,KAAAc,eAAA,GAAId,KAAK,CAACH,QAAQ,cAAAiB,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgBf,IAAI,cAAAgB,oBAAA,eAApBA,oBAAA,CAAsBG,MAAM,EAAE;QAChC,MAAMC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACrB,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACmB,MAAM,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QACjFV,KAAK,CAAC,4BAA4BM,aAAa,EAAE,CAAC;MACpD,CAAC,MAAM,KAAAH,gBAAA,GAAIhB,KAAK,CAACH,QAAQ,cAAAmB,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjB,IAAI,cAAAkB,qBAAA,eAApBA,qBAAA,CAAsBO,OAAO,EAAE;QACxCX,KAAK,CAAC,4BAA4Bb,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACyB,OAAO,EAAE,CAAC;MAClE,CAAC,MAAM;QACLX,KAAK,CAAC,4CAA4C,CAAC;MACrD;IACF,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,sBAAsB,GAAG,MAAOnB,CAAC,IAAK;IAC1CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACrB,aAAa,CAACsB,IAAI,CAAC,CAAC,IAAI,CAAC1B,gBAAgB,EAAE;IAEhDO,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM1B,KAAK,CAAC8C,IAAI,CAAC,GAAGxC,YAAY,cAAc,EAAE;QAC9CyC,IAAI,EAAExB,aAAa,CAACsB,IAAI,CAAC,CAAC;QAC1BkB,UAAU,EAAEd,QAAQ,CAAC9B,gBAAgB;MACvC,CAAC,CAAC;MACFK,gBAAgB,CAAC,EAAE,CAAC;MACpBgB,gBAAgB,CAACrB,gBAAgB,CAAC;MAClCY,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACtBmB,KAAK,CAAC,mCAAmC,CAAC;IAC5C,CAAC,CAAC,OAAOb,KAAK,EAAE;MAAA,IAAA2B,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd7B,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,KAAA2B,gBAAA,GAAI3B,KAAK,CAACH,QAAQ,cAAA8B,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5B,IAAI,cAAA6B,qBAAA,eAApBA,qBAAA,CAAsBV,MAAM,EAAE;QAChC,MAAMC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACrB,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACmB,MAAM,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QACjFV,KAAK,CAAC,+BAA+BM,aAAa,EAAE,CAAC;MACvD,CAAC,MAAM,KAAAU,gBAAA,GAAI7B,KAAK,CAACH,QAAQ,cAAAgC,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9B,IAAI,cAAA+B,qBAAA,eAApBA,qBAAA,CAAsBN,OAAO,EAAE;QACxCX,KAAK,CAAC,+BAA+Bb,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACyB,OAAO,EAAE,CAAC;MACrE,CAAC,MAAM;QACLX,KAAK,CAAC,+CAA+C,CAAC;MACxD;IACF,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0C,UAAU,GAAGvD,aAAa,CAACwD,MAAM;EACvC,MAAMC,UAAU,GAAG,CAAC3C,WAAW,GAAG,CAAC,IAAIE,YAAY;EACnD,MAAM0C,QAAQ,GAAGD,UAAU,GAAGzC,YAAY;EAC1C,MAAM2C,YAAY,GAAG3D,aAAa,CAAC4D,KAAK,CAACH,UAAU,EAAEC,QAAQ,CAAC;EAE9D,MAAMG,gBAAgB,GAAIC,IAAI,IAAK;IACjC/C,cAAc,CAAC+C,IAAI,CAAC;EACtB,CAAC;EAED,oBACExE,OAAA;IAAAyE,QAAA,gBACEzE,OAAA;MAAAyE,QAAA,EAAI;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE5B7E,OAAA;MAAK8E,SAAS,EAAC,KAAK;MAAAL,QAAA,gBAClBzE,OAAA;QAAK8E,SAAS,EAAC,KAAK;QAAAL,QAAA,eAClBzE,OAAA;UAAK8E,SAAS,EAAC,MAAM;UAAAL,QAAA,gBACnBzE,OAAA;YAAAyE,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrB7E,OAAA;YAAK8E,SAAS,EAAC,YAAY;YAAAL,QAAA,gBACzBzE,OAAA;cAAO+E,OAAO,EAAC,gBAAgB;cAAAN,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvD7E,OAAA;cACEgF,EAAE,EAAC,gBAAgB;cACnBF,SAAS,EAAC,cAAc;cACxBG,KAAK,EAAEnE,gBAAiB;cACxBoE,QAAQ,EAAG1C,CAAC,IAAKzB,mBAAmB,CAACyB,CAAC,CAAC2C,MAAM,CAACF,KAAK,CAAE;cAAAR,QAAA,gBAErDzE,OAAA;gBAAQiF,KAAK,EAAC,EAAE;gBAAAR,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxCvE,SAAS,CAAC8E,GAAG,CAAEC,QAAQ,iBACtBrF,OAAA;gBAA0BiF,KAAK,EAAEI,QAAQ,CAACL,EAAG;gBAAAP,QAAA,EAC1CY,QAAQ,CAACC;cAAI,GADHD,QAAQ,CAACL,EAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7E,OAAA;YAAMuF,QAAQ,EAAEhD,oBAAqB;YAAAkC,QAAA,gBACnCzE,OAAA;cAAK8E,SAAS,EAAC,YAAY;cAAAL,QAAA,gBACzBzE,OAAA;gBAAO+E,OAAO,EAAC,cAAc;gBAAAN,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnD7E,OAAA;gBACEwF,IAAI,EAAC,MAAM;gBACXR,EAAE,EAAC,cAAc;gBACjBF,SAAS,EAAC,cAAc;gBACxBG,KAAK,EAAE/D,WAAY;gBACnBgE,QAAQ,EAAG1C,CAAC,IAAKrB,cAAc,CAACqB,CAAC,CAAC2C,MAAM,CAACF,KAAK,CAAE;gBAChDQ,WAAW,EAAC,qBAAqB;gBACjCC,QAAQ;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7E,OAAA;cACEwF,IAAI,EAAC,QAAQ;cACbV,SAAS,EAAC,iBAAiB;cAC3Ba,QAAQ,EAAErE,OAAO,IAAI,CAACR,gBAAiB;cAAA2D,QAAA,EAEtCnD,OAAO,GAAG,WAAW,GAAG;YAAc;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7E,OAAA;QAAK8E,SAAS,EAAC,KAAK;QAAAL,QAAA,eAClBzE,OAAA;UAAK8E,SAAS,EAAC,MAAM;UAAAL,QAAA,gBACnBzE,OAAA;YAAAyE,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB7E,OAAA;YAAK8E,SAAS,EAAC,YAAY;YAAAL,QAAA,gBACzBzE,OAAA;cAAO+E,OAAO,EAAC,gBAAgB;cAAAN,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvD7E,OAAA;cACEgF,EAAE,EAAC,gBAAgB;cACnBF,SAAS,EAAC,cAAc;cACxBG,KAAK,EAAEjE,gBAAiB;cACxBkE,QAAQ,EAAG1C,CAAC,IAAKvB,mBAAmB,CAACuB,CAAC,CAAC2C,MAAM,CAACF,KAAK,CAAE;cAAAR,QAAA,gBAErDzE,OAAA;gBAAQiF,KAAK,EAAC,EAAE;gBAAAR,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxCrE,UAAU,CAAC4E,GAAG,CAAEQ,QAAQ,iBACvB5F,OAAA;gBAA0BiF,KAAK,EAAEW,QAAQ,CAACZ,EAAG;gBAAAP,QAAA,EAC1CmB,QAAQ,CAACN;cAAI,GADHM,QAAQ,CAACZ,EAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7E,OAAA;YAAMuF,QAAQ,EAAEM,uBAAwB;YAAApB,QAAA,gBACtCzE,OAAA;cAAK8E,SAAS,EAAC,YAAY;cAAAL,QAAA,gBACzBzE,OAAA;gBAAO+E,OAAO,EAAC,iBAAiB;gBAAAN,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1D7E,OAAA;gBACEwF,IAAI,EAAC,MAAM;gBACXR,EAAE,EAAC,iBAAiB;gBACpBF,SAAS,EAAC,cAAc;gBACxBG,KAAK,EAAEa,cAAe;gBACtBZ,QAAQ,EAAG1C,CAAC,IAAKuD,iBAAiB,CAACvD,CAAC,CAAC2C,MAAM,CAACF,KAAK,CAAE;gBACnDQ,WAAW,EAAC,yBAAyB;gBACrCC,QAAQ;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7E,OAAA;cACEwF,IAAI,EAAC,QAAQ;cACbV,SAAS,EAAC,iBAAiB;cAC3Ba,QAAQ,EAAErE,OAAO,IAAI,CAACN,gBAAiB;cAAAyD,QAAA,EAEtCnD,OAAO,GAAG,WAAW,GAAG;YAAkB;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7E,OAAA;MAAK8E,SAAS,EAAC,MAAM;MAAAL,QAAA,gBACnBzE,OAAA;QAAAyE,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC3BnE,aAAa,CAACwD,MAAM,KAAK,CAAC,gBACzBlE,OAAA;QAAAyE,QAAA,EAAG;MAA0D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,gBAEjE7E,OAAA,CAAAE,SAAA;QAAAuE,QAAA,gBACEzE,OAAA;UAAO8E,SAAS,EAAC,OAAO;UAAAL,QAAA,gBACtBzE,OAAA;YAAAyE,QAAA,eACEzE,OAAA;cAAAyE,QAAA,gBACEzE,OAAA;gBAAAyE,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjB7E,OAAA;gBAAAyE,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjB7E,OAAA;gBAAAyE,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR7E,OAAA;YAAAyE,QAAA,EACGJ,YAAY,CAACe,GAAG,CAAEQ,QAAQ;cAAA,IAAAI,kBAAA;cAAA,oBACzBhG,OAAA;gBAAAyE,QAAA,gBACEzE,OAAA;kBAAAyE,QAAA,EAAK,EAAAuB,kBAAA,GAAAJ,QAAQ,CAACP,QAAQ,cAAAW,kBAAA,uBAAjBA,kBAAA,CAAmBV,IAAI,KAAI;gBAAkB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxD7E,OAAA;kBAAAyE,QAAA,EAAKmB,QAAQ,CAACN;gBAAI;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxB7E,OAAA;kBAAAyE,QAAA,EACGmB,QAAQ,CAACK,aAAa,IAAIL,QAAQ,CAACK,aAAa,CAAC/B,MAAM,GAAG,CAAC,GACxD0B,QAAQ,CAACK,aAAa,CAACb,GAAG,CAACc,GAAG,IAAIA,GAAG,CAACZ,IAAI,CAAC,CAAC7B,IAAI,CAAC,IAAI,CAAC,GACtD;gBAAmB;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAErB,CAAC;cAAA,GAREe,QAAQ,CAACZ,EAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAShB,CAAC;YAAA,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACR7E,OAAA,CAACF,UAAU;UACT0B,WAAW,EAAEA,WAAY;UACzByC,UAAU,EAAEA,UAAW;UACvBvC,YAAY,EAAEA,YAAa;UAC3ByE,YAAY,EAAE5B;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA,eACF,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxE,EAAA,CAhRID,kBAAkB;AAAAgG,EAAA,GAAlBhG,kBAAkB;AAkRxB,eAAeA,kBAAkB;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}