{"ast": null, "code": "import { useConstant } from '../../utils/use-constant.mjs';\nimport { useUnmountEffect } from '../../utils/use-unmount-effect.mjs';\nimport { createScopedAnimate } from '../animate.mjs';\nfunction useAnimate() {\n  const scope = useConstant(() => ({\n    current: null,\n    animations: []\n  }));\n  const animate = useConstant(() => createScopedAnimate(scope));\n  useUnmountEffect(() => {\n    scope.animations.forEach(animation => animation.stop());\n  });\n  return [scope, animate];\n}\nexport { useAnimate };", "map": {"version": 3, "names": ["useConstant", "useUnmountEffect", "createScopedAnimate", "useAnimate", "scope", "current", "animations", "animate", "for<PERSON>ach", "animation", "stop"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/animation/hooks/use-animate.mjs"], "sourcesContent": ["import { useConstant } from '../../utils/use-constant.mjs';\nimport { useUnmountEffect } from '../../utils/use-unmount-effect.mjs';\nimport { createScopedAnimate } from '../animate.mjs';\n\nfunction useAnimate() {\n    const scope = useConstant(() => ({\n        current: null,\n        animations: [],\n    }));\n    const animate = useConstant(() => createScopedAnimate(scope));\n    useUnmountEffect(() => {\n        scope.animations.forEach((animation) => animation.stop());\n    });\n    return [scope, animate];\n}\n\nexport { useAnimate };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,mBAAmB,QAAQ,gBAAgB;AAEpD,SAASC,UAAUA,CAAA,EAAG;EAClB,MAAMC,KAAK,GAAGJ,WAAW,CAAC,OAAO;IAC7BK,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE;EAChB,CAAC,CAAC,CAAC;EACH,MAAMC,OAAO,GAAGP,WAAW,CAAC,MAAME,mBAAmB,CAACE,KAAK,CAAC,CAAC;EAC7DH,gBAAgB,CAAC,MAAM;IACnBG,KAAK,CAACE,UAAU,CAACE,OAAO,CAAEC,SAAS,IAAKA,SAAS,CAACC,IAAI,CAAC,CAAC,CAAC;EAC7D,CAAC,CAAC;EACF,OAAO,CAACN,KAAK,EAAEG,OAAO,CAAC;AAC3B;AAEA,SAASJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}