# Import/Export System for Person Management

## Overview
A comprehensive import/export system for the Person management module that supports CSV and Excel file formats with robust validation, error handling, and progress tracking.

## Features

### Import Functionality
- **File Format Support**: CSV and Excel (.xlsx, .xls)
- **Bulk Import**: Process large datasets efficiently with batch processing
- **Validation**: Comprehensive row-by-row validation with detailed error reporting
- **Duplicate Handling**: Three modes - Skip, Update, or Fail on duplicates
- **Progress Tracking**: Real-time progress updates for long-running imports
- **Partial Imports**: Continue processing valid rows even if some fail
- **Async Processing**: Background processing for large files

### Export Functionality
- **Multiple Formats**: CSV and Excel export with customizable formatting
- **Filtering**: Export with advanced filtering options
- **Column Selection**: Choose specific columns to export
- **Related Data**: Include division, category, and firm nature names
- **Streaming**: Memory-efficient streaming for large datasets
- **Templates**: Generate import templates with sample data and validation rules

### Validation Features
- **File Validation**: Size limits, format validation, content verification
- **Data Validation**: Field-level validation using existing Person validation rules
- **Business Rules**: Enforce business logic (unique mobile numbers, required fields)
- **Error Reporting**: Detailed error messages with row numbers and field names
- **Warning System**: Non-blocking warnings for data quality issues

## Project Structure

```
backend/
├── Models/ImportExport/
│   ├── PersonImportRequest.cs          # Import request DTO
│   ├── PersonImportResponse.cs         # Import response with progress
│   ├── ImportValidationError.cs        # Validation error details
│   ├── PersonExportRequest.cs          # Export request DTO
│   ├── PersonImportData.cs             # Internal import data structure
│   └── PersonImportColumns.cs          # Column definitions
├── Services/ImportExport/
│   ├── IImportExportService.cs         # Main service interface
│   ├── ImportExportService.cs          # Main service implementation
│   ├── ImportExportServiceExtensions.cs # Helper methods
│   ├── IFileValidationService.cs       # File validation interface
│   └── FileValidationService.cs        # File validation implementation
└── Controllers/
    └── ImportExportController.cs       # API endpoints
```

## API Endpoints

### Import Operations
- `POST /api/import-export/persons/import` - Upload and import person data
- `POST /api/import-export/persons/validate` - Validate file without importing
- `GET /api/import-export/persons/import-status/{jobId}` - Get import status
- `GET /api/import-export/persons/import-progress/{jobId}` - Get detailed progress
- `POST /api/import-export/persons/import-cancel/{jobId}` - Cancel import job

### Export Operations
- `POST /api/import-export/persons/export` - Export person data
- `GET /api/import-export/persons/template` - Download import template
- `GET /api/import-export/persons/export-columns` - Get available columns

### Utility Operations
- `GET /api/import-export/persons/statistics` - Get import/export statistics
- `POST /api/import-export/cleanup` - Clean up old files and jobs

## Import Process Flow

1. **File Upload**: User uploads CSV or Excel file
2. **File Validation**: Validate file format, size, and basic content
3. **File Parsing**: Parse file content into structured data
4. **Data Validation**: Validate each row against business rules
5. **Duplicate Detection**: Check for existing records
6. **Batch Processing**: Process valid records in batches
7. **Progress Updates**: Real-time progress tracking
8. **Summary Report**: Detailed import summary with statistics

## Export Process Flow

1. **Request Processing**: Parse export request with filters
2. **Data Retrieval**: Query persons based on filters
3. **Data Transformation**: Map to export format
4. **File Generation**: Create CSV or Excel file
5. **Streaming Response**: Return file as downloadable content

## Validation Rules

### File Validation
- **Size Limit**: Maximum 10MB per file
- **Format**: Only CSV, XLSX, XLS files allowed
- **Content**: Basic content validation for file integrity
- **Security**: Filename validation to prevent path traversal

### Data Validation
- **Required Fields**: Division Name, Category Name, Name, Mobile Number, Nature
- **Format Validation**: Mobile number (Indian format), email addresses, URLs
- **Business Rules**: Unique mobile numbers within division/category
- **Enum Validation**: Nature, Gender, Working Profiles
- **Date Logic**: Birth date before marriage date, no future dates
- **Conditional Requirements**: Associate details, website links, etc.

## Error Handling

### Error Types
- **Critical**: Stops processing (file format errors, system errors)
- **Error**: Skips row but continues (validation failures, missing data)
- **Warning**: Processes row but flags issues (data quality concerns)

### Error Response Format
```json
{
  "rowNumber": 5,
  "fieldName": "Mobile Number",
  "errorMessage": "Invalid mobile number format",
  "fieldValue": "123456",
  "severity": "Error",
  "errorCode": "INVALID_FORMAT"
}
```

## Import Modes

### Skip Duplicates (Default)
- Skips existing records based on mobile number + division + category
- Continues processing new records
- Reports skipped count in summary

### Update Existing
- Updates existing records with new data
- Creates new records for non-duplicates
- Reports update count in summary

### Fail on Duplicates
- Stops processing when duplicate is found
- Reports error for duplicate records
- Useful for strict data integrity requirements

## Performance Optimizations

### Import Optimizations
- **Batch Processing**: Process records in configurable batches (default 100)
- **Async Processing**: Background processing for large files
- **Memory Management**: Stream processing to handle large files
- **Database Optimization**: Bulk operations where possible

### Export Optimizations
- **Streaming**: Memory-efficient streaming for large datasets
- **Pagination**: Internal pagination for very large exports
- **Caching**: Cache frequently accessed reference data
- **Compression**: Automatic compression for large files

## Configuration

### File Limits
```csharp
MaxFileSize = 10MB
AllowedExtensions = [".csv", ".xlsx", ".xls"]
BatchSize = 100 (configurable)
TempFileRetention = 7 days
```

### Validation Settings
```csharp
MobileNumberPattern = @"^(\+91[\-\s]?)?[0]?(91)?[789]\d{9}$"
MaxNameLength = 255
MaxEmailLength = 255
StarRatingRange = 1-5
```

## Usage Examples

### Import Request
```json
POST /api/import-export/persons/import
Content-Type: multipart/form-data

{
  "file": [uploaded file],
  "importMode": "SkipDuplicates",
  "validateOnly": false,
  "batchSize": 100
}
```

### Export Request
```json
POST /api/import-export/persons/export
{
  "format": "Excel",
  "divisionId": 1,
  "categoryId": 2,
  "includeRelatedData": true,
  "selectedColumns": ["Name", "Mobile Number", "Email"],
  "sortBy": "CreatedAt",
  "sortDirection": "desc"
}
```

### Import Progress Response
```json
{
  "jobId": "abc123",
  "status": "Processing",
  "totalRows": 1000,
  "processedRows": 750,
  "successfulRows": 700,
  "failedRows": 50,
  "currentOperation": "Processing batch 8 of 10",
  "progressPercentage": 75.0
}
```

## Security Considerations

### File Security
- File type validation based on content, not just extension
- Filename sanitization to prevent path traversal
- Temporary file cleanup to prevent disk space issues
- File size limits to prevent DoS attacks

### Data Security
- Validation of all input data
- SQL injection prevention through parameterized queries
- Access control through existing authentication system
- Audit logging for import/export operations

## Monitoring and Logging

### Import/Export Metrics
- Total imports/exports per day/month
- Average processing time
- Success/failure rates
- Most common error types
- File size distributions

### Logging
- File upload events
- Processing start/completion
- Validation errors
- Performance metrics
- Error conditions

## Troubleshooting

### Common Issues
1. **File Format Errors**: Check file extension and content type
2. **Validation Failures**: Review error details for specific field issues
3. **Duplicate Handling**: Verify import mode settings
4. **Performance Issues**: Consider reducing batch size or file size
5. **Memory Issues**: Use streaming for large files

### Error Codes
- `REQUIRED_FIELD`: Missing required field
- `INVALID_FORMAT`: Invalid data format
- `NOT_FOUND`: Referenced entity not found
- `DUPLICATE_RECORD`: Duplicate record detected
- `INVALID_ENUM`: Invalid enumeration value
- `FILE_TOO_LARGE`: File exceeds size limit
- `UNSUPPORTED_FORMAT`: Unsupported file format

## Future Enhancements

### Planned Features
- **Real-time Validation**: Live validation during file upload
- **Data Mapping**: Custom field mapping for different file formats
- **Scheduled Imports**: Automated imports from external sources
- **Data Transformation**: Built-in data transformation rules
- **Advanced Filtering**: More sophisticated export filtering options
- **Audit Trail**: Complete audit trail for all import/export operations
