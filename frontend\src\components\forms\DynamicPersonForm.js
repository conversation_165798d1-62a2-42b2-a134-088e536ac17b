import React, { useState, useEffect } from 'react';
import { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';
import formConfigService from '../../services/formConfigService';
import apiService from '../../services/apiService';
import FormField from './FormField';
import './DynamicPersonForm.css';

const DynamicPersonForm = ({ onSubmit, onCancel, initialData = null, mode = 'create' }) => {
  // Hierarchy state
  const [divisions, setDivisions] = useState([]);
  const [categories, setCategories] = useState([]);
  const [firmNatures, setFirmNatures] = useState([]);

  // Selection state
  const [selectedDivision, setSelectedDivision] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedFirmNature, setSelectedFirmNature] = useState('');

  // Form state
  const [formConfig, setFormConfig] = useState(null);
  const [formData, setFormData] = useState(getDefaultPersonData());
  const [errors, setErrors] = useState({});

  // Loading states
  const [loading, setLoading] = useState({
    divisions: false,
    categories: false,
    firmNatures: false,
    form: false
  });
  const [submitting, setSubmitting] = useState(false);

  // Form availability state
  const [formAvailability, setFormAvailability] = useState({
    categoryHasForm: false,
    firmNatureHasForm: false,
    showFirmNatureDropdown: false,
    message: ''
  });

  // Load divisions on component mount
  useEffect(() => {
    loadDivisions();

    // Test API connectivity
    testApiConnectivity();

    // Load initial data if provided
    if (initialData) {
      setFormData(initialData);
      setSelectedDivision(initialData.divisionId?.toString() || '');
      setSelectedCategory(initialData.categoryId?.toString() || '');
      setSelectedFirmNature(initialData.firmNatureId?.toString() || '');
    } else {
      // Load default form for new person creation
      const defaultForm = formConfigService.getDefaultFormConfig();
      // Ensure no duplicate fields in default form
      if (defaultForm.fields) {
        defaultForm.fields = deduplicateFields(defaultForm.fields);
      }
      setFormConfig(defaultForm);
    }
  }, [initialData]);

  // Test API connectivity
  const testApiConnectivity = async () => {
    try {
      await apiService.getDivisions();
    } catch (error) {
      console.error('API connectivity test failed:', error);
    }
  };

  // Deduplicate fields based on field key
  const deduplicateFields = (fields) => {
    const seen = new Set();
    const deduplicated = [];

    fields.forEach(field => {
      if (!seen.has(field.key)) {
        seen.add(field.key);
        deduplicated.push(field);
      } else {
        console.warn(`DynamicPersonForm: Removing duplicate field: ${field.key}`);
      }
    });

    if (fields.length !== deduplicated.length) {
      console.log(`DynamicPersonForm: Deduplicated ${fields.length} fields to ${deduplicated.length} unique fields`);
    }

    return deduplicated;
  };

  // Load categories when division changes
  useEffect(() => {
    if (selectedDivision) {
      loadCategories(selectedDivision);
      // Reset category and firm nature when division changes
      setSelectedCategory('');
      setSelectedFirmNature('');
      setFirmNatures([]);
      setFormConfig(null);
      setFormAvailability({
        categoryHasForm: false,
        firmNatureHasForm: false,
        showFirmNatureDropdown: false,
        message: ''
      });
    } else {
      setCategories([]);
      setFirmNatures([]);
      setSelectedCategory('');
      setSelectedFirmNature('');
      setFormConfig(null);
    }
  }, [selectedDivision]);

  // Handle category selection and form loading
  useEffect(() => {
    if (selectedCategory) {
      handleCategorySelection(selectedCategory);
      // Reset firm nature when category changes
      setSelectedFirmNature('');
    } else {
      setFirmNatures([]);
      setSelectedFirmNature('');
      setFormConfig(null);
      setFormAvailability({
        categoryHasForm: false,
        firmNatureHasForm: false,
        showFirmNatureDropdown: false,
        message: ''
      });
    }
  }, [selectedCategory]);

  // Handle firm nature selection and form loading
  useEffect(() => {
    if (selectedFirmNature) {
      handleFirmNatureSelection(selectedFirmNature);
    }
  }, [selectedFirmNature]);

  // Load divisions from API
  const loadDivisions = async () => {
    setLoading(prev => ({ ...prev, divisions: true }));
    try {
      const response = await apiService.getDivisions();
      setDivisions(response.data || []);
    } catch (error) {
      console.error('Error loading divisions:', error);
      setErrors(prev => ({ ...prev, divisions: 'Failed to load divisions' }));
    } finally {
      setLoading(prev => ({ ...prev, divisions: false }));
    }
  };

  // Load categories by division
  const loadCategories = async (divisionId) => {
    setLoading(prev => ({ ...prev, categories: true }));
    try {
      const response = await apiService.getCategoriesByDivision(divisionId);
      setCategories(response.data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
      setErrors(prev => ({ ...prev, categories: 'Failed to load categories' }));
      setCategories([]);
    } finally {
      setLoading(prev => ({ ...prev, categories: false }));
    }
  };

  // Load firm natures by category
  const loadFirmNatures = async (categoryId) => {
    setLoading(prev => ({ ...prev, firmNatures: true }));
    try {
      const response = await apiService.getFirmNaturesByCategory(categoryId);
      setFirmNatures(response.data || []);
    } catch (error) {
      console.error('Error loading firm natures:', error);
      setErrors(prev => ({ ...prev, firmNatures: 'Failed to load firm natures' }));
      setFirmNatures([]);
    } finally {
      setLoading(prev => ({ ...prev, firmNatures: false }));
    }
  };

  // Handle category selection and form loading logic
  const handleCategorySelection = async (categoryId) => {
    setLoading(prev => ({ ...prev, form: true }));

    try {
      // Check if category has a form
      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));

      if (categoryHasForm) {
        // Load category form and hide firm nature dropdown
        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));
        // Deduplicate fields in loaded form
        if (categoryForm && categoryForm.fields) {
          categoryForm.fields = deduplicateFields(categoryForm.fields);
        }
        setFormConfig(categoryForm);
        setFormAvailability({
          categoryHasForm: true,
          firmNatureHasForm: false,
          showFirmNatureDropdown: false,
          message: 'Using category-specific form'
        });
      } else {
        // No category form, show firm nature dropdown and load firm natures
        await loadFirmNatures(categoryId);
        // Load default form as fallback
        const defaultForm = formConfigService.getDefaultFormConfig();
        // Deduplicate fields in default form
        if (defaultForm.fields) {
          defaultForm.fields = deduplicateFields(defaultForm.fields);
        }
        setFormConfig(defaultForm);
        setFormAvailability({
          categoryHasForm: false,
          firmNatureHasForm: false,
          showFirmNatureDropdown: true,
          message: 'Using default form. You can select a firm nature if available.'
        });
      }
    } catch (error) {
      console.error('Error handling category selection:', error);
      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));
    } finally {
      setLoading(prev => ({ ...prev, form: false }));
    }
  };

  // Handle firm nature selection and form loading logic
  const handleFirmNatureSelection = async (firmNatureId) => {
    setLoading(prev => ({ ...prev, form: true }));

    try {
      // Check if firm nature has a form
      const firmNatureHasForm = formConfigService.hasFormForFirmNature(parseInt(firmNatureId));

      if (firmNatureHasForm) {
        // Load firm nature form
        const firmNatureForm = formConfigService.loadFormConfig('firmNature', parseInt(firmNatureId));
        // Deduplicate fields in loaded form
        if (firmNatureForm && firmNatureForm.fields) {
          firmNatureForm.fields = deduplicateFields(firmNatureForm.fields);
        }
        setFormConfig(firmNatureForm);
        setFormAvailability({
          categoryHasForm: false,
          firmNatureHasForm: true,
          showFirmNatureDropdown: true,
          message: 'Using firm nature-specific form'
        });
      } else {
        // No firm nature form available, use default form
        const defaultForm = formConfigService.getDefaultFormConfig();
        // Deduplicate fields in default form
        if (defaultForm.fields) {
          defaultForm.fields = deduplicateFields(defaultForm.fields);
        }
        setFormConfig(defaultForm);
        setFormAvailability({
          categoryHasForm: false,
          firmNatureHasForm: false,
          showFirmNatureDropdown: true,
          message: 'No specific form found for this firm nature. Using default form.'
        });
      }
    } catch (error) {
      console.error('Error handling firm nature selection:', error);
      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));
    } finally {
      setLoading(prev => ({ ...prev, form: false }));
    }
  };

  // Handle dropdown changes
  const handleDivisionChange = (e) => {
    const value = e.target.value;
    setSelectedDivision(value);

    // Update form data
    setFormData(prev => ({
      ...prev,
      divisionId: value ? parseInt(value) : null,
      categoryId: null,
      firmNatureId: null
    }));
  };

  const handleCategoryChange = (e) => {
    const value = e.target.value;
    setSelectedCategory(value);

    // Update form data
    setFormData(prev => ({
      ...prev,
      categoryId: value ? parseInt(value) : null,
      firmNatureId: null
    }));
  };

  const handleFirmNatureChange = (e) => {
    const value = e.target.value;
    setSelectedFirmNature(value);

    // Update form data
    setFormData(prev => ({
      ...prev,
      firmNatureId: value ? parseInt(value) : null
    }));
  };

  const handleFieldChange = (fieldKey, value) => {
    setFormData(prev => ({
      ...prev,
      [fieldKey]: value
    }));

    // Clear field error when user starts typing
    if (errors[fieldKey]) {
      setErrors(prev => ({
        ...prev,
        [fieldKey]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate hierarchy selection (required by backend)
    if (!selectedDivision) {
      newErrors.division = 'Division is required';
    } else if (parseInt(selectedDivision) < 1) {
      newErrors.division = 'Division ID must be a positive number';
    }

    if (!selectedCategory) {
      newErrors.category = 'Category is required';
    } else if (parseInt(selectedCategory) < 1) {
      newErrors.category = 'Category ID must be a positive number';
    }

    if (selectedFirmNature && parseInt(selectedFirmNature) < 1) {
      newErrors.firmNature = 'Firm Nature ID must be a positive number';
    }

    // Validate required fields that match backend requirements
    if (!formData.name || formData.name.trim() === '') {
      newErrors.name = 'Name is required';
    } else if (formData.name.length > 255) {
      newErrors.name = 'Name cannot exceed 255 characters';
    }

    if (!formData.mobileNumber || formData.mobileNumber.trim() === '') {
      newErrors.mobileNumber = 'Mobile number is required';
    } else {
      // Validate mobile number format (matches backend regex)
      const mobileRegex = /^(\+91[\-\s]?)?[0]?(91)?[789]\d{9}$/;
      if (!mobileRegex.test(formData.mobileNumber)) {
        newErrors.mobileNumber = 'Invalid mobile number format';
      }
    }

    if (!formData.nature || formData.nature === '' || formData.nature === '0') {
      newErrors.nature = 'Nature is required';
    } else if (![1, 2, 3, 4].includes(parseInt(formData.nature))) {
      newErrors.nature = 'Invalid nature value';
    }

    // Check if form is available (should always have default form as fallback)
    if (!formConfig) {
      newErrors.form = 'Form configuration not loaded. Please try again.';
      return { isValid: false, errors: newErrors };
    }



    // Validate form fields
    formConfig.fields.forEach(field => {
      const value = formData[field.key];
      
      // Required field validation
      if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
        newErrors[field.key] = `${field.label} is required`;
        return;
      }

      // Conditional field validation
      if (field.conditional && shouldShowField(field)) {
        if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
          newErrors[field.key] = `${field.label} is required`;
          return;
        }
      }

      // Type-specific validation
      if (value && typeof value === 'string' && value.trim() !== '') {
        switch (field.type) {
          case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
              newErrors[field.key] = 'Please enter a valid email address';
            }
            break;
          case 'tel':
            const phoneRegex = /^(\+91[\-\s]?)?[0]?(91)?[789]\d{9}$/;
            if (!phoneRegex.test(value)) {
              newErrors[field.key] = 'Please enter a valid mobile number';
            }
            break;
          case 'url':
            try {
              new URL(value);
            } catch {
              newErrors[field.key] = 'Please enter a valid URL';
            }
            break;
          case 'number':
            if (field.validation) {
              const numValue = parseFloat(value);
              if (field.validation.min !== undefined && numValue < field.validation.min) {
                newErrors[field.key] = `Value must be at least ${field.validation.min}`;
              }
              if (field.validation.max !== undefined && numValue > field.validation.max) {
                newErrors[field.key] = `Value must be at most ${field.validation.max}`;
              }
            }
            break;
        }

        // Pattern validation
        if (field.validation?.pattern) {
          const regex = new RegExp(field.validation.pattern);
          if (!regex.test(value)) {
            newErrors[field.key] = `${field.label} format is invalid`;
          }
        }

        // Length validation
        if (field.validation?.minLength && value.length < field.validation.minLength) {
          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;
        }
        if (field.validation?.maxLength && value.length > field.validation.maxLength) {
          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;
        }

        // Backend-specific field validations
        if (field.key === 'primaryEmailId' && value.length > 255) {
          newErrors[field.key] = 'Email cannot exceed 255 characters';
        }
        if (field.key === 'workingState' && value.length > 100) {
          newErrors[field.key] = 'Working state cannot exceed 100 characters';
        }
        if (field.key === 'domesticState' && value.length > 100) {
          newErrors[field.key] = 'Domestic state cannot exceed 100 characters';
        }
        if (field.key === 'district' && value.length > 100) {
          newErrors[field.key] = 'District cannot exceed 100 characters';
        }
        if (field.key === 'address' && value.length > 500) {
          newErrors[field.key] = 'Address cannot exceed 500 characters';
        }
        if (field.key === 'workingArea' && value.length > 200) {
          newErrors[field.key] = 'Working area cannot exceed 200 characters';
        }
        if (field.key === 'associateName' && value.length > 255) {
          newErrors[field.key] = 'Associate name cannot exceed 255 characters';
        }
        if (field.key === 'associateRelation' && value.length > 100) {
          newErrors[field.key] = 'Associate relation cannot exceed 100 characters';
        }
        if (field.key === 'reraRegistrationNumber' && value.length > 50) {
          newErrors[field.key] = 'RERA registration number cannot exceed 50 characters';
        }
        if (field.key === 'source' && value.length > 200) {
          newErrors[field.key] = 'Source cannot exceed 200 characters';
        }
        if (field.key === 'remarks' && value.length > 1000) {
          newErrors[field.key] = 'Remarks cannot exceed 1000 characters';
        }
        if (field.key === 'firmName' && value.length > 255) {
          newErrors[field.key] = 'Firm name cannot exceed 255 characters';
        }
        if (field.key === 'authorizedPersonName' && value.length > 255) {
          newErrors[field.key] = 'Authorized person name cannot exceed 255 characters';
        }
        if (field.key === 'authorizedPersonEmail' && value.length > 255) {
          newErrors[field.key] = 'Authorized person email cannot exceed 255 characters';
        }
        if (field.key === 'designation' && value.length > 100) {
          newErrors[field.key] = 'Designation cannot exceed 100 characters';
        }
        if (field.key === 'marketingContact' && value.length > 255) {
          newErrors[field.key] = 'Marketing contact cannot exceed 255 characters';
        }
        if (field.key === 'marketingDesignation' && value.length > 100) {
          newErrors[field.key] = 'Marketing designation cannot exceed 100 characters';
        }
        if (field.key === 'placeOfPosting' && value.length > 200) {
          newErrors[field.key] = 'Place of posting cannot exceed 200 characters';
        }
        if (field.key === 'department' && value.length > 100) {
          newErrors[field.key] = 'Department cannot exceed 100 characters';
        }
      }

      // Validate numeric fields
      if (field.type === 'number' && value) {
        const numValue = parseFloat(value);
        if (field.key === 'starRating' && (numValue < 1 || numValue > 5)) {
          newErrors[field.key] = 'Star rating must be between 1 and 5';
        }
        if (['numberOfOffices', 'numberOfBranches', 'totalEmployeeStrength'].includes(field.key) && numValue < 0) {
          newErrors[field.key] = `${field.label} must be non-negative`;
        }
        if (field.key === 'transactionValue' && numValue < 0) {
          newErrors[field.key] = 'Transaction value must be non-negative';
        }
      }

      // Validate associate mobile number format
      if (field.key === 'associateMobile' && value && value.trim() !== '') {
        const mobileRegex = /^(\+91[\-\s]?)?[0]?(91)?[789]\d{9}$/;
        if (!mobileRegex.test(value)) {
          newErrors[field.key] = 'Invalid associate mobile number format';
        }
      }
    });

    // Business logic validation
    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {
      const birthDate = new Date(formData.dateOfBirth);
      const marriageDate = new Date(formData.dateOfMarriage);
      if (marriageDate <= birthDate) {
        newErrors.dateOfMarriage = 'Marriage date must be after birth date';
      }
    }

    return {
      isValid: Object.keys(newErrors).length === 0,
      errors: newErrors
    };
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Debug: Basic form data validation
    console.log('Form submission - validation-sensitive fields:', {
      primaryEmailId: formData.primaryEmailId,
      website: formData.website,
      websiteLink: formData.websiteLink,
      crmAppLink: formData.crmAppLink,
      authorizedPersonEmail: formData.authorizedPersonEmail,
      associateMobile: formData.associateMobile
    });

    const validation = validateForm();
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    setSubmitting(true);
    try {
      // Prepare data for API - match backend model exactly (PascalCase field names)
      const submitData = {
        // Required hierarchy fields
        DivisionId: parseInt(selectedDivision),
        CategoryId: parseInt(selectedCategory),
        FirmNatureId: selectedFirmNature ? parseInt(selectedFirmNature) : null,

        // Required fields
        Name: formData.name || '',
        MobileNumber: formData.mobileNumber || '',
        Nature: formData.nature ? parseInt(formData.nature) : 1, // Default to Business (1) if not provided

        // Optional enum fields
        Gender: formData.gender ? parseInt(formData.gender) : null,

        // Contact Information
        AlternateNumbers: Array.isArray(formData.alternateNumbers)
          ? formData.alternateNumbers.filter(num => num && num.trim() !== '')
          : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()).filter(s => s !== '') : [],
        AlternateEmailIds: Array.isArray(formData.alternateEmailIds)
          ? formData.alternateEmailIds.filter(email => email && email.trim() !== '')
          : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()).filter(s => s !== '') : [],
        // Note: PrimaryEmailId is added conditionally below only if it has a valid value

        // Personal Information
        DateOfBirth: formData.dateOfBirth || null,
        IsMarried: Boolean(formData.isMarried),
        DateOfMarriage: formData.dateOfMarriage || null,

        // Location Information
        WorkingState: formData.workingState || '',
        DomesticState: formData.domesticState || '',
        District: formData.district || '',
        Address: formData.address || '',
        WorkingArea: formData.workingArea || '',

        // Associate Information
        HasAssociate: Boolean(formData.hasAssociate),
        AssociateName: formData.associateName || '',
        AssociateRelation: formData.associateRelation || '',
        // Note: AssociateMobile is added conditionally below only if it has a valid value

        // Digital Presence
        UsingWebsite: Boolean(formData.usingWebsite),
        UsingCRMApp: Boolean(formData.usingCRMApp),
        // Note: Website, WebsiteLink, CRMAppLink are added conditionally below only if they have valid values

        // Business Information
        TransactionValue: formData.transactionValue ? parseFloat(formData.transactionValue) : null,
        RERARegistrationNumber: formData.reraRegistrationNumber || '',
        WorkingProfiles: Array.isArray(formData.workingProfiles)
          ? formData.workingProfiles.map(wp => parseInt(wp)).filter(wp => !isNaN(wp))
          : [],
        StarRating: formData.starRating ? parseInt(formData.starRating) : null,
        Source: formData.source || '',
        Remarks: formData.remarks || '',

        // Company Information
        FirmName: formData.firmName || '',
        NumberOfOffices: formData.numberOfOffices && !isNaN(parseInt(formData.numberOfOffices)) ? parseInt(formData.numberOfOffices) : null,
        NumberOfBranches: formData.numberOfBranches && !isNaN(parseInt(formData.numberOfBranches)) ? parseInt(formData.numberOfBranches) : null,
        TotalEmployeeStrength: formData.totalEmployeeStrength && !isNaN(parseInt(formData.totalEmployeeStrength)) ? parseInt(formData.totalEmployeeStrength) : null,

        // Authorized Person
        AuthorizedPersonName: formData.authorizedPersonName || '',
        Designation: formData.designation || '',
        // Note: AuthorizedPersonEmail is added conditionally below only if it has a valid value

        // Marketing Information
        MarketingContact: formData.marketingContact || '',
        MarketingDesignation: formData.marketingDesignation || '',
        PlaceOfPosting: formData.placeOfPosting || '',
        Department: formData.department || ''
      };

      // Only add optional URL and email fields if they have valid values (not empty strings or null)
      // This prevents backend validation errors for empty strings on URL/Email fields

      // Helper function to check if a field has a valid value
      // This function is CRITICAL - it must return false for any value that would cause backend validation errors
      const hasValidValue = (value) => {
        return value !== null && value !== undefined && value !== '' &&
               (typeof value === 'string' ? value.trim() !== '' : true);
      };

      // Debug: Check what values we have for validation-sensitive fields
      console.log('=== SUBMITDATA CONSTRUCTION DEBUG ===');
      console.log('Validation-sensitive field values:', {
        primaryEmailId: formData.primaryEmailId,
        authorizedPersonEmail: formData.authorizedPersonEmail,
        website: formData.website,
        websiteLink: formData.websiteLink,
        crmAppLink: formData.crmAppLink,
        associateMobile: formData.associateMobile
      });

      // Test our hasValidValue function on each field
      console.log('hasValidValue test results:');
      console.log('- primaryEmailId:', hasValidValue(formData.primaryEmailId));
      console.log('- authorizedPersonEmail:', hasValidValue(formData.authorizedPersonEmail));
      console.log('- website:', hasValidValue(formData.website));
      console.log('- websiteLink:', hasValidValue(formData.websiteLink));
      console.log('- crmAppLink:', hasValidValue(formData.crmAppLink));
      console.log('- associateMobile:', hasValidValue(formData.associateMobile));

      // Email fields - only add if not empty and valid
      if (hasValidValue(formData.primaryEmailId)) {
        console.log('Adding PrimaryEmailId:', formData.primaryEmailId);
        submitData.PrimaryEmailId = formData.primaryEmailId.trim();
      }

      if (hasValidValue(formData.authorizedPersonEmail)) {
        console.log('Adding AuthorizedPersonEmail:', formData.authorizedPersonEmail);
        submitData.AuthorizedPersonEmail = formData.authorizedPersonEmail.trim();
      }

      // URL fields - only add if not empty and valid
      if (hasValidValue(formData.website)) {
        console.log('Adding Website:', formData.website);
        submitData.Website = formData.website.trim();
      }

      if (hasValidValue(formData.websiteLink)) {
        console.log('Adding WebsiteLink:', formData.websiteLink);
        submitData.WebsiteLink = formData.websiteLink.trim();
      }

      if (hasValidValue(formData.crmAppLink)) {
        console.log('Adding CRMAppLink:', formData.crmAppLink);
        submitData.CRMAppLink = formData.crmAppLink.trim();
      }

      // Associate mobile - only add if not empty and valid (has regex validation)
      if (hasValidValue(formData.associateMobile)) {
        console.log('Adding AssociateMobile:', formData.associateMobile);
        submitData.AssociateMobile = formData.associateMobile.trim();
      }



      // Validate required fields before submission
      const requiredFieldsCheck = {
        DivisionId: submitData.DivisionId,
        CategoryId: submitData.CategoryId,
        Name: submitData.Name,
        MobileNumber: submitData.MobileNumber,
        Nature: submitData.Nature
      };

      // Check for missing required fields
      const missingFields = Object.entries(requiredFieldsCheck)
        .filter(([, value]) => !value || value === '' || value === null)
        .map(([key]) => key);

      if (missingFields.length > 0) {
        setErrors({ general: 'Missing required fields: ' + missingFields.join(', ') });
        return;
      }

      // FINAL SAFETY CHECK: Remove any validation-sensitive fields that have empty values
      // This is a bulletproof approach to ensure no empty strings reach the backend
      const validationSensitiveFields = [
        'PrimaryEmailId', 'AuthorizedPersonEmail', 'Website', 'WebsiteLink',
        'CRMAppLink', 'AssociateMobile'
      ];

      console.log('=== FINAL SAFETY CHECK ===');
      console.log('Before cleanup:', Object.keys(submitData));

      validationSensitiveFields.forEach(fieldName => {
        if (submitData.hasOwnProperty(fieldName)) {
          const value = submitData[fieldName];
          const isEmpty = value === null || value === undefined || value === '' ||
                         (typeof value === 'string' && value.trim() === '');

          console.log(`Checking ${fieldName}:`, {
            value,
            isEmpty,
            action: isEmpty ? 'REMOVING' : 'KEEPING'
          });

          if (isEmpty) {
            delete submitData[fieldName];
            console.log(`🗑️ REMOVED ${fieldName} (was: "${value}")`);
          }
        }
      });

      console.log('After cleanup:', Object.keys(submitData));
      console.log('Final submitData:', JSON.stringify(submitData, null, 2));

      let result;
      if (mode === 'create') {
        result = await apiService.createPerson(submitData);
      } else {
        result = await apiService.updatePerson(initialData.id, submitData);
      }

      onSubmit(result);
    } catch (error) {
      console.error('Error submitting form:', error);
      console.error('Error details:', {
        status: error.status,
        data: error.data,
        response: error.response,
        message: error.message
      });

      // Enhanced error handling for backend validation errors
      if (error.data?.errors || error.response?.data?.errors) {
        // Handle ASP.NET Core ModelState validation errors
        const validationErrors = error.data?.errors || error.response?.data?.errors;
        const backendErrors = {};

        console.log('Validation errors received:', validationErrors);

        Object.keys(validationErrors).forEach(key => {
          const errorMessages = validationErrors[key];
          // Map backend field names to frontend field names for display
          const frontendFieldName = mapBackendFieldToFrontend(key);
          backendErrors[frontendFieldName] = Array.isArray(errorMessages)
            ? errorMessages.join(', ')
            : errorMessages;
        });
        setErrors(backendErrors);
      } else if (error.data?.title || error.data?.detail || error.response?.data?.title || error.response?.data?.detail) {
        // Handle ProblemDetails format errors
        const errorMessage = error.data?.detail || error.data?.title || error.response?.data?.detail || error.response?.data?.title;
        setErrors({ general: errorMessage });
      } else if (error.isValidationError && error.isValidationError()) {
        const validationErrors = error.getValidationErrors();
        console.log('ApiError validation errors:', validationErrors);
        setErrors(validationErrors);
      } else {
        // Handle other error formats
        const errorMessage = error.data?.message ||
                           error.response?.data?.message ||
                           error.data?.title ||
                           error.message ||
                           'An error occurred while saving the person. Please check your input and try again.';
        setErrors({ general: errorMessage });
      }
    } finally {
      setSubmitting(false);
    }
  };

  // Helper function to map backend field names to frontend field names for error display
  const mapBackendFieldToFrontend = (backendFieldName) => {
    const fieldMapping = {
      'DivisionId': 'division',
      'CategoryId': 'category',
      'FirmNatureId': 'firmNature',
      'Name': 'name',
      'MobileNumber': 'mobileNumber',
      'Nature': 'nature',
      'Gender': 'gender',
      'PrimaryEmailId': 'primaryEmailId',
      'AlternateNumbers': 'alternateNumbers',
      'AlternateEmailIds': 'alternateEmailIds',
      'Website': 'website',
      'DateOfBirth': 'dateOfBirth',
      'IsMarried': 'isMarried',
      'DateOfMarriage': 'dateOfMarriage',
      'WorkingState': 'workingState',
      'DomesticState': 'domesticState',
      'District': 'district',
      'Address': 'address',
      'WorkingArea': 'workingArea',
      'HasAssociate': 'hasAssociate',
      'AssociateName': 'associateName',
      'AssociateRelation': 'associateRelation',
      'AssociateMobile': 'associateMobile',
      'UsingWebsite': 'usingWebsite',
      'WebsiteLink': 'websiteLink',
      'UsingCRMApp': 'usingCRMApp',
      'CRMAppLink': 'crmAppLink',
      'TransactionValue': 'transactionValue',
      'RERARegistrationNumber': 'reraRegistrationNumber',
      'WorkingProfiles': 'workingProfiles',
      'StarRating': 'starRating',
      'Source': 'source',
      'Remarks': 'remarks',
      'FirmName': 'firmName',
      'NumberOfOffices': 'numberOfOffices',
      'NumberOfBranches': 'numberOfBranches',
      'TotalEmployeeStrength': 'totalEmployeeStrength',
      'AuthorizedPersonName': 'authorizedPersonName',
      'AuthorizedPersonEmail': 'authorizedPersonEmail',
      'Designation': 'designation',
      'MarketingContact': 'marketingContact',
      'MarketingDesignation': 'marketingDesignation',
      'PlaceOfPosting': 'placeOfPosting',
      'Department': 'department'
    };

    return fieldMapping[backendFieldName] || backendFieldName.toLowerCase();
  };

  const shouldShowField = (field) => {
    if (!field.conditional) return true;

    const conditionValue = formData[field.conditional.field];
    const expectedValue = field.conditional.value;

    // Handle boolean conditions
    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {
      return conditionValue === (expectedValue === true || expectedValue === 'true');
    }

    return conditionValue === expectedValue;
  };

  const groupFieldsBySections = () => {
    if (!formConfig || !formConfig.fields) return {};

    const sections = {};
    formConfig.fields.forEach((field) => {
      const sectionKey = field.section || 'general';
      if (!sections[sectionKey]) {
        sections[sectionKey] = {
          title: getSectionTitle(sectionKey),
          fields: []
        };
      }
      sections[sectionKey].fields.push(field);
    });

    return sections;
  };

  const getSectionTitle = (sectionKey) => {
    const titles = {
      personalInfo: 'Personal Information',
      contactInfo: 'Contact Information',
      locationInfo: 'Location Information',
      businessInfo: 'Business Information',
      associateInfo: 'Associate Information',
      digitalPresence: 'Digital Presence',
      companyInfo: 'Company Information',
      authorizedPerson: 'Authorized Person',
      marketingInfo: 'Marketing Information',
      general: 'General Information'
    };
    return titles[sectionKey] || sectionKey;
  };

  const sections = formConfig ? groupFieldsBySections() : {};



  return (
    <div className="dynamic-person-form">
      <div className="form-header">
        <h2>{mode === 'create' ? 'Create New Person' : 'Edit Person'}</h2>
        {formConfig && (
          <div className="form-info">
            <span className="form-name">{formConfig.name}</span>
            {formConfig.description && (
              <span className="form-description">{formConfig.description}</span>
            )}
          </div>
        )}
      </div>

      {errors.general && (
        <div className="alert alert-error">{errors.general}</div>
      )}

      {/* Display validation error summary */}
      {Object.keys(errors).length > 0 && !errors.general && (
        <div className="alert alert-error">
          <strong>Please fix the following errors:</strong>
          <ul style={{ margin: '8px 0 0 20px' }}>
            {Object.entries(errors)
              .filter(([key]) => key !== 'general')
              .map(([key, message]) => (
                <li key={key}>{message}</li>
              ))}
          </ul>
        </div>
      )}

      <form onSubmit={handleSubmit} className="person-form">
        {/* Hierarchical Dropdowns */}
        <div className="form-section">
          <h3>Division & Category Selection</h3>

          {/* Division Dropdown */}
          <div className="form-group">
            <label className="form-label">
              Division <span className="required">*</span>
            </label>
            <select
              value={selectedDivision}
              onChange={handleDivisionChange}
              disabled={loading.divisions}
              className={`form-select ${errors.division ? 'error' : ''}`}
              required
            >
              <option value="">
                {loading.divisions ? 'Loading divisions...' : 'Select Division'}
              </option>
              {divisions.map(division => (
                <option key={division.id} value={division.id}>
                  {division.name}
                </option>
              ))}
            </select>
            {errors.division && (
              <div className="error-message">{errors.division}</div>
            )}
            {errors.divisions && (
              <div className="error-message">{errors.divisions}</div>
            )}
          </div>

          {/* Category Dropdown */}
          <div className="form-group">
            <label className="form-label">
              Category <span className="required">*</span>
            </label>
            <select
              value={selectedCategory}
              onChange={handleCategoryChange}
              disabled={!selectedDivision || loading.categories}
              className={`form-select ${errors.category ? 'error' : ''}`}
              required
            >
              <option value="">
                {!selectedDivision
                  ? 'Select Division first'
                  : loading.categories
                    ? 'Loading categories...'
                    : 'Select Category'
                }
              </option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
            {errors.category && (
              <div className="error-message">{errors.category}</div>
            )}
            {errors.categories && (
              <div className="error-message">{errors.categories}</div>
            )}
          </div>

          {/* Firm Nature Dropdown - Only show if category doesn't have a form */}
          {formAvailability.showFirmNatureDropdown && (
            <div className="form-group">
              <label className="form-label">
                Firm Nature (Required)
              </label>
              <select
                value={selectedFirmNature}
                onChange={handleFirmNatureChange}
                disabled={!selectedCategory || loading.firmNatures}
                className="form-select"
                required
              >
                <option value="">
                  {!selectedCategory
                    ? 'Select Category first'
                    : loading.firmNatures
                      ? 'Loading firm natures...'
                      : 'Select Firm Nature (Required)'
                  }
                </option>
                {firmNatures.map(firmNature => (
                  <option key={firmNature.id} value={firmNature.id}>
                    {firmNature.name}
                  </option>
                ))}
              </select>
              {errors.firmNatures && (
                <div className="error-message">{errors.firmNatures}</div>
              )}
            </div>
          )}

          {/* Form Status Messages */}
          {loading.form && (
            <div className="loading-message">
              <span>Loading form configuration...</span>
            </div>
          )}

          {formAvailability.message && !loading.form && (
            <div className={`status-message ${
              formAvailability.categoryHasForm || formAvailability.firmNatureHasForm
                ? 'success'
                : formAvailability.message.includes('No form')
                  ? 'error'
                  : 'info'
            }`}>
              {formAvailability.message}
            </div>
          )}

          {errors.form && (
            <div className="error-message">{errors.form}</div>
          )}
        </div>

        {/* Dynamic Form Fields - Only show when form is available */}
        {formConfig && Object.entries(sections).map(([sectionKey, section]) => (
          <div key={sectionKey} className="form-section">
            <h3>{section.title}</h3>
            <div className="form-fields">
              {section.fields
                .filter(field => shouldShowField(field))
                .map((field, fieldIndex) => (
                  <FormField
                    key={`${sectionKey}-${field.key}-${fieldIndex}`}
                    field={field}
                    value={formData[field.key]}
                    onChange={(value) => handleFieldChange(field.key, value)}
                    error={errors[field.key]}
                  />
                ))}
            </div>
          </div>
        ))}



        {/* Form Actions */}
        <div className="form-actions">
          <button
            type="button"
            onClick={onCancel}
            className="btn btn-outline"
            disabled={submitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={submitting || !selectedDivision || !selectedCategory}
          >
            {submitting
              ? (mode === 'create' ? 'Creating...' : 'Updating...')
              : (mode === 'create' ? 'Create Person' : 'Update Person')
            }
          </button>
        </div>
      </form>
    </div>
  );
};

export default DynamicPersonForm;
