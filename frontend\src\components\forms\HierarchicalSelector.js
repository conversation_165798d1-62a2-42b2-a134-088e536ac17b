import React, { useState, useEffect } from 'react';
import apiService from '../../services/apiService';
import './HierarchicalSelector.css';

const HierarchicalSelector = ({ 
  onSelectionChange, 
  initialSelection = {}, 
  disabled = false,
  showLabels = true,
  required = false 
}) => {
  const [divisions, setDivisions] = useState([]);
  const [categories, setCategories] = useState([]);
  const [firmNatures, setFirmNatures] = useState([]);

  const [selectedDivision, setSelectedDivision] = useState(initialSelection.divisionId || '');
  const [selectedCategory, setSelectedCategory] = useState(initialSelection.categoryId || '');
  const [selectedFirmNature, setSelectedFirmNature] = useState(initialSelection.firmNatureId || '');

  const [loading, setLoading] = useState({
    divisions: false,
    categories: false,
    firmNatures: false
  });
  
  const [errors, setErrors] = useState({});

  // Update state when initialSelection changes
  useEffect(() => {
    setSelectedDivision(initialSelection.divisionId || '');
    setSelectedCategory(initialSelection.categoryId || '');
    setSelectedFirmNature(initialSelection.firmNatureId || '');
  }, [initialSelection.divisionId, initialSelection.categoryId, initialSelection.firmNatureId]);

  // Load divisions on component mount
  useEffect(() => {
    loadDivisions();
  }, []);

  // Load categories when division changes
  useEffect(() => {
    if (selectedDivision) {
      loadCategories(selectedDivision);
    } else {
      setCategories([]);
      setSelectedCategory('');
      setSelectedFirmNature('');
      setFirmNatures([]);
    }
  }, [selectedDivision]);

  // Load firm natures when category changes
  useEffect(() => {
    if (selectedCategory) {
      loadFirmNatures(selectedCategory);
    } else {
      setFirmNatures([]);
      setSelectedFirmNature('');
    }
  }, [selectedCategory]);

  // Notify parent of selection changes
  useEffect(() => {
    const selection = {
      divisionId: selectedDivision ? parseInt(selectedDivision) : null,
      categoryId: selectedCategory ? parseInt(selectedCategory) : null,
      firmNatureId: selectedFirmNature ? parseInt(selectedFirmNature) : null,
      division: divisions.find(d => d.id === parseInt(selectedDivision)) || null,
      category: categories.find(c => c.id === parseInt(selectedCategory)) || null,
      firmNature: firmNatures.find(fn => fn.id === parseInt(selectedFirmNature)) || null
    };

    onSelectionChange(selection);
  }, [selectedDivision, selectedCategory, selectedFirmNature, divisions, categories, firmNatures, onSelectionChange]);

  const loadDivisions = async () => {
    setLoading(prev => ({ ...prev, divisions: true }));
    try {
      const response = await apiService.getDivisions();
      setDivisions(response.data || []);
      setErrors(prev => ({ ...prev, divisions: null }));
    } catch (error) {
      console.error('Error loading divisions:', error);
      setErrors(prev => ({ ...prev, divisions: 'Failed to load divisions' }));
    } finally {
      setLoading(prev => ({ ...prev, divisions: false }));
    }
  };

  const loadCategories = async (divisionId) => {
    setLoading(prev => ({ ...prev, categories: true }));
    try {
      const response = await apiService.getCategoriesByDivision(divisionId);
      setCategories(response.data || []);
      setErrors(prev => ({ ...prev, categories: null }));
    } catch (error) {
      console.error('Error loading categories:', error);
      setErrors(prev => ({ ...prev, categories: 'Failed to load categories' }));
      setCategories([]);
    } finally {
      setLoading(prev => ({ ...prev, categories: false }));
    }
  };

  const loadFirmNatures = async (categoryId) => {
    setLoading(prev => ({ ...prev, firmNatures: true }));
    try {
      const response = await apiService.getFirmNaturesByCategory(categoryId);
      setFirmNatures(response.data || []);
      setErrors(prev => ({ ...prev, firmNatures: null }));
    } catch (error) {
      console.error('Error loading firm natures:', error);
      setErrors(prev => ({ ...prev, firmNatures: 'Failed to load firm natures' }));
      setFirmNatures([]);
    } finally {
      setLoading(prev => ({ ...prev, firmNatures: false }));
    }
  };

  const handleDivisionChange = (e) => {
    const value = e.target.value;
    setSelectedDivision(value);
    setSelectedCategory('');
    setSelectedFirmNature('');
  };

  const handleCategoryChange = (e) => {
    const value = e.target.value;
    setSelectedCategory(value);
    setSelectedFirmNature('');
  };

  const handleFirmNatureChange = (e) => {
    const value = e.target.value;
    setSelectedFirmNature(value);
  };

  return (
    <div className="hierarchical-selector">


      {/* Division Selection */}
      <div className="selector-group">
        {showLabels && (
          <label className="selector-label">
            Division {required && <span className="required">*</span>}
          </label>
        )}
        <select
          value={selectedDivision}
          onChange={handleDivisionChange}
          disabled={disabled || loading.divisions}
          className={`selector-input ${errors.divisions ? 'error' : ''}`}
          required={required}
        >
          <option value="">
            {loading.divisions ? 'Loading divisions...' : 'Select Division'}
          </option>
          {divisions.map(division => (
            <option key={division.id} value={division.id}>
              {division.name}
            </option>
          ))}
        </select>
        {errors.divisions && (
          <div className="error-message">{errors.divisions}</div>
        )}
      </div>

      {/* Category Selection */}
      <div className="selector-group">
        {showLabels && (
          <label className="selector-label">
            Category {required && <span className="required">*</span>}
          </label>
        )}
        <select
          value={selectedCategory}
          onChange={handleCategoryChange}
          disabled={disabled || !selectedDivision || loading.categories}
          className={`selector-input ${errors.categories ? 'error' : ''}`}
          required={required}
        >
          <option value="">
            {!selectedDivision 
              ? 'Select Division first'
              : loading.categories 
                ? 'Loading categories...' 
                : 'Select Category'
            }
          </option>
          {categories.map(category => (
            <option key={category.id} value={category.id}>
              {category.name}
            </option>
          ))}
        </select>
        {errors.categories && (
          <div className="error-message">{errors.categories}</div>
        )}
      </div>

      {/* Firm Nature Selection */}
      <div className="selector-group">
        {showLabels && (
          <label className="selector-label">Firm Nature (Required)</label>
        )}
        <select
          value={selectedFirmNature}
          onChange={handleFirmNatureChange}
          disabled={disabled || !selectedCategory || loading.firmNatures}
          className={`selector-input ${errors.firmNatures ? 'error' : ''}`}
          required
        >
          <option value="">
            {!selectedCategory
              ? 'Select Category first'
              : loading.firmNatures
                ? 'Loading firm natures...'
                : firmNatures.length === 0
                  ? 'No firm natures available'
                  : 'Select Firm Nature (Required)'
            }
          </option>
          {firmNatures.map(firmNature => (
            <option key={firmNature.id} value={firmNature.id}>
              {firmNature.name}
            </option>
          ))}
        </select>
        {errors.firmNatures && (
          <div className="error-message">{errors.firmNatures}</div>
        )}
      </div>

      {/* Selection Summary */}
      {(selectedDivision || selectedCategory || selectedFirmNature) && (
        <div className="selection-summary">
          <h4>Current Selection:</h4>
          <div className="selection-path">
            {selectedDivision && (
              <span className="selection-item">
                {divisions.find(d => d.id === parseInt(selectedDivision))?.name}
              </span>
            )}
            {selectedCategory && (
              <>
                <span className="separator">→</span>
                <span className="selection-item">
                  {categories.find(c => c.id === parseInt(selectedCategory))?.name}
                </span>
              </>
            )}
            {selectedFirmNature && (
              <>
                <span className="separator">→</span>
                <span className="selection-item">
                  {firmNatures.find(fn => fn.id === parseInt(selectedFirmNature))?.name}
                </span>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default HierarchicalSelector;
