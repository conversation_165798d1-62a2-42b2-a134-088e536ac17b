{"ast": null, "code": "function record(data) {\n  if (window.MotionDebug) {\n    window.MotionDebug.record(data);\n  }\n}\nexport { record };", "map": {"version": 3, "names": ["record", "data", "window", "MotionDebug"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/debug/record.mjs"], "sourcesContent": ["function record(data) {\n    if (window.MotionDebug) {\n        window.MotionDebug.record(data);\n    }\n}\n\nexport { record };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,IAAI,EAAE;EAClB,IAAIC,MAAM,CAACC,WAAW,EAAE;IACpBD,MAAM,CAACC,WAAW,CAACH,MAAM,CAACC,IAAI,CAAC;EACnC;AACJ;AAEA,SAASD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}