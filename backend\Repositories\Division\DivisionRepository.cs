using Microsoft.EntityFrameworkCore;
using CrmApi.Data;
using CrmApi.Models.Division;

namespace CrmApi.Repositories.Division
{
    public class DivisionRepository : IDivisionRepository
    {
        private readonly CrmDbContext _context;

        public DivisionRepository(CrmDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Models.Division.Division>> GetAllAsync()
        {
            return await _context.Divisions.ToListAsync();
        }

        public async Task<Models.Division.Division?> GetByIdAsync(int id)
        {
            return await _context.Divisions.FindAsync(id);
        }

        public async Task<Models.Division.Division?> GetByIdWithCategoriesAsync(int id)
        {
            return await _context.Divisions
                .Include(d => d.Categories)
                .ThenInclude(c => c.FirmNatures)
                .FirstOrDefaultAsync(d => d.Id == id);
        }

        public async Task<Models.Division.Division> CreateAsync(Models.Division.Division division)
        {
            division.CreatedAt = DateTime.UtcNow;
            division.UpdatedAt = DateTime.UtcNow;
            
            _context.Divisions.Add(division);
            await _context.SaveChangesAsync();
            return division;
        }

        public async Task<Models.Division.Division> UpdateAsync(Models.Division.Division division)
        {
            division.UpdatedAt = DateTime.UtcNow;
            _context.Entry(division).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return division;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var division = await _context.Divisions.FindAsync(id);
            if (division == null)
                return false;

            _context.Divisions.Remove(division);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.Divisions.AnyAsync(d => d.Id == id);
        }

        public async Task<bool> NameExistsAsync(string name, int? excludeId = null)
        {
            var query = _context.Divisions.Where(d => d.Name == name);
            if (excludeId.HasValue)
                query = query.Where(d => d.Id != excludeId.Value);
            
            return await query.AnyAsync();
        }
    }
}
