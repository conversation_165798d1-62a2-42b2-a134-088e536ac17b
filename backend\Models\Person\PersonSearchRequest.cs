using System.ComponentModel.DataAnnotations;

namespace CrmApi.Models.Person
{
    public class PersonSearchRequest
    {
        // Pagination
        [Range(1, int.MaxValue, ErrorMessage = "Page number must be greater than 0")]
        public int Page { get; set; } = 1;

        [Range(1, 100, ErrorMessage = "Page size must be between 1 and 100")]
        public int PageSize { get; set; } = 10;

        // Search Filters
        public string? Name { get; set; }
        public string? MobileNumber { get; set; }
        public string? Email { get; set; }
        public int? DivisionId { get; set; }
        public int? CategoryId { get; set; }
        public int? FirmNatureId { get; set; }
        public PersonNature? Nature { get; set; }
        public Gender? Gender { get; set; }
        public string? WorkingState { get; set; }
        public string? District { get; set; }
        public string? FirmName { get; set; }
        public int? MinStarRating { get; set; }
        public int? MaxStarRating { get; set; }
        public decimal? MinTransactionValue { get; set; }
        public decimal? MaxTransactionValue { get; set; }
        public bool? HasAssociate { get; set; }
        public bool? UsingWebsite { get; set; }
        public bool? UsingCRMApp { get; set; }
        public DateTime? CreatedAfter { get; set; }
        public DateTime? CreatedBefore { get; set; }

        // Sorting
        public string SortBy { get; set; } = "CreatedAt";
        public string SortDirection { get; set; } = "desc"; // asc or desc
        public bool SortDescending => SortDirection.ToLower() == "desc";

        // Pagination aliases for compatibility
        public int PageNumber => Page;

        // Include related data
        public bool IncludeDivision { get; set; } = true;
        public bool IncludeCategory { get; set; } = true;
        public bool IncludeFirmNature { get; set; } = true;
        public bool IncludeDeleted { get; set; } = false;
    }

    public class PersonSearchResponse
    {
        public List<PersonResponse> Persons { get; set; } = new List<PersonResponse>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasNextPage => Page < TotalPages;
        public bool HasPreviousPage => Page > 1;
    }
}
