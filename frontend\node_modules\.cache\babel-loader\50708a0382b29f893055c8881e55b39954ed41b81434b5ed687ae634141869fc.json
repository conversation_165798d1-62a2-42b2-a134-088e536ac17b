{"ast": null, "code": "import { cubicBezier } from './cubic-bezier.mjs';\nimport { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\nconst backOut = cubicBezier(0.33, 1.53, 0.69, 0.99);\nconst backIn = reverseEasing(backOut);\nconst backInOut = mirrorEasing(backIn);\nexport { backIn, backInOut, backOut };", "map": {"version": 3, "names": ["cubicBezier", "mirrorEasing", "reverseEasing", "backOut", "backIn", "backInOut"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/easing/back.mjs"], "sourcesContent": ["import { cubicBezier } from './cubic-bezier.mjs';\nimport { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst backOut = cubicBezier(0.33, 1.53, 0.69, 0.99);\nconst backIn = reverseEasing(backOut);\nconst backInOut = mirrorEasing(backIn);\n\nexport { backIn, backInOut, backOut };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,aAAa,QAAQ,yBAAyB;AAEvD,MAAMC,OAAO,GAAGH,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACnD,MAAMI,MAAM,GAAGF,aAAa,CAACC,OAAO,CAAC;AACrC,MAAME,SAAS,GAAGJ,YAAY,CAACG,MAAM,CAAC;AAEtC,SAASA,MAAM,EAAEC,SAAS,EAAEF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}