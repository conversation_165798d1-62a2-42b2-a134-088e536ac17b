[{"C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\context\\AuthContext.js": "3", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Navbar.js": "4", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\ProtectedRoute.js": "5", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\CategoryManagement.js": "6", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Dashboard.js": "7", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\DivisionSetup.js": "8", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonsView.js": "9", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonManagement.js": "10", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportPersons.js": "11", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormBuilder.js": "12", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Login.js": "13", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Pagination.js": "14", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonList.js": "15", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\DynamicPersonForm.js": "16", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormSelectionView.js": "17", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FileUpload.js": "18", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportResults.js": "19", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FieldConfigModal.js": "20", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FieldMapping.js": "21", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormPreview.js": "22", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportProgress.js": "23", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\apiService.js": "24", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\formConfigService.js": "25", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\constants\\personConstants.js": "26", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\HierarchicalSelector.js": "27", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormField.js": "28", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\DivisionCategorySelection.js": "29", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\AllFormsModal.js": "30"}, {"size": 263, "mtime": 1753902733652, "results": "31", "hashOfConfig": "32"}, {"size": 2056, "mtime": 1753981799344, "results": "33", "hashOfConfig": "32"}, {"size": 2205, "mtime": 1753902733684, "results": "34", "hashOfConfig": "32"}, {"size": 2627, "mtime": 1753981799344, "results": "35", "hashOfConfig": "32"}, {"size": 480, "mtime": 1753902733668, "results": "36", "hashOfConfig": "32"}, {"size": 9566, "mtime": 1754038867445, "results": "37", "hashOfConfig": "32"}, {"size": 9249, "mtime": 1753902733668, "results": "38", "hashOfConfig": "32"}, {"size": 4195, "mtime": 1753902733668, "results": "39", "hashOfConfig": "32"}, {"size": 22150, "mtime": 1754039466316, "results": "40", "hashOfConfig": "32"}, {"size": 5838, "mtime": 1754017418375, "results": "41", "hashOfConfig": "32"}, {"size": 8156, "mtime": 1753982875438, "results": "42", "hashOfConfig": "32"}, {"size": 36208, "mtime": 1754038578995, "results": "43", "hashOfConfig": "32"}, {"size": 4164, "mtime": 1753902733668, "results": "44", "hashOfConfig": "32"}, {"size": 3257, "mtime": 1753902733668, "results": "45", "hashOfConfig": "32"}, {"size": 11009, "mtime": 1754016212788, "results": "46", "hashOfConfig": "32"}, {"size": 42079, "mtime": 1754037249481, "results": "47", "hashOfConfig": "32"}, {"size": 14452, "mtime": 1753954627628, "results": "48", "hashOfConfig": "32"}, {"size": 12054, "mtime": 1753963443321, "results": "49", "hashOfConfig": "32"}, {"size": 11255, "mtime": 1753981281797, "results": "50", "hashOfConfig": "32"}, {"size": 10100, "mtime": 1753909815406, "results": "51", "hashOfConfig": "32"}, {"size": 14987, "mtime": 1753963037736, "results": "52", "hashOfConfig": "32"}, {"size": 6753, "mtime": 1753910016943, "results": "53", "hashOfConfig": "32"}, {"size": 9504, "mtime": 1753963760972, "results": "54", "hashOfConfig": "32"}, {"size": 4502, "mtime": 1754036940595, "results": "55", "hashOfConfig": "32"}, {"size": 14697, "mtime": 1754012544766, "results": "56", "hashOfConfig": "32"}, {"size": 12511, "mtime": 1753958855389, "results": "57", "hashOfConfig": "32"}, {"size": 9012, "mtime": 1754037406672, "results": "58", "hashOfConfig": "32"}, {"size": 5304, "mtime": 1753944960474, "results": "59", "hashOfConfig": "32"}, {"size": 5024, "mtime": 1754039274810, "results": "60", "hashOfConfig": "32"}, {"size": 9208, "mtime": 1754012719848, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ibwsxa", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\context\\AuthContext.js", ["152"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Navbar.js", ["153"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\CategoryManagement.js", ["154"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\DivisionSetup.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonsView.js", ["155", "156", "157", "158", "159", "160"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonManagement.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportPersons.js", ["161", "162"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormBuilder.js", ["163"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Pagination.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonList.js", ["164"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\DynamicPersonForm.js", ["165", "166", "167", "168", "169", "170", "171", "172", "173"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormSelectionView.js", ["174", "175", "176"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FileUpload.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportResults.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FieldConfigModal.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FieldMapping.js", ["177", "178"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormPreview.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportProgress.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\apiService.js", ["179"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\formConfigService.js", ["180", "181", "182", "183"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\constants\\personConstants.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\HierarchicalSelector.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormField.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\DivisionCategorySelection.js", ["184", "185"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\AllFormsModal.js", ["186", "187"], [], {"ruleId": "188", "severity": 1, "message": "189", "line": 26, "column": 6, "nodeType": "190", "endLine": 26, "endColumn": 13, "suggestions": "191"}, {"ruleId": "192", "severity": 1, "message": "193", "line": 4, "column": 10, "nodeType": "194", "messageId": "195", "endLine": 4, "endColumn": 16}, {"ruleId": "192", "severity": 1, "message": "196", "line": 11, "column": 10, "nodeType": "194", "messageId": "195", "endLine": 11, "endColumn": 21}, {"ruleId": "192", "severity": 1, "message": "197", "line": 6, "column": 3, "nodeType": "194", "messageId": "195", "endLine": 6, "endColumn": 13}, {"ruleId": "192", "severity": 1, "message": "198", "line": 15, "column": 3, "nodeType": "194", "messageId": "195", "endLine": 15, "endColumn": 13}, {"ruleId": "192", "severity": 1, "message": "199", "line": 27, "column": 10, "nodeType": "194", "messageId": "195", "endLine": 27, "endColumn": 20}, {"ruleId": "188", "severity": 1, "message": "200", "line": 63, "column": 6, "nodeType": "190", "endLine": 63, "endColumn": 57, "suggestions": "201"}, {"ruleId": "192", "severity": 1, "message": "202", "line": 210, "column": 9, "nodeType": "194", "messageId": "195", "endLine": 210, "endColumn": 21}, {"ruleId": "192", "severity": 1, "message": "203", "line": 280, "column": 9, "nodeType": "194", "messageId": "195", "endLine": 280, "endColumn": 23}, {"ruleId": "192", "severity": 1, "message": "204", "line": 15, "column": 10, "nodeType": "194", "messageId": "195", "endLine": 15, "endColumn": 22}, {"ruleId": "192", "severity": 1, "message": "205", "line": 24, "column": 10, "nodeType": "194", "messageId": "195", "endLine": 24, "endColumn": 17}, {"ruleId": "188", "severity": 1, "message": "206", "line": 44, "column": 6, "nodeType": "190", "endLine": 44, "endColumn": 21, "suggestions": "207"}, {"ruleId": "188", "severity": 1, "message": "200", "line": 27, "column": 6, "nodeType": "190", "endLine": 27, "endColumn": 44, "suggestions": "208"}, {"ruleId": "192", "severity": 1, "message": "209", "line": 2, "column": 10, "nodeType": "194", "messageId": "195", "endLine": 2, "endColumn": 28}, {"ruleId": "192", "severity": 1, "message": "210", "line": 2, "column": 30, "nodeType": "194", "messageId": "195", "endLine": 2, "endColumn": 42}, {"ruleId": "192", "severity": 1, "message": "211", "line": 2, "column": 44, "nodeType": "194", "messageId": "195", "endLine": 2, "endColumn": 64}, {"ruleId": "188", "severity": 1, "message": "212", "line": 136, "column": 6, "nodeType": "190", "endLine": 136, "endColumn": 24, "suggestions": "213"}, {"ruleId": "188", "severity": 1, "message": "214", "line": 143, "column": 6, "nodeType": "190", "endLine": 143, "endColumn": 26, "suggestions": "215"}, {"ruleId": "216", "severity": 1, "message": "217", "line": 364, "column": 35, "nodeType": "218", "messageId": "219", "endLine": 364, "endColumn": 36, "suggestions": "220"}, {"ruleId": "221", "severity": 1, "message": "222", "line": 404, "column": 9, "nodeType": "223", "messageId": "224", "endLine": 435, "endColumn": 10}, {"ruleId": "216", "severity": 1, "message": "217", "line": 412, "column": 40, "nodeType": "218", "messageId": "219", "endLine": 412, "endColumn": 41, "suggestions": "225"}, {"ruleId": "216", "severity": 1, "message": "217", "line": 529, "column": 37, "nodeType": "218", "messageId": "219", "endLine": 529, "endColumn": 38, "suggestions": "226"}, {"ruleId": "192", "severity": 1, "message": "227", "line": 9, "column": 10, "nodeType": "194", "messageId": "195", "endLine": 9, "endColumn": 23}, {"ruleId": "188", "severity": 1, "message": "228", "line": 28, "column": 6, "nodeType": "190", "endLine": 28, "endColumn": 8, "suggestions": "229"}, {"ruleId": "192", "severity": 1, "message": "230", "line": 163, "column": 9, "nodeType": "194", "messageId": "195", "endLine": 163, "endColumn": 31}, {"ruleId": "188", "severity": 1, "message": "231", "line": 22, "column": 6, "nodeType": "190", "endLine": 22, "endColumn": 19, "suggestions": "232"}, {"ruleId": "188", "severity": 1, "message": "233", "line": 27, "column": 6, "nodeType": "190", "endLine": 27, "endColumn": 21, "suggestions": "234"}, {"ruleId": "235", "severity": 1, "message": "236", "line": 192, "column": 1, "nodeType": "237", "endLine": 192, "endColumn": 33}, {"ruleId": "238", "severity": 1, "message": "239", "line": 36, "column": 9, "nodeType": "240", "messageId": "241", "endLine": 36, "endColumn": 15}, {"ruleId": "242", "severity": 1, "message": "243", "line": 467, "column": 3, "nodeType": "244", "messageId": "241", "endLine": 476, "endColumn": 4}, {"ruleId": "242", "severity": 1, "message": "245", "line": 479, "column": 3, "nodeType": "244", "messageId": "241", "endLine": 495, "endColumn": 4}, {"ruleId": "235", "severity": 1, "message": "236", "line": 498, "column": 1, "nodeType": "237", "endLine": 498, "endColumn": 40}, {"ruleId": "192", "severity": 1, "message": "246", "line": 1, "column": 27, "nodeType": "194", "messageId": "195", "endLine": 1, "endColumn": 36}, {"ruleId": "192", "severity": 1, "message": "247", "line": 11, "column": 21, "nodeType": "194", "messageId": "195", "endLine": 11, "endColumn": 33}, {"ruleId": "192", "severity": 1, "message": "248", "line": 8, "column": 10, "nodeType": "194", "messageId": "195", "endLine": 8, "endColumn": 19}, {"ruleId": "192", "severity": 1, "message": "249", "line": 9, "column": 10, "nodeType": "194", "messageId": "195", "endLine": 9, "endColumn": 20}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'validateToken'. Either include it or remove the dependency array.", "ArrayExpression", ["250"], "no-unused-vars", "'FiHome' is defined but never used.", "Identifier", "unusedVar", "'firmNatures' is assigned a value but never used.", "'FiDownload' is defined but never used.", "'FiBuilding' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadPersons'. Either include it or remove the dependency array.", ["251"], "'handleExport' is assigned a value but never used.", "'getGenderLabel' is assigned a value but never used.", "'fieldMapping' is assigned a value but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadInitialConfig'. Either include it or remove the dependency array.", ["252"], ["253"], "'PersonNatureLabels' is defined but never used.", "'GenderLabels' is defined but never used.", "'WorkingProfileLabels' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleCategorySelection'. Either include it or remove the dependency array.", ["254"], "React Hook useEffect has a missing dependency: 'handleFirmNatureSelection'. Either include it or remove the dependency array.", ["255"], "no-useless-escape", "Unnecessary escape character: \\-.", "Literal", "unnecessaryEscape", ["256", "257"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", ["258", "259"], ["260", "261"], "'subCategories' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadAvailableForms'. Either include it or remove the dependency array.", ["262"], "'getFormsForSubCategory' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'autoMapFields', 'updateRequiredFieldsStatus', and 'updateUnmappedHeaders'. Either include them or remove the dependency array.", ["263"], "React Hook useEffect has missing dependencies: 'mapping' and 'updateRequiredFieldsStatus'. Either include them or remove the dependency array.", ["264"], "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "no-dupe-keys", "Duplicate key 'fields'.", "ObjectExpression", "unexpected", "no-dupe-class-members", "Duplicate name 'deleteFormConfig'.", "MethodDefinition", "Duplicate name 'clearAllFormConfigs'.", "'useEffect' is defined but never used.", "'setIsLoading' is assigned a value but never used.", "'divisions' is assigned a value but never used.", "'categories' is assigned a value but never used.", {"desc": "265", "fix": "266"}, {"desc": "267", "fix": "268"}, {"desc": "269", "fix": "270"}, {"desc": "271", "fix": "272"}, {"desc": "273", "fix": "274"}, {"desc": "275", "fix": "276"}, {"messageId": "277", "fix": "278", "desc": "279"}, {"messageId": "280", "fix": "281", "desc": "282"}, {"messageId": "277", "fix": "283", "desc": "279"}, {"messageId": "280", "fix": "284", "desc": "282"}, {"messageId": "277", "fix": "285", "desc": "279"}, {"messageId": "280", "fix": "286", "desc": "282"}, {"desc": "287", "fix": "288"}, {"desc": "289", "fix": "290"}, {"desc": "291", "fix": "292"}, "Update the dependencies array to be: [token, validateToken]", {"range": "293", "text": "294"}, "Update the dependencies array to be: [currentPage, pageSize, filters, sortBy, sortOrder, loadPersons]", {"range": "295", "text": "296"}, "Update the dependencies array to be: [initialConfig, loadInitialConfig]", {"range": "297", "text": "298"}, "Update the dependencies array to be: [loadPersons, pagination.page, pagination.pageSize]", {"range": "299", "text": "300"}, "Update the dependencies array to be: [handleCategorySelection, selectedCategory]", {"range": "301", "text": "302"}, "Update the dependencies array to be: [handleFirmNatureSelection, selectedFirmNature]", {"range": "303", "text": "304"}, "removeEscape", {"range": "305", "text": "306"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "307", "text": "308"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "309", "text": "306"}, {"range": "310", "text": "308"}, {"range": "311", "text": "306"}, {"range": "312", "text": "308"}, "Update the dependencies array to be: [loadAvailableForms]", {"range": "313", "text": "314"}, "Update the dependencies array to be: [autoMapFields, fileHeaders, updateRequiredFieldsStatus, updateUnmappedHeaders]", {"range": "315", "text": "316"}, "Update the dependencies array to be: [defaultValues, mapping, updateRequiredFieldsStatus]", {"range": "317", "text": "318"}, [747, 754], "[token, validateToken]", [1590, 1641], "[currentPage, pageSize, filters, sortBy, sortOrder, loadPersons]", [1872, 1887], "[initialConfig, loadInitialConfig]", [724, 762], "[loadPersons, pagination.page, pagination.pageSize]", [4219, 4237], "[handleCategorySelection, selectedCategory]", [4406, 4426], "[handleFirmNatureSelection, selectedFirmNature]", [11908, 11909], "", [11908, 11908], "\\", [13619, 13620], [13619, 13619], [19074, 19075], [19074, 19074], [1121, 1123], "[loadAvailableForms]", [961, 974], "[autoMapFields, fileHeaders, updateRequiredFieldsStatus, updateUnmappedHeaders]", [1088, 1103], "[defaultValues, mapping, updateRequiredFieldsStatus]"]