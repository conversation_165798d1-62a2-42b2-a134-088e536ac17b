{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\FormBuilder.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FieldConfigModal from './FieldConfigModal';\nimport FormPreview from './FormPreview';\nimport './FormBuilder.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FormBuilder = ({\n  onSave,\n  onCancel,\n  initialConfig = null\n}) => {\n  _s();\n  const [selectedHierarchy, setSelectedHierarchy] = useState({});\n  const [formName, setFormName] = useState('');\n  const [formDescription, setFormDescription] = useState('');\n  const [availableFields, setAvailableFields] = useState([]);\n  const [selectedFields, setSelectedFields] = useState([]);\n  const [currentSection, setCurrentSection] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n  const [showFieldConfig, setShowFieldConfig] = useState(false);\n  const [configField, setConfigField] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [saving, setSaving] = useState(false);\n  const [savedForms, setSavedForms] = useState([]);\n  const [showSavedForms, setShowSavedForms] = useState(false);\n\n  // State for dropdown data\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [firmNatures, setFirmNatures] = useState([]);\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    firmNatures: false\n  });\n  const [existingFormInfo, setExistingFormInfo] = useState(null);\n  const [isEditMode, setIsEditMode] = useState(false);\n  const [editingFormId, setEditingFormId] = useState(null);\n  useEffect(() => {\n    initializeFields();\n    loadSavedForms();\n    if (initialConfig) {\n      loadInitialConfig(initialConfig);\n    }\n  }, [initialConfig]);\n  const initializeFields = () => {\n    const allFields = getAllPersonFields();\n    setAvailableFields(allFields);\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = fields => {\n    const seen = new Set();\n    const deduplicated = [];\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      }\n    });\n    return deduplicated;\n  };\n  const loadSavedForms = () => {\n    const forms = formConfigService.getAllFormConfigs();\n    setSavedForms(forms);\n  };\n  const loadInitialConfig = config => {\n    setFormName(config.name || '');\n    setFormDescription(config.description || '');\n\n    // Deduplicate fields when loading initial config\n    const fields = config.fields || [];\n    const deduplicatedFields = deduplicateFields(fields);\n    setSelectedFields(deduplicatedFields);\n\n    // Set edit mode when loading existing form\n    setIsEditMode(true);\n    setEditingFormId(config.id || config.key);\n    if (config.type === 'division' && config.associatedId) {\n      // Load division info for hierarchy selector\n      setSelectedHierarchy({\n        divisionId: config.associatedId\n      });\n    } else if (config.type === 'category' && config.associatedId) {\n      // Load category info for hierarchy selector\n      setSelectedHierarchy({\n        categoryId: config.associatedId\n      });\n    } else if (config.type === 'firmnature' && config.associatedId) {\n      // Load firm nature info for hierarchy selector\n      setSelectedHierarchy({\n        firmNatureId: config.associatedId\n      });\n    }\n\n    // If hierarchy is stored in config, use that\n    if (config.hierarchy) {\n      setSelectedHierarchy(config.hierarchy);\n    }\n  };\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n  }, []);\n\n  // Validate form creation rules when hierarchy changes\n  useEffect(() => {\n    if (selectedHierarchy.categoryId) {\n      // Skip validation in edit mode since we're updating an existing form\n      if (!isEditMode) {\n        const formValidation = formConfigService.validateFormCreation(selectedHierarchy.categoryId, selectedHierarchy.firmNatureId);\n        if (!formValidation.isValid) {\n          setErrors(prev => ({\n            ...prev,\n            formCreation: formValidation.errors.join('. ')\n          }));\n        } else {\n          setErrors(prev => {\n            const newErrors = {\n              ...prev\n            };\n            delete newErrors.formCreation;\n            return newErrors;\n          });\n        }\n      } else {\n        // In edit mode, clear any form creation errors\n        setErrors(prev => {\n          const newErrors = {\n            ...prev\n          };\n          delete newErrors.formCreation;\n          return newErrors;\n        });\n      }\n      const formInfo = formConfigService.getExistingFormInfo(selectedHierarchy.categoryId, selectedHierarchy.firmNatureId);\n      setExistingFormInfo(formInfo);\n    } else {\n      setExistingFormInfo(null);\n      setErrors(prev => {\n        const newErrors = {\n          ...prev\n        };\n        delete newErrors.formCreation;\n        return newErrors;\n      });\n    }\n  }, [selectedHierarchy.categoryId, selectedHierarchy.subCategoryId, isEditMode]);\n  const loadDivisions = async () => {\n    setLoading(prev => ({\n      ...prev,\n      divisions: true\n    }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        divisions: false\n      }));\n    }\n  };\n  const loadCategories = async divisionId => {\n    if (!divisionId) {\n      setCategories([]);\n      setFirmNatures([]);\n      return;\n    }\n    setLoading(prev => ({\n      ...prev,\n      categories: true\n    }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        categories: false\n      }));\n    }\n  };\n  const loadFirmNatures = async categoryId => {\n    if (!categoryId) {\n      setFirmNatures([]);\n      return;\n    }\n    setLoading(prev => ({\n      ...prev,\n      firmNatures: true\n    }));\n    try {\n      const response = await apiService.getFirmNaturesByCategory(categoryId);\n      setFirmNatures(response.data || []);\n    } catch (error) {\n      console.error('Error loading firm natures:', error);\n      setFirmNatures([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        firmNatures: false\n      }));\n    }\n  };\n  const handleDivisionChange = e => {\n    const divisionId = e.target.value;\n    const division = divisions.find(d => d.id === parseInt(divisionId));\n    setSelectedHierarchy({\n      divisionId: divisionId || null,\n      categoryId: null,\n      firmNatureId: null,\n      division: division || null,\n      category: null,\n      firmNature: null\n    });\n    setCategories([]);\n    setFirmNatures([]);\n    if (divisionId) {\n      loadCategories(divisionId);\n    }\n  };\n  const handleCategoryChange = e => {\n    const categoryId = e.target.value;\n    const category = categories.find(c => c.id === parseInt(categoryId));\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      categoryId: categoryId || null,\n      firmNatureId: null,\n      category: category || null,\n      firmNature: null\n    }));\n    setFirmNatures([]);\n    if (categoryId) {\n      loadFirmNatures(categoryId);\n    }\n  };\n  const handleFirmNatureChange = e => {\n    const firmNatureId = e.target.value;\n    const firmNature = firmNatures.find(fn => fn.id === parseInt(firmNatureId));\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      firmNatureId: firmNatureId || null,\n      firmNature: firmNature || null\n    }));\n  };\n  const handleFieldToggle = field => {\n    const isSelected = selectedFields.some(f => f.key === field.key);\n    if (isSelected) {\n      // Remove all instances of this field key (in case there are duplicates)\n      setSelectedFields(prev => prev.filter(f => f.key !== field.key));\n    } else {\n      // Add field only if it doesn't already exist\n      setSelectedFields(prev => {\n        const exists = prev.some(f => f.key === field.key);\n        if (exists) {\n          return prev;\n        }\n        return [...prev, {\n          ...field\n        }];\n      });\n    }\n  };\n  const handleFieldConfig = field => {\n    setConfigField(field);\n    setShowFieldConfig(true);\n  };\n  const handleFieldConfigSave = updatedField => {\n    setSelectedFields(prev => prev.map(f => f.key === updatedField.key ? updatedField : f));\n    setShowFieldConfig(false);\n    setConfigField(null);\n  };\n  const handleFieldMove = (fromIndex, toIndex) => {\n    const newFields = [...selectedFields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n    setSelectedFields(newFields);\n  };\n  const handleSelectAllFields = () => {\n    // Get all filtered fields (respects current section and search)\n    const fieldsToAdd = filteredFields.filter(field => !selectedFields.some(selected => selected.key === field.key));\n    setSelectedFields(prev => [...prev, ...fieldsToAdd]);\n  };\n  const handleDeselectAllFields = () => {\n    // Remove all filtered fields from selection\n    const filteredFieldKeys = filteredFields.map(field => field.key);\n    setSelectedFields(prev => prev.filter(field => !filteredFieldKeys.includes(field.key)));\n  };\n  const handleSelectAllAvailableFields = () => {\n    // Select all available fields regardless of current filter\n    const fieldsToAdd = availableFields.filter(field => !selectedFields.some(selected => selected.key === field.key));\n    setSelectedFields(prev => [...prev, ...fieldsToAdd]);\n  };\n  const handleClearAllFields = () => {\n    setSelectedFields([]);\n  };\n  const handleSave = async () => {\n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n    setSaving(true);\n    try {\n      // Ensure complete hierarchy information is included\n      const completeHierarchy = {\n        ...selectedHierarchy,\n        // Include division info (required)\n        divisionId: selectedHierarchy.divisionId,\n        division: selectedHierarchy.division,\n        // Include category info (required)\n        categoryId: selectedHierarchy.categoryId,\n        category: selectedHierarchy.category,\n        // Include subcategory info if selected (optional)\n        subCategoryId: selectedHierarchy.subCategoryId || null,\n        subCategory: selectedHierarchy.subCategory || null\n      };\n      const config = {\n        name: formName,\n        description: formDescription,\n        fields: selectedFields,\n        hierarchy: completeHierarchy,\n        settings: {\n          showSections: true,\n          allowConditionalFields: true,\n          validateOnChange: true\n        }\n      };\n      let savedConfig;\n      if (isEditMode) {\n        // In edit mode, update the existing form\n        // Preserve the original type and associatedId from the form being edited\n        const originalForm = formConfigService.getAllFormConfigs().find(f => f.id === editingFormId || f.key === editingFormId);\n        if (originalForm) {\n          // Update the existing form with new data\n          config.id = originalForm.id;\n          config.key = originalForm.key;\n          config.type = originalForm.type;\n          config.associatedId = originalForm.associatedId;\n          config.createdAt = originalForm.createdAt; // Preserve creation date\n          config.updatedAt = new Date().toISOString(); // Update modification date\n\n          savedConfig = formConfigService.saveFormConfig(originalForm.type, originalForm.associatedId, config);\n        } else {\n          throw new Error('Original form not found for editing');\n        }\n      } else {\n        // Create new form with the most specific level selected, but include complete hierarchy\n        if (selectedHierarchy.subCategoryId) {\n          savedConfig = formConfigService.saveFormConfig('subcategory', selectedHierarchy.subCategoryId, config);\n        } else if (selectedHierarchy.categoryId) {\n          savedConfig = formConfigService.saveFormConfig('category', selectedHierarchy.categoryId, config);\n        } else {\n          throw new Error('Please select both division and category');\n        }\n      }\n\n      // Reload saved forms list\n      loadSavedForms();\n      if (onSave) {\n        onSave(savedConfig);\n      } else {\n        // Show success message for standalone usage\n        const message = isEditMode ? 'Form updated successfully!' : 'Form configuration saved successfully!';\n        alert(message);\n        // Reset form\n        setFormName('');\n        setFormDescription('');\n        setSelectedFields([]);\n        setSelectedHierarchy({});\n        setIsEditMode(false);\n        setEditingFormId(null);\n      }\n    } catch (error) {\n      console.error('Error saving form:', error);\n      setErrors({\n        general: error.message\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n  const validateForm = () => {\n    const errors = {};\n    if (!formName.trim()) {\n      errors.formName = 'Form name is required';\n    }\n\n    // Division and category are required, subcategory is optional\n    if (!selectedHierarchy.divisionId) {\n      errors.hierarchy = 'Please select a division';\n    } else if (!selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Please select a category';\n    } else if (selectedHierarchy.subCategoryId && !selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Cannot select subcategory without selecting category';\n    }\n\n    // Validate form creation rules (skip in edit mode)\n    if (selectedHierarchy.categoryId && !isEditMode) {\n      const formValidation = formConfigService.validateFormCreation(selectedHierarchy.categoryId, selectedHierarchy.subCategoryId);\n      if (!formValidation.isValid) {\n        errors.formCreation = formValidation.errors.join('. ');\n      }\n    }\n    if (selectedFields.length === 0) {\n      errors.fields = 'Please select at least one field';\n    }\n\n    // Check required fields\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = selectedFields.map(f => f.key);\n    const missingRequired = requiredFields.filter(rf => !selectedFieldKeys.includes(rf));\n    if (missingRequired.length > 0) {\n      errors.requiredFields = `Required fields missing: ${missingRequired.join(', ')}`;\n    }\n    return {\n      isValid: Object.keys(errors).length === 0,\n      errors\n    };\n  };\n  const getFilteredFields = () => {\n    let filtered = availableFields;\n\n    // Filter by section\n    if (currentSection !== 'all') {\n      filtered = filtered.filter(field => field.section === currentSection);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(field => field.label.toLowerCase().includes(searchTerm.toLowerCase()) || field.key.toLowerCase().includes(searchTerm.toLowerCase()));\n    }\n    return filtered;\n  };\n  const getSectionCounts = () => {\n    const counts = {\n      all: availableFields.length\n    };\n    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {\n      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;\n    });\n    return counts;\n  };\n  const sectionCounts = getSectionCounts();\n  const filteredFields = getFilteredFields();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-builder\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-builder-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Form Builder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => setShowPreview(true),\n          className: \"btn btn-secondary\",\n          disabled: selectedFields.length === 0,\n          children: \"Preview Form\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleSave,\n          className: \"btn btn-primary\",\n          disabled: saving || errors.formCreation,\n          children: saving ? isEditMode ? 'Updating...' : 'Saving...' : isEditMode ? 'Update Form' : 'Save Form'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this), onCancel && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 9\n    }, this), Object.keys(errors).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"validation-errors\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Please fix the following issues:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: Object.entries(errors).map(([key, message]) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: message\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 533,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-builder-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"config-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Saved Forms (\", savedForms.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn-link\",\n              onClick: () => setShowSavedForms(!showSavedForms),\n              children: showSavedForms ? 'Hide' : 'Show'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 13\n          }, this), showSavedForms && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"saved-forms-list\",\n            children: savedForms.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-forms-message\",\n              children: \"No saved forms yet. Create your first form below!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 19\n            }, this) : savedForms.map(form => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"saved-form-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-item-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: form.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"form-type-badge\",\n                  children: form.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-description\",\n                children: form.description || 'No description'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [form.summary.fieldCount, \" fields\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Updated \", new Date(form.updatedAt).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn-small btn-outline\",\n                  onClick: () => loadInitialConfig(form),\n                  children: \"Load\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn-small btn-danger\",\n                  onClick: () => {\n                    if (window.confirm('Are you sure you want to delete this form?')) {\n                      formConfigService.deleteFormConfig(form.type, form.associatedId);\n                      loadSavedForms();\n                    }\n                  },\n                  children: \"Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 23\n              }, this)]\n            }, form.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Form Configuration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Form Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formName,\n              onChange: e => setFormName(e.target.value),\n              placeholder: \"Enter form name\",\n              className: errors.formName ? 'error' : ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 15\n            }, this), errors.formName && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"error-message\",\n              children: errors.formName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: formDescription,\n              onChange: e => setFormDescription(e.target.value),\n              placeholder: \"Enter form description\",\n              rows: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Selected Fields (\", selectedFields.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 13\n          }, this), errors.fields && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.fields\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 31\n          }, this), errors.requiredFields && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.requiredFields\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 39\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-fields\",\n            children: selectedFields.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: \"No fields selected. Choose fields from the available fields panel.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 17\n            }, this) : selectedFields.map((field, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"selected-field\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"field-label\",\n                  children: field.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"field-type\",\n                  children: field.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 23\n                }, this), field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"required-badge\",\n                  children: \"Required\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldConfig(field),\n                  className: \"btn-icon\",\n                  title: \"Configure field\",\n                  children: \"\\u2699\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldMove(index, Math.max(0, index - 1)),\n                  className: \"btn-icon\",\n                  disabled: index === 0,\n                  title: \"Move up\",\n                  children: \"\\u2191\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1)),\n                  className: \"btn-icon\",\n                  disabled: index === selectedFields.length - 1,\n                  title: \"Move down\",\n                  children: \"\\u2193\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldToggle(field),\n                  className: \"btn-icon remove\",\n                  title: \"Remove field\",\n                  children: \"\\u2715\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 21\n              }, this)]\n            }, field.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fields-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Available Fields\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 697,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hierarchy-selection\",\n          style: {\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #e1e5e9',\n            borderRadius: '8px',\n            padding: '1.5rem',\n            margin: '1rem 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 1rem 0',\n              color: '#495057'\n            },\n            children: \"Associate with Division & Category (Required) / SubCategory (Optional) *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6c757d',\n              marginBottom: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8f9fa',\n              borderRadius: '4px',\n              border: '1px solid #e9ecef'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Form Creation Rules:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              style: {\n                margin: '0.5rem 0 0 1rem',\n                paddingLeft: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"If you create a form for a category, you cannot create forms for its subcategories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Each subcategory can have only one form\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"If subcategories already have forms, you cannot create a form for the parent category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hierarchy-dropdowns\",\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"Division *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.divisionId || '',\n                onChange: handleDivisionChange,\n                disabled: loading.divisions,\n                className: errors.divisionId ? 'error' : '',\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Division\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 758,\n                  columnNumber: 19\n                }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: division.id,\n                  children: division.name\n                }, division.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 760,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 17\n              }, this), errors.divisionId && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#dc3545',\n                  fontSize: '0.875rem',\n                  marginTop: '0.25rem'\n                },\n                children: errors.divisionId\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"Category *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.categoryId || '',\n                onChange: handleCategoryChange,\n                disabled: !selectedHierarchy.divisionId || loading.categories,\n                className: errors.categoryId ? 'error' : '',\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: !selectedHierarchy.divisionId ? 'Select Division first' : 'Select Category'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 17\n              }, this), errors.categoryId && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#dc3545',\n                  fontSize: '0.875rem',\n                  marginTop: '0.25rem'\n                },\n                children: errors.categoryId\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"SubCategory (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.subCategoryId || '',\n                onChange: handleSubCategoryChange,\n                disabled: !selectedHierarchy.categoryId || loading.subCategories,\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: !selectedHierarchy.categoryId ? 'Select Category first' : 'Select SubCategory (Optional)'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 19\n                }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: subCategory.id,\n                  children: subCategory.name\n                }, subCategory.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 839,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 13\n          }, this), errors.hierarchy && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#dc3545',\n              fontSize: '0.875rem',\n              marginTop: '0.5rem'\n            },\n            children: errors.hierarchy\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 15\n          }, this), errors.formCreation && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#dc3545',\n              fontSize: '0.875rem',\n              marginTop: '0.5rem',\n              fontWeight: 'bold'\n            },\n            children: [\"\\u26A0\\uFE0F \", errors.formCreation]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 15\n          }, this), existingFormInfo && existingFormInfo.existingForms.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#fff3cd',\n              border: '1px solid #ffeaa7',\n              borderRadius: '4px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\uD83D\\uDCCB Existing Forms:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 17\n            }, this), existingFormInfo.existingForms.map((form, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [form.type === 'category' ? 'Category' : 'SubCategory', \" Form:\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 871,\n                columnNumber: 21\n              }, this), \" \", form.name, form.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#6c757d'\n                },\n                children: form.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 42\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 870,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 860,\n            columnNumber: 15\n          }, this), existingFormInfo && existingFormInfo.subCategoriesWithForms.length > 0 && !selectedHierarchy.subCategoryId && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8d7da',\n              border: '1px solid #f5c6cb',\n              borderRadius: '4px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u26A0\\uFE0F Warning:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 17\n            }, this), \" This category has \", existingFormInfo.subCategoriesWithForms.length, \" subcategory form(s). You cannot create a form for this category.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 880,\n            columnNumber: 15\n          }, this), selectedHierarchy.categoryId && !errors.formCreation && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#d4edda',\n              border: '1px solid #c3e6cb',\n              borderRadius: '4px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u2705 Valid Selection:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 903,\n              columnNumber: 17\n            }, this), \" You can create a form for this \", selectedHierarchy.subCategoryId ? 'subcategory' : 'category', \".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 895,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-controls\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search fields...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 909,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-tabs\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setCurrentSection('all'),\n            className: `section-tab ${currentSection === 'all' ? 'active' : ''}`,\n            children: [\"All (\", sectionCounts.all, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 921,\n            columnNumber: 13\n          }, this), Object.entries(PersonFieldDefinitions).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setCurrentSection(sectionKey),\n            className: `section-tab ${currentSection === sectionKey ? 'active' : ''}`,\n            children: [section.title, \" (\", sectionCounts[sectionKey], \")\"]\n          }, sectionKey, true, {\n            fileName: _jsxFileName,\n            lineNumber: 929,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 920,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bulk-selection-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bulk-controls-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bulk-controls-label\",\n              children: \"Quick Actions:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 943,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleSelectAllFields,\n              className: \"btn btn-sm btn-outline\",\n              title: `Select all ${filteredFields.length} filtered fields`,\n              children: [\"Select Filtered (\", filteredFields.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 944,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleDeselectAllFields,\n              className: \"btn btn-sm btn-outline\",\n              title: \"Deselect all filtered fields\",\n              children: \"Deselect Filtered\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleSelectAllAvailableFields,\n              className: \"btn btn-sm btn-primary\",\n              title: `Select all ${availableFields.length} available fields`,\n              children: [\"Select All (\", availableFields.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleClearAllFields,\n              className: \"btn btn-sm btn-outline btn-danger\",\n              title: \"Clear all selected fields\",\n              disabled: selectedFields.length === 0,\n              children: \"Clear All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 942,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selection-summary\",\n            children: [selectedFields.length, \" of \", availableFields.length, \" fields selected\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 978,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 941,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-list\",\n          children: filteredFields.map(field => {\n            const isSelected = selectedFields.some(f => f.key === field.key);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `field-item ${isSelected ? 'selected' : ''}`,\n              onClick: () => handleFieldToggle(field),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-checkbox\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: isSelected,\n                  onChange: () => handleFieldToggle(field)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 994,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 993,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"field-name\",\n                  children: field.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1001,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"field-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"field-type\",\n                    children: field.type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1003,\n                    columnNumber: 23\n                  }, this), field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"required-badge\",\n                    children: \"Required\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1004,\n                    columnNumber: 42\n                  }, this), field.conditional && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"conditional-badge\",\n                    children: \"Conditional\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1005,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1002,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1000,\n                columnNumber: 19\n              }, this)]\n            }, field.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 988,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 984,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 543,\n      columnNumber: 7\n    }, this), showFieldConfig && configField && /*#__PURE__*/_jsxDEV(FieldConfigModal, {\n      field: configField,\n      onSave: handleFieldConfigSave,\n      onCancel: () => {\n        setShowFieldConfig(false);\n        setConfigField(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1017,\n      columnNumber: 9\n    }, this), showPreview && /*#__PURE__*/_jsxDEV(FormPreview, {\n      fields: selectedFields,\n      formName: formName,\n      onClose: () => setShowPreview(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1028,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 495,\n    columnNumber: 5\n  }, this);\n};\n_s(FormBuilder, \"y3bzwENh58K6Al/BOkHOsxoa9Ss=\");\n_c = FormBuilder;\nexport default FormBuilder;\nvar _c;\n$RefreshReg$(_c, \"FormBuilder\");", "map": {"version": 3, "names": ["useState", "useEffect", "PersonFieldDefinitions", "<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>s", "formConfigService", "apiService", "FieldConfigModal", "FormPreview", "jsxDEV", "_jsxDEV", "FormBuilder", "onSave", "onCancel", "initialConfig", "_s", "selectedHierarchy", "setSelectedHierarchy", "formName", "setFormName", "formDescription", "setFormDescription", "availableFields", "setAvailableFields", "<PERSON><PERSON><PERSON>s", "setSelectedFields", "currentSection", "setCurrentSection", "searchTerm", "setSearchTerm", "showPreview", "setShowPreview", "showFieldConfig", "setShowFieldConfig", "config<PERSON><PERSON>", "setConfigField", "errors", "setErrors", "saving", "setSaving", "savedForms", "setSavedForms", "showSavedForms", "setShowSavedForms", "divisions", "setDivisions", "categories", "setCategories", "firmNatures", "setFirmNatures", "loading", "setLoading", "existingFormInfo", "setExistingFormInfo", "isEditMode", "setIsEditMode", "editingFormId", "setEditingFormId", "initializeFields", "loadSavedForms", "loadInitialConfig", "allFields", "deduplicateFields", "fields", "seen", "Set", "deduplicated", "for<PERSON>ach", "field", "has", "key", "add", "push", "forms", "getAllFormConfigs", "config", "name", "description", "deduplicatedFields", "id", "type", "associatedId", "divisionId", "categoryId", "firmNatureId", "hierarchy", "loadDivisions", "formValidation", "validateFormCreation", "<PERSON><PERSON><PERSON><PERSON>", "prev", "formCreation", "join", "newErrors", "formInfo", "getExistingFormInfo", "subCategoryId", "response", "getDivisions", "data", "error", "console", "loadCategories", "getCategoriesByDivision", "loadFirmNatures", "getFirmNaturesByCategory", "handleDivisionChange", "e", "target", "value", "division", "find", "d", "parseInt", "category", "firmNature", "handleCategoryChange", "c", "handleFirmNatureChange", "fn", "handleFieldToggle", "isSelected", "some", "f", "filter", "exists", "handleFieldConfig", "handleFieldConfigSave", "updatedField", "map", "handleFieldMove", "fromIndex", "toIndex", "new<PERSON>ields", "movedField", "splice", "handleSelectAllFields", "fieldsToAdd", "filteredFields", "selected", "handleDeselectAllFields", "filteredFieldKeys", "includes", "handleSelectAllAvailableFields", "handleClearAllFields", "handleSave", "validation", "validateForm", "completeHierarchy", "subCategory", "settings", "showSections", "allowConditionalFields", "validateOnChange", "savedConfig", "originalForm", "createdAt", "updatedAt", "Date", "toISOString", "saveFormConfig", "Error", "message", "alert", "general", "trim", "length", "requiredFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingRequired", "rf", "Object", "keys", "getFilteredFields", "filtered", "section", "label", "toLowerCase", "getSectionCounts", "counts", "all", "sectionKey", "sectionCounts", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "entries", "form", "summary", "fieldCount", "toLocaleDateString", "window", "confirm", "deleteFormConfig", "onChange", "placeholder", "rows", "index", "required", "title", "Math", "max", "min", "style", "backgroundColor", "border", "borderRadius", "padding", "margin", "color", "fontSize", "marginBottom", "paddingLeft", "display", "gridTemplateColumns", "gap", "fontWeight", "width", "marginTop", "handleSubCategoryChange", "subCategories", "existingForms", "subCategoriesWithForms", "checked", "conditional", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/FormBuilder.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FieldConfigModal from './FieldConfigModal';\nimport FormPreview from './FormPreview';\nimport './FormBuilder.css';\n\nconst FormBuilder = ({ onSave, onCancel, initialConfig = null }) => {\n  const [selectedHierarchy, setSelectedHierarchy] = useState({});\n  const [formName, setFormName] = useState('');\n  const [formDescription, setFormDescription] = useState('');\n  const [availableFields, setAvailableFields] = useState([]);\n  const [selectedFields, setSelectedFields] = useState([]);\n  const [currentSection, setCurrentSection] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n  const [showFieldConfig, setShowFieldConfig] = useState(false);\n  const [configField, setConfigField] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [saving, setSaving] = useState(false);\n  const [savedForms, setSavedForms] = useState([]);\n  const [showSavedForms, setShowSavedForms] = useState(false);\n\n  // State for dropdown data\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [firmNatures, setFirmNatures] = useState([]);\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    firmNatures: false\n  });\n  const [existingFormInfo, setExistingFormInfo] = useState(null);\n  const [isEditMode, setIsEditMode] = useState(false);\n  const [editingFormId, setEditingFormId] = useState(null);\n\n  useEffect(() => {\n    initializeFields();\n    loadSavedForms();\n    if (initialConfig) {\n      loadInitialConfig(initialConfig);\n    }\n  }, [initialConfig]);\n\n  const initializeFields = () => {\n    const allFields = getAllPersonFields();\n    setAvailableFields(allFields);\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = (fields) => {\n    const seen = new Set();\n    const deduplicated = [];\n\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      }\n    });\n\n    return deduplicated;\n  };\n\n  const loadSavedForms = () => {\n    const forms = formConfigService.getAllFormConfigs();\n    setSavedForms(forms);\n  };\n\n  const loadInitialConfig = (config) => {\n    setFormName(config.name || '');\n    setFormDescription(config.description || '');\n\n    // Deduplicate fields when loading initial config\n    const fields = config.fields || [];\n    const deduplicatedFields = deduplicateFields(fields);\n    setSelectedFields(deduplicatedFields);\n\n    // Set edit mode when loading existing form\n    setIsEditMode(true);\n    setEditingFormId(config.id || config.key);\n\n    if (config.type === 'division' && config.associatedId) {\n      // Load division info for hierarchy selector\n      setSelectedHierarchy({\n        divisionId: config.associatedId\n      });\n    } else if (config.type === 'category' && config.associatedId) {\n      // Load category info for hierarchy selector\n      setSelectedHierarchy({\n        categoryId: config.associatedId\n      });\n    } else if (config.type === 'firmnature' && config.associatedId) {\n      // Load firm nature info for hierarchy selector\n      setSelectedHierarchy({\n        firmNatureId: config.associatedId\n      });\n    }\n\n    // If hierarchy is stored in config, use that\n    if (config.hierarchy) {\n      setSelectedHierarchy(config.hierarchy);\n    }\n  };\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n  }, []);\n\n  // Validate form creation rules when hierarchy changes\n  useEffect(() => {\n    if (selectedHierarchy.categoryId) {\n      // Skip validation in edit mode since we're updating an existing form\n      if (!isEditMode) {\n        const formValidation = formConfigService.validateFormCreation(\n          selectedHierarchy.categoryId,\n          selectedHierarchy.firmNatureId\n        );\n\n        if (!formValidation.isValid) {\n          setErrors(prev => ({\n            ...prev,\n            formCreation: formValidation.errors.join('. ')\n          }));\n        } else {\n          setErrors(prev => {\n            const newErrors = { ...prev };\n            delete newErrors.formCreation;\n            return newErrors;\n          });\n        }\n      } else {\n        // In edit mode, clear any form creation errors\n        setErrors(prev => {\n          const newErrors = { ...prev };\n          delete newErrors.formCreation;\n          return newErrors;\n        });\n      }\n\n      const formInfo = formConfigService.getExistingFormInfo(\n        selectedHierarchy.categoryId,\n        selectedHierarchy.firmNatureId\n      );\n      setExistingFormInfo(formInfo);\n    } else {\n      setExistingFormInfo(null);\n      setErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors.formCreation;\n        return newErrors;\n      });\n    }\n  }, [selectedHierarchy.categoryId, selectedHierarchy.subCategoryId, isEditMode]);\n\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  const loadCategories = async (divisionId) => {\n    if (!divisionId) {\n      setCategories([]);\n      setFirmNatures([]);\n      return;\n    }\n\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  const loadFirmNatures = async (categoryId) => {\n    if (!categoryId) {\n      setFirmNatures([]);\n      return;\n    }\n\n    setLoading(prev => ({ ...prev, firmNatures: true }));\n    try {\n      const response = await apiService.getFirmNaturesByCategory(categoryId);\n      setFirmNatures(response.data || []);\n    } catch (error) {\n      console.error('Error loading firm natures:', error);\n      setFirmNatures([]);\n    } finally {\n      setLoading(prev => ({ ...prev, firmNatures: false }));\n    }\n  };\n\n  const handleDivisionChange = (e) => {\n    const divisionId = e.target.value;\n    const division = divisions.find(d => d.id === parseInt(divisionId));\n\n    setSelectedHierarchy({\n      divisionId: divisionId || null,\n      categoryId: null,\n      firmNatureId: null,\n      division: division || null,\n      category: null,\n      firmNature: null\n    });\n\n    setCategories([]);\n    setFirmNatures([]);\n\n    if (divisionId) {\n      loadCategories(divisionId);\n    }\n  };\n\n  const handleCategoryChange = (e) => {\n    const categoryId = e.target.value;\n    const category = categories.find(c => c.id === parseInt(categoryId));\n\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      categoryId: categoryId || null,\n      firmNatureId: null,\n      category: category || null,\n      firmNature: null\n    }));\n\n    setFirmNatures([]);\n\n    if (categoryId) {\n      loadFirmNatures(categoryId);\n    }\n  };\n\n  const handleFirmNatureChange = (e) => {\n    const firmNatureId = e.target.value;\n    const firmNature = firmNatures.find(fn => fn.id === parseInt(firmNatureId));\n\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      firmNatureId: firmNatureId || null,\n      firmNature: firmNature || null\n    }));\n  };\n\n  const handleFieldToggle = (field) => {\n    const isSelected = selectedFields.some(f => f.key === field.key);\n\n    if (isSelected) {\n      // Remove all instances of this field key (in case there are duplicates)\n      setSelectedFields(prev => prev.filter(f => f.key !== field.key));\n    } else {\n      // Add field only if it doesn't already exist\n      setSelectedFields(prev => {\n        const exists = prev.some(f => f.key === field.key);\n        if (exists) {\n          return prev;\n        }\n        return [...prev, { ...field }];\n      });\n    }\n  };\n\n  const handleFieldConfig = (field) => {\n    setConfigField(field);\n    setShowFieldConfig(true);\n  };\n\n  const handleFieldConfigSave = (updatedField) => {\n    setSelectedFields(prev => \n      prev.map(f => f.key === updatedField.key ? updatedField : f)\n    );\n    setShowFieldConfig(false);\n    setConfigField(null);\n  };\n\n  const handleFieldMove = (fromIndex, toIndex) => {\n    const newFields = [...selectedFields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n    setSelectedFields(newFields);\n  };\n\n  const handleSelectAllFields = () => {\n    // Get all filtered fields (respects current section and search)\n    const fieldsToAdd = filteredFields.filter(field =>\n      !selectedFields.some(selected => selected.key === field.key)\n    );\n\n    setSelectedFields(prev => [...prev, ...fieldsToAdd]);\n  };\n\n  const handleDeselectAllFields = () => {\n    // Remove all filtered fields from selection\n    const filteredFieldKeys = filteredFields.map(field => field.key);\n    setSelectedFields(prev =>\n      prev.filter(field => !filteredFieldKeys.includes(field.key))\n    );\n  };\n\n  const handleSelectAllAvailableFields = () => {\n    // Select all available fields regardless of current filter\n    const fieldsToAdd = availableFields.filter(field =>\n      !selectedFields.some(selected => selected.key === field.key)\n    );\n\n    setSelectedFields(prev => [...prev, ...fieldsToAdd]);\n  };\n\n  const handleClearAllFields = () => {\n    setSelectedFields([]);\n  };\n\n  const handleSave = async () => {\n    const validation = validateForm();\n\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSaving(true);\n    try {\n      // Ensure complete hierarchy information is included\n      const completeHierarchy = {\n        ...selectedHierarchy,\n        // Include division info (required)\n        divisionId: selectedHierarchy.divisionId,\n        division: selectedHierarchy.division,\n        // Include category info (required)\n        categoryId: selectedHierarchy.categoryId,\n        category: selectedHierarchy.category,\n        // Include subcategory info if selected (optional)\n        subCategoryId: selectedHierarchy.subCategoryId || null,\n        subCategory: selectedHierarchy.subCategory || null\n      };\n\n      const config = {\n        name: formName,\n        description: formDescription,\n        fields: selectedFields,\n        hierarchy: completeHierarchy,\n        settings: {\n          showSections: true,\n          allowConditionalFields: true,\n          validateOnChange: true\n        }\n      };\n\n      let savedConfig;\n\n      if (isEditMode) {\n        // In edit mode, update the existing form\n        // Preserve the original type and associatedId from the form being edited\n        const originalForm = formConfigService.getAllFormConfigs().find(f => f.id === editingFormId || f.key === editingFormId);\n        if (originalForm) {\n          // Update the existing form with new data\n          config.id = originalForm.id;\n          config.key = originalForm.key;\n          config.type = originalForm.type;\n          config.associatedId = originalForm.associatedId;\n          config.createdAt = originalForm.createdAt; // Preserve creation date\n          config.updatedAt = new Date().toISOString(); // Update modification date\n\n          savedConfig = formConfigService.saveFormConfig(originalForm.type, originalForm.associatedId, config);\n        } else {\n          throw new Error('Original form not found for editing');\n        }\n      } else {\n        // Create new form with the most specific level selected, but include complete hierarchy\n        if (selectedHierarchy.subCategoryId) {\n          savedConfig = formConfigService.saveFormConfig('subcategory', selectedHierarchy.subCategoryId, config);\n        } else if (selectedHierarchy.categoryId) {\n          savedConfig = formConfigService.saveFormConfig('category', selectedHierarchy.categoryId, config);\n        } else {\n          throw new Error('Please select both division and category');\n        }\n      }\n\n      // Reload saved forms list\n      loadSavedForms();\n\n      if (onSave) {\n        onSave(savedConfig);\n      } else {\n        // Show success message for standalone usage\n        const message = isEditMode ? 'Form updated successfully!' : 'Form configuration saved successfully!';\n        alert(message);\n        // Reset form\n        setFormName('');\n        setFormDescription('');\n        setSelectedFields([]);\n        setSelectedHierarchy({});\n        setIsEditMode(false);\n        setEditingFormId(null);\n      }\n    } catch (error) {\n      console.error('Error saving form:', error);\n      setErrors({ general: error.message });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const validateForm = () => {\n    const errors = {};\n\n    if (!formName.trim()) {\n      errors.formName = 'Form name is required';\n    }\n\n    // Division and category are required, subcategory is optional\n    if (!selectedHierarchy.divisionId) {\n      errors.hierarchy = 'Please select a division';\n    } else if (!selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Please select a category';\n    } else if (selectedHierarchy.subCategoryId && !selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Cannot select subcategory without selecting category';\n    }\n\n    // Validate form creation rules (skip in edit mode)\n    if (selectedHierarchy.categoryId && !isEditMode) {\n      const formValidation = formConfigService.validateFormCreation(\n        selectedHierarchy.categoryId,\n        selectedHierarchy.subCategoryId\n      );\n\n      if (!formValidation.isValid) {\n        errors.formCreation = formValidation.errors.join('. ');\n      }\n    }\n\n    if (selectedFields.length === 0) {\n      errors.fields = 'Please select at least one field';\n    }\n\n    // Check required fields\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = selectedFields.map(f => f.key);\n    const missingRequired = requiredFields.filter(rf => !selectedFieldKeys.includes(rf));\n\n    if (missingRequired.length > 0) {\n      errors.requiredFields = `Required fields missing: ${missingRequired.join(', ')}`;\n    }\n\n    return {\n      isValid: Object.keys(errors).length === 0,\n      errors\n    };\n  };\n\n  const getFilteredFields = () => {\n    let filtered = availableFields;\n\n    // Filter by section\n    if (currentSection !== 'all') {\n      filtered = filtered.filter(field => field.section === currentSection);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(field => \n        field.label.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        field.key.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    }\n\n    return filtered;\n  };\n\n  const getSectionCounts = () => {\n    const counts = { all: availableFields.length };\n    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {\n      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;\n    });\n    return counts;\n  };\n\n  const sectionCounts = getSectionCounts();\n  const filteredFields = getFilteredFields();\n\n  return (\n    <div className=\"form-builder\">\n      <div className=\"form-builder-header\">\n        <h2>Form Builder</h2>\n        <div className=\"header-actions\">\n          <button \n            type=\"button\" \n            onClick={() => setShowPreview(true)}\n            className=\"btn btn-secondary\"\n            disabled={selectedFields.length === 0}\n          >\n            Preview Form\n          </button>\n          <button\n            type=\"button\"\n            onClick={handleSave}\n            className=\"btn btn-primary\"\n            disabled={saving || errors.formCreation}\n          >\n            {saving ? (isEditMode ? 'Updating...' : 'Saving...') : (isEditMode ? 'Update Form' : 'Save Form')}\n          </button>\n          {onCancel && (\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              className=\"btn btn-outline\"\n            >\n              Cancel\n            </button>\n          )}\n        </div>\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      {/* Validation Status */}\n      {Object.keys(errors).length > 0 && (\n        <div className=\"validation-errors\">\n          <h4>Please fix the following issues:</h4>\n          <ul>\n            {Object.entries(errors).map(([key, message]) => (\n              <li key={key}>{message}</li>\n            ))}\n          </ul>\n        </div>\n      )}\n\n      <div className=\"form-builder-content\">\n        {/* Form Configuration Panel */}\n        <div className=\"config-panel\">\n          {/* Saved Forms Section */}\n          <div className=\"config-section\">\n            <div className=\"section-header\">\n              <h3>Saved Forms ({savedForms.length})</h3>\n              <button\n                type=\"button\"\n                className=\"btn-link\"\n                onClick={() => setShowSavedForms(!showSavedForms)}\n              >\n                {showSavedForms ? 'Hide' : 'Show'}\n              </button>\n            </div>\n\n            {showSavedForms && (\n              <div className=\"saved-forms-list\">\n                {savedForms.length === 0 ? (\n                  <p className=\"no-forms-message\">No saved forms yet. Create your first form below!</p>\n                ) : (\n                  savedForms.map((form) => (\n                    <div key={form.key} className=\"saved-form-item\">\n                      <div className=\"form-item-header\">\n                        <h4>{form.name}</h4>\n                        <span className=\"form-type-badge\">{form.type}</span>\n                      </div>\n                      <p className=\"form-description\">{form.description || 'No description'}</p>\n                      <div className=\"form-meta\">\n                        <span>{form.summary.fieldCount} fields</span>\n                        <span>•</span>\n                        <span>Updated {new Date(form.updatedAt).toLocaleDateString()}</span>\n                      </div>\n                      <div className=\"form-actions\">\n                        <button\n                          type=\"button\"\n                          className=\"btn-small btn-outline\"\n                          onClick={() => loadInitialConfig(form)}\n                        >\n                          Load\n                        </button>\n                        <button\n                          type=\"button\"\n                          className=\"btn-small btn-danger\"\n                          onClick={() => {\n                            if (window.confirm('Are you sure you want to delete this form?')) {\n                              formConfigService.deleteFormConfig(form.type, form.associatedId);\n                              loadSavedForms();\n                            }\n                          }}\n                        >\n                          Delete\n                        </button>\n                      </div>\n                    </div>\n                  ))\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* Form Configuration Section */}\n          <div className=\"config-section\">\n            <h3>Form Configuration</h3>\n\n            <div className=\"form-group\">\n              <label>Form Name *</label>\n              <input\n                type=\"text\"\n                value={formName}\n                onChange={(e) => setFormName(e.target.value)}\n                placeholder=\"Enter form name\"\n                className={errors.formName ? 'error' : ''}\n              />\n              {errors.formName && <div className=\"error-message\">{errors.formName}</div>}\n            </div>\n\n            <div className=\"form-group\">\n              <label>Description</label>\n              <textarea\n                value={formDescription}\n                onChange={(e) => setFormDescription(e.target.value)}\n                placeholder=\"Enter form description\"\n                rows=\"3\"\n              />\n            </div>\n\n\n          </div>\n\n          {/* Selected Fields Panel */}\n          <div className=\"config-section\">\n            <h3>Selected Fields ({selectedFields.length})</h3>\n            {errors.fields && <div className=\"error-message\">{errors.fields}</div>}\n            {errors.requiredFields && <div className=\"error-message\">{errors.requiredFields}</div>}\n            \n            <div className=\"selected-fields\">\n              {selectedFields.length === 0 ? (\n                <div className=\"empty-state\">\n                  No fields selected. Choose fields from the available fields panel.\n                </div>\n              ) : (\n                selectedFields.map((field, index) => (\n                  <div key={field.key} className=\"selected-field\">\n                    <div className=\"field-info\">\n                      <span className=\"field-label\">{field.label}</span>\n                      <span className=\"field-type\">{field.type}</span>\n                      {field.required && <span className=\"required-badge\">Required</span>}\n                    </div>\n                    <div className=\"field-actions\">\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldConfig(field)}\n                        className=\"btn-icon\"\n                        title=\"Configure field\"\n                      >\n                        ⚙️\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldMove(index, Math.max(0, index - 1))}\n                        className=\"btn-icon\"\n                        disabled={index === 0}\n                        title=\"Move up\"\n                      >\n                        ↑\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1))}\n                        className=\"btn-icon\"\n                        disabled={index === selectedFields.length - 1}\n                        title=\"Move down\"\n                      >\n                        ↓\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldToggle(field)}\n                        className=\"btn-icon remove\"\n                        title=\"Remove field\"\n                      >\n                        ✕\n                      </button>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Available Fields Panel */}\n        <div className=\"fields-panel\">\n          <div className=\"fields-header\">\n            <h3>Available Fields</h3>\n          </div>\n\n          {/* Division/Category/SubCategory Selection */}\n          <div className=\"hierarchy-selection\" style={{\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #e1e5e9',\n            borderRadius: '8px',\n            padding: '1.5rem',\n            margin: '1rem 0'\n          }}>\n            <h4 style={{ margin: '0 0 1rem 0', color: '#495057' }}>\n              Associate with Division & Category (Required) / SubCategory (Optional) *\n            </h4>\n            <div style={{\n              fontSize: '0.875rem',\n              color: '#6c757d',\n              marginBottom: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8f9fa',\n              borderRadius: '4px',\n              border: '1px solid #e9ecef'\n            }}>\n              <strong>Form Creation Rules:</strong>\n              <ul style={{ margin: '0.5rem 0 0 1rem', paddingLeft: '1rem' }}>\n                <li>If you create a form for a category, you cannot create forms for its subcategories</li>\n                <li>Each subcategory can have only one form</li>\n                <li>If subcategories already have forms, you cannot create a form for the parent category</li>\n              </ul>\n            </div>\n\n            <div className=\"hierarchy-dropdowns\" style={{\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '1rem'\n            }}>\n              {/* Division Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  Division *\n                </label>\n                <select\n                  value={selectedHierarchy.divisionId || ''}\n                  onChange={handleDivisionChange}\n                  disabled={loading.divisions}\n                  className={errors.divisionId ? 'error' : ''}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">Select Division</option>\n                  {divisions.map(division => (\n                    <option key={division.id} value={division.id}>\n                      {division.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.divisionId && (\n                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>\n                    {errors.divisionId}\n                  </div>\n                )}\n              </div>\n\n              {/* Category Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  Category *\n                </label>\n                <select\n                  value={selectedHierarchy.categoryId || ''}\n                  onChange={handleCategoryChange}\n                  disabled={!selectedHierarchy.divisionId || loading.categories}\n                  className={errors.categoryId ? 'error' : ''}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">\n                    {!selectedHierarchy.divisionId ? 'Select Division first' : 'Select Category'}\n                  </option>\n                  {categories.map(category => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.categoryId && (\n                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>\n                    {errors.categoryId}\n                  </div>\n                )}\n              </div>\n\n              {/* SubCategory Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  SubCategory (Optional)\n                </label>\n                <select\n                  value={selectedHierarchy.subCategoryId || ''}\n                  onChange={handleSubCategoryChange}\n                  disabled={!selectedHierarchy.categoryId || loading.subCategories}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">\n                    {!selectedHierarchy.categoryId ? 'Select Category first' : 'Select SubCategory (Optional)'}\n                  </option>\n                  {subCategories.map(subCategory => (\n                    <option key={subCategory.id} value={subCategory.id}>\n                      {subCategory.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            {errors.hierarchy && (\n              <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.5rem' }}>\n                {errors.hierarchy}\n              </div>\n            )}\n            {errors.formCreation && (\n              <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.5rem', fontWeight: 'bold' }}>\n                ⚠️ {errors.formCreation}\n              </div>\n            )}\n\n            {/* Existing Form Information */}\n            {existingFormInfo && existingFormInfo.existingForms.length > 0 && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#fff3cd',\n                border: '1px solid #ffeaa7',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>📋 Existing Forms:</strong>\n                {existingFormInfo.existingForms.map((form, index) => (\n                  <div key={index} style={{ marginTop: '0.5rem' }}>\n                    <strong>{form.type === 'category' ? 'Category' : 'SubCategory'} Form:</strong> {form.name}\n                    {form.description && <div style={{ color: '#6c757d' }}>{form.description}</div>}\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {/* Subcategories with Forms Warning */}\n            {existingFormInfo && existingFormInfo.subCategoriesWithForms.length > 0 && !selectedHierarchy.subCategoryId && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#f8d7da',\n                border: '1px solid #f5c6cb',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>⚠️ Warning:</strong> This category has {existingFormInfo.subCategoriesWithForms.length} subcategory form(s).\n                You cannot create a form for this category.\n              </div>\n            )}\n\n            {/* Success Message for Valid Selection */}\n            {selectedHierarchy.categoryId && !errors.formCreation && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#d4edda',\n                border: '1px solid #c3e6cb',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>✅ Valid Selection:</strong> You can create a form for this {selectedHierarchy.subCategoryId ? 'subcategory' : 'category'}.\n              </div>\n            )}\n          </div>\n\n          {/* Search and Filter Controls */}\n          <div className=\"fields-controls\">\n            <input\n              type=\"text\"\n              placeholder=\"Search fields...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"search-input\"\n            />\n          </div>\n\n          {/* Section Tabs */}\n          <div className=\"section-tabs\">\n            <button\n              type=\"button\"\n              onClick={() => setCurrentSection('all')}\n              className={`section-tab ${currentSection === 'all' ? 'active' : ''}`}\n            >\n              All ({sectionCounts.all})\n            </button>\n            {Object.entries(PersonFieldDefinitions).map(([sectionKey, section]) => (\n              <button\n                key={sectionKey}\n                type=\"button\"\n                onClick={() => setCurrentSection(sectionKey)}\n                className={`section-tab ${currentSection === sectionKey ? 'active' : ''}`}\n              >\n                {section.title} ({sectionCounts[sectionKey]})\n              </button>\n            ))}\n          </div>\n\n          {/* Bulk Selection Controls */}\n          <div className=\"bulk-selection-controls\">\n            <div className=\"bulk-controls-section\">\n              <span className=\"bulk-controls-label\">Quick Actions:</span>\n              <button\n                type=\"button\"\n                onClick={handleSelectAllFields}\n                className=\"btn btn-sm btn-outline\"\n                title={`Select all ${filteredFields.length} filtered fields`}\n              >\n                Select Filtered ({filteredFields.length})\n              </button>\n              <button\n                type=\"button\"\n                onClick={handleDeselectAllFields}\n                className=\"btn btn-sm btn-outline\"\n                title=\"Deselect all filtered fields\"\n              >\n                Deselect Filtered\n              </button>\n              <button\n                type=\"button\"\n                onClick={handleSelectAllAvailableFields}\n                className=\"btn btn-sm btn-primary\"\n                title={`Select all ${availableFields.length} available fields`}\n              >\n                Select All ({availableFields.length})\n              </button>\n              <button\n                type=\"button\"\n                onClick={handleClearAllFields}\n                className=\"btn btn-sm btn-outline btn-danger\"\n                title=\"Clear all selected fields\"\n                disabled={selectedFields.length === 0}\n              >\n                Clear All\n              </button>\n            </div>\n            <div className=\"selection-summary\">\n              {selectedFields.length} of {availableFields.length} fields selected\n            </div>\n          </div>\n\n          {/* Fields List */}\n          <div className=\"fields-list\">\n            {filteredFields.map(field => {\n              const isSelected = selectedFields.some(f => f.key === field.key);\n              return (\n                <div\n                  key={field.key}\n                  className={`field-item ${isSelected ? 'selected' : ''}`}\n                  onClick={() => handleFieldToggle(field)}\n                >\n                  <div className=\"field-checkbox\">\n                    <input\n                      type=\"checkbox\"\n                      checked={isSelected}\n                      onChange={() => handleFieldToggle(field)}\n                    />\n                  </div>\n                  <div className=\"field-details\">\n                    <div className=\"field-name\">{field.label}</div>\n                    <div className=\"field-meta\">\n                      <span className=\"field-type\">{field.type}</span>\n                      {field.required && <span className=\"required-badge\">Required</span>}\n                      {field.conditional && <span className=\"conditional-badge\">Conditional</span>}\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Modals */}\n      {showFieldConfig && configField && (\n        <FieldConfigModal\n          field={configField}\n          onSave={handleFieldConfigSave}\n          onCancel={() => {\n            setShowFieldConfig(false);\n            setConfigField(null);\n          }}\n        />\n      )}\n\n      {showPreview && (\n        <FormPreview\n          fields={selectedFields}\n          formName={formName}\n          onClose={() => setShowPreview(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default FormBuilder;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,iCAAiC;AAC5F,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACqC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC;IACrC2C,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAE,KAAK;IACjBE,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAExDC,SAAS,CAAC,MAAM;IACdwD,gBAAgB,CAAC,CAAC;IAClBC,cAAc,CAAC,CAAC;IAChB,IAAI7C,aAAa,EAAE;MACjB8C,iBAAiB,CAAC9C,aAAa,CAAC;IAClC;EACF,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,MAAM4C,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMG,SAAS,GAAGzD,kBAAkB,CAAC,CAAC;IACtCmB,kBAAkB,CAACsC,SAAS,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAIC,MAAM,IAAK;IACpC,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,EAAE;IAEvBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAI;MACtB,IAAI,CAACJ,IAAI,CAACK,GAAG,CAACD,KAAK,CAACE,GAAG,CAAC,EAAE;QACxBN,IAAI,CAACO,GAAG,CAACH,KAAK,CAACE,GAAG,CAAC;QACnBJ,YAAY,CAACM,IAAI,CAACJ,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IAEF,OAAOF,YAAY;EACrB,CAAC;EAED,MAAMP,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMc,KAAK,GAAGpE,iBAAiB,CAACqE,iBAAiB,CAAC,CAAC;IACnDjC,aAAa,CAACgC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMb,iBAAiB,GAAIe,MAAM,IAAK;IACpCxD,WAAW,CAACwD,MAAM,CAACC,IAAI,IAAI,EAAE,CAAC;IAC9BvD,kBAAkB,CAACsD,MAAM,CAACE,WAAW,IAAI,EAAE,CAAC;;IAE5C;IACA,MAAMd,MAAM,GAAGY,MAAM,CAACZ,MAAM,IAAI,EAAE;IAClC,MAAMe,kBAAkB,GAAGhB,iBAAiB,CAACC,MAAM,CAAC;IACpDtC,iBAAiB,CAACqD,kBAAkB,CAAC;;IAErC;IACAvB,aAAa,CAAC,IAAI,CAAC;IACnBE,gBAAgB,CAACkB,MAAM,CAACI,EAAE,IAAIJ,MAAM,CAACL,GAAG,CAAC;IAEzC,IAAIK,MAAM,CAACK,IAAI,KAAK,UAAU,IAAIL,MAAM,CAACM,YAAY,EAAE;MACrD;MACAhE,oBAAoB,CAAC;QACnBiE,UAAU,EAAEP,MAAM,CAACM;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIN,MAAM,CAACK,IAAI,KAAK,UAAU,IAAIL,MAAM,CAACM,YAAY,EAAE;MAC5D;MACAhE,oBAAoB,CAAC;QACnBkE,UAAU,EAAER,MAAM,CAACM;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIN,MAAM,CAACK,IAAI,KAAK,YAAY,IAAIL,MAAM,CAACM,YAAY,EAAE;MAC9D;MACAhE,oBAAoB,CAAC;QACnBmE,YAAY,EAAET,MAAM,CAACM;MACvB,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIN,MAAM,CAACU,SAAS,EAAE;MACpBpE,oBAAoB,CAAC0D,MAAM,CAACU,SAAS,CAAC;IACxC;EACF,CAAC;;EAED;EACAnF,SAAS,CAAC,MAAM;IACdoF,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApF,SAAS,CAAC,MAAM;IACd,IAAIc,iBAAiB,CAACmE,UAAU,EAAE;MAChC;MACA,IAAI,CAAC7B,UAAU,EAAE;QACf,MAAMiC,cAAc,GAAGlF,iBAAiB,CAACmF,oBAAoB,CAC3DxE,iBAAiB,CAACmE,UAAU,EAC5BnE,iBAAiB,CAACoE,YACpB,CAAC;QAED,IAAI,CAACG,cAAc,CAACE,OAAO,EAAE;UAC3BpD,SAAS,CAACqD,IAAI,KAAK;YACjB,GAAGA,IAAI;YACPC,YAAY,EAAEJ,cAAc,CAACnD,MAAM,CAACwD,IAAI,CAAC,IAAI;UAC/C,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACLvD,SAAS,CAACqD,IAAI,IAAI;YAChB,MAAMG,SAAS,GAAG;cAAE,GAAGH;YAAK,CAAC;YAC7B,OAAOG,SAAS,CAACF,YAAY;YAC7B,OAAOE,SAAS;UAClB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACAxD,SAAS,CAACqD,IAAI,IAAI;UAChB,MAAMG,SAAS,GAAG;YAAE,GAAGH;UAAK,CAAC;UAC7B,OAAOG,SAAS,CAACF,YAAY;UAC7B,OAAOE,SAAS;QAClB,CAAC,CAAC;MACJ;MAEA,MAAMC,QAAQ,GAAGzF,iBAAiB,CAAC0F,mBAAmB,CACpD/E,iBAAiB,CAACmE,UAAU,EAC5BnE,iBAAiB,CAACoE,YACpB,CAAC;MACD/B,mBAAmB,CAACyC,QAAQ,CAAC;IAC/B,CAAC,MAAM;MACLzC,mBAAmB,CAAC,IAAI,CAAC;MACzBhB,SAAS,CAACqD,IAAI,IAAI;QAChB,MAAMG,SAAS,GAAG;UAAE,GAAGH;QAAK,CAAC;QAC7B,OAAOG,SAAS,CAACF,YAAY;QAC7B,OAAOE,SAAS;MAClB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC7E,iBAAiB,CAACmE,UAAU,EAAEnE,iBAAiB,CAACgF,aAAa,EAAE1C,UAAU,CAAC,CAAC;EAE/E,MAAMgC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCnC,UAAU,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9C,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAClD,IAAI;MACF,MAAMqD,QAAQ,GAAG,MAAM3F,UAAU,CAAC4F,YAAY,CAAC,CAAC;MAChDrD,YAAY,CAACoD,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRjD,UAAU,CAACuC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE9C,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;EAED,MAAM0D,cAAc,GAAG,MAAOpB,UAAU,IAAK;IAC3C,IAAI,CAACA,UAAU,EAAE;MACfnC,aAAa,CAAC,EAAE,CAAC;MACjBE,cAAc,CAAC,EAAE,CAAC;MAClB;IACF;IAEAE,UAAU,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE5C,UAAU,EAAE;IAAK,CAAC,CAAC,CAAC;IACnD,IAAI;MACF,MAAMmD,QAAQ,GAAG,MAAM3F,UAAU,CAACiG,uBAAuB,CAACrB,UAAU,CAAC;MACrEnC,aAAa,CAACkD,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDrD,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRI,UAAU,CAACuC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE5C,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAM0D,eAAe,GAAG,MAAOrB,UAAU,IAAK;IAC5C,IAAI,CAACA,UAAU,EAAE;MACflC,cAAc,CAAC,EAAE,CAAC;MAClB;IACF;IAEAE,UAAU,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1C,WAAW,EAAE;IAAK,CAAC,CAAC,CAAC;IACpD,IAAI;MACF,MAAMiD,QAAQ,GAAG,MAAM3F,UAAU,CAACmG,wBAAwB,CAACtB,UAAU,CAAC;MACtElC,cAAc,CAACgD,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACrC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDnD,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,SAAS;MACRE,UAAU,CAACuC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE1C,WAAW,EAAE;MAAM,CAAC,CAAC,CAAC;IACvD;EACF,CAAC;EAED,MAAM0D,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMzB,UAAU,GAAGyB,CAAC,CAACC,MAAM,CAACC,KAAK;IACjC,MAAMC,QAAQ,GAAGlE,SAAS,CAACmE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjC,EAAE,KAAKkC,QAAQ,CAAC/B,UAAU,CAAC,CAAC;IAEnEjE,oBAAoB,CAAC;MACnBiE,UAAU,EAAEA,UAAU,IAAI,IAAI;MAC9BC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClB0B,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BI,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,CAAC;IAEFpE,aAAa,CAAC,EAAE,CAAC;IACjBE,cAAc,CAAC,EAAE,CAAC;IAElB,IAAIiC,UAAU,EAAE;MACdoB,cAAc,CAACpB,UAAU,CAAC;IAC5B;EACF,CAAC;EAED,MAAMkC,oBAAoB,GAAIT,CAAC,IAAK;IAClC,MAAMxB,UAAU,GAAGwB,CAAC,CAACC,MAAM,CAACC,KAAK;IACjC,MAAMK,QAAQ,GAAGpE,UAAU,CAACiE,IAAI,CAACM,CAAC,IAAIA,CAAC,CAACtC,EAAE,KAAKkC,QAAQ,CAAC9B,UAAU,CAAC,CAAC;IAEpElE,oBAAoB,CAACyE,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACPP,UAAU,EAAEA,UAAU,IAAI,IAAI;MAC9BC,YAAY,EAAE,IAAI;MAClB8B,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BC,UAAU,EAAE;IACd,CAAC,CAAC,CAAC;IAEHlE,cAAc,CAAC,EAAE,CAAC;IAElB,IAAIkC,UAAU,EAAE;MACdqB,eAAe,CAACrB,UAAU,CAAC;IAC7B;EACF,CAAC;EAED,MAAMmC,sBAAsB,GAAIX,CAAC,IAAK;IACpC,MAAMvB,YAAY,GAAGuB,CAAC,CAACC,MAAM,CAACC,KAAK;IACnC,MAAMM,UAAU,GAAGnE,WAAW,CAAC+D,IAAI,CAACQ,EAAE,IAAIA,EAAE,CAACxC,EAAE,KAAKkC,QAAQ,CAAC7B,YAAY,CAAC,CAAC;IAE3EnE,oBAAoB,CAACyE,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACPN,YAAY,EAAEA,YAAY,IAAI,IAAI;MAClC+B,UAAU,EAAEA,UAAU,IAAI;IAC5B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,iBAAiB,GAAIpD,KAAK,IAAK;IACnC,MAAMqD,UAAU,GAAGjG,cAAc,CAACkG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC;IAEhE,IAAImD,UAAU,EAAE;MACd;MACAhG,iBAAiB,CAACiE,IAAI,IAAIA,IAAI,CAACkC,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACrD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC,CAAC;IAClE,CAAC,MAAM;MACL;MACA7C,iBAAiB,CAACiE,IAAI,IAAI;QACxB,MAAMmC,MAAM,GAAGnC,IAAI,CAACgC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC;QAClD,IAAIuD,MAAM,EAAE;UACV,OAAOnC,IAAI;QACb;QACA,OAAO,CAAC,GAAGA,IAAI,EAAE;UAAE,GAAGtB;QAAM,CAAC,CAAC;MAChC,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM0D,iBAAiB,GAAI1D,KAAK,IAAK;IACnCjC,cAAc,CAACiC,KAAK,CAAC;IACrBnC,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM8F,qBAAqB,GAAIC,YAAY,IAAK;IAC9CvG,iBAAiB,CAACiE,IAAI,IACpBA,IAAI,CAACuC,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACrD,GAAG,KAAK0D,YAAY,CAAC1D,GAAG,GAAG0D,YAAY,GAAGL,CAAC,CAC7D,CAAC;IACD1F,kBAAkB,CAAC,KAAK,CAAC;IACzBE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM+F,eAAe,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IAC9C,MAAMC,SAAS,GAAG,CAAC,GAAG7G,cAAc,CAAC;IACrC,MAAM,CAAC8G,UAAU,CAAC,GAAGD,SAAS,CAACE,MAAM,CAACJ,SAAS,EAAE,CAAC,CAAC;IACnDE,SAAS,CAACE,MAAM,CAACH,OAAO,EAAE,CAAC,EAAEE,UAAU,CAAC;IACxC7G,iBAAiB,CAAC4G,SAAS,CAAC;EAC9B,CAAC;EAED,MAAMG,qBAAqB,GAAGA,CAAA,KAAM;IAClC;IACA,MAAMC,WAAW,GAAGC,cAAc,CAACd,MAAM,CAACxD,KAAK,IAC7C,CAAC5C,cAAc,CAACkG,IAAI,CAACiB,QAAQ,IAAIA,QAAQ,CAACrE,GAAG,KAAKF,KAAK,CAACE,GAAG,CAC7D,CAAC;IAED7C,iBAAiB,CAACiE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAG+C,WAAW,CAAC,CAAC;EACtD,CAAC;EAED,MAAMG,uBAAuB,GAAGA,CAAA,KAAM;IACpC;IACA,MAAMC,iBAAiB,GAAGH,cAAc,CAACT,GAAG,CAAC7D,KAAK,IAAIA,KAAK,CAACE,GAAG,CAAC;IAChE7C,iBAAiB,CAACiE,IAAI,IACpBA,IAAI,CAACkC,MAAM,CAACxD,KAAK,IAAI,CAACyE,iBAAiB,CAACC,QAAQ,CAAC1E,KAAK,CAACE,GAAG,CAAC,CAC7D,CAAC;EACH,CAAC;EAED,MAAMyE,8BAA8B,GAAGA,CAAA,KAAM;IAC3C;IACA,MAAMN,WAAW,GAAGnH,eAAe,CAACsG,MAAM,CAACxD,KAAK,IAC9C,CAAC5C,cAAc,CAACkG,IAAI,CAACiB,QAAQ,IAAIA,QAAQ,CAACrE,GAAG,KAAKF,KAAK,CAACE,GAAG,CAC7D,CAAC;IAED7C,iBAAiB,CAACiE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAG+C,WAAW,CAAC,CAAC;EACtD,CAAC;EAED,MAAMO,oBAAoB,GAAGA,CAAA,KAAM;IACjCvH,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC;EAED,MAAMwH,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMC,UAAU,GAAGC,YAAY,CAAC,CAAC;IAEjC,IAAI,CAACD,UAAU,CAACzD,OAAO,EAAE;MACvBpD,SAAS,CAAC6G,UAAU,CAAC9G,MAAM,CAAC;MAC5B;IACF;IAEAG,SAAS,CAAC,IAAI,CAAC;IACf,IAAI;MACF;MACA,MAAM6G,iBAAiB,GAAG;QACxB,GAAGpI,iBAAiB;QACpB;QACAkE,UAAU,EAAElE,iBAAiB,CAACkE,UAAU;QACxC4B,QAAQ,EAAE9F,iBAAiB,CAAC8F,QAAQ;QACpC;QACA3B,UAAU,EAAEnE,iBAAiB,CAACmE,UAAU;QACxC+B,QAAQ,EAAElG,iBAAiB,CAACkG,QAAQ;QACpC;QACAlB,aAAa,EAAEhF,iBAAiB,CAACgF,aAAa,IAAI,IAAI;QACtDqD,WAAW,EAAErI,iBAAiB,CAACqI,WAAW,IAAI;MAChD,CAAC;MAED,MAAM1E,MAAM,GAAG;QACbC,IAAI,EAAE1D,QAAQ;QACd2D,WAAW,EAAEzD,eAAe;QAC5B2C,MAAM,EAAEvC,cAAc;QACtB6D,SAAS,EAAE+D,iBAAiB;QAC5BE,QAAQ,EAAE;UACRC,YAAY,EAAE,IAAI;UAClBC,sBAAsB,EAAE,IAAI;UAC5BC,gBAAgB,EAAE;QACpB;MACF,CAAC;MAED,IAAIC,WAAW;MAEf,IAAIpG,UAAU,EAAE;QACd;QACA;QACA,MAAMqG,YAAY,GAAGtJ,iBAAiB,CAACqE,iBAAiB,CAAC,CAAC,CAACqC,IAAI,CAACY,CAAC,IAAIA,CAAC,CAAC5C,EAAE,KAAKvB,aAAa,IAAImE,CAAC,CAACrD,GAAG,KAAKd,aAAa,CAAC;QACvH,IAAImG,YAAY,EAAE;UAChB;UACAhF,MAAM,CAACI,EAAE,GAAG4E,YAAY,CAAC5E,EAAE;UAC3BJ,MAAM,CAACL,GAAG,GAAGqF,YAAY,CAACrF,GAAG;UAC7BK,MAAM,CAACK,IAAI,GAAG2E,YAAY,CAAC3E,IAAI;UAC/BL,MAAM,CAACM,YAAY,GAAG0E,YAAY,CAAC1E,YAAY;UAC/CN,MAAM,CAACiF,SAAS,GAAGD,YAAY,CAACC,SAAS,CAAC,CAAC;UAC3CjF,MAAM,CAACkF,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;;UAE7CL,WAAW,GAAGrJ,iBAAiB,CAAC2J,cAAc,CAACL,YAAY,CAAC3E,IAAI,EAAE2E,YAAY,CAAC1E,YAAY,EAAEN,MAAM,CAAC;QACtG,CAAC,MAAM;UACL,MAAM,IAAIsF,KAAK,CAAC,qCAAqC,CAAC;QACxD;MACF,CAAC,MAAM;QACL;QACA,IAAIjJ,iBAAiB,CAACgF,aAAa,EAAE;UACnC0D,WAAW,GAAGrJ,iBAAiB,CAAC2J,cAAc,CAAC,aAAa,EAAEhJ,iBAAiB,CAACgF,aAAa,EAAErB,MAAM,CAAC;QACxG,CAAC,MAAM,IAAI3D,iBAAiB,CAACmE,UAAU,EAAE;UACvCuE,WAAW,GAAGrJ,iBAAiB,CAAC2J,cAAc,CAAC,UAAU,EAAEhJ,iBAAiB,CAACmE,UAAU,EAAER,MAAM,CAAC;QAClG,CAAC,MAAM;UACL,MAAM,IAAIsF,KAAK,CAAC,0CAA0C,CAAC;QAC7D;MACF;;MAEA;MACAtG,cAAc,CAAC,CAAC;MAEhB,IAAI/C,MAAM,EAAE;QACVA,MAAM,CAAC8I,WAAW,CAAC;MACrB,CAAC,MAAM;QACL;QACA,MAAMQ,OAAO,GAAG5G,UAAU,GAAG,4BAA4B,GAAG,wCAAwC;QACpG6G,KAAK,CAACD,OAAO,CAAC;QACd;QACA/I,WAAW,CAAC,EAAE,CAAC;QACfE,kBAAkB,CAAC,EAAE,CAAC;QACtBI,iBAAiB,CAAC,EAAE,CAAC;QACrBR,oBAAoB,CAAC,CAAC,CAAC,CAAC;QACxBsC,aAAa,CAAC,KAAK,CAAC;QACpBE,gBAAgB,CAAC,IAAI,CAAC;MACxB;IACF,CAAC,CAAC,OAAO2C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C/D,SAAS,CAAC;QAAE+H,OAAO,EAAEhE,KAAK,CAAC8D;MAAQ,CAAC,CAAC;IACvC,CAAC,SAAS;MACR3H,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAM4G,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM/G,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAAClB,QAAQ,CAACmJ,IAAI,CAAC,CAAC,EAAE;MACpBjI,MAAM,CAAClB,QAAQ,GAAG,uBAAuB;IAC3C;;IAEA;IACA,IAAI,CAACF,iBAAiB,CAACkE,UAAU,EAAE;MACjC9C,MAAM,CAACiD,SAAS,GAAG,0BAA0B;IAC/C,CAAC,MAAM,IAAI,CAACrE,iBAAiB,CAACmE,UAAU,EAAE;MACxC/C,MAAM,CAACiD,SAAS,GAAG,0BAA0B;IAC/C,CAAC,MAAM,IAAIrE,iBAAiB,CAACgF,aAAa,IAAI,CAAChF,iBAAiB,CAACmE,UAAU,EAAE;MAC3E/C,MAAM,CAACiD,SAAS,GAAG,sDAAsD;IAC3E;;IAEA;IACA,IAAIrE,iBAAiB,CAACmE,UAAU,IAAI,CAAC7B,UAAU,EAAE;MAC/C,MAAMiC,cAAc,GAAGlF,iBAAiB,CAACmF,oBAAoB,CAC3DxE,iBAAiB,CAACmE,UAAU,EAC5BnE,iBAAiB,CAACgF,aACpB,CAAC;MAED,IAAI,CAACT,cAAc,CAACE,OAAO,EAAE;QAC3BrD,MAAM,CAACuD,YAAY,GAAGJ,cAAc,CAACnD,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC;MACxD;IACF;IAEA,IAAIpE,cAAc,CAAC8I,MAAM,KAAK,CAAC,EAAE;MAC/BlI,MAAM,CAAC2B,MAAM,GAAG,kCAAkC;IACpD;;IAEA;IACA,MAAMwG,cAAc,GAAG,CAAC,MAAM,EAAE,cAAc,EAAE,QAAQ,CAAC;IACzD,MAAMC,iBAAiB,GAAGhJ,cAAc,CAACyG,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACrD,GAAG,CAAC;IACxD,MAAMmG,eAAe,GAAGF,cAAc,CAAC3C,MAAM,CAAC8C,EAAE,IAAI,CAACF,iBAAiB,CAAC1B,QAAQ,CAAC4B,EAAE,CAAC,CAAC;IAEpF,IAAID,eAAe,CAACH,MAAM,GAAG,CAAC,EAAE;MAC9BlI,MAAM,CAACmI,cAAc,GAAG,4BAA4BE,eAAe,CAAC7E,IAAI,CAAC,IAAI,CAAC,EAAE;IAClF;IAEA,OAAO;MACLH,OAAO,EAAEkF,MAAM,CAACC,IAAI,CAACxI,MAAM,CAAC,CAACkI,MAAM,KAAK,CAAC;MACzClI;IACF,CAAC;EACH,CAAC;EAED,MAAMyI,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIC,QAAQ,GAAGxJ,eAAe;;IAE9B;IACA,IAAII,cAAc,KAAK,KAAK,EAAE;MAC5BoJ,QAAQ,GAAGA,QAAQ,CAAClD,MAAM,CAACxD,KAAK,IAAIA,KAAK,CAAC2G,OAAO,KAAKrJ,cAAc,CAAC;IACvE;;IAEA;IACA,IAAIE,UAAU,EAAE;MACdkJ,QAAQ,GAAGA,QAAQ,CAAClD,MAAM,CAACxD,KAAK,IAC9BA,KAAK,CAAC4G,KAAK,CAACC,WAAW,CAAC,CAAC,CAACnC,QAAQ,CAAClH,UAAU,CAACqJ,WAAW,CAAC,CAAC,CAAC,IAC5D7G,KAAK,CAACE,GAAG,CAAC2G,WAAW,CAAC,CAAC,CAACnC,QAAQ,CAAClH,UAAU,CAACqJ,WAAW,CAAC,CAAC,CAC3D,CAAC;IACH;IAEA,OAAOH,QAAQ;EACjB,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,MAAM,GAAG;MAAEC,GAAG,EAAE9J,eAAe,CAACgJ;IAAO,CAAC;IAC9CK,MAAM,CAACC,IAAI,CAACzK,sBAAsB,CAAC,CAACgE,OAAO,CAACkH,UAAU,IAAI;MACxDF,MAAM,CAACE,UAAU,CAAC,GAAG/J,eAAe,CAACsG,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACoD,OAAO,KAAKM,UAAU,CAAC,CAACf,MAAM;IACnF,CAAC,CAAC;IACF,OAAOa,MAAM;EACf,CAAC;EAED,MAAMG,aAAa,GAAGJ,gBAAgB,CAAC,CAAC;EACxC,MAAMxC,cAAc,GAAGmC,iBAAiB,CAAC,CAAC;EAE1C,oBACEnK,OAAA;IAAK6K,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B9K,OAAA;MAAK6K,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC9K,OAAA;QAAA8K,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrBlL,OAAA;QAAK6K,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B9K,OAAA;UACEsE,IAAI,EAAC,QAAQ;UACb6G,OAAO,EAAEA,CAAA,KAAM9J,cAAc,CAAC,IAAI,CAAE;UACpCwJ,SAAS,EAAC,mBAAmB;UAC7BO,QAAQ,EAAEtK,cAAc,CAAC8I,MAAM,KAAK,CAAE;UAAAkB,QAAA,EACvC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlL,OAAA;UACEsE,IAAI,EAAC,QAAQ;UACb6G,OAAO,EAAE5C,UAAW;UACpBsC,SAAS,EAAC,iBAAiB;UAC3BO,QAAQ,EAAExJ,MAAM,IAAIF,MAAM,CAACuD,YAAa;UAAA6F,QAAA,EAEvClJ,MAAM,GAAIgB,UAAU,GAAG,aAAa,GAAG,WAAW,GAAKA,UAAU,GAAG,aAAa,GAAG;QAAY;UAAAmI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F,CAAC,EACR/K,QAAQ,iBACPH,OAAA;UACEsE,IAAI,EAAC,QAAQ;UACb6G,OAAO,EAAEhL,QAAS;UAClB0K,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELxJ,MAAM,CAACgI,OAAO,iBACb1J,OAAA;MAAK6K,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAEpJ,MAAM,CAACgI;IAAO;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,EAGAjB,MAAM,CAACC,IAAI,CAACxI,MAAM,CAAC,CAACkI,MAAM,GAAG,CAAC,iBAC7B5J,OAAA;MAAK6K,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC9K,OAAA;QAAA8K,QAAA,EAAI;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzClL,OAAA;QAAA8K,QAAA,EACGb,MAAM,CAACoB,OAAO,CAAC3J,MAAM,CAAC,CAAC6F,GAAG,CAAC,CAAC,CAAC3D,GAAG,EAAE4F,OAAO,CAAC,kBACzCxJ,OAAA;UAAA8K,QAAA,EAAetB;QAAO,GAAb5F,GAAG;UAAAmH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACN,eAEDlL,OAAA;MAAK6K,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnC9K,OAAA;QAAK6K,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAE3B9K,OAAA;UAAK6K,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B9K,OAAA;YAAK6K,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B9K,OAAA;cAAA8K,QAAA,GAAI,eAAa,EAAChJ,UAAU,CAAC8H,MAAM,EAAC,GAAC;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1ClL,OAAA;cACEsE,IAAI,EAAC,QAAQ;cACbuG,SAAS,EAAC,UAAU;cACpBM,OAAO,EAAEA,CAAA,KAAMlJ,iBAAiB,CAAC,CAACD,cAAc,CAAE;cAAA8I,QAAA,EAEjD9I,cAAc,GAAG,MAAM,GAAG;YAAM;cAAA+I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELlJ,cAAc,iBACbhC,OAAA;YAAK6K,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9BhJ,UAAU,CAAC8H,MAAM,KAAK,CAAC,gBACtB5J,OAAA;cAAG6K,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,GAErFpJ,UAAU,CAACyF,GAAG,CAAE+D,IAAI,iBAClBtL,OAAA;cAAoB6K,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC7C9K,OAAA;gBAAK6K,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B9K,OAAA;kBAAA8K,QAAA,EAAKQ,IAAI,CAACpH;gBAAI;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpBlL,OAAA;kBAAM6K,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAEQ,IAAI,CAAChH;gBAAI;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACNlL,OAAA;gBAAG6K,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEQ,IAAI,CAACnH,WAAW,IAAI;cAAgB;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1ElL,OAAA;gBAAK6K,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB9K,OAAA;kBAAA8K,QAAA,GAAOQ,IAAI,CAACC,OAAO,CAACC,UAAU,EAAC,SAAO;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7ClL,OAAA;kBAAA8K,QAAA,EAAM;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACdlL,OAAA;kBAAA8K,QAAA,GAAM,UAAQ,EAAC,IAAI1B,IAAI,CAACkC,IAAI,CAACnC,SAAS,CAAC,CAACsC,kBAAkB,CAAC,CAAC;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNlL,OAAA;gBAAK6K,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B9K,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACbuG,SAAS,EAAC,uBAAuB;kBACjCM,OAAO,EAAEA,CAAA,KAAMjI,iBAAiB,CAACoI,IAAI,CAAE;kBAAAR,QAAA,EACxC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlL,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACbuG,SAAS,EAAC,sBAAsB;kBAChCM,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIO,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;sBAChEhM,iBAAiB,CAACiM,gBAAgB,CAACN,IAAI,CAAChH,IAAI,EAAEgH,IAAI,CAAC/G,YAAY,CAAC;sBAChEtB,cAAc,CAAC,CAAC;oBAClB;kBACF,CAAE;kBAAA6H,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GA/BEI,IAAI,CAAC1H,GAAG;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCb,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNlL,OAAA;UAAK6K,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B9K,OAAA;YAAA8K,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE3BlL,OAAA;YAAK6K,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9K,OAAA;cAAA8K,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1BlL,OAAA;cACEsE,IAAI,EAAC,MAAM;cACX6B,KAAK,EAAE3F,QAAS;cAChBqL,QAAQ,EAAG5F,CAAC,IAAKxF,WAAW,CAACwF,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;cAC7C2F,WAAW,EAAC,iBAAiB;cAC7BjB,SAAS,EAAEnJ,MAAM,CAAClB,QAAQ,GAAG,OAAO,GAAG;YAAG;cAAAuK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,EACDxJ,MAAM,CAAClB,QAAQ,iBAAIR,OAAA;cAAK6K,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEpJ,MAAM,CAAClB;YAAQ;cAAAuK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAENlL,OAAA;YAAK6K,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9K,OAAA;cAAA8K,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1BlL,OAAA;cACEmG,KAAK,EAAEzF,eAAgB;cACvBmL,QAAQ,EAAG5F,CAAC,IAAKtF,kBAAkB,CAACsF,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;cACpD2F,WAAW,EAAC,wBAAwB;cACpCC,IAAI,EAAC;YAAG;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGH,CAAC,eAGNlL,OAAA;UAAK6K,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B9K,OAAA;YAAA8K,QAAA,GAAI,mBAAiB,EAAChK,cAAc,CAAC8I,MAAM,EAAC,GAAC;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACjDxJ,MAAM,CAAC2B,MAAM,iBAAIrD,OAAA;YAAK6K,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEpJ,MAAM,CAAC2B;UAAM;YAAA0H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACrExJ,MAAM,CAACmI,cAAc,iBAAI7J,OAAA;YAAK6K,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEpJ,MAAM,CAACmI;UAAc;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEtFlL,OAAA;YAAK6K,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BhK,cAAc,CAAC8I,MAAM,KAAK,CAAC,gBAC1B5J,OAAA;cAAK6K,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAENpK,cAAc,CAACyG,GAAG,CAAC,CAAC7D,KAAK,EAAEsI,KAAK,kBAC9BhM,OAAA;cAAqB6K,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7C9K,OAAA;gBAAK6K,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9K,OAAA;kBAAM6K,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEpH,KAAK,CAAC4G;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDlL,OAAA;kBAAM6K,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEpH,KAAK,CAACY;gBAAI;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC/CxH,KAAK,CAACuI,QAAQ,iBAAIjM,OAAA;kBAAM6K,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACNlL,OAAA;gBAAK6K,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B9K,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACb6G,OAAO,EAAEA,CAAA,KAAM/D,iBAAiB,CAAC1D,KAAK,CAAE;kBACxCmH,SAAS,EAAC,UAAU;kBACpBqB,KAAK,EAAC,iBAAiB;kBAAApB,QAAA,EACxB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlL,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACb6G,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAACwE,KAAK,EAAEG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,KAAK,GAAG,CAAC,CAAC,CAAE;kBAC9DnB,SAAS,EAAC,UAAU;kBACpBO,QAAQ,EAAEY,KAAK,KAAK,CAAE;kBACtBE,KAAK,EAAC,SAAS;kBAAApB,QAAA,EAChB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlL,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACb6G,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAACwE,KAAK,EAAEG,IAAI,CAACE,GAAG,CAACvL,cAAc,CAAC8I,MAAM,GAAG,CAAC,EAAEoC,KAAK,GAAG,CAAC,CAAC,CAAE;kBACtFnB,SAAS,EAAC,UAAU;kBACpBO,QAAQ,EAAEY,KAAK,KAAKlL,cAAc,CAAC8I,MAAM,GAAG,CAAE;kBAC9CsC,KAAK,EAAC,WAAW;kBAAApB,QAAA,EAClB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlL,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACb6G,OAAO,EAAEA,CAAA,KAAMrE,iBAAiB,CAACpD,KAAK,CAAE;kBACxCmH,SAAS,EAAC,iBAAiB;kBAC3BqB,KAAK,EAAC,cAAc;kBAAApB,QAAA,EACrB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAzCExH,KAAK,CAACE,GAAG;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0Cd,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlL,OAAA;QAAK6K,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B9K,OAAA;UAAK6K,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B9K,OAAA;YAAA8K,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAGNlL,OAAA;UAAK6K,SAAS,EAAC,qBAAqB;UAACyB,KAAK,EAAE;YAC1CC,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,QAAQ;YACjBC,MAAM,EAAE;UACV,CAAE;UAAA7B,QAAA,gBACA9K,OAAA;YAAIsM,KAAK,EAAE;cAAEK,MAAM,EAAE,YAAY;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAA9B,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlL,OAAA;YAAKsM,KAAK,EAAE;cACVO,QAAQ,EAAE,UAAU;cACpBD,KAAK,EAAE,SAAS;cAChBE,YAAY,EAAE,MAAM;cACpBJ,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BE,YAAY,EAAE,KAAK;cACnBD,MAAM,EAAE;YACV,CAAE;YAAA1B,QAAA,gBACA9K,OAAA;cAAA8K,QAAA,EAAQ;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrClL,OAAA;cAAIsM,KAAK,EAAE;gBAAEK,MAAM,EAAE,iBAAiB;gBAAEI,WAAW,EAAE;cAAO,CAAE;cAAAjC,QAAA,gBAC5D9K,OAAA;gBAAA8K,QAAA,EAAI;cAAkF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3FlL,OAAA;gBAAA8K,QAAA,EAAI;cAAuC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDlL,OAAA;gBAAA8K,QAAA,EAAI;cAAqF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENlL,OAAA;YAAK6K,SAAS,EAAC,qBAAqB;YAACyB,KAAK,EAAE;cAC1CU,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE,sCAAsC;cAC3DC,GAAG,EAAE;YACP,CAAE;YAAApC,QAAA,gBAEA9K,OAAA;cAAK6K,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9K,OAAA;gBAAOsM,KAAK,EAAE;kBACZU,OAAO,EAAE,OAAO;kBAChBF,YAAY,EAAE,QAAQ;kBACtBK,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAA9B,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlL,OAAA;gBACEmG,KAAK,EAAE7F,iBAAiB,CAACkE,UAAU,IAAI,EAAG;gBAC1CqH,QAAQ,EAAE7F,oBAAqB;gBAC/BoF,QAAQ,EAAE5I,OAAO,CAACN,SAAU;gBAC5B2I,SAAS,EAAEnJ,MAAM,CAAC8C,UAAU,GAAG,OAAO,GAAG,EAAG;gBAC5C8H,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACbV,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBI,QAAQ,EAAE,MAAM;kBAChBN,eAAe,EAAE;gBACnB,CAAE;gBAAAzB,QAAA,gBAEF9K,OAAA;kBAAQmG,KAAK,EAAC,EAAE;kBAAA2E,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACxChJ,SAAS,CAACqF,GAAG,CAACnB,QAAQ,iBACrBpG,OAAA;kBAA0BmG,KAAK,EAAEC,QAAQ,CAAC/B,EAAG;kBAAAyG,QAAA,EAC1C1E,QAAQ,CAAClC;gBAAI,GADHkC,QAAQ,CAAC/B,EAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACRxJ,MAAM,CAAC8C,UAAU,iBAChBxE,OAAA;gBAAKsM,KAAK,EAAE;kBAAEM,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,UAAU;kBAAEQ,SAAS,EAAE;gBAAU,CAAE;gBAAAvC,QAAA,EAC1EpJ,MAAM,CAAC8C;cAAU;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNlL,OAAA;cAAK6K,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9K,OAAA;gBAAOsM,KAAK,EAAE;kBACZU,OAAO,EAAE,OAAO;kBAChBF,YAAY,EAAE,QAAQ;kBACtBK,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAA9B,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlL,OAAA;gBACEmG,KAAK,EAAE7F,iBAAiB,CAACmE,UAAU,IAAI,EAAG;gBAC1CoH,QAAQ,EAAEnF,oBAAqB;gBAC/B0E,QAAQ,EAAE,CAAC9K,iBAAiB,CAACkE,UAAU,IAAIhC,OAAO,CAACJ,UAAW;gBAC9DyI,SAAS,EAAEnJ,MAAM,CAAC+C,UAAU,GAAG,OAAO,GAAG,EAAG;gBAC5C6H,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACbV,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBI,QAAQ,EAAE,MAAM;kBAChBN,eAAe,EAAE;gBACnB,CAAE;gBAAAzB,QAAA,gBAEF9K,OAAA;kBAAQmG,KAAK,EAAC,EAAE;kBAAA2E,QAAA,EACb,CAACxK,iBAAiB,CAACkE,UAAU,GAAG,uBAAuB,GAAG;gBAAiB;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,EACR9I,UAAU,CAACmF,GAAG,CAACf,QAAQ,iBACtBxG,OAAA;kBAA0BmG,KAAK,EAAEK,QAAQ,CAACnC,EAAG;kBAAAyG,QAAA,EAC1CtE,QAAQ,CAACtC;gBAAI,GADHsC,QAAQ,CAACnC,EAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACRxJ,MAAM,CAAC+C,UAAU,iBAChBzE,OAAA;gBAAKsM,KAAK,EAAE;kBAAEM,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,UAAU;kBAAEQ,SAAS,EAAE;gBAAU,CAAE;gBAAAvC,QAAA,EAC1EpJ,MAAM,CAAC+C;cAAU;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNlL,OAAA;cAAK6K,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9K,OAAA;gBAAOsM,KAAK,EAAE;kBACZU,OAAO,EAAE,OAAO;kBAChBF,YAAY,EAAE,QAAQ;kBACtBK,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAA9B,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlL,OAAA;gBACEmG,KAAK,EAAE7F,iBAAiB,CAACgF,aAAa,IAAI,EAAG;gBAC7CuG,QAAQ,EAAEyB,uBAAwB;gBAClClC,QAAQ,EAAE,CAAC9K,iBAAiB,CAACmE,UAAU,IAAIjC,OAAO,CAAC+K,aAAc;gBACjEjB,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACbV,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBI,QAAQ,EAAE,MAAM;kBAChBN,eAAe,EAAE;gBACnB,CAAE;gBAAAzB,QAAA,gBAEF9K,OAAA;kBAAQmG,KAAK,EAAC,EAAE;kBAAA2E,QAAA,EACb,CAACxK,iBAAiB,CAACmE,UAAU,GAAG,uBAAuB,GAAG;gBAA+B;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,EACRqC,aAAa,CAAChG,GAAG,CAACoB,WAAW,iBAC5B3I,OAAA;kBAA6BmG,KAAK,EAAEwC,WAAW,CAACtE,EAAG;kBAAAyG,QAAA,EAChDnC,WAAW,CAACzE;gBAAI,GADNyE,WAAW,CAACtE,EAAE;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEnB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELxJ,MAAM,CAACiD,SAAS,iBACf3E,OAAA;YAAKsM,KAAK,EAAE;cAAEM,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEQ,SAAS,EAAE;YAAS,CAAE;YAAAvC,QAAA,EACzEpJ,MAAM,CAACiD;UAAS;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CACN,EACAxJ,MAAM,CAACuD,YAAY,iBAClBjF,OAAA;YAAKsM,KAAK,EAAE;cAAEM,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEQ,SAAS,EAAE,QAAQ;cAAEF,UAAU,EAAE;YAAO,CAAE;YAAArC,QAAA,GAAC,eAC5F,EAACpJ,MAAM,CAACuD,YAAY;UAAA;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CACN,EAGAxI,gBAAgB,IAAIA,gBAAgB,CAAC8K,aAAa,CAAC5D,MAAM,GAAG,CAAC,iBAC5D5J,OAAA;YAAKsM,KAAK,EAAE;cACVe,SAAS,EAAE,MAAM;cACjBX,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBI,QAAQ,EAAE;YACZ,CAAE;YAAA/B,QAAA,gBACA9K,OAAA;cAAA8K,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAClCxI,gBAAgB,CAAC8K,aAAa,CAACjG,GAAG,CAAC,CAAC+D,IAAI,EAAEU,KAAK,kBAC9ChM,OAAA;cAAiBsM,KAAK,EAAE;gBAAEe,SAAS,EAAE;cAAS,CAAE;cAAAvC,QAAA,gBAC9C9K,OAAA;gBAAA8K,QAAA,GAASQ,IAAI,CAAChH,IAAI,KAAK,UAAU,GAAG,UAAU,GAAG,aAAa,EAAC,QAAM;cAAA;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACI,IAAI,CAACpH,IAAI,EACxFoH,IAAI,CAACnH,WAAW,iBAAInE,OAAA;gBAAKsM,KAAK,EAAE;kBAAEM,KAAK,EAAE;gBAAU,CAAE;gBAAA9B,QAAA,EAAEQ,IAAI,CAACnH;cAAW;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAFvEc,KAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAGAxI,gBAAgB,IAAIA,gBAAgB,CAAC+K,sBAAsB,CAAC7D,MAAM,GAAG,CAAC,IAAI,CAACtJ,iBAAiB,CAACgF,aAAa,iBACzGtF,OAAA;YAAKsM,KAAK,EAAE;cACVe,SAAS,EAAE,MAAM;cACjBX,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBI,QAAQ,EAAE;YACZ,CAAE;YAAA/B,QAAA,gBACA9K,OAAA;cAAA8K,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,uBAAmB,EAACxI,gBAAgB,CAAC+K,sBAAsB,CAAC7D,MAAM,EAAC,mEAEjG;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,EAGA5K,iBAAiB,CAACmE,UAAU,IAAI,CAAC/C,MAAM,CAACuD,YAAY,iBACnDjF,OAAA;YAAKsM,KAAK,EAAE;cACVe,SAAS,EAAE,MAAM;cACjBX,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBI,QAAQ,EAAE;YACZ,CAAE;YAAA/B,QAAA,gBACA9K,OAAA;cAAA8K,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,oCAAgC,EAAC5K,iBAAiB,CAACgF,aAAa,GAAG,aAAa,GAAG,UAAU,EAAC,GACnI;UAAA;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNlL,OAAA;UAAK6K,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B9K,OAAA;YACEsE,IAAI,EAAC,MAAM;YACXwH,WAAW,EAAC,kBAAkB;YAC9B3F,KAAK,EAAEjF,UAAW;YAClB2K,QAAQ,EAAG5F,CAAC,IAAK9E,aAAa,CAAC8E,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;YAC/C0E,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlL,OAAA;UAAK6K,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B9K,OAAA;YACEsE,IAAI,EAAC,QAAQ;YACb6G,OAAO,EAAEA,CAAA,KAAMlK,iBAAiB,CAAC,KAAK,CAAE;YACxC4J,SAAS,EAAE,eAAe7J,cAAc,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAA8J,QAAA,GACtE,OACM,EAACF,aAAa,CAACF,GAAG,EAAC,GAC1B;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACRjB,MAAM,CAACoB,OAAO,CAAC5L,sBAAsB,CAAC,CAAC8H,GAAG,CAAC,CAAC,CAACoD,UAAU,EAAEN,OAAO,CAAC,kBAChErK,OAAA;YAEEsE,IAAI,EAAC,QAAQ;YACb6G,OAAO,EAAEA,CAAA,KAAMlK,iBAAiB,CAAC0J,UAAU,CAAE;YAC7CE,SAAS,EAAE,eAAe7J,cAAc,KAAK2J,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAAG,QAAA,GAEzET,OAAO,CAAC6B,KAAK,EAAC,IAAE,EAACtB,aAAa,CAACD,UAAU,CAAC,EAAC,GAC9C;UAAA,GANOA,UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMT,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlL,OAAA;UAAK6K,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC9K,OAAA;YAAK6K,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpC9K,OAAA;cAAM6K,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3DlL,OAAA;cACEsE,IAAI,EAAC,QAAQ;cACb6G,OAAO,EAAErD,qBAAsB;cAC/B+C,SAAS,EAAC,wBAAwB;cAClCqB,KAAK,EAAE,cAAclE,cAAc,CAAC4B,MAAM,kBAAmB;cAAAkB,QAAA,GAC9D,mBACkB,EAAC9C,cAAc,CAAC4B,MAAM,EAAC,GAC1C;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlL,OAAA;cACEsE,IAAI,EAAC,QAAQ;cACb6G,OAAO,EAAEjD,uBAAwB;cACjC2C,SAAS,EAAC,wBAAwB;cAClCqB,KAAK,EAAC,8BAA8B;cAAApB,QAAA,EACrC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlL,OAAA;cACEsE,IAAI,EAAC,QAAQ;cACb6G,OAAO,EAAE9C,8BAA+B;cACxCwC,SAAS,EAAC,wBAAwB;cAClCqB,KAAK,EAAE,cAActL,eAAe,CAACgJ,MAAM,mBAAoB;cAAAkB,QAAA,GAChE,cACa,EAAClK,eAAe,CAACgJ,MAAM,EAAC,GACtC;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlL,OAAA;cACEsE,IAAI,EAAC,QAAQ;cACb6G,OAAO,EAAE7C,oBAAqB;cAC9BuC,SAAS,EAAC,mCAAmC;cAC7CqB,KAAK,EAAC,2BAA2B;cACjCd,QAAQ,EAAEtK,cAAc,CAAC8I,MAAM,KAAK,CAAE;cAAAkB,QAAA,EACvC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNlL,OAAA;YAAK6K,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAC/BhK,cAAc,CAAC8I,MAAM,EAAC,MAAI,EAAChJ,eAAe,CAACgJ,MAAM,EAAC,kBACrD;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlL,OAAA;UAAK6K,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzB9C,cAAc,CAACT,GAAG,CAAC7D,KAAK,IAAI;YAC3B,MAAMqD,UAAU,GAAGjG,cAAc,CAACkG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC;YAChE,oBACE5D,OAAA;cAEE6K,SAAS,EAAE,cAAc9D,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;cACxDoE,OAAO,EAAEA,CAAA,KAAMrE,iBAAiB,CAACpD,KAAK,CAAE;cAAAoH,QAAA,gBAExC9K,OAAA;gBAAK6K,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7B9K,OAAA;kBACEsE,IAAI,EAAC,UAAU;kBACfoJ,OAAO,EAAE3G,UAAW;kBACpB8E,QAAQ,EAAEA,CAAA,KAAM/E,iBAAiB,CAACpD,KAAK;gBAAE;kBAAAqH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNlL,OAAA;gBAAK6K,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B9K,OAAA;kBAAK6K,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEpH,KAAK,CAAC4G;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/ClL,OAAA;kBAAK6K,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB9K,OAAA;oBAAM6K,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEpH,KAAK,CAACY;kBAAI;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC/CxH,KAAK,CAACuI,QAAQ,iBAAIjM,OAAA;oBAAM6K,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAClExH,KAAK,CAACiK,WAAW,iBAAI3N,OAAA;oBAAM6K,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAlBDxH,KAAK,CAACE,GAAG;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBX,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL5J,eAAe,IAAIE,WAAW,iBAC7BxB,OAAA,CAACH,gBAAgB;MACf6D,KAAK,EAAElC,WAAY;MACnBtB,MAAM,EAAEmH,qBAAsB;MAC9BlH,QAAQ,EAAEA,CAAA,KAAM;QACdoB,kBAAkB,CAAC,KAAK,CAAC;QACzBE,cAAc,CAAC,IAAI,CAAC;MACtB;IAAE;MAAAsJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEA9J,WAAW,iBACVpB,OAAA,CAACF,WAAW;MACVuD,MAAM,EAAEvC,cAAe;MACvBN,QAAQ,EAAEA,QAAS;MACnBoN,OAAO,EAAEA,CAAA,KAAMvM,cAAc,CAAC,KAAK;IAAE;MAAA0J,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC7K,EAAA,CAngCIJ,WAAW;AAAA4N,EAAA,GAAX5N,WAAW;AAqgCjB,eAAeA,WAAW;AAAC,IAAA4N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}