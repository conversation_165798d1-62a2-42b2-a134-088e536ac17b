using CrmApi.Models.Person;

namespace CrmApi.Services.Person
{
    public interface IPersonService
    {
        // Basic CRUD Operations
        Task<IEnumerable<PersonResponse>> GetAllPersonsAsync(bool includeDeleted = false);
        Task<PersonResponse?> GetPersonByIdAsync(int id, bool includeDeleted = false);
        Task<PersonResponse> CreatePersonAsync(CreatePersonRequest request);
        Task<PersonResponse> UpdatePersonAsync(int id, UpdatePersonRequest request);
        Task<bool> DeletePersonAsync(int id);
        Task<bool> SoftDeletePersonAsync(int id);
        Task<bool> RestorePersonAsync(int id);

        // Search and Filter Operations
        Task<PersonSearchResponse> SearchPersonsAsync(PersonSearchRequest request);
        Task<IEnumerable<PersonResponse>> GetPersonsByDivisionAsync(int divisionId, bool includeDeleted = false);
        Task<IEnumerable<PersonResponse>> GetPersonsByCategoryAsync(int categoryId, bool includeDeleted = false);
        Task<IEnumerable<PersonResponse>> GetPersonsByFirmNatureAsync(int firmNatureId, bool includeDeleted = false);
        Task<IEnumerable<PersonResponse>> GetPersonsByMobileNumberAsync(string mobileNumber, bool includeDeleted = false);

        // Validation Operations
        Task<bool> ValidatePersonDataAsync(CreatePersonRequest request);
        Task<bool> ValidatePersonUpdateDataAsync(int id, UpdatePersonRequest request);

        // Statistics Operations
        Task<PersonStatisticsResponse> GetPersonStatisticsAsync();
        Task<PersonStatisticsResponse> GetPersonStatisticsByDivisionAsync(int divisionId);
        Task<PersonStatisticsResponse> GetPersonStatisticsByCategoryAsync(int categoryId);

        // Bulk Operations
        Task<IEnumerable<PersonResponse>> CreateBulkPersonsAsync(IEnumerable<CreatePersonRequest> requests);
        Task<bool> SoftDeleteBulkPersonsAsync(IEnumerable<int> ids);
        Task<bool> RestoreBulkPersonsAsync(IEnumerable<int> ids);
    }

    public class PersonStatisticsResponse
    {
        public int TotalPersons { get; set; }
        public int ActivePersons { get; set; }
        public int DeletedPersons { get; set; }
        public Dictionary<string, int> PersonsByNature { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> PersonsByGender { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> PersonsByDivision { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> PersonsByCategory { get; set; } = new Dictionary<string, int>();
        public decimal AverageStarRating { get; set; }
        public decimal TotalTransactionValue { get; set; }
        public int PersonsWithAssociates { get; set; }
        public int PersonsUsingWebsite { get; set; }
        public int PersonsUsingCRMApp { get; set; }
    }
}
