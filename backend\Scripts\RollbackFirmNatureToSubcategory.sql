-- =====================================================
-- Rollback Script: Firm Nature to Subcategory
-- Description: Rollback the migration from firm_natures back to subcategories
-- Database: SQL Server
-- Author: CRM System Migration
-- Date: 2025-01-01
-- =====================================================

USE [CrmDatabase]
GO

PRINT 'Starting rollback from Firm Nature to Subcategory...'
GO

-- =====================================================
-- STEP 1: VERIFY BACKUP TABLES EXIST
-- =====================================================

PRINT 'Step 1: Verifying backup tables exist...'
GO

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='subcategories_backup' AND xtype='U')
BEGIN
    PRINT '✗ ERROR: subcategories_backup table does not exist!'
    PRINT 'Cannot proceed with rollback without backup data.'
    RETURN
END
ELSE
    PRINT '✓ subcategories_backup table found'

-- =====================================================
-- STEP 2: DROP PARTITIONED INDEXES
-- =====================================================

PRINT 'Step 2: Dropping partitioned indexes...'
GO

-- Drop partitioned indexes
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_persons_firm_nature_id_partitioned')
BEGIN
    DROP INDEX IX_persons_firm_nature_id_partitioned ON persons;
    PRINT '✓ Dropped IX_persons_firm_nature_id_partitioned index';
END

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_persons_mobile_division_category_firm_nature_unique')
BEGIN
    DROP INDEX IX_persons_mobile_division_category_firm_nature_unique ON persons;
    PRINT '✓ Dropped IX_persons_mobile_division_category_firm_nature_unique index';
END

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_persons_name_firm_nature_partitioned')
BEGIN
    DROP INDEX IX_persons_name_firm_nature_partitioned ON persons;
    PRINT '✓ Dropped IX_persons_name_firm_nature_partitioned index';
END

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_persons_created_at_firm_nature_partitioned')
BEGIN
    DROP INDEX IX_persons_created_at_firm_nature_partitioned ON persons;
    PRINT '✓ Dropped IX_persons_created_at_firm_nature_partitioned index';
END
GO

-- =====================================================
-- STEP 3: DROP PARTITION SCHEME AND FUNCTION
-- =====================================================

PRINT 'Step 3: Dropping partition scheme and function...'
GO

-- Drop partition scheme
IF EXISTS (SELECT * FROM sys.partition_schemes WHERE name = 'ps_firm_nature_id')
BEGIN
    DROP PARTITION SCHEME ps_firm_nature_id;
    PRINT '✓ Dropped partition scheme ps_firm_nature_id';
END

-- Drop partition function
IF EXISTS (SELECT * FROM sys.partition_functions WHERE name = 'pf_firm_nature_id')
BEGIN
    DROP PARTITION FUNCTION pf_firm_nature_id;
    PRINT '✓ Dropped partition function pf_firm_nature_id';
END
GO

-- =====================================================
-- STEP 4: DROP FOREIGN KEY CONSTRAINTS
-- =====================================================

PRINT 'Step 4: Dropping foreign key constraints...'
GO

-- Drop foreign key constraint from persons table
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_persons_firm_natures')
BEGIN
    ALTER TABLE persons DROP CONSTRAINT FK_persons_firm_natures;
    PRINT '✓ Dropped FK_persons_firm_natures constraint';
END
GO

-- =====================================================
-- STEP 5: DROP INDEXES
-- =====================================================

PRINT 'Step 5: Dropping indexes...'
GO

-- Drop indexes on firm_natures table
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_firm_natures_category_id')
BEGIN
    DROP INDEX IX_firm_natures_category_id ON firm_natures;
    PRINT '✓ Dropped IX_firm_natures_category_id index';
END

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_firm_natures_name_category_id')
BEGIN
    DROP INDEX IX_firm_natures_name_category_id ON firm_natures;
    PRINT '✓ Dropped IX_firm_natures_name_category_id index';
END

-- Drop indexes on persons table related to firm_nature
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_persons_firm_nature_id')
BEGIN
    DROP INDEX IX_persons_firm_nature_id ON persons;
    PRINT '✓ Dropped IX_persons_firm_nature_id index';
END
GO

-- =====================================================
-- STEP 6: RENAME COLUMN IN PERSONS TABLE
-- =====================================================

PRINT 'Step 6: Renaming firm_nature_id column back to subcategory_id in persons table...'
GO

-- Rename the column
EXEC sp_rename 'persons.firm_nature_id', 'subcategory_id', 'COLUMN';
PRINT '✓ Renamed firm_nature_id column back to subcategory_id in persons table';
GO

-- =====================================================
-- STEP 7: RENAME FIRM_NATURES TABLE BACK TO SUBCATEGORIES
-- =====================================================

PRINT 'Step 7: Renaming firm_natures table back to subcategories...'
GO

-- Rename the unique constraint
EXEC sp_rename 'UQ_firm_natures_name_category', 'UQ_subcategories_name_category';
PRINT '✓ Renamed unique constraint back to UQ_subcategories_name_category';

-- Rename the foreign key constraint
EXEC sp_rename 'FK_firm_natures_categories', 'FK_subcategories_categories';
PRINT '✓ Renamed foreign key constraint back to FK_subcategories_categories';

-- Rename the primary key constraint (this might have a different name)
-- Note: You may need to check the actual constraint name and adjust accordingly
-- EXEC sp_rename 'PK_firm_natures', 'PK_subcategories';
-- PRINT '✓ Renamed primary key constraint back to PK_subcategories';

-- Rename the table
EXEC sp_rename 'firm_natures', 'subcategories';
PRINT '✓ Renamed firm_natures table back to subcategories';
GO

-- =====================================================
-- STEP 8: RESTORE DATA FROM BACKUP (IF NEEDED)
-- =====================================================

PRINT 'Step 8: Checking if data restoration is needed...'
GO

-- Compare record counts
DECLARE @current_count INT, @backup_count INT
SELECT @current_count = COUNT(*) FROM subcategories
SELECT @backup_count = COUNT(*) FROM subcategories_backup

IF @current_count != @backup_count
BEGIN
    PRINT 'Data count mismatch detected. Restoring from backup...'
    
    -- Truncate current table and restore from backup
    TRUNCATE TABLE subcategories;
    
    INSERT INTO subcategories 
    SELECT * FROM subcategories_backup;
    
    PRINT '✓ Data restored from backup'
END
ELSE
    PRINT '✓ Data counts match, no restoration needed'
GO

-- =====================================================
-- STEP 9: RECREATE ORIGINAL INDEXES
-- =====================================================

PRINT 'Step 9: Recreating original indexes...'
GO

-- Create indexes on subcategories table
CREATE INDEX IX_subcategories_category_id ON subcategories(category_id);
PRINT '✓ Created IX_subcategories_category_id index';

CREATE INDEX IX_subcategories_name_category_id ON subcategories(name, category_id);
PRINT '✓ Created IX_subcategories_name_category_id index';

-- Create index on persons table for subcategory_id
CREATE INDEX IX_persons_subcategory_id ON persons(subcategory_id);
PRINT '✓ Created IX_persons_subcategory_id index';
GO

-- =====================================================
-- STEP 10: RECREATE FOREIGN KEY CONSTRAINT
-- =====================================================

PRINT 'Step 10: Recreating original foreign key constraint...'
GO

-- Add foreign key constraint from persons to subcategories
ALTER TABLE persons 
ADD CONSTRAINT FK_persons_subcategories 
FOREIGN KEY (subcategory_id) REFERENCES subcategories(id);
PRINT '✓ Created FK_persons_subcategories constraint';
GO

-- =====================================================
-- STEP 11: RECREATE ORIGINAL UNIQUE CONSTRAINT
-- =====================================================

PRINT 'Step 11: Recreating original unique constraint...'
GO

-- Recreate original unique constraint for mobile number
CREATE UNIQUE INDEX IX_persons_mobile_division_category_unique 
ON persons(mobile_number, division_id, category_id) 
WHERE is_deleted = 0;
PRINT '✓ Created original unique constraint for mobile number';
GO

-- =====================================================
-- STEP 12: UPDATE STATISTICS
-- =====================================================

PRINT 'Step 12: Updating statistics...'
GO

-- Update statistics on subcategories table
UPDATE STATISTICS subcategories;
PRINT '✓ Updated statistics on subcategories table';

-- Update statistics on persons table
UPDATE STATISTICS persons;
PRINT '✓ Updated statistics on persons table';
GO

-- =====================================================
-- STEP 13: VERIFICATION
-- =====================================================

PRINT 'Step 13: Verifying rollback...'
GO

-- Verify table exists
IF EXISTS (SELECT * FROM sysobjects WHERE name='subcategories' AND xtype='U')
    PRINT '✓ subcategories table exists'
ELSE
    PRINT '✗ ERROR: subcategories table does not exist'

-- Verify column exists
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('persons') AND name = 'subcategory_id')
    PRINT '✓ subcategory_id column exists in persons table'
ELSE
    PRINT '✗ ERROR: subcategory_id column does not exist in persons table'

-- Verify foreign key constraint
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_persons_subcategories')
    PRINT '✓ FK_persons_subcategories constraint exists'
ELSE
    PRINT '✗ ERROR: FK_persons_subcategories constraint does not exist'

-- Verify indexes
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_subcategories_category_id')
    PRINT '✓ IX_subcategories_category_id index exists'
ELSE
    PRINT '✗ ERROR: IX_subcategories_category_id index does not exist'

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_persons_subcategory_id')
    PRINT '✓ IX_persons_subcategory_id index exists'
ELSE
    PRINT '✗ ERROR: IX_persons_subcategory_id index does not exist'

-- Show record counts
DECLARE @subcategory_count INT, @persons_count INT
SELECT @subcategory_count = COUNT(*) FROM subcategories
SELECT @persons_count = COUNT(*) FROM persons

PRINT '✓ Record counts:'
PRINT '  - subcategories: ' + CAST(@subcategory_count AS VARCHAR(10))
PRINT '  - persons: ' + CAST(@persons_count AS VARCHAR(10))
GO

PRINT ''
PRINT '=================================================='
PRINT 'ROLLBACK COMPLETED SUCCESSFULLY!'
PRINT '=================================================='
PRINT ''
PRINT 'Summary of rollback:'
PRINT '✓ Renamed firm_natures table back to subcategories'
PRINT '✓ Renamed firm_nature_id column back to subcategory_id'
PRINT '✓ Restored all original foreign key constraints'
PRINT '✓ Recreated all original indexes'
PRINT '✓ Removed partitioning'
PRINT '✓ Restored data from backup (if needed)'
PRINT '✓ Updated statistics'
PRINT ''
PRINT 'Next steps:'
PRINT '1. Update your application code to use original table/column names'
PRINT '2. Test the application thoroughly'
PRINT '3. Remove backup tables when confident rollback is successful'
PRINT ''
GO
