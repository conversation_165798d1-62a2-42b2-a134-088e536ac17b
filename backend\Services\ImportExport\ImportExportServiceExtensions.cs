using System.Globalization;
using System.Text;
using CsvHelper;
using CsvHelper.Configuration;
using OfficeOpenXml;
using CrmApi.Models.ImportExport;
using CrmApi.Models.Person;

namespace CrmApi.Services.ImportExport
{
    public partial class ImportExportService
    {
        private CreatePersonRequest MapToCreateRequest(PersonImportData data)
        {
            return new CreatePersonRequest
            {
                DivisionId = data.DivisionId!.Value,
                CategoryId = data.CategoryId!.Value,
                FirmNatureId = data.FirmNatureId!.Value,
                Name = data.Name,
                MobileNumber = data.MobileNumber,
                Nature = Enum.Parse<PersonNature>(data.Nature!, true),
                Gender = !string.IsNullOrEmpty(data.Gender) ? Enum.Parse<Gender>(data.Gender, true) : null,
                AlternateNumbers = ParseStringArray(data.AlternateNumbers),
                PrimaryEmailId = data.PrimaryEmailId ?? string.Empty,
                AlternateEmailIds = ParseStringArray(data.AlternateEmailIds),
                Website = data.Website ?? string.Empty,
                DateOfBirth = ParseDateTime(data.DateOfBirth),
                IsMarried = ParseBoolean(data.IsMarried),
                DateOfMarriage = ParseDateTime(data.DateOfMarriage),
                WorkingState = data.WorkingState ?? string.Empty,
                DomesticState = data.DomesticState ?? string.Empty,
                District = data.District ?? string.Empty,
                Address = data.Address ?? string.Empty,
                WorkingArea = data.WorkingArea ?? string.Empty,
                HasAssociate = ParseBoolean(data.HasAssociate) ?? false,
                AssociateName = data.AssociateName ?? string.Empty,
                AssociateRelation = data.AssociateRelation ?? string.Empty,
                AssociateMobile = data.AssociateMobile ?? string.Empty,
                UsingWebsite = ParseBoolean(data.UsingWebsite) ?? false,
                WebsiteLink = data.WebsiteLink ?? string.Empty,
                UsingCRMApp = ParseBoolean(data.UsingCRMApp) ?? false,
                CRMAppLink = data.CRMAppLink ?? string.Empty,
                TransactionValue = ParseDecimal(data.TransactionValue),
                RERARegistrationNumber = data.RERARegistrationNumber ?? string.Empty,
                WorkingProfiles = ParseWorkingProfiles(data.WorkingProfiles),
                StarRating = ParseInt(data.StarRating),
                Source = data.Source ?? string.Empty,
                Remarks = data.Remarks ?? string.Empty,
                FirmName = data.FirmName ?? string.Empty,
                NumberOfOffices = ParseInt(data.NumberOfOffices),
                NumberOfBranches = ParseInt(data.NumberOfBranches),
                TotalEmployeeStrength = ParseInt(data.TotalEmployeeStrength),
                AuthorizedPersonName = data.AuthorizedPersonName ?? string.Empty,
                AuthorizedPersonEmail = data.AuthorizedPersonEmail ?? string.Empty,
                Designation = data.Designation ?? string.Empty,
                MarketingContact = data.MarketingContact ?? string.Empty,
                MarketingDesignation = data.MarketingDesignation ?? string.Empty,
                PlaceOfPosting = data.PlaceOfPosting ?? string.Empty,
                Department = data.Department ?? string.Empty
            };
        }

        private UpdatePersonRequest MapToUpdateRequest(PersonImportData data)
        {
            return new UpdatePersonRequest
            {
                DivisionId = data.DivisionId,
                CategoryId = data.CategoryId,
                FirmNatureId = data.FirmNatureId,
                Name = data.Name,
                MobileNumber = data.MobileNumber,
                Nature = !string.IsNullOrEmpty(data.Nature) ? Enum.Parse<PersonNature>(data.Nature, true) : null,
                Gender = !string.IsNullOrEmpty(data.Gender) ? Enum.Parse<Gender>(data.Gender, true) : null,
                AlternateNumbers = ParseStringArray(data.AlternateNumbers),
                PrimaryEmailId = data.PrimaryEmailId,
                AlternateEmailIds = ParseStringArray(data.AlternateEmailIds),
                Website = data.Website,
                DateOfBirth = ParseDateTime(data.DateOfBirth),
                IsMarried = ParseBoolean(data.IsMarried),
                DateOfMarriage = ParseDateTime(data.DateOfMarriage),
                WorkingState = data.WorkingState,
                DomesticState = data.DomesticState,
                District = data.District,
                Address = data.Address,
                WorkingArea = data.WorkingArea,
                HasAssociate = ParseBoolean(data.HasAssociate),
                AssociateName = data.AssociateName,
                AssociateRelation = data.AssociateRelation,
                AssociateMobile = data.AssociateMobile,
                UsingWebsite = ParseBoolean(data.UsingWebsite),
                WebsiteLink = data.WebsiteLink,
                UsingCRMApp = ParseBoolean(data.UsingCRMApp),
                CRMAppLink = data.CRMAppLink,
                TransactionValue = ParseDecimal(data.TransactionValue),
                RERARegistrationNumber = data.RERARegistrationNumber,
                WorkingProfiles = ParseWorkingProfiles(data.WorkingProfiles),
                StarRating = ParseInt(data.StarRating),
                Source = data.Source,
                Remarks = data.Remarks,
                FirmName = data.FirmName,
                NumberOfOffices = ParseInt(data.NumberOfOffices),
                NumberOfBranches = ParseInt(data.NumberOfBranches),
                TotalEmployeeStrength = ParseInt(data.TotalEmployeeStrength),
                AuthorizedPersonName = data.AuthorizedPersonName,
                AuthorizedPersonEmail = data.AuthorizedPersonEmail,
                Designation = data.Designation,
                MarketingContact = data.MarketingContact,
                MarketingDesignation = data.MarketingDesignation,
                PlaceOfPosting = data.PlaceOfPosting,
                Department = data.Department
            };
        }

        private string[] ParseStringArray(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return Array.Empty<string>();

            return value.Split(',', StringSplitOptions.RemoveEmptyEntries)
                       .Select(s => s.Trim())
                       .Where(s => !string.IsNullOrEmpty(s))
                       .ToArray();
        }

        private DateTime? ParseDateTime(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return null;

            if (DateTime.TryParse(value, out var result))
                return result;

            return null;
        }

        private bool? ParseBoolean(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return null;

            if (bool.TryParse(value, out var result))
                return result;

            // Handle common string representations
            var lowerValue = value.ToLowerInvariant();
            return lowerValue switch
            {
                "yes" or "y" or "1" or "true" or "on" => true,
                "no" or "n" or "0" or "false" or "off" => false,
                _ => null
            };
        }

        private decimal? ParseDecimal(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return null;

            if (decimal.TryParse(value, out var result))
                return result;

            return null;
        }

        private int? ParseInt(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return null;

            if (int.TryParse(value, out var result))
                return result;

            return null;
        }

        private WorkingProfile[] ParseWorkingProfiles(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return Array.Empty<WorkingProfile>();

            var profiles = new List<WorkingProfile>();
            var parts = value.Split(',', StringSplitOptions.RemoveEmptyEntries);

            foreach (var part in parts)
            {
                var trimmed = part.Trim();
                if (Enum.TryParse<WorkingProfile>(trimmed, true, out var profile))
                {
                    profiles.Add(profile);
                }
            }

            return profiles.ToArray();
        }

        private async Task<byte[]> GenerateExcelExportAsync(List<PersonResponse> persons, PersonExportRequest request)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Persons");

            var columns = GetExportColumns(request);
            
            // Add headers
            for (int i = 0; i < columns.Count; i++)
            {
                worksheet.Cells[1, i + 1].Value = columns[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Add data
            for (int row = 0; row < persons.Count; row++)
            {
                var person = persons[row];
                for (int col = 0; col < columns.Count; col++)
                {
                    var value = GetPersonPropertyValue(person, columns[col]);
                    worksheet.Cells[row + 2, col + 1].Value = value;
                }
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            return package.GetAsByteArray();
        }

        private async Task<byte[]> GenerateCsvExportAsync(List<PersonResponse> persons, PersonExportRequest request)
        {
            using var memoryStream = new MemoryStream();
            using var writer = new StreamWriter(memoryStream, Encoding.UTF8);
            using var csv = new CsvWriter(writer, new CsvConfiguration(CultureInfo.InvariantCulture));

            var columns = GetExportColumns(request);

            // Write headers
            if (request.IncludeHeaders)
            {
                foreach (var column in columns)
                {
                    csv.WriteField(column);
                }
                csv.NextRecord();
            }

            // Write data
            foreach (var person in persons)
            {
                foreach (var column in columns)
                {
                    var value = GetPersonPropertyValue(person, column);
                    csv.WriteField(value);
                }
                csv.NextRecord();
            }

            writer.Flush();
            return memoryStream.ToArray();
        }

        private async Task<byte[]> GenerateExcelTemplateAsync(ExportTemplateRequest request)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Person Import Template");

            var columns = PersonImportColumns.AllColumns;

            // Add headers
            for (int i = 0; i < columns.Count; i++)
            {
                worksheet.Cells[1, i + 1].Value = columns[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                
                // Mark required columns
                if (PersonImportColumns.RequiredColumns.Contains(columns[i]))
                {
                    worksheet.Cells[1, i + 1].Style.Font.Color.SetColor(System.Drawing.Color.Red);
                }
            }

            // Add sample data if requested
            if (request.IncludeSampleData)
            {
                worksheet.Cells[2, 1].Value = "Sample Division";
                worksheet.Cells[2, 2].Value = "Sample Category";
                worksheet.Cells[2, 3].Value = "Sample Firm Nature";
                worksheet.Cells[2, 4].Value = "John Doe";
                worksheet.Cells[2, 5].Value = "9876543210";
                worksheet.Cells[2, 6].Value = "Business";
                worksheet.Cells[2, 7].Value = "Male";
            }

            // Add validation rules if requested
            if (request.IncludeValidationRules)
            {
                var validationSheet = package.Workbook.Worksheets.Add("Validation Rules");
                validationSheet.Cells[1, 1].Value = "Field";
                validationSheet.Cells[1, 2].Value = "Rules";
                validationSheet.Cells[1, 1].Style.Font.Bold = true;
                validationSheet.Cells[1, 2].Style.Font.Bold = true;

                var rules = new[]
                {
                    ("Division Name", "Must match existing division"),
                    ("Category Name", "Must match existing category"),
                    ("Name", "Required, max 255 characters"),
                    ("Mobile Number", "Required, Indian format (10 digits)"),
                    ("Nature", "Business, Corporate, Agriculture, Individual"),
                    ("Gender", "Male, Female, Other"),
                    ("Primary Email", "Valid email format"),
                    ("Star Rating", "1-5")
                };

                for (int i = 0; i < rules.Length; i++)
                {
                    validationSheet.Cells[i + 2, 1].Value = rules[i].Item1;
                    validationSheet.Cells[i + 2, 2].Value = rules[i].Item2;
                }
            }

            worksheet.Cells.AutoFitColumns();
            return package.GetAsByteArray();
        }

        private async Task<byte[]> GenerateCsvTemplateAsync(ExportTemplateRequest request)
        {
            using var memoryStream = new MemoryStream();
            using var writer = new StreamWriter(memoryStream, Encoding.UTF8);
            using var csv = new CsvWriter(writer, new CsvConfiguration(CultureInfo.InvariantCulture));

            var columns = PersonImportColumns.AllColumns;

            // Write headers
            foreach (var column in columns)
            {
                csv.WriteField(column);
            }
            csv.NextRecord();

            // Add sample data if requested
            if (request.IncludeSampleData)
            {
                csv.WriteField("Sample Division");
                csv.WriteField("Sample Category");
                csv.WriteField("Sample Firm Nature");
                csv.WriteField("John Doe");
                csv.WriteField("9876543210");
                csv.WriteField("Business");
                csv.WriteField("Male");
                
                // Fill remaining columns with empty values
                for (int i = 7; i < columns.Count; i++)
                {
                    csv.WriteField("");
                }
                csv.NextRecord();
            }

            writer.Flush();
            return memoryStream.ToArray();
        }

        private List<string> GetExportColumns(PersonExportRequest request)
        {
            if (request.IncludeAllColumns || !request.SelectedColumns.Any())
            {
                return PersonImportColumns.AllColumns;
            }

            return request.SelectedColumns;
        }

        private object? GetPersonPropertyValue(PersonResponse person, string columnName)
        {
            return columnName switch
            {
                PersonImportColumns.DivisionName => person.Division?.Name,
                PersonImportColumns.CategoryName => person.Category?.Name,
                PersonImportColumns.FirmNatureName => person.FirmNature?.Name,
                PersonImportColumns.Name => person.Name,
                PersonImportColumns.MobileNumber => person.MobileNumber,
                PersonImportColumns.Nature => person.NatureDisplay,
                PersonImportColumns.Gender => person.GenderDisplay,
                PersonImportColumns.AlternateNumbers => string.Join(", ", person.AlternateNumbers),
                PersonImportColumns.PrimaryEmailId => person.PrimaryEmailId,
                PersonImportColumns.AlternateEmailIds => string.Join(", ", person.AlternateEmailIds),
                PersonImportColumns.Website => person.Website,
                PersonImportColumns.DateOfBirth => person.DateOfBirth?.ToString("yyyy-MM-dd"),
                PersonImportColumns.IsMarried => person.IsMarried?.ToString(),
                PersonImportColumns.DateOfMarriage => person.DateOfMarriage?.ToString("yyyy-MM-dd"),
                PersonImportColumns.WorkingState => person.WorkingState,
                PersonImportColumns.DomesticState => person.DomesticState,
                PersonImportColumns.District => person.District,
                PersonImportColumns.Address => person.Address,
                PersonImportColumns.WorkingArea => person.WorkingArea,
                PersonImportColumns.HasAssociate => person.HasAssociate.ToString(),
                PersonImportColumns.AssociateName => person.AssociateName,
                PersonImportColumns.AssociateRelation => person.AssociateRelation,
                PersonImportColumns.AssociateMobile => person.AssociateMobile,
                PersonImportColumns.UsingWebsite => person.UsingWebsite.ToString(),
                PersonImportColumns.WebsiteLink => person.WebsiteLink,
                PersonImportColumns.UsingCRMApp => person.UsingCRMApp.ToString(),
                PersonImportColumns.CRMAppLink => person.CRMAppLink,
                PersonImportColumns.TransactionValue => person.TransactionValue?.ToString(),
                PersonImportColumns.RERARegistrationNumber => person.RERARegistrationNumber,
                PersonImportColumns.WorkingProfiles => string.Join(", ", person.WorkingProfilesDisplay),
                PersonImportColumns.StarRating => person.StarRating?.ToString(),
                PersonImportColumns.Source => person.Source,
                PersonImportColumns.Remarks => person.Remarks,
                PersonImportColumns.FirmName => person.FirmName,
                PersonImportColumns.NumberOfOffices => person.NumberOfOffices?.ToString(),
                PersonImportColumns.NumberOfBranches => person.NumberOfBranches?.ToString(),
                PersonImportColumns.TotalEmployeeStrength => person.TotalEmployeeStrength?.ToString(),
                PersonImportColumns.AuthorizedPersonName => person.AuthorizedPersonName,
                PersonImportColumns.AuthorizedPersonEmail => person.AuthorizedPersonEmail,
                PersonImportColumns.Designation => person.Designation,
                PersonImportColumns.MarketingContact => person.MarketingContact,
                PersonImportColumns.MarketingDesignation => person.MarketingDesignation,
                PersonImportColumns.PlaceOfPosting => person.PlaceOfPosting,
                PersonImportColumns.Department => person.Department,
                _ => null
            };
        }
    }
}
