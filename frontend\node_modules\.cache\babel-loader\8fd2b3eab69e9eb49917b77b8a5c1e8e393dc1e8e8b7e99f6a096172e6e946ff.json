{"ast": null, "code": "import React,{useState,useEffect}from'react';import'./ImportProgress.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ImportProgress=_ref=>{var _importJob$processedR,_importJob$totalRows,_importJob$successful,_importJob$failedRows,_importJob$skippedRow,_importJob$updatedRow;let{importJob,onCancel,error}=_ref;const[progress,setProgress]=useState(0);const[timeElapsed,setTimeElapsed]=useState(0);const[estimatedTimeRemaining,setEstimatedTimeRemaining]=useState(null);useEffect(()=>{if(importJob){const progressPercentage=importJob.totalRows>0?Math.round(importJob.processedRows/importJob.totalRows*100):0;setProgress(progressPercentage);}},[importJob]);useEffect(()=>{const timer=setInterval(()=>{if(importJob!==null&&importJob!==void 0&&importJob.startedAt){const elapsed=Math.floor((Date.now()-new Date(importJob.startedAt).getTime())/1000);setTimeElapsed(elapsed);// Calculate estimated time remaining\nif(importJob.processedRows>0&&importJob.totalRows>0){const rate=importJob.processedRows/elapsed;const remaining=(importJob.totalRows-importJob.processedRows)/rate;setEstimatedTimeRemaining(Math.ceil(remaining));}}},1000);return()=>clearInterval(timer);},[importJob]);const formatTime=seconds=>{if(seconds<60)return`${seconds}s`;const minutes=Math.floor(seconds/60);const remainingSeconds=seconds%60;return`${minutes}m ${remainingSeconds}s`;};// Convert status to string (handles both enum numbers and strings)\nconst getStatusString=status=>{if(typeof status==='string'){return status;}// Handle enum values\nconst statusMap={1:'Pending',2:'Processing',3:'Completed',4:'Failed',5:'Cancelled'};return statusMap[status]||'Unknown';};const getStatusColor=status=>{const statusString=getStatusString(status);switch(statusString){case'Processing':return'#007bff';case'Completed':return'#28a745';case'Failed':return'#dc3545';case'Cancelled':return'#6c757d';default:return'#6c757d';}};const getStatusIcon=status=>{const statusString=getStatusString(status);switch(statusString){case'Processing':return'⏳';case'Completed':return'✅';case'Failed':return'❌';case'Cancelled':return'⏹️';default:return'⏳';}};if(!importJob){return/*#__PURE__*/_jsx(\"div\",{className:\"import-progress\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"progress-loading\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Starting import...\"})]})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"import-progress\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"progress-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Import Progress\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"job-info\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"job-id\",children:[\"Job ID: \",importJob.jobId]}),/*#__PURE__*/_jsxs(\"span\",{className:\"job-status\",style:{color:getStatusColor(importJob.status)},children:[getStatusIcon(importJob.status),\" \",getStatusString(importJob.status)]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"progress-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"progress-bar-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"progress-bar\",children:/*#__PURE__*/_jsx(\"div\",{className:\"progress-fill\",style:{width:`${progress}%`,backgroundColor:getStatusColor(importJob.status)}})}),/*#__PURE__*/_jsxs(\"div\",{className:\"progress-text\",children:[progress,\"% Complete\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"progress-details\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"detail-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"detail-label\",children:\"Processed:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"detail-value\",children:[((_importJob$processedR=importJob.processedRows)===null||_importJob$processedR===void 0?void 0:_importJob$processedR.toLocaleString())||0,\" / \",((_importJob$totalRows=importJob.totalRows)===null||_importJob$totalRows===void 0?void 0:_importJob$totalRows.toLocaleString())||0]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"detail-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"detail-label\",children:\"Time Elapsed:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"detail-value\",children:formatTime(timeElapsed)})]}),estimatedTimeRemaining&&getStatusString(importJob.status)==='Processing'&&/*#__PURE__*/_jsxs(\"div\",{className:\"detail-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"detail-label\",children:\"Est. Remaining:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"detail-value\",children:formatTime(estimatedTimeRemaining)})]})]})]}),importJob.currentOperation&&/*#__PURE__*/_jsxs(\"div\",{className:\"current-operation\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"operation-icon\",children:\"\\uD83D\\uDD04\"}),/*#__PURE__*/_jsx(\"div\",{className:\"operation-text\",children:importJob.currentOperation})]}),/*#__PURE__*/_jsx(\"div\",{className:\"progress-stats\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"stats-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card success\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\u2705\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:((_importJob$successful=importJob.successfulRows)===null||_importJob$successful===void 0?void 0:_importJob$successful.toLocaleString())||0}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"Successful\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card error\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\u274C\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:((_importJob$failedRows=importJob.failedRows)===null||_importJob$failedRows===void 0?void 0:_importJob$failedRows.toLocaleString())||0}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"Failed\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card warning\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\u23ED\\uFE0F\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:((_importJob$skippedRow=importJob.skippedRows)===null||_importJob$skippedRow===void 0?void 0:_importJob$skippedRow.toLocaleString())||0}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"Skipped\"})]})]}),importJob.updatedRows>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\uD83D\\uDD04\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:((_importJob$updatedRow=importJob.updatedRows)===null||_importJob$updatedRow===void 0?void 0:_importJob$updatedRow.toLocaleString())||0}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"Updated\"})]})]})]})}),importJob.errors&&importJob.errors.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"recent-errors\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Recent Errors\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"errors-list\",children:[importJob.errors.slice(0,5).map((error,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"error-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"error-row\",children:[\"Row \",error.rowNumber]}),/*#__PURE__*/_jsx(\"div\",{className:\"error-field\",children:error.fieldName}),/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:error.errorMessage})]},index)),importJob.errors.length>5&&/*#__PURE__*/_jsxs(\"div\",{className:\"more-errors\",children:[\"+\",importJob.errors.length-5,\" more errors\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"processing-log\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Processing Log\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"log-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"log-entry\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"log-time\",children:new Date(importJob.startedAt).toLocaleTimeString()}),/*#__PURE__*/_jsx(\"span\",{className:\"log-message\",children:\"Import started\"})]}),getStatusString(importJob.status)==='Processing'&&/*#__PURE__*/_jsxs(\"div\",{className:\"log-entry current\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"log-time\",children:new Date().toLocaleTimeString()}),/*#__PURE__*/_jsx(\"span\",{className:\"log-message\",children:importJob.currentOperation||'Processing...'})]}),importJob.completedAt&&/*#__PURE__*/_jsxs(\"div\",{className:\"log-entry\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"log-time\",children:new Date(importJob.completedAt).toLocaleTimeString()}),/*#__PURE__*/_jsxs(\"span\",{className:\"log-message\",children:[\"Import \",getStatusString(importJob.status).toLowerCase()]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"progress-actions\",children:[getStatusString(importJob.status)==='Processing'&&/*#__PURE__*/_jsx(\"button\",{onClick:onCancel,className:\"btn btn-outline\",children:\"Cancel Import\"}),(importJob.status==='Completed'||importJob.status==='Failed'||importJob.status==='Cancelled')&&/*#__PURE__*/_jsxs(\"div\",{className:\"completion-message\",children:[/*#__PURE__*/_jsx(\"div\",{className:`completion-icon ${importJob.status.toLowerCase()}`,children:getStatusIcon(importJob.status)}),/*#__PURE__*/_jsxs(\"div\",{className:\"completion-text\",children:[\"Import \",importJob.status.toLowerCase(),importJob.status==='Completed'&&/*#__PURE__*/_jsxs(\"div\",{className:\"completion-details\",children:[\"Successfully processed \",importJob.successfulRows,\" records\",importJob.failedRows>0&&` with ${importJob.failedRows} errors`]})]})]})]}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"progress-error\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"error-icon\",children:\"\\u26A0\\uFE0F\"}),/*#__PURE__*/_jsx(\"span\",{className:\"error-message\",children:error})]})]});};export default ImportProgress;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "ImportProgress", "_ref", "_importJob$processedR", "_importJob$totalRows", "_importJob$successful", "_importJob$failedRows", "_importJob$skippedRow", "_importJob$updatedRow", "importJob", "onCancel", "error", "progress", "setProgress", "timeElapsed", "setTimeElapsed", "estimatedTimeRemaining", "setEstimatedTimeRemaining", "progressPercentage", "totalRows", "Math", "round", "processedRows", "timer", "setInterval", "startedAt", "elapsed", "floor", "Date", "now", "getTime", "rate", "remaining", "ceil", "clearInterval", "formatTime", "seconds", "minutes", "remainingSeconds", "getStatusString", "status", "statusMap", "getStatusColor", "statusString", "getStatusIcon", "className", "children", "jobId", "style", "color", "width", "backgroundColor", "toLocaleString", "currentOperation", "successfulRows", "failedRows", "skippedRows", "updatedRows", "errors", "length", "slice", "map", "index", "rowNumber", "fieldName", "errorMessage", "toLocaleTimeString", "completedAt", "toLowerCase", "onClick"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/import/ImportProgress.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './ImportProgress.css';\n\nconst ImportProgress = ({ importJob, onCancel, error }) => {\n  const [progress, setProgress] = useState(0);\n  const [timeElapsed, setTimeElapsed] = useState(0);\n  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState(null);\n\n  useEffect(() => {\n    if (importJob) {\n      const progressPercentage = importJob.totalRows > 0 \n        ? Math.round((importJob.processedRows / importJob.totalRows) * 100)\n        : 0;\n      setProgress(progressPercentage);\n    }\n  }, [importJob]);\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      if (importJob?.startedAt) {\n        const elapsed = Math.floor((Date.now() - new Date(importJob.startedAt).getTime()) / 1000);\n        setTimeElapsed(elapsed);\n\n        // Calculate estimated time remaining\n        if (importJob.processedRows > 0 && importJob.totalRows > 0) {\n          const rate = importJob.processedRows / elapsed;\n          const remaining = (importJob.totalRows - importJob.processedRows) / rate;\n          setEstimatedTimeRemaining(Math.ceil(remaining));\n        }\n      }\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [importJob]);\n\n  const formatTime = (seconds) => {\n    if (seconds < 60) return `${seconds}s`;\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n\n  // Convert status to string (handles both enum numbers and strings)\n  const getStatusString = (status) => {\n    if (typeof status === 'string') {\n      return status;\n    }\n\n    // Handle enum values\n    const statusMap = {\n      1: 'Pending',\n      2: 'Processing',\n      3: 'Completed',\n      4: 'Failed',\n      5: 'Cancelled'\n    };\n\n    return statusMap[status] || 'Unknown';\n  };\n\n  const getStatusColor = (status) => {\n    const statusString = getStatusString(status);\n    switch (statusString) {\n      case 'Processing': return '#007bff';\n      case 'Completed': return '#28a745';\n      case 'Failed': return '#dc3545';\n      case 'Cancelled': return '#6c757d';\n      default: return '#6c757d';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    const statusString = getStatusString(status);\n    switch (statusString) {\n      case 'Processing': return '⏳';\n      case 'Completed': return '✅';\n      case 'Failed': return '❌';\n      case 'Cancelled': return '⏹️';\n      default: return '⏳';\n    }\n  };\n\n  if (!importJob) {\n    return (\n      <div className=\"import-progress\">\n        <div className=\"progress-loading\">\n          <div className=\"loading-spinner\"></div>\n          <p>Starting import...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"import-progress\">\n      <div className=\"progress-header\">\n        <h3>Import Progress</h3>\n        <div className=\"job-info\">\n          <span className=\"job-id\">Job ID: {importJob.jobId}</span>\n          <span \n            className=\"job-status\"\n            style={{ color: getStatusColor(importJob.status) }}\n          >\n            {getStatusIcon(importJob.status)} {getStatusString(importJob.status)}\n          </span>\n        </div>\n      </div>\n\n      {/* Progress Bar */}\n      <div className=\"progress-section\">\n        <div className=\"progress-bar-container\">\n          <div className=\"progress-bar\">\n            <div \n              className=\"progress-fill\"\n              style={{ \n                width: `${progress}%`,\n                backgroundColor: getStatusColor(importJob.status)\n              }}\n            ></div>\n          </div>\n          <div className=\"progress-text\">\n            {progress}% Complete\n          </div>\n        </div>\n\n        <div className=\"progress-details\">\n          <div className=\"detail-item\">\n            <span className=\"detail-label\">Processed:</span>\n            <span className=\"detail-value\">\n              {importJob.processedRows?.toLocaleString() || 0} / {importJob.totalRows?.toLocaleString() || 0}\n            </span>\n          </div>\n          <div className=\"detail-item\">\n            <span className=\"detail-label\">Time Elapsed:</span>\n            <span className=\"detail-value\">{formatTime(timeElapsed)}</span>\n          </div>\n          {estimatedTimeRemaining && getStatusString(importJob.status) === 'Processing' && (\n            <div className=\"detail-item\">\n              <span className=\"detail-label\">Est. Remaining:</span>\n              <span className=\"detail-value\">{formatTime(estimatedTimeRemaining)}</span>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Current Operation */}\n      {importJob.currentOperation && (\n        <div className=\"current-operation\">\n          <div className=\"operation-icon\">🔄</div>\n          <div className=\"operation-text\">{importJob.currentOperation}</div>\n        </div>\n      )}\n\n      {/* Statistics */}\n      <div className=\"progress-stats\">\n        <div className=\"stats-grid\">\n          <div className=\"stat-card success\">\n            <div className=\"stat-icon\">✅</div>\n            <div className=\"stat-content\">\n              <div className=\"stat-value\">{importJob.successfulRows?.toLocaleString() || 0}</div>\n              <div className=\"stat-label\">Successful</div>\n            </div>\n          </div>\n          \n          <div className=\"stat-card error\">\n            <div className=\"stat-icon\">❌</div>\n            <div className=\"stat-content\">\n              <div className=\"stat-value\">{importJob.failedRows?.toLocaleString() || 0}</div>\n              <div className=\"stat-label\">Failed</div>\n            </div>\n          </div>\n          \n          <div className=\"stat-card warning\">\n            <div className=\"stat-icon\">⏭️</div>\n            <div className=\"stat-content\">\n              <div className=\"stat-value\">{importJob.skippedRows?.toLocaleString() || 0}</div>\n              <div className=\"stat-label\">Skipped</div>\n            </div>\n          </div>\n          \n          {importJob.updatedRows > 0 && (\n            <div className=\"stat-card info\">\n              <div className=\"stat-icon\">🔄</div>\n              <div className=\"stat-content\">\n                <div className=\"stat-value\">{importJob.updatedRows?.toLocaleString() || 0}</div>\n                <div className=\"stat-label\">Updated</div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Recent Errors */}\n      {importJob.errors && importJob.errors.length > 0 && (\n        <div className=\"recent-errors\">\n          <h4>Recent Errors</h4>\n          <div className=\"errors-list\">\n            {importJob.errors.slice(0, 5).map((error, index) => (\n              <div key={index} className=\"error-item\">\n                <div className=\"error-row\">Row {error.rowNumber}</div>\n                <div className=\"error-field\">{error.fieldName}</div>\n                <div className=\"error-message\">{error.errorMessage}</div>\n              </div>\n            ))}\n            {importJob.errors.length > 5 && (\n              <div className=\"more-errors\">\n                +{importJob.errors.length - 5} more errors\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Processing Log */}\n      <div className=\"processing-log\">\n        <h4>Processing Log</h4>\n        <div className=\"log-container\">\n          <div className=\"log-entry\">\n            <span className=\"log-time\">{new Date(importJob.startedAt).toLocaleTimeString()}</span>\n            <span className=\"log-message\">Import started</span>\n          </div>\n          {getStatusString(importJob.status) === 'Processing' && (\n            <div className=\"log-entry current\">\n              <span className=\"log-time\">{new Date().toLocaleTimeString()}</span>\n              <span className=\"log-message\">{importJob.currentOperation || 'Processing...'}</span>\n            </div>\n          )}\n          {importJob.completedAt && (\n            <div className=\"log-entry\">\n              <span className=\"log-time\">{new Date(importJob.completedAt).toLocaleTimeString()}</span>\n              <span className=\"log-message\">Import {getStatusString(importJob.status).toLowerCase()}</span>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Actions */}\n      <div className=\"progress-actions\">\n        {getStatusString(importJob.status) === 'Processing' && (\n          <button onClick={onCancel} className=\"btn btn-outline\">\n            Cancel Import\n          </button>\n        )}\n        \n        {(importJob.status === 'Completed' || importJob.status === 'Failed' || importJob.status === 'Cancelled') && (\n          <div className=\"completion-message\">\n            <div className={`completion-icon ${importJob.status.toLowerCase()}`}>\n              {getStatusIcon(importJob.status)}\n            </div>\n            <div className=\"completion-text\">\n              Import {importJob.status.toLowerCase()}\n              {importJob.status === 'Completed' && (\n                <div className=\"completion-details\">\n                  Successfully processed {importJob.successfulRows} records\n                  {importJob.failedRows > 0 && ` with ${importJob.failedRows} errors`}\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {error && (\n        <div className=\"progress-error\">\n          <span className=\"error-icon\">⚠️</span>\n          <span className=\"error-message\">{error}</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ImportProgress;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9B,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAoC,KAAAC,qBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,IAAnC,CAAEC,SAAS,CAAEC,QAAQ,CAAEC,KAAM,CAAC,CAAAT,IAAA,CACpD,KAAM,CAACU,QAAQ,CAAEC,WAAW,CAAC,CAAGlB,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACmB,WAAW,CAAEC,cAAc,CAAC,CAAGpB,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAACqB,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGtB,QAAQ,CAAC,IAAI,CAAC,CAE1EC,SAAS,CAAC,IAAM,CACd,GAAIa,SAAS,CAAE,CACb,KAAM,CAAAS,kBAAkB,CAAGT,SAAS,CAACU,SAAS,CAAG,CAAC,CAC9CC,IAAI,CAACC,KAAK,CAAEZ,SAAS,CAACa,aAAa,CAAGb,SAAS,CAACU,SAAS,CAAI,GAAG,CAAC,CACjE,CAAC,CACLN,WAAW,CAACK,kBAAkB,CAAC,CACjC,CACF,CAAC,CAAE,CAACT,SAAS,CAAC,CAAC,CAEfb,SAAS,CAAC,IAAM,CACd,KAAM,CAAA2B,KAAK,CAAGC,WAAW,CAAC,IAAM,CAC9B,GAAIf,SAAS,SAATA,SAAS,WAATA,SAAS,CAAEgB,SAAS,CAAE,CACxB,KAAM,CAAAC,OAAO,CAAGN,IAAI,CAACO,KAAK,CAAC,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,GAAI,CAAAD,IAAI,CAACnB,SAAS,CAACgB,SAAS,CAAC,CAACK,OAAO,CAAC,CAAC,EAAI,IAAI,CAAC,CACzFf,cAAc,CAACW,OAAO,CAAC,CAEvB;AACA,GAAIjB,SAAS,CAACa,aAAa,CAAG,CAAC,EAAIb,SAAS,CAACU,SAAS,CAAG,CAAC,CAAE,CAC1D,KAAM,CAAAY,IAAI,CAAGtB,SAAS,CAACa,aAAa,CAAGI,OAAO,CAC9C,KAAM,CAAAM,SAAS,CAAG,CAACvB,SAAS,CAACU,SAAS,CAAGV,SAAS,CAACa,aAAa,EAAIS,IAAI,CACxEd,yBAAyB,CAACG,IAAI,CAACa,IAAI,CAACD,SAAS,CAAC,CAAC,CACjD,CACF,CACF,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAME,aAAa,CAACX,KAAK,CAAC,CACnC,CAAC,CAAE,CAACd,SAAS,CAAC,CAAC,CAEf,KAAM,CAAA0B,UAAU,CAAIC,OAAO,EAAK,CAC9B,GAAIA,OAAO,CAAG,EAAE,CAAE,MAAO,GAAGA,OAAO,GAAG,CACtC,KAAM,CAAAC,OAAO,CAAGjB,IAAI,CAACO,KAAK,CAACS,OAAO,CAAG,EAAE,CAAC,CACxC,KAAM,CAAAE,gBAAgB,CAAGF,OAAO,CAAG,EAAE,CACrC,MAAO,GAAGC,OAAO,KAAKC,gBAAgB,GAAG,CAC3C,CAAC,CAED;AACA,KAAM,CAAAC,eAAe,CAAIC,MAAM,EAAK,CAClC,GAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAE,CAC9B,MAAO,CAAAA,MAAM,CACf,CAEA;AACA,KAAM,CAAAC,SAAS,CAAG,CAChB,CAAC,CAAE,SAAS,CACZ,CAAC,CAAE,YAAY,CACf,CAAC,CAAE,WAAW,CACd,CAAC,CAAE,QAAQ,CACX,CAAC,CAAE,WACL,CAAC,CAED,MAAO,CAAAA,SAAS,CAACD,MAAM,CAAC,EAAI,SAAS,CACvC,CAAC,CAED,KAAM,CAAAE,cAAc,CAAIF,MAAM,EAAK,CACjC,KAAM,CAAAG,YAAY,CAAGJ,eAAe,CAACC,MAAM,CAAC,CAC5C,OAAQG,YAAY,EAClB,IAAK,YAAY,CAAE,MAAO,SAAS,CACnC,IAAK,WAAW,CAAE,MAAO,SAAS,CAClC,IAAK,QAAQ,CAAE,MAAO,SAAS,CAC/B,IAAK,WAAW,CAAE,MAAO,SAAS,CAClC,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAED,KAAM,CAAAC,aAAa,CAAIJ,MAAM,EAAK,CAChC,KAAM,CAAAG,YAAY,CAAGJ,eAAe,CAACC,MAAM,CAAC,CAC5C,OAAQG,YAAY,EAClB,IAAK,YAAY,CAAE,MAAO,GAAG,CAC7B,IAAK,WAAW,CAAE,MAAO,GAAG,CAC5B,IAAK,QAAQ,CAAE,MAAO,GAAG,CACzB,IAAK,WAAW,CAAE,MAAO,IAAI,CAC7B,QAAS,MAAO,GAAG,CACrB,CACF,CAAC,CAED,GAAI,CAAClC,SAAS,CAAE,CACd,mBACEX,IAAA,QAAK+C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B9C,KAAA,QAAK6C,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BhD,IAAA,QAAK+C,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvC/C,IAAA,MAAAgD,QAAA,CAAG,oBAAkB,CAAG,CAAC,EACtB,CAAC,CACH,CAAC,CAEV,CAEA,mBACE9C,KAAA,QAAK6C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B9C,KAAA,QAAK6C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BhD,IAAA,OAAAgD,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxB9C,KAAA,QAAK6C,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvB9C,KAAA,SAAM6C,SAAS,CAAC,QAAQ,CAAAC,QAAA,EAAC,UAAQ,CAACrC,SAAS,CAACsC,KAAK,EAAO,CAAC,cACzD/C,KAAA,SACE6C,SAAS,CAAC,YAAY,CACtBG,KAAK,CAAE,CAAEC,KAAK,CAAEP,cAAc,CAACjC,SAAS,CAAC+B,MAAM,CAAE,CAAE,CAAAM,QAAA,EAElDF,aAAa,CAACnC,SAAS,CAAC+B,MAAM,CAAC,CAAC,GAAC,CAACD,eAAe,CAAC9B,SAAS,CAAC+B,MAAM,CAAC,EAChE,CAAC,EACJ,CAAC,EACH,CAAC,cAGNxC,KAAA,QAAK6C,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B9C,KAAA,QAAK6C,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrChD,IAAA,QAAK+C,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BhD,IAAA,QACE+C,SAAS,CAAC,eAAe,CACzBG,KAAK,CAAE,CACLE,KAAK,CAAE,GAAGtC,QAAQ,GAAG,CACrBuC,eAAe,CAAET,cAAc,CAACjC,SAAS,CAAC+B,MAAM,CAClD,CAAE,CACE,CAAC,CACJ,CAAC,cACNxC,KAAA,QAAK6C,SAAS,CAAC,eAAe,CAAAC,QAAA,EAC3BlC,QAAQ,CAAC,YACZ,EAAK,CAAC,EACH,CAAC,cAENZ,KAAA,QAAK6C,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B9C,KAAA,QAAK6C,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BhD,IAAA,SAAM+C,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,cAChD9C,KAAA,SAAM6C,SAAS,CAAC,cAAc,CAAAC,QAAA,EAC3B,EAAA3C,qBAAA,CAAAM,SAAS,CAACa,aAAa,UAAAnB,qBAAA,iBAAvBA,qBAAA,CAAyBiD,cAAc,CAAC,CAAC,GAAI,CAAC,CAAC,KAAG,CAAC,EAAAhD,oBAAA,CAAAK,SAAS,CAACU,SAAS,UAAAf,oBAAA,iBAAnBA,oBAAA,CAAqBgD,cAAc,CAAC,CAAC,GAAI,CAAC,EAC1F,CAAC,EACJ,CAAC,cACNpD,KAAA,QAAK6C,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BhD,IAAA,SAAM+C,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,eAAa,CAAM,CAAC,cACnDhD,IAAA,SAAM+C,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEX,UAAU,CAACrB,WAAW,CAAC,CAAO,CAAC,EAC5D,CAAC,CACLE,sBAAsB,EAAIuB,eAAe,CAAC9B,SAAS,CAAC+B,MAAM,CAAC,GAAK,YAAY,eAC3ExC,KAAA,QAAK6C,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BhD,IAAA,SAAM+C,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,iBAAe,CAAM,CAAC,cACrDhD,IAAA,SAAM+C,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEX,UAAU,CAACnB,sBAAsB,CAAC,CAAO,CAAC,EACvE,CACN,EACE,CAAC,EACH,CAAC,CAGLP,SAAS,CAAC4C,gBAAgB,eACzBrD,KAAA,QAAK6C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChD,IAAA,QAAK+C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACxChD,IAAA,QAAK+C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAErC,SAAS,CAAC4C,gBAAgB,CAAM,CAAC,EAC/D,CACN,cAGDvD,IAAA,QAAK+C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B9C,KAAA,QAAK6C,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9C,KAAA,QAAK6C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChD,IAAA,QAAK+C,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cAClC9C,KAAA,QAAK6C,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BhD,IAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAE,EAAAzC,qBAAA,CAAAI,SAAS,CAAC6C,cAAc,UAAAjD,qBAAA,iBAAxBA,qBAAA,CAA0B+C,cAAc,CAAC,CAAC,GAAI,CAAC,CAAM,CAAC,cACnFtD,IAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,YAAU,CAAK,CAAC,EACzC,CAAC,EACH,CAAC,cAEN9C,KAAA,QAAK6C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BhD,IAAA,QAAK+C,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cAClC9C,KAAA,QAAK6C,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BhD,IAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAE,EAAAxC,qBAAA,CAAAG,SAAS,CAAC8C,UAAU,UAAAjD,qBAAA,iBAApBA,qBAAA,CAAsB8C,cAAc,CAAC,CAAC,GAAI,CAAC,CAAM,CAAC,cAC/EtD,IAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAM,CAAK,CAAC,EACrC,CAAC,EACH,CAAC,cAEN9C,KAAA,QAAK6C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChD,IAAA,QAAK+C,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACnC9C,KAAA,QAAK6C,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BhD,IAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAE,EAAAvC,qBAAA,CAAAE,SAAS,CAAC+C,WAAW,UAAAjD,qBAAA,iBAArBA,qBAAA,CAAuB6C,cAAc,CAAC,CAAC,GAAI,CAAC,CAAM,CAAC,cAChFtD,IAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,SAAO,CAAK,CAAC,EACtC,CAAC,EACH,CAAC,CAELrC,SAAS,CAACgD,WAAW,CAAG,CAAC,eACxBzD,KAAA,QAAK6C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BhD,IAAA,QAAK+C,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACnC9C,KAAA,QAAK6C,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BhD,IAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAE,EAAAtC,qBAAA,CAAAC,SAAS,CAACgD,WAAW,UAAAjD,qBAAA,iBAArBA,qBAAA,CAAuB4C,cAAc,CAAC,CAAC,GAAI,CAAC,CAAM,CAAC,cAChFtD,IAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,SAAO,CAAK,CAAC,EACtC,CAAC,EACH,CACN,EACE,CAAC,CACH,CAAC,CAGLrC,SAAS,CAACiD,MAAM,EAAIjD,SAAS,CAACiD,MAAM,CAACC,MAAM,CAAG,CAAC,eAC9C3D,KAAA,QAAK6C,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BhD,IAAA,OAAAgD,QAAA,CAAI,eAAa,CAAI,CAAC,cACtB9C,KAAA,QAAK6C,SAAS,CAAC,aAAa,CAAAC,QAAA,EACzBrC,SAAS,CAACiD,MAAM,CAACE,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAAClD,KAAK,CAAEmD,KAAK,gBAC7C9D,KAAA,QAAiB6C,SAAS,CAAC,YAAY,CAAAC,QAAA,eACrC9C,KAAA,QAAK6C,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,MAAI,CAACnC,KAAK,CAACoD,SAAS,EAAM,CAAC,cACtDjE,IAAA,QAAK+C,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEnC,KAAK,CAACqD,SAAS,CAAM,CAAC,cACpDlE,IAAA,QAAK+C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEnC,KAAK,CAACsD,YAAY,CAAM,CAAC,GAHjDH,KAIL,CACN,CAAC,CACDrD,SAAS,CAACiD,MAAM,CAACC,MAAM,CAAG,CAAC,eAC1B3D,KAAA,QAAK6C,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAC,GAC1B,CAACrC,SAAS,CAACiD,MAAM,CAACC,MAAM,CAAG,CAAC,CAAC,cAChC,EAAK,CACN,EACE,CAAC,EACH,CACN,cAGD3D,KAAA,QAAK6C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BhD,IAAA,OAAAgD,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvB9C,KAAA,QAAK6C,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B9C,KAAA,QAAK6C,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBhD,IAAA,SAAM+C,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAE,GAAI,CAAAlB,IAAI,CAACnB,SAAS,CAACgB,SAAS,CAAC,CAACyC,kBAAkB,CAAC,CAAC,CAAO,CAAC,cACtFpE,IAAA,SAAM+C,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,gBAAc,CAAM,CAAC,EAChD,CAAC,CACLP,eAAe,CAAC9B,SAAS,CAAC+B,MAAM,CAAC,GAAK,YAAY,eACjDxC,KAAA,QAAK6C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChD,IAAA,SAAM+C,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAE,GAAI,CAAAlB,IAAI,CAAC,CAAC,CAACsC,kBAAkB,CAAC,CAAC,CAAO,CAAC,cACnEpE,IAAA,SAAM+C,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAErC,SAAS,CAAC4C,gBAAgB,EAAI,eAAe,CAAO,CAAC,EACjF,CACN,CACA5C,SAAS,CAAC0D,WAAW,eACpBnE,KAAA,QAAK6C,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBhD,IAAA,SAAM+C,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAE,GAAI,CAAAlB,IAAI,CAACnB,SAAS,CAAC0D,WAAW,CAAC,CAACD,kBAAkB,CAAC,CAAC,CAAO,CAAC,cACxFlE,KAAA,SAAM6C,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAC,SAAO,CAACP,eAAe,CAAC9B,SAAS,CAAC+B,MAAM,CAAC,CAAC4B,WAAW,CAAC,CAAC,EAAO,CAAC,EAC1F,CACN,EACE,CAAC,EACH,CAAC,cAGNpE,KAAA,QAAK6C,SAAS,CAAC,kBAAkB,CAAAC,QAAA,EAC9BP,eAAe,CAAC9B,SAAS,CAAC+B,MAAM,CAAC,GAAK,YAAY,eACjD1C,IAAA,WAAQuE,OAAO,CAAE3D,QAAS,CAACmC,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,eAEvD,CAAQ,CACT,CAEA,CAACrC,SAAS,CAAC+B,MAAM,GAAK,WAAW,EAAI/B,SAAS,CAAC+B,MAAM,GAAK,QAAQ,EAAI/B,SAAS,CAAC+B,MAAM,GAAK,WAAW,gBACrGxC,KAAA,QAAK6C,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjChD,IAAA,QAAK+C,SAAS,CAAE,mBAAmBpC,SAAS,CAAC+B,MAAM,CAAC4B,WAAW,CAAC,CAAC,EAAG,CAAAtB,QAAA,CACjEF,aAAa,CAACnC,SAAS,CAAC+B,MAAM,CAAC,CAC7B,CAAC,cACNxC,KAAA,QAAK6C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAAC,SACxB,CAACrC,SAAS,CAAC+B,MAAM,CAAC4B,WAAW,CAAC,CAAC,CACrC3D,SAAS,CAAC+B,MAAM,GAAK,WAAW,eAC/BxC,KAAA,QAAK6C,SAAS,CAAC,oBAAoB,CAAAC,QAAA,EAAC,yBACX,CAACrC,SAAS,CAAC6C,cAAc,CAAC,UACjD,CAAC7C,SAAS,CAAC8C,UAAU,CAAG,CAAC,EAAI,SAAS9C,SAAS,CAAC8C,UAAU,SAAS,EAChE,CACN,EACE,CAAC,EACH,CACN,EACE,CAAC,CAEL5C,KAAK,eACJX,KAAA,QAAK6C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BhD,IAAA,SAAM+C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cACtChD,IAAA,SAAM+C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEnC,KAAK,CAAO,CAAC,EAC3C,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAV,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}