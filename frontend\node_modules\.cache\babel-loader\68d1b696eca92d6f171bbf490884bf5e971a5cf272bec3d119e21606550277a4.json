{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\DynamicPersonForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DynamicPersonForm = ({\n  onSubmit,\n  onCancel,\n  initialData = null,\n  mode = 'create'\n}) => {\n  _s();\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [firmNatures, setFirmNatures] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedFirmNature, setSelectedFirmNature] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    firmNatures: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Test API connectivity\n    testApiConnectivity();\n\n    // Load initial data if provided\n    if (initialData) {\n      var _initialData$division, _initialData$category, _initialData$subCateg;\n      setFormData(initialData);\n      setSelectedDivision(((_initialData$division = initialData.divisionId) === null || _initialData$division === void 0 ? void 0 : _initialData$division.toString()) || '');\n      setSelectedCategory(((_initialData$category = initialData.categoryId) === null || _initialData$category === void 0 ? void 0 : _initialData$category.toString()) || '');\n      setSelectedSubCategory(((_initialData$subCateg = initialData.subCategoryId) === null || _initialData$subCateg === void 0 ? void 0 : _initialData$subCateg.toString()) || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      // Ensure no duplicate fields in default form\n      if (defaultForm.fields) {\n        defaultForm.fields = deduplicateFields(defaultForm.fields);\n      }\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Test API connectivity\n  const testApiConnectivity = async () => {\n    try {\n      await apiService.getDivisions();\n    } catch (error) {\n      console.error('API connectivity test failed:', error);\n    }\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = fields => {\n    const seen = new Set();\n    const deduplicated = [];\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      } else {\n        console.warn(`DynamicPersonForm: Removing duplicate field: ${field.key}`);\n      }\n    });\n    if (fields.length !== deduplicated.length) {\n      console.log(`DynamicPersonForm: Deduplicated ${fields.length} fields to ${deduplicated.length} unique fields`);\n    }\n    return deduplicated;\n  };\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({\n      ...prev,\n      divisions: true\n    }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({\n        ...prev,\n        divisions: 'Failed to load divisions'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        divisions: false\n      }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async divisionId => {\n    setLoading(prev => ({\n      ...prev,\n      categories: true\n    }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({\n        ...prev,\n        categories: 'Failed to load categories'\n      }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        categories: false\n      }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      subCategories: true\n    }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({\n        ...prev,\n        subCategories: 'Failed to load subcategories'\n      }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        subCategories: false\n      }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        // Deduplicate fields in loaded form\n        if (categoryForm && categoryForm.fields) {\n          categoryForm.fields = deduplicateFields(categoryForm.fields);\n        }\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async subCategoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        // Deduplicate fields in loaded form\n        if (subCategoryForm && subCategoryForm.fields) {\n          subCategoryForm.fields = deduplicateFields(subCategoryForm.fields);\n        }\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = e => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n  const handleCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n  const handleSubCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection (required by backend)\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    } else if (parseInt(selectedDivision) < 1) {\n      newErrors.division = 'Division ID must be a positive number';\n    }\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    } else if (parseInt(selectedCategory) < 1) {\n      newErrors.category = 'Category ID must be a positive number';\n    }\n    if (selectedSubCategory && parseInt(selectedSubCategory) < 1) {\n      newErrors.subCategory = 'SubCategory ID must be a positive number';\n    }\n\n    // Validate required fields that match backend requirements\n    if (!formData.name || formData.name.trim() === '') {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.length > 255) {\n      newErrors.name = 'Name cannot exceed 255 characters';\n    }\n    if (!formData.mobileNumber || formData.mobileNumber.trim() === '') {\n      newErrors.mobileNumber = 'Mobile number is required';\n    } else {\n      // Validate mobile number format (matches backend regex)\n      const mobileRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n      if (!mobileRegex.test(formData.mobileNumber)) {\n        newErrors.mobileNumber = 'Invalid mobile number format';\n      }\n    }\n    if (!formData.nature || formData.nature === '' || formData.nature === '0') {\n      newErrors.nature = 'Nature is required';\n    } else if (![1, 2, 3, 4].includes(parseInt(formData.nature))) {\n      newErrors.nature = 'Invalid nature value';\n    }\n\n    // Check if form is available (should always have default form as fallback)\n    if (!formConfig) {\n      newErrors.form = 'Form configuration not loaded. Please try again.';\n      return {\n        isValid: false,\n        errors: newErrors\n      };\n    }\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n\n      // Required field validation\n      if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        var _field$validation, _field$validation2, _field$validation3;\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if ((_field$validation = field.validation) !== null && _field$validation !== void 0 && _field$validation.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if ((_field$validation2 = field.validation) !== null && _field$validation2 !== void 0 && _field$validation2.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if ((_field$validation3 = field.validation) !== null && _field$validation3 !== void 0 && _field$validation3.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n\n        // Backend-specific field validations\n        if (field.key === 'primaryEmailId' && value.length > 255) {\n          newErrors[field.key] = 'Email cannot exceed 255 characters';\n        }\n        if (field.key === 'workingState' && value.length > 100) {\n          newErrors[field.key] = 'Working state cannot exceed 100 characters';\n        }\n        if (field.key === 'domesticState' && value.length > 100) {\n          newErrors[field.key] = 'Domestic state cannot exceed 100 characters';\n        }\n        if (field.key === 'district' && value.length > 100) {\n          newErrors[field.key] = 'District cannot exceed 100 characters';\n        }\n        if (field.key === 'address' && value.length > 500) {\n          newErrors[field.key] = 'Address cannot exceed 500 characters';\n        }\n        if (field.key === 'workingArea' && value.length > 200) {\n          newErrors[field.key] = 'Working area cannot exceed 200 characters';\n        }\n        if (field.key === 'associateName' && value.length > 255) {\n          newErrors[field.key] = 'Associate name cannot exceed 255 characters';\n        }\n        if (field.key === 'associateRelation' && value.length > 100) {\n          newErrors[field.key] = 'Associate relation cannot exceed 100 characters';\n        }\n        if (field.key === 'reraRegistrationNumber' && value.length > 50) {\n          newErrors[field.key] = 'RERA registration number cannot exceed 50 characters';\n        }\n        if (field.key === 'source' && value.length > 200) {\n          newErrors[field.key] = 'Source cannot exceed 200 characters';\n        }\n        if (field.key === 'remarks' && value.length > 1000) {\n          newErrors[field.key] = 'Remarks cannot exceed 1000 characters';\n        }\n        if (field.key === 'firmName' && value.length > 255) {\n          newErrors[field.key] = 'Firm name cannot exceed 255 characters';\n        }\n        if (field.key === 'authorizedPersonName' && value.length > 255) {\n          newErrors[field.key] = 'Authorized person name cannot exceed 255 characters';\n        }\n        if (field.key === 'authorizedPersonEmail' && value.length > 255) {\n          newErrors[field.key] = 'Authorized person email cannot exceed 255 characters';\n        }\n        if (field.key === 'designation' && value.length > 100) {\n          newErrors[field.key] = 'Designation cannot exceed 100 characters';\n        }\n        if (field.key === 'marketingContact' && value.length > 255) {\n          newErrors[field.key] = 'Marketing contact cannot exceed 255 characters';\n        }\n        if (field.key === 'marketingDesignation' && value.length > 100) {\n          newErrors[field.key] = 'Marketing designation cannot exceed 100 characters';\n        }\n        if (field.key === 'placeOfPosting' && value.length > 200) {\n          newErrors[field.key] = 'Place of posting cannot exceed 200 characters';\n        }\n        if (field.key === 'department' && value.length > 100) {\n          newErrors[field.key] = 'Department cannot exceed 100 characters';\n        }\n      }\n\n      // Validate numeric fields\n      if (field.type === 'number' && value) {\n        const numValue = parseFloat(value);\n        if (field.key === 'starRating' && (numValue < 1 || numValue > 5)) {\n          newErrors[field.key] = 'Star rating must be between 1 and 5';\n        }\n        if (['numberOfOffices', 'numberOfBranches', 'totalEmployeeStrength'].includes(field.key) && numValue < 0) {\n          newErrors[field.key] = `${field.label} must be non-negative`;\n        }\n        if (field.key === 'transactionValue' && numValue < 0) {\n          newErrors[field.key] = 'Transaction value must be non-negative';\n        }\n      }\n\n      // Validate associate mobile number format\n      if (field.key === 'associateMobile' && value && value.trim() !== '') {\n        const mobileRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n        if (!mobileRegex.test(value)) {\n          newErrors[field.key] = 'Invalid associate mobile number format';\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Debug: Basic form data validation\n    console.log('Form submission - validation-sensitive fields:', {\n      primaryEmailId: formData.primaryEmailId,\n      website: formData.website,\n      websiteLink: formData.websiteLink,\n      crmAppLink: formData.crmAppLink,\n      authorizedPersonEmail: formData.authorizedPersonEmail,\n      associateMobile: formData.associateMobile\n    });\n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n    setSubmitting(true);\n    try {\n      // Prepare data for API - match backend model exactly (PascalCase field names)\n      const submitData = {\n        // Required hierarchy fields\n        DivisionId: parseInt(selectedDivision),\n        CategoryId: parseInt(selectedCategory),\n        SubCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n        // Required fields\n        Name: formData.name || '',\n        MobileNumber: formData.mobileNumber || '',\n        Nature: formData.nature ? parseInt(formData.nature) : 1,\n        // Default to Business (1) if not provided\n\n        // Optional enum fields\n        Gender: formData.gender ? parseInt(formData.gender) : null,\n        // Contact Information\n        AlternateNumbers: Array.isArray(formData.alternateNumbers) ? formData.alternateNumbers.filter(num => num && num.trim() !== '') : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()).filter(s => s !== '') : [],\n        AlternateEmailIds: Array.isArray(formData.alternateEmailIds) ? formData.alternateEmailIds.filter(email => email && email.trim() !== '') : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()).filter(s => s !== '') : [],\n        // Note: PrimaryEmailId is added conditionally below only if it has a valid value\n\n        // Personal Information\n        DateOfBirth: formData.dateOfBirth || null,\n        IsMarried: Boolean(formData.isMarried),\n        DateOfMarriage: formData.dateOfMarriage || null,\n        // Location Information\n        WorkingState: formData.workingState || '',\n        DomesticState: formData.domesticState || '',\n        District: formData.district || '',\n        Address: formData.address || '',\n        WorkingArea: formData.workingArea || '',\n        // Associate Information\n        HasAssociate: Boolean(formData.hasAssociate),\n        AssociateName: formData.associateName || '',\n        AssociateRelation: formData.associateRelation || '',\n        // Note: AssociateMobile is added conditionally below only if it has a valid value\n\n        // Digital Presence\n        UsingWebsite: Boolean(formData.usingWebsite),\n        UsingCRMApp: Boolean(formData.usingCRMApp),\n        // Note: Website, WebsiteLink, CRMAppLink are added conditionally below only if they have valid values\n\n        // Business Information\n        TransactionValue: formData.transactionValue ? parseFloat(formData.transactionValue) : null,\n        RERARegistrationNumber: formData.reraRegistrationNumber || '',\n        WorkingProfiles: Array.isArray(formData.workingProfiles) ? formData.workingProfiles.map(wp => parseInt(wp)).filter(wp => !isNaN(wp)) : [],\n        StarRating: formData.starRating ? parseInt(formData.starRating) : null,\n        Source: formData.source || '',\n        Remarks: formData.remarks || '',\n        // Company Information\n        FirmName: formData.firmName || '',\n        NumberOfOffices: formData.numberOfOffices && !isNaN(parseInt(formData.numberOfOffices)) ? parseInt(formData.numberOfOffices) : null,\n        NumberOfBranches: formData.numberOfBranches && !isNaN(parseInt(formData.numberOfBranches)) ? parseInt(formData.numberOfBranches) : null,\n        TotalEmployeeStrength: formData.totalEmployeeStrength && !isNaN(parseInt(formData.totalEmployeeStrength)) ? parseInt(formData.totalEmployeeStrength) : null,\n        // Authorized Person\n        AuthorizedPersonName: formData.authorizedPersonName || '',\n        Designation: formData.designation || '',\n        // Note: AuthorizedPersonEmail is added conditionally below only if it has a valid value\n\n        // Marketing Information\n        MarketingContact: formData.marketingContact || '',\n        MarketingDesignation: formData.marketingDesignation || '',\n        PlaceOfPosting: formData.placeOfPosting || '',\n        Department: formData.department || ''\n      };\n\n      // Only add optional URL and email fields if they have valid values (not empty strings or null)\n      // This prevents backend validation errors for empty strings on URL/Email fields\n\n      // Helper function to check if a field has a valid value\n      // This function is CRITICAL - it must return false for any value that would cause backend validation errors\n      const hasValidValue = value => {\n        return value !== null && value !== undefined && value !== '' && (typeof value === 'string' ? value.trim() !== '' : true);\n      };\n\n      // Debug: Check what values we have for validation-sensitive fields\n      console.log('=== SUBMITDATA CONSTRUCTION DEBUG ===');\n      console.log('Validation-sensitive field values:', {\n        primaryEmailId: formData.primaryEmailId,\n        authorizedPersonEmail: formData.authorizedPersonEmail,\n        website: formData.website,\n        websiteLink: formData.websiteLink,\n        crmAppLink: formData.crmAppLink,\n        associateMobile: formData.associateMobile\n      });\n\n      // Test our hasValidValue function on each field\n      console.log('hasValidValue test results:');\n      console.log('- primaryEmailId:', hasValidValue(formData.primaryEmailId));\n      console.log('- authorizedPersonEmail:', hasValidValue(formData.authorizedPersonEmail));\n      console.log('- website:', hasValidValue(formData.website));\n      console.log('- websiteLink:', hasValidValue(formData.websiteLink));\n      console.log('- crmAppLink:', hasValidValue(formData.crmAppLink));\n      console.log('- associateMobile:', hasValidValue(formData.associateMobile));\n\n      // Email fields - only add if not empty and valid\n      if (hasValidValue(formData.primaryEmailId)) {\n        console.log('Adding PrimaryEmailId:', formData.primaryEmailId);\n        submitData.PrimaryEmailId = formData.primaryEmailId.trim();\n      }\n      if (hasValidValue(formData.authorizedPersonEmail)) {\n        console.log('Adding AuthorizedPersonEmail:', formData.authorizedPersonEmail);\n        submitData.AuthorizedPersonEmail = formData.authorizedPersonEmail.trim();\n      }\n\n      // URL fields - only add if not empty and valid\n      if (hasValidValue(formData.website)) {\n        console.log('Adding Website:', formData.website);\n        submitData.Website = formData.website.trim();\n      }\n      if (hasValidValue(formData.websiteLink)) {\n        console.log('Adding WebsiteLink:', formData.websiteLink);\n        submitData.WebsiteLink = formData.websiteLink.trim();\n      }\n      if (hasValidValue(formData.crmAppLink)) {\n        console.log('Adding CRMAppLink:', formData.crmAppLink);\n        submitData.CRMAppLink = formData.crmAppLink.trim();\n      }\n\n      // Associate mobile - only add if not empty and valid (has regex validation)\n      if (hasValidValue(formData.associateMobile)) {\n        console.log('Adding AssociateMobile:', formData.associateMobile);\n        submitData.AssociateMobile = formData.associateMobile.trim();\n      }\n\n      // Validate required fields before submission\n      const requiredFieldsCheck = {\n        DivisionId: submitData.DivisionId,\n        CategoryId: submitData.CategoryId,\n        Name: submitData.Name,\n        MobileNumber: submitData.MobileNumber,\n        Nature: submitData.Nature\n      };\n\n      // Check for missing required fields\n      const missingFields = Object.entries(requiredFieldsCheck).filter(([, value]) => !value || value === '' || value === null).map(([key]) => key);\n      if (missingFields.length > 0) {\n        setErrors({\n          general: 'Missing required fields: ' + missingFields.join(', ')\n        });\n        return;\n      }\n\n      // FINAL SAFETY CHECK: Remove any validation-sensitive fields that have empty values\n      // This is a bulletproof approach to ensure no empty strings reach the backend\n      const validationSensitiveFields = ['PrimaryEmailId', 'AuthorizedPersonEmail', 'Website', 'WebsiteLink', 'CRMAppLink', 'AssociateMobile'];\n      console.log('=== FINAL SAFETY CHECK ===');\n      console.log('Before cleanup:', Object.keys(submitData));\n      validationSensitiveFields.forEach(fieldName => {\n        if (submitData.hasOwnProperty(fieldName)) {\n          const value = submitData[fieldName];\n          const isEmpty = value === null || value === undefined || value === '' || typeof value === 'string' && value.trim() === '';\n          console.log(`Checking ${fieldName}:`, {\n            value,\n            isEmpty,\n            action: isEmpty ? 'REMOVING' : 'KEEPING'\n          });\n          if (isEmpty) {\n            delete submitData[fieldName];\n            console.log(`🗑️ REMOVED ${fieldName} (was: \"${value}\")`);\n          }\n        }\n      });\n      console.log('After cleanup:', Object.keys(submitData));\n      console.log('Final submitData:', JSON.stringify(submitData, null, 2));\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n      onSubmit(result);\n    } catch (error) {\n      var _error$data, _error$response, _error$response$data, _error$data3, _error$data4, _error$response3, _error$response3$data, _error$response4, _error$response4$data;\n      console.error('Error submitting form:', error);\n      console.error('Error details:', {\n        status: error.status,\n        data: error.data,\n        response: error.response,\n        message: error.message\n      });\n\n      // Enhanced error handling for backend validation errors\n      if ((_error$data = error.data) !== null && _error$data !== void 0 && _error$data.errors || (_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.errors) {\n        var _error$data2, _error$response2, _error$response2$data;\n        // Handle ASP.NET Core ModelState validation errors\n        const validationErrors = ((_error$data2 = error.data) === null || _error$data2 === void 0 ? void 0 : _error$data2.errors) || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.errors);\n        const backendErrors = {};\n        console.log('Validation errors received:', validationErrors);\n        Object.keys(validationErrors).forEach(key => {\n          const errorMessages = validationErrors[key];\n          // Map backend field names to frontend field names for display\n          const frontendFieldName = mapBackendFieldToFrontend(key);\n          backendErrors[frontendFieldName] = Array.isArray(errorMessages) ? errorMessages.join(', ') : errorMessages;\n        });\n        setErrors(backendErrors);\n      } else if ((_error$data3 = error.data) !== null && _error$data3 !== void 0 && _error$data3.title || (_error$data4 = error.data) !== null && _error$data4 !== void 0 && _error$data4.detail || (_error$response3 = error.response) !== null && _error$response3 !== void 0 && (_error$response3$data = _error$response3.data) !== null && _error$response3$data !== void 0 && _error$response3$data.title || (_error$response4 = error.response) !== null && _error$response4 !== void 0 && (_error$response4$data = _error$response4.data) !== null && _error$response4$data !== void 0 && _error$response4$data.detail) {\n        var _error$data5, _error$data6, _error$response5, _error$response5$data, _error$response6, _error$response6$data;\n        // Handle ProblemDetails format errors\n        const errorMessage = ((_error$data5 = error.data) === null || _error$data5 === void 0 ? void 0 : _error$data5.detail) || ((_error$data6 = error.data) === null || _error$data6 === void 0 ? void 0 : _error$data6.title) || ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.title);\n        setErrors({\n          general: errorMessage\n        });\n      } else if (error.isValidationError && error.isValidationError()) {\n        const validationErrors = error.getValidationErrors();\n        console.log('ApiError validation errors:', validationErrors);\n        setErrors(validationErrors);\n      } else {\n        var _error$data7, _error$response7, _error$response7$data, _error$data8;\n        // Handle other error formats\n        const errorMessage = ((_error$data7 = error.data) === null || _error$data7 === void 0 ? void 0 : _error$data7.message) || ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.message) || ((_error$data8 = error.data) === null || _error$data8 === void 0 ? void 0 : _error$data8.title) || error.message || 'An error occurred while saving the person. Please check your input and try again.';\n        setErrors({\n          general: errorMessage\n        });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  // Helper function to map backend field names to frontend field names for error display\n  const mapBackendFieldToFrontend = backendFieldName => {\n    const fieldMapping = {\n      'DivisionId': 'division',\n      'CategoryId': 'category',\n      'SubCategoryId': 'subCategory',\n      'Name': 'name',\n      'MobileNumber': 'mobileNumber',\n      'Nature': 'nature',\n      'Gender': 'gender',\n      'PrimaryEmailId': 'primaryEmailId',\n      'AlternateNumbers': 'alternateNumbers',\n      'AlternateEmailIds': 'alternateEmailIds',\n      'Website': 'website',\n      'DateOfBirth': 'dateOfBirth',\n      'IsMarried': 'isMarried',\n      'DateOfMarriage': 'dateOfMarriage',\n      'WorkingState': 'workingState',\n      'DomesticState': 'domesticState',\n      'District': 'district',\n      'Address': 'address',\n      'WorkingArea': 'workingArea',\n      'HasAssociate': 'hasAssociate',\n      'AssociateName': 'associateName',\n      'AssociateRelation': 'associateRelation',\n      'AssociateMobile': 'associateMobile',\n      'UsingWebsite': 'usingWebsite',\n      'WebsiteLink': 'websiteLink',\n      'UsingCRMApp': 'usingCRMApp',\n      'CRMAppLink': 'crmAppLink',\n      'TransactionValue': 'transactionValue',\n      'RERARegistrationNumber': 'reraRegistrationNumber',\n      'WorkingProfiles': 'workingProfiles',\n      'StarRating': 'starRating',\n      'Source': 'source',\n      'Remarks': 'remarks',\n      'FirmName': 'firmName',\n      'NumberOfOffices': 'numberOfOffices',\n      'NumberOfBranches': 'numberOfBranches',\n      'TotalEmployeeStrength': 'totalEmployeeStrength',\n      'AuthorizedPersonName': 'authorizedPersonName',\n      'AuthorizedPersonEmail': 'authorizedPersonEmail',\n      'Designation': 'designation',\n      'MarketingContact': 'marketingContact',\n      'MarketingDesignation': 'marketingDesignation',\n      'PlaceOfPosting': 'placeOfPosting',\n      'Department': 'department'\n    };\n    return fieldMapping[backendFieldName] || backendFieldName.toLowerCase();\n  };\n  const shouldShowField = field => {\n    if (!field.conditional) return true;\n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n\n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    return conditionValue === expectedValue;\n  };\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n    const sections = {};\n    formConfig.fields.forEach(field => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    return sections;\n  };\n  const getSectionTitle = sectionKey => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n  const sections = formConfig ? groupFieldsBySections() : {};\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dynamic-person-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: mode === 'create' ? 'Create New Person' : 'Edit Person'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 926,\n        columnNumber: 9\n      }, this), formConfig && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-name\",\n          children: formConfig.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 929,\n          columnNumber: 13\n        }, this), formConfig.description && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-description\",\n          children: formConfig.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 931,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 928,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 925,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 938,\n      columnNumber: 9\n    }, this), Object.keys(errors).length > 0 && !errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Please fix the following errors:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 944,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        style: {\n          margin: '8px 0 0 20px'\n        },\n        children: Object.entries(errors).filter(([key]) => key !== 'general').map(([key, message]) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: message\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 949,\n          columnNumber: 17\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 945,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 943,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"person-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Division & Category Selection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 958,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Division \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 962,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedDivision,\n            onChange: handleDivisionChange,\n            disabled: loading.divisions,\n            className: `form-select ${errors.division ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: loading.divisions ? 'Loading divisions...' : 'Select Division'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 972,\n              columnNumber: 15\n            }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: division.id,\n              children: division.name\n            }, division.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 13\n          }, this), errors.division && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.division\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 982,\n            columnNumber: 15\n          }, this), errors.divisions && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.divisions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 961,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Category \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 992,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 991,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: handleCategoryChange,\n            disabled: !selectedDivision || loading.categories,\n            className: `form-select ${errors.category ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedDivision ? 'Select Division first' : loading.categories ? 'Loading categories...' : 'Select Category'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 15\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category.id,\n              children: category.name\n            }, category.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1010,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 994,\n            columnNumber: 13\n          }, this), errors.category && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1016,\n            columnNumber: 15\n          }, this), errors.categories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.categories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1019,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 990,\n          columnNumber: 11\n        }, this), formAvailability.showSubCategoryDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Subcategory (Optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1026,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedSubCategory,\n            onChange: handleSubCategoryChange,\n            disabled: !selectedCategory || loading.subCategories,\n            className: \"form-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedCategory ? 'Select Category first' : loading.subCategories ? 'Loading subcategories...' : 'Select Subcategory (Optional)'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1035,\n              columnNumber: 17\n            }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: subCategory.id,\n              children: subCategory.name\n            }, subCategory.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1044,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1029,\n            columnNumber: 15\n          }, this), errors.subCategories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.subCategories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1050,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1025,\n          columnNumber: 13\n        }, this), loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-message\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Loading form configuration...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1058,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1057,\n          columnNumber: 13\n        }, this), formAvailability.message && !loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `status-message ${formAvailability.categoryHasForm || formAvailability.subCategoryHasForm ? 'success' : formAvailability.message.includes('No form') ? 'error' : 'info'}`,\n          children: formAvailability.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1063,\n          columnNumber: 13\n        }, this), errors.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: errors.form\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1075,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 957,\n        columnNumber: 9\n      }, this), formConfig && Object.entries(sections).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: section.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1082,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-fields\",\n          children: section.fields.filter(field => shouldShowField(field)).map((field, fieldIndex) => /*#__PURE__*/_jsxDEV(FormField, {\n            field: field,\n            value: formData[field.key],\n            onChange: value => handleFieldChange(field.key, value),\n            error: errors[field.key]\n          }, `${sectionKey}-${field.key}-${fieldIndex}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1087,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1083,\n          columnNumber: 13\n        }, this)]\n      }, sectionKey, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1081,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          disabled: submitting,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          disabled: submitting || !selectedDivision || !selectedCategory,\n          children: submitting ? mode === 'create' ? 'Creating...' : 'Updating...' : mode === 'create' ? 'Create Person' : 'Update Person'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 955,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 924,\n    columnNumber: 5\n  }, this);\n};\n_s(DynamicPersonForm, \"c4LNu3gpCd1dq3KphxhXeeay2EQ=\");\n_c = DynamicPersonForm;\nexport default DynamicPersonForm;\nvar _c;\n$RefreshReg$(_c, \"DynamicPersonForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PersonNatureLabels", "<PERSON><PERSON><PERSON><PERSON>", "WorkingProfileLabels", "getDefaultPersonData", "formConfigService", "apiService", "FormField", "jsxDEV", "_jsxDEV", "DynamicPersonForm", "onSubmit", "onCancel", "initialData", "mode", "_s", "divisions", "setDivisions", "categories", "setCategories", "firmNatures", "setFirmNatures", "selectedDivision", "setSelectedDivision", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedFirmNature", "setSelectedFirmNature", "formConfig", "setFormConfig", "formData", "setFormData", "errors", "setErrors", "loading", "setLoading", "form", "submitting", "setSubmitting", "formAvailability", "setFormAvailability", "categoryHasForm", "subCategoryHasForm", "showSubCategoryDropdown", "message", "loadDivisions", "testApiConnectivity", "_initialData$division", "_initialData$category", "_initialData$subCateg", "divisionId", "toString", "categoryId", "setSelectedSubCategory", "subCategoryId", "defaultForm", "getDefaultFormConfig", "fields", "deduplicateFields", "getDivisions", "error", "console", "seen", "Set", "deduplicated", "for<PERSON>ach", "field", "has", "key", "add", "push", "warn", "length", "log", "loadCategories", "setSubCategories", "handleCategorySelection", "selectedSubCategory", "handleSubCategorySelection", "prev", "response", "data", "getCategoriesByDivision", "loadSubCategories", "subCategories", "getSubCategoriesByCategory", "hasFormForCategory", "parseInt", "categoryForm", "loadFormConfig", "hasFormForSubCategory", "subCategoryForm", "handleDivisionChange", "e", "value", "target", "handleCategoryChange", "handleSubCategoryChange", "handleFieldChange", "<PERSON><PERSON><PERSON>", "validateForm", "newErrors", "division", "category", "subCategory", "name", "trim", "mobileNumber", "mobileRegex", "test", "nature", "includes", "<PERSON><PERSON><PERSON><PERSON>", "required", "label", "conditional", "shouldShowField", "_field$validation", "_field$validation2", "_field$validation3", "type", "emailRegex", "phoneRegex", "URL", "validation", "numValue", "parseFloat", "min", "undefined", "max", "pattern", "regex", "RegExp", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dateOfMarriage", "dateOfBirth", "birthDate", "Date", "marriageDate", "Object", "keys", "handleSubmit", "preventDefault", "primaryEmailId", "website", "websiteLink", "crmAppLink", "authorizedPersonEmail", "associate<PERSON><PERSON><PERSON>", "submitData", "DivisionId", "CategoryId", "SubCategoryId", "Name", "MobileNumber", "Nature", "Gender", "gender", "AlternateNumbers", "Array", "isArray", "alternateNumbers", "filter", "num", "split", "map", "s", "AlternateEmailIds", "alternateEmailIds", "email", "DateOfBirth", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "DateOfMarriage", "WorkingState", "workingState", "DomesticState", "domesticState", "District", "district", "Address", "address", "WorkingArea", "workingArea", "HasAssociate", "hasAssociate", "<PERSON><PERSON><PERSON>", "associate<PERSON><PERSON>", "AssociateRelation", "associateRelation", "UsingWebsite", "usingWebsite", "UsingCRMApp", "usingCRMApp", "TransactionValue", "transactionValue", "RERARegistrationNumber", "reraRegistrationNumber", "WorkingProfiles", "workingProfiles", "wp", "isNaN", "StarRating", "starRating", "Source", "source", "Remarks", "remarks", "FirmName", "firmName", "NumberOfOffices", "numberOfOffices", "NumberOfBranches", "numberOfBranches", "TotalEmployeeStrength", "totalEmployeeStrength", "AuthorizedPersonName", "authorizedPersonName", "Designation", "designation", "MarketingContact", "marketingContact", "MarketingDesignation", "marketingDesignation", "PlaceOfPosting", "placeOfPosting", "Department", "department", "hasValidValue", "PrimaryEmailId", "AuthorizedPersonEmail", "Website", "WebsiteLink", "CRMAppLink", "AssociateMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingFields", "entries", "general", "join", "validationSensitiveFields", "fieldName", "hasOwnProperty", "isEmpty", "action", "JSON", "stringify", "result", "create<PERSON>erson", "update<PERSON><PERSON>", "id", "_error$data", "_error$response", "_error$response$data", "_error$data3", "_error$data4", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data", "status", "_error$data2", "_error$response2", "_error$response2$data", "validationErrors", "backendErrors", "errorMessages", "frontendFieldName", "mapBackendFieldToFrontend", "title", "detail", "_error$data5", "_error$data6", "_error$response5", "_error$response5$data", "_error$response6", "_error$response6$data", "errorMessage", "isValidationError", "getValidationErrors", "_error$data7", "_error$response7", "_error$response7$data", "_error$data8", "backendFieldName", "fieldMapping", "toLowerCase", "conditionValue", "expectedValue", "groupFieldsBySections", "sections", "sectionKey", "section", "getSectionTitle", "titles", "personalInfo", "contactInfo", "locationInfo", "businessInfo", "associateInfo", "digitalPresence", "companyInfo", "<PERSON><PERSON><PERSON>", "marketingInfo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "style", "margin", "onChange", "disabled", "fieldIndex", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/DynamicPersonForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\n\nconst DynamicPersonForm = ({ onSubmit, onCancel, initialData = null, mode = 'create' }) => {\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [firmNatures, setFirmNatures] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedFirmNature, setSelectedFirmNature] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    firmNatures: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Test API connectivity\n    testApiConnectivity();\n\n    // Load initial data if provided\n    if (initialData) {\n      setFormData(initialData);\n      setSelectedDivision(initialData.divisionId?.toString() || '');\n      setSelectedCategory(initialData.categoryId?.toString() || '');\n      setSelectedSubCategory(initialData.subCategoryId?.toString() || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      // Ensure no duplicate fields in default form\n      if (defaultForm.fields) {\n        defaultForm.fields = deduplicateFields(defaultForm.fields);\n      }\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Test API connectivity\n  const testApiConnectivity = async () => {\n    try {\n      await apiService.getDivisions();\n    } catch (error) {\n      console.error('API connectivity test failed:', error);\n    }\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = (fields) => {\n    const seen = new Set();\n    const deduplicated = [];\n\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      } else {\n        console.warn(`DynamicPersonForm: Removing duplicate field: ${field.key}`);\n      }\n    });\n\n    if (fields.length !== deduplicated.length) {\n      console.log(`DynamicPersonForm: Deduplicated ${fields.length} fields to ${deduplicated.length} unique fields`);\n    }\n\n    return deduplicated;\n  };\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({ ...prev, divisions: 'Failed to load divisions' }));\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async (divisionId) => {\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({ ...prev, categories: 'Failed to load categories' }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async (categoryId) => {\n    setLoading(prev => ({ ...prev, subCategories: true }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({ ...prev, subCategories: 'Failed to load subcategories' }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, subCategories: false }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async (categoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        // Deduplicate fields in loaded form\n        if (categoryForm && categoryForm.fields) {\n          categoryForm.fields = deduplicateFields(categoryForm.fields);\n        }\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async (subCategoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        // Deduplicate fields in loaded form\n        if (subCategoryForm && subCategoryForm.fields) {\n          subCategoryForm.fields = deduplicateFields(subCategoryForm.fields);\n        }\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = (e) => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleSubCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection (required by backend)\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    } else if (parseInt(selectedDivision) < 1) {\n      newErrors.division = 'Division ID must be a positive number';\n    }\n\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    } else if (parseInt(selectedCategory) < 1) {\n      newErrors.category = 'Category ID must be a positive number';\n    }\n\n    if (selectedSubCategory && parseInt(selectedSubCategory) < 1) {\n      newErrors.subCategory = 'SubCategory ID must be a positive number';\n    }\n\n    // Validate required fields that match backend requirements\n    if (!formData.name || formData.name.trim() === '') {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.length > 255) {\n      newErrors.name = 'Name cannot exceed 255 characters';\n    }\n\n    if (!formData.mobileNumber || formData.mobileNumber.trim() === '') {\n      newErrors.mobileNumber = 'Mobile number is required';\n    } else {\n      // Validate mobile number format (matches backend regex)\n      const mobileRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n      if (!mobileRegex.test(formData.mobileNumber)) {\n        newErrors.mobileNumber = 'Invalid mobile number format';\n      }\n    }\n\n    if (!formData.nature || formData.nature === '' || formData.nature === '0') {\n      newErrors.nature = 'Nature is required';\n    } else if (![1, 2, 3, 4].includes(parseInt(formData.nature))) {\n      newErrors.nature = 'Invalid nature value';\n    }\n\n    // Check if form is available (should always have default form as fallback)\n    if (!formConfig) {\n      newErrors.form = 'Form configuration not loaded. Please try again.';\n      return { isValid: false, errors: newErrors };\n    }\n\n\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n      \n      // Required field validation\n      if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if (field.validation?.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if (field.validation?.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if (field.validation?.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n\n        // Backend-specific field validations\n        if (field.key === 'primaryEmailId' && value.length > 255) {\n          newErrors[field.key] = 'Email cannot exceed 255 characters';\n        }\n        if (field.key === 'workingState' && value.length > 100) {\n          newErrors[field.key] = 'Working state cannot exceed 100 characters';\n        }\n        if (field.key === 'domesticState' && value.length > 100) {\n          newErrors[field.key] = 'Domestic state cannot exceed 100 characters';\n        }\n        if (field.key === 'district' && value.length > 100) {\n          newErrors[field.key] = 'District cannot exceed 100 characters';\n        }\n        if (field.key === 'address' && value.length > 500) {\n          newErrors[field.key] = 'Address cannot exceed 500 characters';\n        }\n        if (field.key === 'workingArea' && value.length > 200) {\n          newErrors[field.key] = 'Working area cannot exceed 200 characters';\n        }\n        if (field.key === 'associateName' && value.length > 255) {\n          newErrors[field.key] = 'Associate name cannot exceed 255 characters';\n        }\n        if (field.key === 'associateRelation' && value.length > 100) {\n          newErrors[field.key] = 'Associate relation cannot exceed 100 characters';\n        }\n        if (field.key === 'reraRegistrationNumber' && value.length > 50) {\n          newErrors[field.key] = 'RERA registration number cannot exceed 50 characters';\n        }\n        if (field.key === 'source' && value.length > 200) {\n          newErrors[field.key] = 'Source cannot exceed 200 characters';\n        }\n        if (field.key === 'remarks' && value.length > 1000) {\n          newErrors[field.key] = 'Remarks cannot exceed 1000 characters';\n        }\n        if (field.key === 'firmName' && value.length > 255) {\n          newErrors[field.key] = 'Firm name cannot exceed 255 characters';\n        }\n        if (field.key === 'authorizedPersonName' && value.length > 255) {\n          newErrors[field.key] = 'Authorized person name cannot exceed 255 characters';\n        }\n        if (field.key === 'authorizedPersonEmail' && value.length > 255) {\n          newErrors[field.key] = 'Authorized person email cannot exceed 255 characters';\n        }\n        if (field.key === 'designation' && value.length > 100) {\n          newErrors[field.key] = 'Designation cannot exceed 100 characters';\n        }\n        if (field.key === 'marketingContact' && value.length > 255) {\n          newErrors[field.key] = 'Marketing contact cannot exceed 255 characters';\n        }\n        if (field.key === 'marketingDesignation' && value.length > 100) {\n          newErrors[field.key] = 'Marketing designation cannot exceed 100 characters';\n        }\n        if (field.key === 'placeOfPosting' && value.length > 200) {\n          newErrors[field.key] = 'Place of posting cannot exceed 200 characters';\n        }\n        if (field.key === 'department' && value.length > 100) {\n          newErrors[field.key] = 'Department cannot exceed 100 characters';\n        }\n      }\n\n      // Validate numeric fields\n      if (field.type === 'number' && value) {\n        const numValue = parseFloat(value);\n        if (field.key === 'starRating' && (numValue < 1 || numValue > 5)) {\n          newErrors[field.key] = 'Star rating must be between 1 and 5';\n        }\n        if (['numberOfOffices', 'numberOfBranches', 'totalEmployeeStrength'].includes(field.key) && numValue < 0) {\n          newErrors[field.key] = `${field.label} must be non-negative`;\n        }\n        if (field.key === 'transactionValue' && numValue < 0) {\n          newErrors[field.key] = 'Transaction value must be non-negative';\n        }\n      }\n\n      // Validate associate mobile number format\n      if (field.key === 'associateMobile' && value && value.trim() !== '') {\n        const mobileRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n        if (!mobileRegex.test(value)) {\n          newErrors[field.key] = 'Invalid associate mobile number format';\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    // Debug: Basic form data validation\n    console.log('Form submission - validation-sensitive fields:', {\n      primaryEmailId: formData.primaryEmailId,\n      website: formData.website,\n      websiteLink: formData.websiteLink,\n      crmAppLink: formData.crmAppLink,\n      authorizedPersonEmail: formData.authorizedPersonEmail,\n      associateMobile: formData.associateMobile\n    });\n\n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSubmitting(true);\n    try {\n      // Prepare data for API - match backend model exactly (PascalCase field names)\n      const submitData = {\n        // Required hierarchy fields\n        DivisionId: parseInt(selectedDivision),\n        CategoryId: parseInt(selectedCategory),\n        SubCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n\n        // Required fields\n        Name: formData.name || '',\n        MobileNumber: formData.mobileNumber || '',\n        Nature: formData.nature ? parseInt(formData.nature) : 1, // Default to Business (1) if not provided\n\n        // Optional enum fields\n        Gender: formData.gender ? parseInt(formData.gender) : null,\n\n        // Contact Information\n        AlternateNumbers: Array.isArray(formData.alternateNumbers)\n          ? formData.alternateNumbers.filter(num => num && num.trim() !== '')\n          : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()).filter(s => s !== '') : [],\n        AlternateEmailIds: Array.isArray(formData.alternateEmailIds)\n          ? formData.alternateEmailIds.filter(email => email && email.trim() !== '')\n          : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()).filter(s => s !== '') : [],\n        // Note: PrimaryEmailId is added conditionally below only if it has a valid value\n\n        // Personal Information\n        DateOfBirth: formData.dateOfBirth || null,\n        IsMarried: Boolean(formData.isMarried),\n        DateOfMarriage: formData.dateOfMarriage || null,\n\n        // Location Information\n        WorkingState: formData.workingState || '',\n        DomesticState: formData.domesticState || '',\n        District: formData.district || '',\n        Address: formData.address || '',\n        WorkingArea: formData.workingArea || '',\n\n        // Associate Information\n        HasAssociate: Boolean(formData.hasAssociate),\n        AssociateName: formData.associateName || '',\n        AssociateRelation: formData.associateRelation || '',\n        // Note: AssociateMobile is added conditionally below only if it has a valid value\n\n        // Digital Presence\n        UsingWebsite: Boolean(formData.usingWebsite),\n        UsingCRMApp: Boolean(formData.usingCRMApp),\n        // Note: Website, WebsiteLink, CRMAppLink are added conditionally below only if they have valid values\n\n        // Business Information\n        TransactionValue: formData.transactionValue ? parseFloat(formData.transactionValue) : null,\n        RERARegistrationNumber: formData.reraRegistrationNumber || '',\n        WorkingProfiles: Array.isArray(formData.workingProfiles)\n          ? formData.workingProfiles.map(wp => parseInt(wp)).filter(wp => !isNaN(wp))\n          : [],\n        StarRating: formData.starRating ? parseInt(formData.starRating) : null,\n        Source: formData.source || '',\n        Remarks: formData.remarks || '',\n\n        // Company Information\n        FirmName: formData.firmName || '',\n        NumberOfOffices: formData.numberOfOffices && !isNaN(parseInt(formData.numberOfOffices)) ? parseInt(formData.numberOfOffices) : null,\n        NumberOfBranches: formData.numberOfBranches && !isNaN(parseInt(formData.numberOfBranches)) ? parseInt(formData.numberOfBranches) : null,\n        TotalEmployeeStrength: formData.totalEmployeeStrength && !isNaN(parseInt(formData.totalEmployeeStrength)) ? parseInt(formData.totalEmployeeStrength) : null,\n\n        // Authorized Person\n        AuthorizedPersonName: formData.authorizedPersonName || '',\n        Designation: formData.designation || '',\n        // Note: AuthorizedPersonEmail is added conditionally below only if it has a valid value\n\n        // Marketing Information\n        MarketingContact: formData.marketingContact || '',\n        MarketingDesignation: formData.marketingDesignation || '',\n        PlaceOfPosting: formData.placeOfPosting || '',\n        Department: formData.department || ''\n      };\n\n      // Only add optional URL and email fields if they have valid values (not empty strings or null)\n      // This prevents backend validation errors for empty strings on URL/Email fields\n\n      // Helper function to check if a field has a valid value\n      // This function is CRITICAL - it must return false for any value that would cause backend validation errors\n      const hasValidValue = (value) => {\n        return value !== null && value !== undefined && value !== '' &&\n               (typeof value === 'string' ? value.trim() !== '' : true);\n      };\n\n      // Debug: Check what values we have for validation-sensitive fields\n      console.log('=== SUBMITDATA CONSTRUCTION DEBUG ===');\n      console.log('Validation-sensitive field values:', {\n        primaryEmailId: formData.primaryEmailId,\n        authorizedPersonEmail: formData.authorizedPersonEmail,\n        website: formData.website,\n        websiteLink: formData.websiteLink,\n        crmAppLink: formData.crmAppLink,\n        associateMobile: formData.associateMobile\n      });\n\n      // Test our hasValidValue function on each field\n      console.log('hasValidValue test results:');\n      console.log('- primaryEmailId:', hasValidValue(formData.primaryEmailId));\n      console.log('- authorizedPersonEmail:', hasValidValue(formData.authorizedPersonEmail));\n      console.log('- website:', hasValidValue(formData.website));\n      console.log('- websiteLink:', hasValidValue(formData.websiteLink));\n      console.log('- crmAppLink:', hasValidValue(formData.crmAppLink));\n      console.log('- associateMobile:', hasValidValue(formData.associateMobile));\n\n      // Email fields - only add if not empty and valid\n      if (hasValidValue(formData.primaryEmailId)) {\n        console.log('Adding PrimaryEmailId:', formData.primaryEmailId);\n        submitData.PrimaryEmailId = formData.primaryEmailId.trim();\n      }\n\n      if (hasValidValue(formData.authorizedPersonEmail)) {\n        console.log('Adding AuthorizedPersonEmail:', formData.authorizedPersonEmail);\n        submitData.AuthorizedPersonEmail = formData.authorizedPersonEmail.trim();\n      }\n\n      // URL fields - only add if not empty and valid\n      if (hasValidValue(formData.website)) {\n        console.log('Adding Website:', formData.website);\n        submitData.Website = formData.website.trim();\n      }\n\n      if (hasValidValue(formData.websiteLink)) {\n        console.log('Adding WebsiteLink:', formData.websiteLink);\n        submitData.WebsiteLink = formData.websiteLink.trim();\n      }\n\n      if (hasValidValue(formData.crmAppLink)) {\n        console.log('Adding CRMAppLink:', formData.crmAppLink);\n        submitData.CRMAppLink = formData.crmAppLink.trim();\n      }\n\n      // Associate mobile - only add if not empty and valid (has regex validation)\n      if (hasValidValue(formData.associateMobile)) {\n        console.log('Adding AssociateMobile:', formData.associateMobile);\n        submitData.AssociateMobile = formData.associateMobile.trim();\n      }\n\n\n\n      // Validate required fields before submission\n      const requiredFieldsCheck = {\n        DivisionId: submitData.DivisionId,\n        CategoryId: submitData.CategoryId,\n        Name: submitData.Name,\n        MobileNumber: submitData.MobileNumber,\n        Nature: submitData.Nature\n      };\n\n      // Check for missing required fields\n      const missingFields = Object.entries(requiredFieldsCheck)\n        .filter(([, value]) => !value || value === '' || value === null)\n        .map(([key]) => key);\n\n      if (missingFields.length > 0) {\n        setErrors({ general: 'Missing required fields: ' + missingFields.join(', ') });\n        return;\n      }\n\n      // FINAL SAFETY CHECK: Remove any validation-sensitive fields that have empty values\n      // This is a bulletproof approach to ensure no empty strings reach the backend\n      const validationSensitiveFields = [\n        'PrimaryEmailId', 'AuthorizedPersonEmail', 'Website', 'WebsiteLink',\n        'CRMAppLink', 'AssociateMobile'\n      ];\n\n      console.log('=== FINAL SAFETY CHECK ===');\n      console.log('Before cleanup:', Object.keys(submitData));\n\n      validationSensitiveFields.forEach(fieldName => {\n        if (submitData.hasOwnProperty(fieldName)) {\n          const value = submitData[fieldName];\n          const isEmpty = value === null || value === undefined || value === '' ||\n                         (typeof value === 'string' && value.trim() === '');\n\n          console.log(`Checking ${fieldName}:`, {\n            value,\n            isEmpty,\n            action: isEmpty ? 'REMOVING' : 'KEEPING'\n          });\n\n          if (isEmpty) {\n            delete submitData[fieldName];\n            console.log(`🗑️ REMOVED ${fieldName} (was: \"${value}\")`);\n          }\n        }\n      });\n\n      console.log('After cleanup:', Object.keys(submitData));\n      console.log('Final submitData:', JSON.stringify(submitData, null, 2));\n\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n\n      onSubmit(result);\n    } catch (error) {\n      console.error('Error submitting form:', error);\n      console.error('Error details:', {\n        status: error.status,\n        data: error.data,\n        response: error.response,\n        message: error.message\n      });\n\n      // Enhanced error handling for backend validation errors\n      if (error.data?.errors || error.response?.data?.errors) {\n        // Handle ASP.NET Core ModelState validation errors\n        const validationErrors = error.data?.errors || error.response?.data?.errors;\n        const backendErrors = {};\n\n        console.log('Validation errors received:', validationErrors);\n\n        Object.keys(validationErrors).forEach(key => {\n          const errorMessages = validationErrors[key];\n          // Map backend field names to frontend field names for display\n          const frontendFieldName = mapBackendFieldToFrontend(key);\n          backendErrors[frontendFieldName] = Array.isArray(errorMessages)\n            ? errorMessages.join(', ')\n            : errorMessages;\n        });\n        setErrors(backendErrors);\n      } else if (error.data?.title || error.data?.detail || error.response?.data?.title || error.response?.data?.detail) {\n        // Handle ProblemDetails format errors\n        const errorMessage = error.data?.detail || error.data?.title || error.response?.data?.detail || error.response?.data?.title;\n        setErrors({ general: errorMessage });\n      } else if (error.isValidationError && error.isValidationError()) {\n        const validationErrors = error.getValidationErrors();\n        console.log('ApiError validation errors:', validationErrors);\n        setErrors(validationErrors);\n      } else {\n        // Handle other error formats\n        const errorMessage = error.data?.message ||\n                           error.response?.data?.message ||\n                           error.data?.title ||\n                           error.message ||\n                           'An error occurred while saving the person. Please check your input and try again.';\n        setErrors({ general: errorMessage });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  // Helper function to map backend field names to frontend field names for error display\n  const mapBackendFieldToFrontend = (backendFieldName) => {\n    const fieldMapping = {\n      'DivisionId': 'division',\n      'CategoryId': 'category',\n      'SubCategoryId': 'subCategory',\n      'Name': 'name',\n      'MobileNumber': 'mobileNumber',\n      'Nature': 'nature',\n      'Gender': 'gender',\n      'PrimaryEmailId': 'primaryEmailId',\n      'AlternateNumbers': 'alternateNumbers',\n      'AlternateEmailIds': 'alternateEmailIds',\n      'Website': 'website',\n      'DateOfBirth': 'dateOfBirth',\n      'IsMarried': 'isMarried',\n      'DateOfMarriage': 'dateOfMarriage',\n      'WorkingState': 'workingState',\n      'DomesticState': 'domesticState',\n      'District': 'district',\n      'Address': 'address',\n      'WorkingArea': 'workingArea',\n      'HasAssociate': 'hasAssociate',\n      'AssociateName': 'associateName',\n      'AssociateRelation': 'associateRelation',\n      'AssociateMobile': 'associateMobile',\n      'UsingWebsite': 'usingWebsite',\n      'WebsiteLink': 'websiteLink',\n      'UsingCRMApp': 'usingCRMApp',\n      'CRMAppLink': 'crmAppLink',\n      'TransactionValue': 'transactionValue',\n      'RERARegistrationNumber': 'reraRegistrationNumber',\n      'WorkingProfiles': 'workingProfiles',\n      'StarRating': 'starRating',\n      'Source': 'source',\n      'Remarks': 'remarks',\n      'FirmName': 'firmName',\n      'NumberOfOffices': 'numberOfOffices',\n      'NumberOfBranches': 'numberOfBranches',\n      'TotalEmployeeStrength': 'totalEmployeeStrength',\n      'AuthorizedPersonName': 'authorizedPersonName',\n      'AuthorizedPersonEmail': 'authorizedPersonEmail',\n      'Designation': 'designation',\n      'MarketingContact': 'marketingContact',\n      'MarketingDesignation': 'marketingDesignation',\n      'PlaceOfPosting': 'placeOfPosting',\n      'Department': 'department'\n    };\n\n    return fieldMapping[backendFieldName] || backendFieldName.toLowerCase();\n  };\n\n  const shouldShowField = (field) => {\n    if (!field.conditional) return true;\n\n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n\n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n\n    return conditionValue === expectedValue;\n  };\n\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n\n    const sections = {};\n    formConfig.fields.forEach((field) => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n\n    return sections;\n  };\n\n  const getSectionTitle = (sectionKey) => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n\n  const sections = formConfig ? groupFieldsBySections() : {};\n\n\n\n  return (\n    <div className=\"dynamic-person-form\">\n      <div className=\"form-header\">\n        <h2>{mode === 'create' ? 'Create New Person' : 'Edit Person'}</h2>\n        {formConfig && (\n          <div className=\"form-info\">\n            <span className=\"form-name\">{formConfig.name}</span>\n            {formConfig.description && (\n              <span className=\"form-description\">{formConfig.description}</span>\n            )}\n          </div>\n        )}\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      {/* Display validation error summary */}\n      {Object.keys(errors).length > 0 && !errors.general && (\n        <div className=\"alert alert-error\">\n          <strong>Please fix the following errors:</strong>\n          <ul style={{ margin: '8px 0 0 20px' }}>\n            {Object.entries(errors)\n              .filter(([key]) => key !== 'general')\n              .map(([key, message]) => (\n                <li key={key}>{message}</li>\n              ))}\n          </ul>\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit} className=\"person-form\">\n        {/* Hierarchical Dropdowns */}\n        <div className=\"form-section\">\n          <h3>Division & Category Selection</h3>\n\n          {/* Division Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Division <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedDivision}\n              onChange={handleDivisionChange}\n              disabled={loading.divisions}\n              className={`form-select ${errors.division ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {loading.divisions ? 'Loading divisions...' : 'Select Division'}\n              </option>\n              {divisions.map(division => (\n                <option key={division.id} value={division.id}>\n                  {division.name}\n                </option>\n              ))}\n            </select>\n            {errors.division && (\n              <div className=\"error-message\">{errors.division}</div>\n            )}\n            {errors.divisions && (\n              <div className=\"error-message\">{errors.divisions}</div>\n            )}\n          </div>\n\n          {/* Category Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Category <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedCategory}\n              onChange={handleCategoryChange}\n              disabled={!selectedDivision || loading.categories}\n              className={`form-select ${errors.category ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {!selectedDivision\n                  ? 'Select Division first'\n                  : loading.categories\n                    ? 'Loading categories...'\n                    : 'Select Category'\n                }\n              </option>\n              {categories.map(category => (\n                <option key={category.id} value={category.id}>\n                  {category.name}\n                </option>\n              ))}\n            </select>\n            {errors.category && (\n              <div className=\"error-message\">{errors.category}</div>\n            )}\n            {errors.categories && (\n              <div className=\"error-message\">{errors.categories}</div>\n            )}\n          </div>\n\n          {/* Subcategory Dropdown - Only show if category doesn't have a form */}\n          {formAvailability.showSubCategoryDropdown && (\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                Subcategory (Optional)\n              </label>\n              <select\n                value={selectedSubCategory}\n                onChange={handleSubCategoryChange}\n                disabled={!selectedCategory || loading.subCategories}\n                className=\"form-select\"\n              >\n                <option value=\"\">\n                  {!selectedCategory\n                    ? 'Select Category first'\n                    : loading.subCategories\n                      ? 'Loading subcategories...'\n                      : 'Select Subcategory (Optional)'\n                  }\n                </option>\n                {subCategories.map(subCategory => (\n                  <option key={subCategory.id} value={subCategory.id}>\n                    {subCategory.name}\n                  </option>\n                ))}\n              </select>\n              {errors.subCategories && (\n                <div className=\"error-message\">{errors.subCategories}</div>\n              )}\n            </div>\n          )}\n\n          {/* Form Status Messages */}\n          {loading.form && (\n            <div className=\"loading-message\">\n              <span>Loading form configuration...</span>\n            </div>\n          )}\n\n          {formAvailability.message && !loading.form && (\n            <div className={`status-message ${\n              formAvailability.categoryHasForm || formAvailability.subCategoryHasForm\n                ? 'success'\n                : formAvailability.message.includes('No form')\n                  ? 'error'\n                  : 'info'\n            }`}>\n              {formAvailability.message}\n            </div>\n          )}\n\n          {errors.form && (\n            <div className=\"error-message\">{errors.form}</div>\n          )}\n        </div>\n\n        {/* Dynamic Form Fields - Only show when form is available */}\n        {formConfig && Object.entries(sections).map(([sectionKey, section]) => (\n          <div key={sectionKey} className=\"form-section\">\n            <h3>{section.title}</h3>\n            <div className=\"form-fields\">\n              {section.fields\n                .filter(field => shouldShowField(field))\n                .map((field, fieldIndex) => (\n                  <FormField\n                    key={`${sectionKey}-${field.key}-${fieldIndex}`}\n                    field={field}\n                    value={formData[field.key]}\n                    onChange={(value) => handleFieldChange(field.key, value)}\n                    error={errors[field.key]}\n                  />\n                ))}\n            </div>\n          </div>\n        ))}\n\n\n\n        {/* Form Actions */}\n        <div className=\"form-actions\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"btn btn-outline\"\n            disabled={submitting}\n          >\n            Cancel\n          </button>\n          <button\n            type=\"submit\"\n            className=\"btn btn-primary\"\n            disabled={submitting || !selectedDivision || !selectedCategory}\n          >\n            {submitting\n              ? (mode === 'create' ? 'Creating...' : 'Updating...')\n              : (mode === 'create' ? 'Create Person' : 'Update Person')\n            }\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default DynamicPersonForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,kBAAkB,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,iCAAiC;AAC9H,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,WAAW,GAAG,IAAI;EAAEC,IAAI,GAAG;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC2B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAACK,oBAAoB,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC;IACrCiB,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAE,KAAK;IACjBE,WAAW,EAAE,KAAK;IAClBgB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC;IACvD0C,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,KAAK;IACzBC,uBAAuB,EAAE,KAAK;IAC9BC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA5C,SAAS,CAAC,MAAM;IACd6C,aAAa,CAAC,CAAC;;IAEf;IACAC,mBAAmB,CAAC,CAAC;;IAErB;IACA,IAAIjC,WAAW,EAAE;MAAA,IAAAkC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MACflB,WAAW,CAAClB,WAAW,CAAC;MACxBU,mBAAmB,CAAC,EAAAwB,qBAAA,GAAAlC,WAAW,CAACqC,UAAU,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBI,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7D1B,mBAAmB,CAAC,EAAAuB,qBAAA,GAAAnC,WAAW,CAACuC,UAAU,cAAAJ,qBAAA,uBAAtBA,qBAAA,CAAwBG,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7DE,sBAAsB,CAAC,EAAAJ,qBAAA,GAAApC,WAAW,CAACyC,aAAa,cAAAL,qBAAA,uBAAzBA,qBAAA,CAA2BE,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;IACrE,CAAC,MAAM;MACL;MACA,MAAMI,WAAW,GAAGlD,iBAAiB,CAACmD,oBAAoB,CAAC,CAAC;MAC5D;MACA,IAAID,WAAW,CAACE,MAAM,EAAE;QACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;MAC5D;MACA5B,aAAa,CAAC0B,WAAW,CAAC;IAC5B;EACF,CAAC,EAAE,CAAC1C,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMiC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMxC,UAAU,CAACqD,YAAY,CAAC,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMF,iBAAiB,GAAID,MAAM,IAAK;IACpC,MAAMK,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,EAAE;IAEvBP,MAAM,CAACQ,OAAO,CAACC,KAAK,IAAI;MACtB,IAAI,CAACJ,IAAI,CAACK,GAAG,CAACD,KAAK,CAACE,GAAG,CAAC,EAAE;QACxBN,IAAI,CAACO,GAAG,CAACH,KAAK,CAACE,GAAG,CAAC;QACnBJ,YAAY,CAACM,IAAI,CAACJ,KAAK,CAAC;MAC1B,CAAC,MAAM;QACLL,OAAO,CAACU,IAAI,CAAC,gDAAgDL,KAAK,CAACE,GAAG,EAAE,CAAC;MAC3E;IACF,CAAC,CAAC;IAEF,IAAIX,MAAM,CAACe,MAAM,KAAKR,YAAY,CAACQ,MAAM,EAAE;MACzCX,OAAO,CAACY,GAAG,CAAC,mCAAmChB,MAAM,CAACe,MAAM,cAAcR,YAAY,CAACQ,MAAM,gBAAgB,CAAC;IAChH;IAEA,OAAOR,YAAY;EACrB,CAAC;;EAED;EACAhE,SAAS,CAAC,MAAM;IACd,IAAIsB,gBAAgB,EAAE;MACpBoD,cAAc,CAACpD,gBAAgB,CAAC;MAChC;MACAG,mBAAmB,CAAC,EAAE,CAAC;MACvB4B,sBAAsB,CAAC,EAAE,CAAC;MAC1BsB,gBAAgB,CAAC,EAAE,CAAC;MACpB9C,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM;MACLzB,aAAa,CAAC,EAAE,CAAC;MACjBwD,gBAAgB,CAAC,EAAE,CAAC;MACpBlD,mBAAmB,CAAC,EAAE,CAAC;MACvB4B,sBAAsB,CAAC,EAAE,CAAC;MAC1BxB,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,gBAAgB,CAAC,CAAC;;EAEtB;EACAtB,SAAS,CAAC,MAAM;IACd,IAAIwB,gBAAgB,EAAE;MACpBoD,uBAAuB,CAACpD,gBAAgB,CAAC;MACzC;MACA6B,sBAAsB,CAAC,EAAE,CAAC;IAC5B,CAAC,MAAM;MACLsB,gBAAgB,CAAC,EAAE,CAAC;MACpBtB,sBAAsB,CAAC,EAAE,CAAC;MAC1BxB,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACpB,gBAAgB,CAAC,CAAC;;EAEtB;EACAxB,SAAS,CAAC,MAAM;IACd,IAAI6E,mBAAmB,EAAE;MACvBC,0BAA0B,CAACD,mBAAmB,CAAC;IACjD;EACF,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACA,MAAMhC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCV,UAAU,CAAC4C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE/D,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAClD,IAAI;MACF,MAAMgE,QAAQ,GAAG,MAAM1E,UAAU,CAACqD,YAAY,CAAC,CAAC;MAChD1C,YAAY,CAAC+D,QAAQ,CAACC,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD3B,SAAS,CAAC8C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE/D,SAAS,EAAE;MAA2B,CAAC,CAAC,CAAC;IACzE,CAAC,SAAS;MACRmB,UAAU,CAAC4C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE/D,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAM0D,cAAc,GAAG,MAAOxB,UAAU,IAAK;IAC3Cf,UAAU,CAAC4C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE7D,UAAU,EAAE;IAAK,CAAC,CAAC,CAAC;IACnD,IAAI;MACF,MAAM8D,QAAQ,GAAG,MAAM1E,UAAU,CAAC4E,uBAAuB,CAAChC,UAAU,CAAC;MACrE/B,aAAa,CAAC6D,QAAQ,CAACC,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD3B,SAAS,CAAC8C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE7D,UAAU,EAAE;MAA4B,CAAC,CAAC,CAAC;MACzEC,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRgB,UAAU,CAAC4C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE7D,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMiE,iBAAiB,GAAG,MAAO/B,UAAU,IAAK;IAC9CjB,UAAU,CAAC4C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;IACtD,IAAI;MACF,MAAMJ,QAAQ,GAAG,MAAM1E,UAAU,CAAC+E,0BAA0B,CAACjC,UAAU,CAAC;MACxEuB,gBAAgB,CAACK,QAAQ,CAACC,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD3B,SAAS,CAAC8C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEK,aAAa,EAAE;MAA+B,CAAC,CAAC,CAAC;MAC/ET,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRxC,UAAU,CAAC4C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEK,aAAa,EAAE;MAAM,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMR,uBAAuB,GAAG,MAAOxB,UAAU,IAAK;IACpDjB,UAAU,CAAC4C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE3C,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMK,eAAe,GAAGpC,iBAAiB,CAACiF,kBAAkB,CAACC,QAAQ,CAACnC,UAAU,CAAC,CAAC;MAElF,IAAIX,eAAe,EAAE;QACnB;QACA,MAAM+C,YAAY,GAAGnF,iBAAiB,CAACoF,cAAc,CAAC,UAAU,EAAEF,QAAQ,CAACnC,UAAU,CAAC,CAAC;QACvF;QACA,IAAIoC,YAAY,IAAIA,YAAY,CAAC/B,MAAM,EAAE;UACvC+B,YAAY,CAAC/B,MAAM,GAAGC,iBAAiB,CAAC8B,YAAY,CAAC/B,MAAM,CAAC;QAC9D;QACA5B,aAAa,CAAC2D,YAAY,CAAC;QAC3BhD,mBAAmB,CAAC;UAClBC,eAAe,EAAE,IAAI;UACrBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,KAAK;UAC9BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMuC,iBAAiB,CAAC/B,UAAU,CAAC;QACnC;QACA,MAAMG,WAAW,GAAGlD,iBAAiB,CAACmD,oBAAoB,CAAC,CAAC;QAC5D;QACA,IAAID,WAAW,CAACE,MAAM,EAAE;UACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;QAC5D;QACA5B,aAAa,CAAC0B,WAAW,CAAC;QAC1Bf,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D3B,SAAS,CAAC8C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3C,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAAC4C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3C,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAM0C,0BAA0B,GAAG,MAAOxB,aAAa,IAAK;IAC1DnB,UAAU,CAAC4C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE3C,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMM,kBAAkB,GAAGrC,iBAAiB,CAACqF,qBAAqB,CAACH,QAAQ,CAACjC,aAAa,CAAC,CAAC;MAE3F,IAAIZ,kBAAkB,EAAE;QACtB;QACA,MAAMiD,eAAe,GAAGtF,iBAAiB,CAACoF,cAAc,CAAC,aAAa,EAAEF,QAAQ,CAACjC,aAAa,CAAC,CAAC;QAChG;QACA,IAAIqC,eAAe,IAAIA,eAAe,CAAClC,MAAM,EAAE;UAC7CkC,eAAe,CAAClC,MAAM,GAAGC,iBAAiB,CAACiC,eAAe,CAAClC,MAAM,CAAC;QACpE;QACA5B,aAAa,CAAC8D,eAAe,CAAC;QAC9BnD,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,IAAI;UACxBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMW,WAAW,GAAGlD,iBAAiB,CAACmD,oBAAoB,CAAC,CAAC;QAC5D;QACA,IAAID,WAAW,CAACE,MAAM,EAAE;UACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;QAC5D;QACA5B,aAAa,CAAC0B,WAAW,CAAC;QAC1Bf,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D3B,SAAS,CAAC8C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3C,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAAC4C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3C,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMwD,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BvE,mBAAmB,CAACuE,KAAK,CAAC;;IAE1B;IACA/D,WAAW,CAACgD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP7B,UAAU,EAAE4C,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1C1C,UAAU,EAAE,IAAI;MAChBE,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM0C,oBAAoB,GAAIH,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BrE,mBAAmB,CAACqE,KAAK,CAAC;;IAE1B;IACA/D,WAAW,CAACgD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP3B,UAAU,EAAE0C,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1CxC,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM2C,uBAAuB,GAAIJ,CAAC,IAAK;IACrC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BzC,sBAAsB,CAACyC,KAAK,CAAC;;IAE7B;IACA/D,WAAW,CAACgD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPzB,aAAa,EAAEwC,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG;IAC3C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,iBAAiB,GAAGA,CAACC,QAAQ,EAAEL,KAAK,KAAK;IAC7C/D,WAAW,CAACgD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACoB,QAAQ,GAAGL;IACd,CAAC,CAAC,CAAC;;IAEH;IACA,IAAI9D,MAAM,CAACmE,QAAQ,CAAC,EAAE;MACpBlE,SAAS,CAAC8C,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACoB,QAAQ,GAAG;MACd,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAAC/E,gBAAgB,EAAE;MACrB+E,SAAS,CAACC,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIf,QAAQ,CAACjE,gBAAgB,CAAC,GAAG,CAAC,EAAE;MACzC+E,SAAS,CAACC,QAAQ,GAAG,uCAAuC;IAC9D;IAEA,IAAI,CAAC9E,gBAAgB,EAAE;MACrB6E,SAAS,CAACE,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIhB,QAAQ,CAAC/D,gBAAgB,CAAC,GAAG,CAAC,EAAE;MACzC6E,SAAS,CAACE,QAAQ,GAAG,uCAAuC;IAC9D;IAEA,IAAI1B,mBAAmB,IAAIU,QAAQ,CAACV,mBAAmB,CAAC,GAAG,CAAC,EAAE;MAC5DwB,SAAS,CAACG,WAAW,GAAG,0CAA0C;IACpE;;IAEA;IACA,IAAI,CAAC1E,QAAQ,CAAC2E,IAAI,IAAI3E,QAAQ,CAAC2E,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjDL,SAAS,CAACI,IAAI,GAAG,kBAAkB;IACrC,CAAC,MAAM,IAAI3E,QAAQ,CAAC2E,IAAI,CAACjC,MAAM,GAAG,GAAG,EAAE;MACrC6B,SAAS,CAACI,IAAI,GAAG,mCAAmC;IACtD;IAEA,IAAI,CAAC3E,QAAQ,CAAC6E,YAAY,IAAI7E,QAAQ,CAAC6E,YAAY,CAACD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEL,SAAS,CAACM,YAAY,GAAG,2BAA2B;IACtD,CAAC,MAAM;MACL;MACA,MAAMC,WAAW,GAAG,qCAAqC;MACzD,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC/E,QAAQ,CAAC6E,YAAY,CAAC,EAAE;QAC5CN,SAAS,CAACM,YAAY,GAAG,8BAA8B;MACzD;IACF;IAEA,IAAI,CAAC7E,QAAQ,CAACgF,MAAM,IAAIhF,QAAQ,CAACgF,MAAM,KAAK,EAAE,IAAIhF,QAAQ,CAACgF,MAAM,KAAK,GAAG,EAAE;MACzET,SAAS,CAACS,MAAM,GAAG,oBAAoB;IACzC,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,QAAQ,CAACxB,QAAQ,CAACzD,QAAQ,CAACgF,MAAM,CAAC,CAAC,EAAE;MAC5DT,SAAS,CAACS,MAAM,GAAG,sBAAsB;IAC3C;;IAEA;IACA,IAAI,CAAClF,UAAU,EAAE;MACfyE,SAAS,CAACjE,IAAI,GAAG,kDAAkD;MACnE,OAAO;QAAE4E,OAAO,EAAE,KAAK;QAAEhF,MAAM,EAAEqE;MAAU,CAAC;IAC9C;;IAIA;IACAzE,UAAU,CAAC6B,MAAM,CAACQ,OAAO,CAACC,KAAK,IAAI;MACjC,MAAM4B,KAAK,GAAGhE,QAAQ,CAACoC,KAAK,CAACE,GAAG,CAAC;;MAEjC;MACA,IAAIF,KAAK,CAAC+C,QAAQ,KAAK,CAACnB,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;QACpFL,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACgD,KAAK,cAAc;QACnD;MACF;;MAEA;MACA,IAAIhD,KAAK,CAACiD,WAAW,IAAIC,eAAe,CAAClD,KAAK,CAAC,EAAE;QAC/C,IAAIA,KAAK,CAAC+C,QAAQ,KAAK,CAACnB,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;UACpFL,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACgD,KAAK,cAAc;UACnD;QACF;MACF;;MAEA;MACA,IAAIpB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAAA,IAAAW,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;QAC7D,QAAQrD,KAAK,CAACsD,IAAI;UAChB,KAAK,OAAO;YACV,MAAMC,UAAU,GAAG,4BAA4B;YAC/C,IAAI,CAACA,UAAU,CAACZ,IAAI,CAACf,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,MAAMsD,UAAU,GAAG,qCAAqC;YACxD,IAAI,CAACA,UAAU,CAACb,IAAI,CAACf,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,IAAI;cACF,IAAIuD,GAAG,CAAC7B,KAAK,CAAC;YAChB,CAAC,CAAC,MAAM;cACNO,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,0BAA0B;YACnD;YACA;UACF,KAAK,QAAQ;YACX,IAAIF,KAAK,CAAC0D,UAAU,EAAE;cACpB,MAAMC,QAAQ,GAAGC,UAAU,CAAChC,KAAK,CAAC;cAClC,IAAI5B,KAAK,CAAC0D,UAAU,CAACG,GAAG,KAAKC,SAAS,IAAIH,QAAQ,GAAG3D,KAAK,CAAC0D,UAAU,CAACG,GAAG,EAAE;gBACzE1B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,0BAA0BF,KAAK,CAAC0D,UAAU,CAACG,GAAG,EAAE;cACzE;cACA,IAAI7D,KAAK,CAAC0D,UAAU,CAACK,GAAG,KAAKD,SAAS,IAAIH,QAAQ,GAAG3D,KAAK,CAAC0D,UAAU,CAACK,GAAG,EAAE;gBACzE5B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,yBAAyBF,KAAK,CAAC0D,UAAU,CAACK,GAAG,EAAE;cACxE;YACF;YACA;QACJ;;QAEA;QACA,KAAAZ,iBAAA,GAAInD,KAAK,CAAC0D,UAAU,cAAAP,iBAAA,eAAhBA,iBAAA,CAAkBa,OAAO,EAAE;UAC7B,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAAClE,KAAK,CAAC0D,UAAU,CAACM,OAAO,CAAC;UAClD,IAAI,CAACC,KAAK,CAACtB,IAAI,CAACf,KAAK,CAAC,EAAE;YACtBO,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACgD,KAAK,oBAAoB;UAC3D;QACF;;QAEA;QACA,IAAI,CAAAI,kBAAA,GAAApD,KAAK,CAAC0D,UAAU,cAAAN,kBAAA,eAAhBA,kBAAA,CAAkBe,SAAS,IAAIvC,KAAK,CAACtB,MAAM,GAAGN,KAAK,CAAC0D,UAAU,CAACS,SAAS,EAAE;UAC5EhC,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACgD,KAAK,qBAAqBhD,KAAK,CAAC0D,UAAU,CAACS,SAAS,aAAa;QACnG;QACA,IAAI,CAAAd,kBAAA,GAAArD,KAAK,CAAC0D,UAAU,cAAAL,kBAAA,eAAhBA,kBAAA,CAAkBe,SAAS,IAAIxC,KAAK,CAACtB,MAAM,GAAGN,KAAK,CAAC0D,UAAU,CAACU,SAAS,EAAE;UAC5EjC,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACgD,KAAK,oBAAoBhD,KAAK,CAAC0D,UAAU,CAACU,SAAS,aAAa;QAClG;;QAEA;QACA,IAAIpE,KAAK,CAACE,GAAG,KAAK,gBAAgB,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UACxD6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,oCAAoC;QAC7D;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,cAAc,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UACtD6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,4CAA4C;QACrE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,eAAe,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UACvD6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,6CAA6C;QACtE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,UAAU,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UAClD6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,uCAAuC;QAChE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,SAAS,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UACjD6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,sCAAsC;QAC/D;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,aAAa,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UACrD6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,2CAA2C;QACpE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,eAAe,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UACvD6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,6CAA6C;QACtE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,mBAAmB,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UAC3D6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,iDAAiD;QAC1E;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,wBAAwB,IAAI0B,KAAK,CAACtB,MAAM,GAAG,EAAE,EAAE;UAC/D6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,sDAAsD;QAC/E;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,QAAQ,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UAChD6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,qCAAqC;QAC9D;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,SAAS,IAAI0B,KAAK,CAACtB,MAAM,GAAG,IAAI,EAAE;UAClD6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,uCAAuC;QAChE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,UAAU,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UAClD6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,wCAAwC;QACjE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,sBAAsB,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UAC9D6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,qDAAqD;QAC9E;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,uBAAuB,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UAC/D6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,sDAAsD;QAC/E;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,aAAa,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UACrD6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,0CAA0C;QACnE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,kBAAkB,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UAC1D6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,gDAAgD;QACzE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,sBAAsB,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UAC9D6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,oDAAoD;QAC7E;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,gBAAgB,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UACxD6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,+CAA+C;QACxE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,YAAY,IAAI0B,KAAK,CAACtB,MAAM,GAAG,GAAG,EAAE;UACpD6B,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,yCAAyC;QAClE;MACF;;MAEA;MACA,IAAIF,KAAK,CAACsD,IAAI,KAAK,QAAQ,IAAI1B,KAAK,EAAE;QACpC,MAAM+B,QAAQ,GAAGC,UAAU,CAAChC,KAAK,CAAC;QAClC,IAAI5B,KAAK,CAACE,GAAG,KAAK,YAAY,KAAKyD,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,CAAC,CAAC,EAAE;UAChExB,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,qCAAqC;QAC9D;QACA,IAAI,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,uBAAuB,CAAC,CAAC2C,QAAQ,CAAC7C,KAAK,CAACE,GAAG,CAAC,IAAIyD,QAAQ,GAAG,CAAC,EAAE;UACxGxB,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACgD,KAAK,uBAAuB;QAC9D;QACA,IAAIhD,KAAK,CAACE,GAAG,KAAK,kBAAkB,IAAIyD,QAAQ,GAAG,CAAC,EAAE;UACpDxB,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,wCAAwC;QACjE;MACF;;MAEA;MACA,IAAIF,KAAK,CAACE,GAAG,KAAK,iBAAiB,IAAI0B,KAAK,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACnE,MAAME,WAAW,GAAG,qCAAqC;QACzD,IAAI,CAACA,WAAW,CAACC,IAAI,CAACf,KAAK,CAAC,EAAE;UAC5BO,SAAS,CAACnC,KAAK,CAACE,GAAG,CAAC,GAAG,wCAAwC;QACjE;MACF;IACF,CAAC,CAAC;;IAEF;IACA,IAAItC,QAAQ,CAACyG,SAAS,IAAIzG,QAAQ,CAAC0G,cAAc,IAAI1G,QAAQ,CAAC2G,WAAW,EAAE;MACzE,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC7G,QAAQ,CAAC2G,WAAW,CAAC;MAChD,MAAMG,YAAY,GAAG,IAAID,IAAI,CAAC7G,QAAQ,CAAC0G,cAAc,CAAC;MACtD,IAAII,YAAY,IAAIF,SAAS,EAAE;QAC7BrC,SAAS,CAACmC,cAAc,GAAG,wCAAwC;MACrE;IACF;IAEA,OAAO;MACLxB,OAAO,EAAE6B,MAAM,CAACC,IAAI,CAACzC,SAAS,CAAC,CAAC7B,MAAM,KAAK,CAAC;MAC5CxC,MAAM,EAAEqE;IACV,CAAC;EACH,CAAC;EAED,MAAM0C,YAAY,GAAG,MAAOlD,CAAC,IAAK;IAChCA,CAAC,CAACmD,cAAc,CAAC,CAAC;;IAElB;IACAnF,OAAO,CAACY,GAAG,CAAC,gDAAgD,EAAE;MAC5DwE,cAAc,EAAEnH,QAAQ,CAACmH,cAAc;MACvCC,OAAO,EAAEpH,QAAQ,CAACoH,OAAO;MACzBC,WAAW,EAAErH,QAAQ,CAACqH,WAAW;MACjCC,UAAU,EAAEtH,QAAQ,CAACsH,UAAU;MAC/BC,qBAAqB,EAAEvH,QAAQ,CAACuH,qBAAqB;MACrDC,eAAe,EAAExH,QAAQ,CAACwH;IAC5B,CAAC,CAAC;IAEF,MAAM1B,UAAU,GAAGxB,YAAY,CAAC,CAAC;IACjC,IAAI,CAACwB,UAAU,CAACZ,OAAO,EAAE;MACvB/E,SAAS,CAAC2F,UAAU,CAAC5F,MAAM,CAAC;MAC5B;IACF;IAEAM,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF;MACA,MAAMiH,UAAU,GAAG;QACjB;QACAC,UAAU,EAAEjE,QAAQ,CAACjE,gBAAgB,CAAC;QACtCmI,UAAU,EAAElE,QAAQ,CAAC/D,gBAAgB,CAAC;QACtCkI,aAAa,EAAE7E,mBAAmB,GAAGU,QAAQ,CAACV,mBAAmB,CAAC,GAAG,IAAI;QAEzE;QACA8E,IAAI,EAAE7H,QAAQ,CAAC2E,IAAI,IAAI,EAAE;QACzBmD,YAAY,EAAE9H,QAAQ,CAAC6E,YAAY,IAAI,EAAE;QACzCkD,MAAM,EAAE/H,QAAQ,CAACgF,MAAM,GAAGvB,QAAQ,CAACzD,QAAQ,CAACgF,MAAM,CAAC,GAAG,CAAC;QAAE;;QAEzD;QACAgD,MAAM,EAAEhI,QAAQ,CAACiI,MAAM,GAAGxE,QAAQ,CAACzD,QAAQ,CAACiI,MAAM,CAAC,GAAG,IAAI;QAE1D;QACAC,gBAAgB,EAAEC,KAAK,CAACC,OAAO,CAACpI,QAAQ,CAACqI,gBAAgB,CAAC,GACtDrI,QAAQ,CAACqI,gBAAgB,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAAC3D,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,GACjE5E,QAAQ,CAACqI,gBAAgB,GAAGrI,QAAQ,CAACqI,gBAAgB,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC9D,IAAI,CAAC,CAAC,CAAC,CAAC0D,MAAM,CAACI,CAAC,IAAIA,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE;QAClHC,iBAAiB,EAAER,KAAK,CAACC,OAAO,CAACpI,QAAQ,CAAC4I,iBAAiB,CAAC,GACxD5I,QAAQ,CAAC4I,iBAAiB,CAACN,MAAM,CAACO,KAAK,IAAIA,KAAK,IAAIA,KAAK,CAACjE,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,GACxE5E,QAAQ,CAAC4I,iBAAiB,GAAG5I,QAAQ,CAAC4I,iBAAiB,CAACJ,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC9D,IAAI,CAAC,CAAC,CAAC,CAAC0D,MAAM,CAACI,CAAC,IAAIA,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE;QACpH;;QAEA;QACAI,WAAW,EAAE9I,QAAQ,CAAC2G,WAAW,IAAI,IAAI;QACzCoC,SAAS,EAAEC,OAAO,CAAChJ,QAAQ,CAACyG,SAAS,CAAC;QACtCwC,cAAc,EAAEjJ,QAAQ,CAAC0G,cAAc,IAAI,IAAI;QAE/C;QACAwC,YAAY,EAAElJ,QAAQ,CAACmJ,YAAY,IAAI,EAAE;QACzCC,aAAa,EAAEpJ,QAAQ,CAACqJ,aAAa,IAAI,EAAE;QAC3CC,QAAQ,EAAEtJ,QAAQ,CAACuJ,QAAQ,IAAI,EAAE;QACjCC,OAAO,EAAExJ,QAAQ,CAACyJ,OAAO,IAAI,EAAE;QAC/BC,WAAW,EAAE1J,QAAQ,CAAC2J,WAAW,IAAI,EAAE;QAEvC;QACAC,YAAY,EAAEZ,OAAO,CAAChJ,QAAQ,CAAC6J,YAAY,CAAC;QAC5CC,aAAa,EAAE9J,QAAQ,CAAC+J,aAAa,IAAI,EAAE;QAC3CC,iBAAiB,EAAEhK,QAAQ,CAACiK,iBAAiB,IAAI,EAAE;QACnD;;QAEA;QACAC,YAAY,EAAElB,OAAO,CAAChJ,QAAQ,CAACmK,YAAY,CAAC;QAC5CC,WAAW,EAAEpB,OAAO,CAAChJ,QAAQ,CAACqK,WAAW,CAAC;QAC1C;;QAEA;QACAC,gBAAgB,EAAEtK,QAAQ,CAACuK,gBAAgB,GAAGvE,UAAU,CAAChG,QAAQ,CAACuK,gBAAgB,CAAC,GAAG,IAAI;QAC1FC,sBAAsB,EAAExK,QAAQ,CAACyK,sBAAsB,IAAI,EAAE;QAC7DC,eAAe,EAAEvC,KAAK,CAACC,OAAO,CAACpI,QAAQ,CAAC2K,eAAe,CAAC,GACpD3K,QAAQ,CAAC2K,eAAe,CAAClC,GAAG,CAACmC,EAAE,IAAInH,QAAQ,CAACmH,EAAE,CAAC,CAAC,CAACtC,MAAM,CAACsC,EAAE,IAAI,CAACC,KAAK,CAACD,EAAE,CAAC,CAAC,GACzE,EAAE;QACNE,UAAU,EAAE9K,QAAQ,CAAC+K,UAAU,GAAGtH,QAAQ,CAACzD,QAAQ,CAAC+K,UAAU,CAAC,GAAG,IAAI;QACtEC,MAAM,EAAEhL,QAAQ,CAACiL,MAAM,IAAI,EAAE;QAC7BC,OAAO,EAAElL,QAAQ,CAACmL,OAAO,IAAI,EAAE;QAE/B;QACAC,QAAQ,EAAEpL,QAAQ,CAACqL,QAAQ,IAAI,EAAE;QACjCC,eAAe,EAAEtL,QAAQ,CAACuL,eAAe,IAAI,CAACV,KAAK,CAACpH,QAAQ,CAACzD,QAAQ,CAACuL,eAAe,CAAC,CAAC,GAAG9H,QAAQ,CAACzD,QAAQ,CAACuL,eAAe,CAAC,GAAG,IAAI;QACnIC,gBAAgB,EAAExL,QAAQ,CAACyL,gBAAgB,IAAI,CAACZ,KAAK,CAACpH,QAAQ,CAACzD,QAAQ,CAACyL,gBAAgB,CAAC,CAAC,GAAGhI,QAAQ,CAACzD,QAAQ,CAACyL,gBAAgB,CAAC,GAAG,IAAI;QACvIC,qBAAqB,EAAE1L,QAAQ,CAAC2L,qBAAqB,IAAI,CAACd,KAAK,CAACpH,QAAQ,CAACzD,QAAQ,CAAC2L,qBAAqB,CAAC,CAAC,GAAGlI,QAAQ,CAACzD,QAAQ,CAAC2L,qBAAqB,CAAC,GAAG,IAAI;QAE3J;QACAC,oBAAoB,EAAE5L,QAAQ,CAAC6L,oBAAoB,IAAI,EAAE;QACzDC,WAAW,EAAE9L,QAAQ,CAAC+L,WAAW,IAAI,EAAE;QACvC;;QAEA;QACAC,gBAAgB,EAAEhM,QAAQ,CAACiM,gBAAgB,IAAI,EAAE;QACjDC,oBAAoB,EAAElM,QAAQ,CAACmM,oBAAoB,IAAI,EAAE;QACzDC,cAAc,EAAEpM,QAAQ,CAACqM,cAAc,IAAI,EAAE;QAC7CC,UAAU,EAAEtM,QAAQ,CAACuM,UAAU,IAAI;MACrC,CAAC;;MAED;MACA;;MAEA;MACA;MACA,MAAMC,aAAa,GAAIxI,KAAK,IAAK;QAC/B,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKkC,SAAS,IAAIlC,KAAK,KAAK,EAAE,KACpD,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC;MACjE,CAAC;;MAED;MACA7C,OAAO,CAACY,GAAG,CAAC,uCAAuC,CAAC;MACpDZ,OAAO,CAACY,GAAG,CAAC,oCAAoC,EAAE;QAChDwE,cAAc,EAAEnH,QAAQ,CAACmH,cAAc;QACvCI,qBAAqB,EAAEvH,QAAQ,CAACuH,qBAAqB;QACrDH,OAAO,EAAEpH,QAAQ,CAACoH,OAAO;QACzBC,WAAW,EAAErH,QAAQ,CAACqH,WAAW;QACjCC,UAAU,EAAEtH,QAAQ,CAACsH,UAAU;QAC/BE,eAAe,EAAExH,QAAQ,CAACwH;MAC5B,CAAC,CAAC;;MAEF;MACAzF,OAAO,CAACY,GAAG,CAAC,6BAA6B,CAAC;MAC1CZ,OAAO,CAACY,GAAG,CAAC,mBAAmB,EAAE6J,aAAa,CAACxM,QAAQ,CAACmH,cAAc,CAAC,CAAC;MACxEpF,OAAO,CAACY,GAAG,CAAC,0BAA0B,EAAE6J,aAAa,CAACxM,QAAQ,CAACuH,qBAAqB,CAAC,CAAC;MACtFxF,OAAO,CAACY,GAAG,CAAC,YAAY,EAAE6J,aAAa,CAACxM,QAAQ,CAACoH,OAAO,CAAC,CAAC;MAC1DrF,OAAO,CAACY,GAAG,CAAC,gBAAgB,EAAE6J,aAAa,CAACxM,QAAQ,CAACqH,WAAW,CAAC,CAAC;MAClEtF,OAAO,CAACY,GAAG,CAAC,eAAe,EAAE6J,aAAa,CAACxM,QAAQ,CAACsH,UAAU,CAAC,CAAC;MAChEvF,OAAO,CAACY,GAAG,CAAC,oBAAoB,EAAE6J,aAAa,CAACxM,QAAQ,CAACwH,eAAe,CAAC,CAAC;;MAE1E;MACA,IAAIgF,aAAa,CAACxM,QAAQ,CAACmH,cAAc,CAAC,EAAE;QAC1CpF,OAAO,CAACY,GAAG,CAAC,wBAAwB,EAAE3C,QAAQ,CAACmH,cAAc,CAAC;QAC9DM,UAAU,CAACgF,cAAc,GAAGzM,QAAQ,CAACmH,cAAc,CAACvC,IAAI,CAAC,CAAC;MAC5D;MAEA,IAAI4H,aAAa,CAACxM,QAAQ,CAACuH,qBAAqB,CAAC,EAAE;QACjDxF,OAAO,CAACY,GAAG,CAAC,+BAA+B,EAAE3C,QAAQ,CAACuH,qBAAqB,CAAC;QAC5EE,UAAU,CAACiF,qBAAqB,GAAG1M,QAAQ,CAACuH,qBAAqB,CAAC3C,IAAI,CAAC,CAAC;MAC1E;;MAEA;MACA,IAAI4H,aAAa,CAACxM,QAAQ,CAACoH,OAAO,CAAC,EAAE;QACnCrF,OAAO,CAACY,GAAG,CAAC,iBAAiB,EAAE3C,QAAQ,CAACoH,OAAO,CAAC;QAChDK,UAAU,CAACkF,OAAO,GAAG3M,QAAQ,CAACoH,OAAO,CAACxC,IAAI,CAAC,CAAC;MAC9C;MAEA,IAAI4H,aAAa,CAACxM,QAAQ,CAACqH,WAAW,CAAC,EAAE;QACvCtF,OAAO,CAACY,GAAG,CAAC,qBAAqB,EAAE3C,QAAQ,CAACqH,WAAW,CAAC;QACxDI,UAAU,CAACmF,WAAW,GAAG5M,QAAQ,CAACqH,WAAW,CAACzC,IAAI,CAAC,CAAC;MACtD;MAEA,IAAI4H,aAAa,CAACxM,QAAQ,CAACsH,UAAU,CAAC,EAAE;QACtCvF,OAAO,CAACY,GAAG,CAAC,oBAAoB,EAAE3C,QAAQ,CAACsH,UAAU,CAAC;QACtDG,UAAU,CAACoF,UAAU,GAAG7M,QAAQ,CAACsH,UAAU,CAAC1C,IAAI,CAAC,CAAC;MACpD;;MAEA;MACA,IAAI4H,aAAa,CAACxM,QAAQ,CAACwH,eAAe,CAAC,EAAE;QAC3CzF,OAAO,CAACY,GAAG,CAAC,yBAAyB,EAAE3C,QAAQ,CAACwH,eAAe,CAAC;QAChEC,UAAU,CAACqF,eAAe,GAAG9M,QAAQ,CAACwH,eAAe,CAAC5C,IAAI,CAAC,CAAC;MAC9D;;MAIA;MACA,MAAMmI,mBAAmB,GAAG;QAC1BrF,UAAU,EAAED,UAAU,CAACC,UAAU;QACjCC,UAAU,EAAEF,UAAU,CAACE,UAAU;QACjCE,IAAI,EAAEJ,UAAU,CAACI,IAAI;QACrBC,YAAY,EAAEL,UAAU,CAACK,YAAY;QACrCC,MAAM,EAAEN,UAAU,CAACM;MACrB,CAAC;;MAED;MACA,MAAMiF,aAAa,GAAGjG,MAAM,CAACkG,OAAO,CAACF,mBAAmB,CAAC,CACtDzE,MAAM,CAAC,CAAC,GAAGtE,KAAK,CAAC,KAAK,CAACA,KAAK,IAAIA,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,IAAI,CAAC,CAC/DyE,GAAG,CAAC,CAAC,CAACnG,GAAG,CAAC,KAAKA,GAAG,CAAC;MAEtB,IAAI0K,aAAa,CAACtK,MAAM,GAAG,CAAC,EAAE;QAC5BvC,SAAS,CAAC;UAAE+M,OAAO,EAAE,2BAA2B,GAAGF,aAAa,CAACG,IAAI,CAAC,IAAI;QAAE,CAAC,CAAC;QAC9E;MACF;;MAEA;MACA;MACA,MAAMC,yBAAyB,GAAG,CAChC,gBAAgB,EAAE,uBAAuB,EAAE,SAAS,EAAE,aAAa,EACnE,YAAY,EAAE,iBAAiB,CAChC;MAEDrL,OAAO,CAACY,GAAG,CAAC,4BAA4B,CAAC;MACzCZ,OAAO,CAACY,GAAG,CAAC,iBAAiB,EAAEoE,MAAM,CAACC,IAAI,CAACS,UAAU,CAAC,CAAC;MAEvD2F,yBAAyB,CAACjL,OAAO,CAACkL,SAAS,IAAI;QAC7C,IAAI5F,UAAU,CAAC6F,cAAc,CAACD,SAAS,CAAC,EAAE;UACxC,MAAMrJ,KAAK,GAAGyD,UAAU,CAAC4F,SAAS,CAAC;UACnC,MAAME,OAAO,GAAGvJ,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKkC,SAAS,IAAIlC,KAAK,KAAK,EAAE,IACrD,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAG;UAEjE7C,OAAO,CAACY,GAAG,CAAC,YAAY0K,SAAS,GAAG,EAAE;YACpCrJ,KAAK;YACLuJ,OAAO;YACPC,MAAM,EAAED,OAAO,GAAG,UAAU,GAAG;UACjC,CAAC,CAAC;UAEF,IAAIA,OAAO,EAAE;YACX,OAAO9F,UAAU,CAAC4F,SAAS,CAAC;YAC5BtL,OAAO,CAACY,GAAG,CAAC,eAAe0K,SAAS,WAAWrJ,KAAK,IAAI,CAAC;UAC3D;QACF;MACF,CAAC,CAAC;MAEFjC,OAAO,CAACY,GAAG,CAAC,gBAAgB,EAAEoE,MAAM,CAACC,IAAI,CAACS,UAAU,CAAC,CAAC;MACtD1F,OAAO,CAACY,GAAG,CAAC,mBAAmB,EAAE8K,IAAI,CAACC,SAAS,CAACjG,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAErE,IAAIkG,MAAM;MACV,IAAI3O,IAAI,KAAK,QAAQ,EAAE;QACrB2O,MAAM,GAAG,MAAMnP,UAAU,CAACoP,YAAY,CAACnG,UAAU,CAAC;MACpD,CAAC,MAAM;QACLkG,MAAM,GAAG,MAAMnP,UAAU,CAACqP,YAAY,CAAC9O,WAAW,CAAC+O,EAAE,EAAErG,UAAU,CAAC;MACpE;MAEA5I,QAAQ,CAAC8O,MAAM,CAAC;IAClB,CAAC,CAAC,OAAO7L,KAAK,EAAE;MAAA,IAAAiM,WAAA,EAAAC,eAAA,EAAAC,oBAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdxM,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAE;QAC9B0M,MAAM,EAAE1M,KAAK,CAAC0M,MAAM;QACpBrL,IAAI,EAAErB,KAAK,CAACqB,IAAI;QAChBD,QAAQ,EAAEpB,KAAK,CAACoB,QAAQ;QACxBpC,OAAO,EAAEgB,KAAK,CAAChB;MACjB,CAAC,CAAC;;MAEF;MACA,IAAI,CAAAiN,WAAA,GAAAjM,KAAK,CAACqB,IAAI,cAAA4K,WAAA,eAAVA,WAAA,CAAY7N,MAAM,KAAA8N,eAAA,GAAIlM,KAAK,CAACoB,QAAQ,cAAA8K,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgB7K,IAAI,cAAA8K,oBAAA,eAApBA,oBAAA,CAAsB/N,MAAM,EAAE;QAAA,IAAAuO,YAAA,EAAAC,gBAAA,EAAAC,qBAAA;QACtD;QACA,MAAMC,gBAAgB,GAAG,EAAAH,YAAA,GAAA3M,KAAK,CAACqB,IAAI,cAAAsL,YAAA,uBAAVA,YAAA,CAAYvO,MAAM,OAAAwO,gBAAA,GAAI5M,KAAK,CAACoB,QAAQ,cAAAwL,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvL,IAAI,cAAAwL,qBAAA,uBAApBA,qBAAA,CAAsBzO,MAAM;QAC3E,MAAM2O,aAAa,GAAG,CAAC,CAAC;QAExB9M,OAAO,CAACY,GAAG,CAAC,6BAA6B,EAAEiM,gBAAgB,CAAC;QAE5D7H,MAAM,CAACC,IAAI,CAAC4H,gBAAgB,CAAC,CAACzM,OAAO,CAACG,GAAG,IAAI;UAC3C,MAAMwM,aAAa,GAAGF,gBAAgB,CAACtM,GAAG,CAAC;UAC3C;UACA,MAAMyM,iBAAiB,GAAGC,yBAAyB,CAAC1M,GAAG,CAAC;UACxDuM,aAAa,CAACE,iBAAiB,CAAC,GAAG5G,KAAK,CAACC,OAAO,CAAC0G,aAAa,CAAC,GAC3DA,aAAa,CAAC3B,IAAI,CAAC,IAAI,CAAC,GACxB2B,aAAa;QACnB,CAAC,CAAC;QACF3O,SAAS,CAAC0O,aAAa,CAAC;MAC1B,CAAC,MAAM,IAAI,CAAAX,YAAA,GAAApM,KAAK,CAACqB,IAAI,cAAA+K,YAAA,eAAVA,YAAA,CAAYe,KAAK,KAAAd,YAAA,GAAIrM,KAAK,CAACqB,IAAI,cAAAgL,YAAA,eAAVA,YAAA,CAAYe,MAAM,KAAAd,gBAAA,GAAItM,KAAK,CAACoB,QAAQ,cAAAkL,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjL,IAAI,cAAAkL,qBAAA,eAApBA,qBAAA,CAAsBY,KAAK,KAAAX,gBAAA,GAAIxM,KAAK,CAACoB,QAAQ,cAAAoL,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnL,IAAI,cAAAoL,qBAAA,eAApBA,qBAAA,CAAsBW,MAAM,EAAE;QAAA,IAAAC,YAAA,EAAAC,YAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;QACjH;QACA,MAAMC,YAAY,GAAG,EAAAN,YAAA,GAAArN,KAAK,CAACqB,IAAI,cAAAgM,YAAA,uBAAVA,YAAA,CAAYD,MAAM,OAAAE,YAAA,GAAItN,KAAK,CAACqB,IAAI,cAAAiM,YAAA,uBAAVA,YAAA,CAAYH,KAAK,OAAAI,gBAAA,GAAIvN,KAAK,CAACoB,QAAQ,cAAAmM,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlM,IAAI,cAAAmM,qBAAA,uBAApBA,qBAAA,CAAsBJ,MAAM,OAAAK,gBAAA,GAAIzN,KAAK,CAACoB,QAAQ,cAAAqM,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpM,IAAI,cAAAqM,qBAAA,uBAApBA,qBAAA,CAAsBP,KAAK;QAC3H9O,SAAS,CAAC;UAAE+M,OAAO,EAAEuC;QAAa,CAAC,CAAC;MACtC,CAAC,MAAM,IAAI3N,KAAK,CAAC4N,iBAAiB,IAAI5N,KAAK,CAAC4N,iBAAiB,CAAC,CAAC,EAAE;QAC/D,MAAMd,gBAAgB,GAAG9M,KAAK,CAAC6N,mBAAmB,CAAC,CAAC;QACpD5N,OAAO,CAACY,GAAG,CAAC,6BAA6B,EAAEiM,gBAAgB,CAAC;QAC5DzO,SAAS,CAACyO,gBAAgB,CAAC;MAC7B,CAAC,MAAM;QAAA,IAAAgB,YAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,YAAA;QACL;QACA,MAAMN,YAAY,GAAG,EAAAG,YAAA,GAAA9N,KAAK,CAACqB,IAAI,cAAAyM,YAAA,uBAAVA,YAAA,CAAY9O,OAAO,OAAA+O,gBAAA,GACrB/N,KAAK,CAACoB,QAAQ,cAAA2M,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1M,IAAI,cAAA2M,qBAAA,uBAApBA,qBAAA,CAAsBhP,OAAO,OAAAiP,YAAA,GAC7BjO,KAAK,CAACqB,IAAI,cAAA4M,YAAA,uBAAVA,YAAA,CAAYd,KAAK,KACjBnN,KAAK,CAAChB,OAAO,IACb,mFAAmF;QACtGX,SAAS,CAAC;UAAE+M,OAAO,EAAEuC;QAAa,CAAC,CAAC;MACtC;IACF,CAAC,SAAS;MACRjP,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMwO,yBAAyB,GAAIgB,gBAAgB,IAAK;IACtD,MAAMC,YAAY,GAAG;MACnB,YAAY,EAAE,UAAU;MACxB,YAAY,EAAE,UAAU;MACxB,eAAe,EAAE,aAAa;MAC9B,MAAM,EAAE,MAAM;MACd,cAAc,EAAE,cAAc;MAC9B,QAAQ,EAAE,QAAQ;MAClB,QAAQ,EAAE,QAAQ;MAClB,gBAAgB,EAAE,gBAAgB;MAClC,kBAAkB,EAAE,kBAAkB;MACtC,mBAAmB,EAAE,mBAAmB;MACxC,SAAS,EAAE,SAAS;MACpB,aAAa,EAAE,aAAa;MAC5B,WAAW,EAAE,WAAW;MACxB,gBAAgB,EAAE,gBAAgB;MAClC,cAAc,EAAE,cAAc;MAC9B,eAAe,EAAE,eAAe;MAChC,UAAU,EAAE,UAAU;MACtB,SAAS,EAAE,SAAS;MACpB,aAAa,EAAE,aAAa;MAC5B,cAAc,EAAE,cAAc;MAC9B,eAAe,EAAE,eAAe;MAChC,mBAAmB,EAAE,mBAAmB;MACxC,iBAAiB,EAAE,iBAAiB;MACpC,cAAc,EAAE,cAAc;MAC9B,aAAa,EAAE,aAAa;MAC5B,aAAa,EAAE,aAAa;MAC5B,YAAY,EAAE,YAAY;MAC1B,kBAAkB,EAAE,kBAAkB;MACtC,wBAAwB,EAAE,wBAAwB;MAClD,iBAAiB,EAAE,iBAAiB;MACpC,YAAY,EAAE,YAAY;MAC1B,QAAQ,EAAE,QAAQ;MAClB,SAAS,EAAE,SAAS;MACpB,UAAU,EAAE,UAAU;MACtB,iBAAiB,EAAE,iBAAiB;MACpC,kBAAkB,EAAE,kBAAkB;MACtC,uBAAuB,EAAE,uBAAuB;MAChD,sBAAsB,EAAE,sBAAsB;MAC9C,uBAAuB,EAAE,uBAAuB;MAChD,aAAa,EAAE,aAAa;MAC5B,kBAAkB,EAAE,kBAAkB;MACtC,sBAAsB,EAAE,sBAAsB;MAC9C,gBAAgB,EAAE,gBAAgB;MAClC,YAAY,EAAE;IAChB,CAAC;IAED,OAAOA,YAAY,CAACD,gBAAgB,CAAC,IAAIA,gBAAgB,CAACE,WAAW,CAAC,CAAC;EACzE,CAAC;EAED,MAAM5K,eAAe,GAAIlD,KAAK,IAAK;IACjC,IAAI,CAACA,KAAK,CAACiD,WAAW,EAAE,OAAO,IAAI;IAEnC,MAAM8K,cAAc,GAAGnQ,QAAQ,CAACoC,KAAK,CAACiD,WAAW,CAACjD,KAAK,CAAC;IACxD,MAAMgO,aAAa,GAAGhO,KAAK,CAACiD,WAAW,CAACrB,KAAK;;IAE7C;IACA,IAAI,OAAOoM,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,OAAO,EAAE;MAC/F,OAAOD,cAAc,MAAMC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,MAAM,CAAC;IAChF;IAEA,OAAOD,cAAc,KAAKC,aAAa;EACzC,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACvQ,UAAU,IAAI,CAACA,UAAU,CAAC6B,MAAM,EAAE,OAAO,CAAC,CAAC;IAEhD,MAAM2O,QAAQ,GAAG,CAAC,CAAC;IACnBxQ,UAAU,CAAC6B,MAAM,CAACQ,OAAO,CAAEC,KAAK,IAAK;MACnC,MAAMmO,UAAU,GAAGnO,KAAK,CAACoO,OAAO,IAAI,SAAS;MAC7C,IAAI,CAACF,QAAQ,CAACC,UAAU,CAAC,EAAE;QACzBD,QAAQ,CAACC,UAAU,CAAC,GAAG;UACrBtB,KAAK,EAAEwB,eAAe,CAACF,UAAU,CAAC;UAClC5O,MAAM,EAAE;QACV,CAAC;MACH;MACA2O,QAAQ,CAACC,UAAU,CAAC,CAAC5O,MAAM,CAACa,IAAI,CAACJ,KAAK,CAAC;IACzC,CAAC,CAAC;IAEF,OAAOkO,QAAQ;EACjB,CAAC;EAED,MAAMG,eAAe,GAAIF,UAAU,IAAK;IACtC,MAAMG,MAAM,GAAG;MACbC,YAAY,EAAE,sBAAsB;MACpCC,WAAW,EAAE,qBAAqB;MAClCC,YAAY,EAAE,sBAAsB;MACpCC,YAAY,EAAE,sBAAsB;MACpCC,aAAa,EAAE,uBAAuB;MACtCC,eAAe,EAAE,kBAAkB;MACnCC,WAAW,EAAE,qBAAqB;MAClCC,gBAAgB,EAAE,mBAAmB;MACrCC,aAAa,EAAE,uBAAuB;MACtCjE,OAAO,EAAE;IACX,CAAC;IACD,OAAOwD,MAAM,CAACH,UAAU,CAAC,IAAIA,UAAU;EACzC,CAAC;EAED,MAAMD,QAAQ,GAAGxQ,UAAU,GAAGuQ,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC;EAI1D,oBACE1R,OAAA;IAAKyS,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClC1S,OAAA;MAAKyS,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B1S,OAAA;QAAA0S,QAAA,EAAKrS,IAAI,KAAK,QAAQ,GAAG,mBAAmB,GAAG;MAAa;QAAAsS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACjE3R,UAAU,iBACTnB,OAAA;QAAKyS,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB1S,OAAA;UAAMyS,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEvR,UAAU,CAAC6E;QAAI;UAAA2M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACnD3R,UAAU,CAAC4R,WAAW,iBACrB/S,OAAA;UAAMyS,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAEvR,UAAU,CAAC4R;QAAW;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAClE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELvR,MAAM,CAACgN,OAAO,iBACbvO,OAAA;MAAKyS,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAEnR,MAAM,CAACgN;IAAO;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,EAGA1K,MAAM,CAACC,IAAI,CAAC9G,MAAM,CAAC,CAACwC,MAAM,GAAG,CAAC,IAAI,CAACxC,MAAM,CAACgN,OAAO,iBAChDvO,OAAA;MAAKyS,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC1S,OAAA;QAAA0S,QAAA,EAAQ;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACjD9S,OAAA;QAAIgT,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAe,CAAE;QAAAP,QAAA,EACnCtK,MAAM,CAACkG,OAAO,CAAC/M,MAAM,CAAC,CACpBoI,MAAM,CAAC,CAAC,CAAChG,GAAG,CAAC,KAAKA,GAAG,KAAK,SAAS,CAAC,CACpCmG,GAAG,CAAC,CAAC,CAACnG,GAAG,EAAExB,OAAO,CAAC,kBAClBnC,OAAA;UAAA0S,QAAA,EAAevQ;QAAO,GAAbwB,GAAG;UAAAgP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACN,eAED9S,OAAA;MAAME,QAAQ,EAAEoI,YAAa;MAACmK,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAEnD1S,OAAA;QAAKyS,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B1S,OAAA;UAAA0S,QAAA,EAAI;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGtC9S,OAAA;UAAKyS,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1S,OAAA;YAAOyS,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAA1S,OAAA;cAAMyS,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACR9S,OAAA;YACEqF,KAAK,EAAExE,gBAAiB;YACxBqS,QAAQ,EAAE/N,oBAAqB;YAC/BgO,QAAQ,EAAE1R,OAAO,CAAClB,SAAU;YAC5BkS,SAAS,EAAE,eAAelR,MAAM,CAACsE,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DW,QAAQ;YAAAkM,QAAA,gBAER1S,OAAA;cAAQqF,KAAK,EAAC,EAAE;cAAAqN,QAAA,EACbjR,OAAO,CAAClB,SAAS,GAAG,sBAAsB,GAAG;YAAiB;cAAAoS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,EACRvS,SAAS,CAACuJ,GAAG,CAACjE,QAAQ,iBACrB7F,OAAA;cAA0BqF,KAAK,EAAEQ,QAAQ,CAACsJ,EAAG;cAAAuD,QAAA,EAC1C7M,QAAQ,CAACG;YAAI,GADHH,QAAQ,CAACsJ,EAAE;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRvR,MAAM,CAACsE,QAAQ,iBACd7F,OAAA;YAAKyS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEnR,MAAM,CAACsE;UAAQ;YAAA8M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACAvR,MAAM,CAAChB,SAAS,iBACfP,OAAA;YAAKyS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEnR,MAAM,CAAChB;UAAS;YAAAoS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN9S,OAAA;UAAKyS,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1S,OAAA;YAAOyS,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAA1S,OAAA;cAAMyS,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACR9S,OAAA;YACEqF,KAAK,EAAEtE,gBAAiB;YACxBmS,QAAQ,EAAE3N,oBAAqB;YAC/B4N,QAAQ,EAAE,CAACtS,gBAAgB,IAAIY,OAAO,CAAChB,UAAW;YAClDgS,SAAS,EAAE,eAAelR,MAAM,CAACuE,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DU,QAAQ;YAAAkM,QAAA,gBAER1S,OAAA;cAAQqF,KAAK,EAAC,EAAE;cAAAqN,QAAA,EACb,CAAC7R,gBAAgB,GACd,uBAAuB,GACvBY,OAAO,CAAChB,UAAU,GAChB,uBAAuB,GACvB;YAAiB;cAAAkS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CAAC,EACRrS,UAAU,CAACqJ,GAAG,CAAChE,QAAQ,iBACtB9F,OAAA;cAA0BqF,KAAK,EAAES,QAAQ,CAACqJ,EAAG;cAAAuD,QAAA,EAC1C5M,QAAQ,CAACE;YAAI,GADHF,QAAQ,CAACqJ,EAAE;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRvR,MAAM,CAACuE,QAAQ,iBACd9F,OAAA;YAAKyS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEnR,MAAM,CAACuE;UAAQ;YAAA6M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACAvR,MAAM,CAACd,UAAU,iBAChBT,OAAA;YAAKyS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEnR,MAAM,CAACd;UAAU;YAAAkS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLhR,gBAAgB,CAACI,uBAAuB,iBACvClC,OAAA;UAAKyS,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1S,OAAA;YAAOyS,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9S,OAAA;YACEqF,KAAK,EAAEjB,mBAAoB;YAC3B8O,QAAQ,EAAE1N,uBAAwB;YAClC2N,QAAQ,EAAE,CAACpS,gBAAgB,IAAIU,OAAO,CAACkD,aAAc;YACrD8N,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAEvB1S,OAAA;cAAQqF,KAAK,EAAC,EAAE;cAAAqN,QAAA,EACb,CAAC3R,gBAAgB,GACd,uBAAuB,GACvBU,OAAO,CAACkD,aAAa,GACnB,0BAA0B,GAC1B;YAA+B;cAAAgO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE/B,CAAC,EACRnO,aAAa,CAACmF,GAAG,CAAC/D,WAAW,iBAC5B/F,OAAA;cAA6BqF,KAAK,EAAEU,WAAW,CAACoJ,EAAG;cAAAuD,QAAA,EAChD3M,WAAW,CAACC;YAAI,GADND,WAAW,CAACoJ,EAAE;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEnB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRvR,MAAM,CAACoD,aAAa,iBACnB3E,OAAA;YAAKyS,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEnR,MAAM,CAACoD;UAAa;YAAAgO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC3D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAGArR,OAAO,CAACE,IAAI,iBACX3B,OAAA;UAAKyS,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1S,OAAA;YAAA0S,QAAA,EAAM;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN,EAEAhR,gBAAgB,CAACK,OAAO,IAAI,CAACV,OAAO,CAACE,IAAI,iBACxC3B,OAAA;UAAKyS,SAAS,EAAE,kBACd3Q,gBAAgB,CAACE,eAAe,IAAIF,gBAAgB,CAACG,kBAAkB,GACnE,SAAS,GACTH,gBAAgB,CAACK,OAAO,CAACmE,QAAQ,CAAC,SAAS,CAAC,GAC1C,OAAO,GACP,MAAM,EACX;UAAAoM,QAAA,EACA5Q,gBAAgB,CAACK;QAAO;UAAAwQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACN,EAEAvR,MAAM,CAACI,IAAI,iBACV3B,OAAA;UAAKyS,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEnR,MAAM,CAACI;QAAI;UAAAgR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAClD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL3R,UAAU,IAAIiH,MAAM,CAACkG,OAAO,CAACqD,QAAQ,CAAC,CAAC7H,GAAG,CAAC,CAAC,CAAC8H,UAAU,EAAEC,OAAO,CAAC,kBAChE7R,OAAA;QAAsByS,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC5C1S,OAAA;UAAA0S,QAAA,EAAKb,OAAO,CAACvB;QAAK;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxB9S,OAAA;UAAKyS,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBb,OAAO,CAAC7O,MAAM,CACZ2G,MAAM,CAAClG,KAAK,IAAIkD,eAAe,CAAClD,KAAK,CAAC,CAAC,CACvCqG,GAAG,CAAC,CAACrG,KAAK,EAAE2P,UAAU,kBACrBpT,OAAA,CAACF,SAAS;YAER2D,KAAK,EAAEA,KAAM;YACb4B,KAAK,EAAEhE,QAAQ,CAACoC,KAAK,CAACE,GAAG,CAAE;YAC3BuP,QAAQ,EAAG7N,KAAK,IAAKI,iBAAiB,CAAChC,KAAK,CAACE,GAAG,EAAE0B,KAAK,CAAE;YACzDlC,KAAK,EAAE5B,MAAM,CAACkC,KAAK,CAACE,GAAG;UAAE,GAJpB,GAAGiO,UAAU,IAAInO,KAAK,CAACE,GAAG,IAAIyP,UAAU,EAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKhD,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA,GAdElB,UAAU;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAef,CACN,CAAC,eAKF9S,OAAA;QAAKyS,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B1S,OAAA;UACE+G,IAAI,EAAC,QAAQ;UACbsM,OAAO,EAAElT,QAAS;UAClBsS,SAAS,EAAC,iBAAiB;UAC3BU,QAAQ,EAAEvR,UAAW;UAAA8Q,QAAA,EACtB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9S,OAAA;UACE+G,IAAI,EAAC,QAAQ;UACb0L,SAAS,EAAC,iBAAiB;UAC3BU,QAAQ,EAAEvR,UAAU,IAAI,CAACf,gBAAgB,IAAI,CAACE,gBAAiB;UAAA2R,QAAA,EAE9D9Q,UAAU,GACNvB,IAAI,KAAK,QAAQ,GAAG,aAAa,GAAG,aAAa,GACjDA,IAAI,KAAK,QAAQ,GAAG,eAAe,GAAG;QAAgB;UAAAsS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACxS,EAAA,CA7lCIL,iBAAiB;AAAAqT,EAAA,GAAjBrT,iBAAiB;AA+lCvB,eAAeA,iBAAiB;AAAC,IAAAqT,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}