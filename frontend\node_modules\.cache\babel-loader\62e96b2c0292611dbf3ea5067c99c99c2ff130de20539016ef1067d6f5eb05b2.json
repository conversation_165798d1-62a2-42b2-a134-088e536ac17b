{"ast": null, "code": "import React,{useState}from'react';import{motion}from'framer-motion';import{<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>yeOff}from'react-icons/fi';import{useAuth}from'../context/AuthContext';import{toast}from'react-toastify';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Login=()=>{const[formData,setFormData]=useState({username:'',password:''});const[showPassword,setShowPassword]=useState(false);const[loading,setLoading]=useState(false);const{login}=useAuth();const handleChange=e=>{setFormData({...formData,[e.target.name]:e.target.value});};const handleSubmit=async e=>{e.preventDefault();setLoading(true);const result=await login(formData.username,formData.password);if(result.success){toast.success('Login successful!');}else{toast.error(result.message);}setLoading(false);};return/*#__PURE__*/_jsxs(\"div\",{className:\"login-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"login-background\",children:/*#__PURE__*/_jsx(\"div\",{className:\"login-overlay\"})}),/*#__PURE__*/_jsxs(motion.div,{className:\"login-card\",initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:0.6},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"login-header\",children:[/*#__PURE__*/_jsx(motion.div,{className:\"login-logo\",initial:{scale:0},animate:{scale:1},transition:{delay:0.2,duration:0.5},children:/*#__PURE__*/_jsx(\"div\",{className:\"logo-icon\",children:/*#__PURE__*/_jsx(FiUser,{size:40})})}),/*#__PURE__*/_jsx(\"h1\",{children:\"Admin Panel\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Welcome back! Please sign in to continue.\"})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"login-form\",children:[/*#__PURE__*/_jsx(motion.div,{className:\"form-group\",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:0.3,duration:0.5},children:/*#__PURE__*/_jsxs(\"div\",{className:\"input-wrapper\",children:[/*#__PURE__*/_jsx(FiUser,{className:\"input-icon\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"username\",placeholder:\"Username\",value:formData.username,onChange:handleChange,required:true,className:\"form-input\"})]})}),/*#__PURE__*/_jsx(motion.div,{className:\"form-group\",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:0.4,duration:0.5},children:/*#__PURE__*/_jsxs(\"div\",{className:\"input-wrapper\",children:[/*#__PURE__*/_jsx(FiLock,{className:\"input-icon\"}),/*#__PURE__*/_jsx(\"input\",{type:showPassword?'text':'password',name:\"password\",placeholder:\"Password\",value:formData.password,onChange:handleChange,required:true,className:\"form-input\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"password-toggle\",onClick:()=>setShowPassword(!showPassword),children:showPassword?/*#__PURE__*/_jsx(FiEyeOff,{}):/*#__PURE__*/_jsx(FiEye,{})})]})}),/*#__PURE__*/_jsx(motion.button,{type:\"submit\",className:\"login-btn\",disabled:loading,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.5,duration:0.5},whileHover:{scale:1.02},whileTap:{scale:0.98},children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}):'Sign In'})]}),/*#__PURE__*/_jsx(\"div\",{className:\"login-footer\",children:/*#__PURE__*/_jsx(\"p\",{children:\"Default credentials: admin / admin123\"})})]})]});};export default Login;", "map": {"version": 3, "names": ["React", "useState", "motion", "FiUser", "FiLock", "FiEye", "Fi<PERSON>ye<PERSON>ff", "useAuth", "toast", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "formData", "setFormData", "username", "password", "showPassword", "setShowPassword", "loading", "setLoading", "login", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "result", "success", "error", "message", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "scale", "delay", "size", "onSubmit", "x", "type", "placeholder", "onChange", "required", "onClick", "button", "disabled", "whileHover", "whileTap"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Fi<PERSON>yeOff } from 'react-icons/fi';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport { toast } from 'react-toastify';\r\n\r\nconst Login = () => {\r\n  const [formData, setFormData] = useState({\r\n    username: '',\r\n    password: ''\r\n  });\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const { login } = useAuth();\r\n\r\n  const handleChange = (e) => {\r\n    setFormData({\r\n      ...formData,\r\n      [e.target.name]: e.target.value\r\n    });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n\r\n    const result = await login(formData.username, formData.password);\r\n    \r\n    if (result.success) {\r\n      toast.success('Login successful!');\r\n    } else {\r\n      toast.error(result.message);\r\n    }\r\n    \r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"login-container\">\r\n      <div className=\"login-background\">\r\n        <div className=\"login-overlay\"></div>\r\n      </div>\r\n      \r\n      <motion.div \r\n        className=\"login-card\"\r\n        initial={{ opacity: 0, y: 50 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.6 }}\r\n      >\r\n        <div className=\"login-header\">\r\n          <motion.div \r\n            className=\"login-logo\"\r\n            initial={{ scale: 0 }}\r\n            animate={{ scale: 1 }}\r\n            transition={{ delay: 0.2, duration: 0.5 }}\r\n          >\r\n            <div className=\"logo-icon\">\r\n              <FiUser size={40} />\r\n            </div>\r\n          </motion.div>\r\n          <h1>Admin Panel</h1>\r\n          <p>Welcome back! Please sign in to continue.</p>\r\n        </div>\r\n\r\n        <form onSubmit={handleSubmit} className=\"login-form\">\r\n          <motion.div \r\n            className=\"form-group\"\r\n            initial={{ opacity: 0, x: -20 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            transition={{ delay: 0.3, duration: 0.5 }}\r\n          >\r\n            <div className=\"input-wrapper\">\r\n              <FiUser className=\"input-icon\" />\r\n              <input\r\n                type=\"text\"\r\n                name=\"username\"\r\n                placeholder=\"Username\"\r\n                value={formData.username}\r\n                onChange={handleChange}\r\n                required\r\n                className=\"form-input\"\r\n              />\r\n            </div>\r\n          </motion.div>\r\n\r\n          <motion.div \r\n            className=\"form-group\"\r\n            initial={{ opacity: 0, x: -20 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            transition={{ delay: 0.4, duration: 0.5 }}\r\n          >\r\n            <div className=\"input-wrapper\">\r\n              <FiLock className=\"input-icon\" />\r\n              <input\r\n                type={showPassword ? 'text' : 'password'}\r\n                name=\"password\"\r\n                placeholder=\"Password\"\r\n                value={formData.password}\r\n                onChange={handleChange}\r\n                required\r\n                className=\"form-input\"\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                className=\"password-toggle\"\r\n                onClick={() => setShowPassword(!showPassword)}\r\n              >\r\n                {showPassword ? <FiEyeOff /> : <FiEye />}\r\n              </button>\r\n            </div>\r\n          </motion.div>\r\n\r\n          <motion.button\r\n            type=\"submit\"\r\n            className=\"login-btn\"\r\n            disabled={loading}\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.5, duration: 0.5 }}\r\n            whileHover={{ scale: 1.02 }}\r\n            whileTap={{ scale: 0.98 }}\r\n          >\r\n            {loading ? (\r\n              <div className=\"loading-spinner\"></div>\r\n            ) : (\r\n              'Sign In'\r\n            )}\r\n          </motion.button>\r\n        </form>\r\n\r\n        <div className=\"login-footer\">\r\n          <p>Default credentials: admin / admin123</p>\r\n        </div>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,gBAAgB,CAChE,OAASC,OAAO,KAAQ,wBAAwB,CAChD,OAASC,KAAK,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvC,KAAM,CAAAC,KAAK,CAAGA,CAAA,GAAM,CAClB,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGd,QAAQ,CAAC,CACvCe,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,EACZ,CAAC,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGlB,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACmB,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAEqB,KAAM,CAAC,CAAGf,OAAO,CAAC,CAAC,CAE3B,KAAM,CAAAgB,YAAY,CAAIC,CAAC,EAAK,CAC1BT,WAAW,CAAC,CACV,GAAGD,QAAQ,CACX,CAACU,CAAC,CAACC,MAAM,CAACC,IAAI,EAAGF,CAAC,CAACC,MAAM,CAACE,KAC5B,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAJ,CAAC,EAAK,CAChCA,CAAC,CAACK,cAAc,CAAC,CAAC,CAClBR,UAAU,CAAC,IAAI,CAAC,CAEhB,KAAM,CAAAS,MAAM,CAAG,KAAM,CAAAR,KAAK,CAACR,QAAQ,CAACE,QAAQ,CAAEF,QAAQ,CAACG,QAAQ,CAAC,CAEhE,GAAIa,MAAM,CAACC,OAAO,CAAE,CAClBvB,KAAK,CAACuB,OAAO,CAAC,mBAAmB,CAAC,CACpC,CAAC,IAAM,CACLvB,KAAK,CAACwB,KAAK,CAACF,MAAM,CAACG,OAAO,CAAC,CAC7B,CAEAZ,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED,mBACET,KAAA,QAAKsB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BzB,IAAA,QAAKwB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BzB,IAAA,QAAKwB,SAAS,CAAC,eAAe,CAAM,CAAC,CAClC,CAAC,cAENtB,KAAA,CAACV,MAAM,CAACkC,GAAG,EACTF,SAAS,CAAC,YAAY,CACtBG,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAP,QAAA,eAE9BvB,KAAA,QAAKsB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BzB,IAAA,CAACR,MAAM,CAACkC,GAAG,EACTF,SAAS,CAAC,YAAY,CACtBG,OAAO,CAAE,CAAEM,KAAK,CAAE,CAAE,CAAE,CACtBH,OAAO,CAAE,CAAEG,KAAK,CAAE,CAAE,CAAE,CACtBF,UAAU,CAAE,CAAEG,KAAK,CAAE,GAAG,CAAEF,QAAQ,CAAE,GAAI,CAAE,CAAAP,QAAA,cAE1CzB,IAAA,QAAKwB,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBzB,IAAA,CAACP,MAAM,EAAC0C,IAAI,CAAE,EAAG,CAAE,CAAC,CACjB,CAAC,CACI,CAAC,cACbnC,IAAA,OAAAyB,QAAA,CAAI,aAAW,CAAI,CAAC,cACpBzB,IAAA,MAAAyB,QAAA,CAAG,2CAAyC,CAAG,CAAC,EAC7C,CAAC,cAENvB,KAAA,SAAMkC,QAAQ,CAAElB,YAAa,CAACM,SAAS,CAAC,YAAY,CAAAC,QAAA,eAClDzB,IAAA,CAACR,MAAM,CAACkC,GAAG,EACTF,SAAS,CAAC,YAAY,CACtBG,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAES,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCP,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAES,CAAC,CAAE,CAAE,CAAE,CAC9BN,UAAU,CAAE,CAAEG,KAAK,CAAE,GAAG,CAAEF,QAAQ,CAAE,GAAI,CAAE,CAAAP,QAAA,cAE1CvB,KAAA,QAAKsB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BzB,IAAA,CAACP,MAAM,EAAC+B,SAAS,CAAC,YAAY,CAAE,CAAC,cACjCxB,IAAA,UACEsC,IAAI,CAAC,MAAM,CACXtB,IAAI,CAAC,UAAU,CACfuB,WAAW,CAAC,UAAU,CACtBtB,KAAK,CAAEb,QAAQ,CAACE,QAAS,CACzBkC,QAAQ,CAAE3B,YAAa,CACvB4B,QAAQ,MACRjB,SAAS,CAAC,YAAY,CACvB,CAAC,EACC,CAAC,CACI,CAAC,cAEbxB,IAAA,CAACR,MAAM,CAACkC,GAAG,EACTF,SAAS,CAAC,YAAY,CACtBG,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAES,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCP,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAES,CAAC,CAAE,CAAE,CAAE,CAC9BN,UAAU,CAAE,CAAEG,KAAK,CAAE,GAAG,CAAEF,QAAQ,CAAE,GAAI,CAAE,CAAAP,QAAA,cAE1CvB,KAAA,QAAKsB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BzB,IAAA,CAACN,MAAM,EAAC8B,SAAS,CAAC,YAAY,CAAE,CAAC,cACjCxB,IAAA,UACEsC,IAAI,CAAE9B,YAAY,CAAG,MAAM,CAAG,UAAW,CACzCQ,IAAI,CAAC,UAAU,CACfuB,WAAW,CAAC,UAAU,CACtBtB,KAAK,CAAEb,QAAQ,CAACG,QAAS,CACzBiC,QAAQ,CAAE3B,YAAa,CACvB4B,QAAQ,MACRjB,SAAS,CAAC,YAAY,CACvB,CAAC,cACFxB,IAAA,WACEsC,IAAI,CAAC,QAAQ,CACbd,SAAS,CAAC,iBAAiB,CAC3BkB,OAAO,CAAEA,CAAA,GAAMjC,eAAe,CAAC,CAACD,YAAY,CAAE,CAAAiB,QAAA,CAE7CjB,YAAY,cAAGR,IAAA,CAACJ,QAAQ,GAAE,CAAC,cAAGI,IAAA,CAACL,KAAK,GAAE,CAAC,CAClC,CAAC,EACN,CAAC,CACI,CAAC,cAEbK,IAAA,CAACR,MAAM,CAACmD,MAAM,EACZL,IAAI,CAAC,QAAQ,CACbd,SAAS,CAAC,WAAW,CACrBoB,QAAQ,CAAElC,OAAQ,CAClBiB,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEG,KAAK,CAAE,GAAG,CAAEF,QAAQ,CAAE,GAAI,CAAE,CAC1Ca,UAAU,CAAE,CAAEZ,KAAK,CAAE,IAAK,CAAE,CAC5Ba,QAAQ,CAAE,CAAEb,KAAK,CAAE,IAAK,CAAE,CAAAR,QAAA,CAEzBf,OAAO,cACNV,IAAA,QAAKwB,SAAS,CAAC,iBAAiB,CAAM,CAAC,CAEvC,SACD,CACY,CAAC,EACZ,CAAC,cAEPxB,IAAA,QAAKwB,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BzB,IAAA,MAAAyB,QAAA,CAAG,uCAAqC,CAAG,CAAC,CACzC,CAAC,EACI,CAAC,EACV,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}