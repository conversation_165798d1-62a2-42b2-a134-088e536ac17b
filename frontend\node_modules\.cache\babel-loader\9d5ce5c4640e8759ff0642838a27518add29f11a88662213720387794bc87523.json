{"ast": null, "code": "import{useState,useEffect}from'react';import{PersonFieldDefinitions,getAllPersonFields}from'../../constants/personConstants';import formConfigService from'../../services/formConfigService';import apiService from'../../services/apiService';import FieldConfigModal from'./FieldConfigModal';import FormPreview from'./FormPreview';import'./FormBuilder.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FormBuilder=_ref=>{let{onSave,onCancel,initialConfig=null}=_ref;const[selectedHierarchy,setSelectedHierarchy]=useState({});const[formName,setFormName]=useState('');const[formDescription,setFormDescription]=useState('');const[availableFields,setAvailableFields]=useState([]);const[selectedFields,setSelectedFields]=useState([]);const[currentSection,setCurrentSection]=useState('all');const[searchTerm,setSearchTerm]=useState('');const[showPreview,setShowPreview]=useState(false);const[showFieldConfig,setShowFieldConfig]=useState(false);const[configField,setConfigField]=useState(null);const[errors,setErrors]=useState({});const[saving,setSaving]=useState(false);const[savedForms,setSavedForms]=useState([]);const[showSavedForms,setShowSavedForms]=useState(false);// State for dropdown data\nconst[divisions,setDivisions]=useState([]);const[categories,setCategories]=useState([]);const[firmNatures,setFirmNatures]=useState([]);const[loading,setLoading]=useState({divisions:false,categories:false,firmNatures:false});const[existingFormInfo,setExistingFormInfo]=useState(null);const[isEditMode,setIsEditMode]=useState(false);const[editingFormId,setEditingFormId]=useState(null);useEffect(()=>{initializeFields();loadSavedForms();if(initialConfig){loadInitialConfig(initialConfig);}},[initialConfig]);const initializeFields=()=>{const allFields=getAllPersonFields();setAvailableFields(allFields);};// Deduplicate fields based on field key\nconst deduplicateFields=fields=>{const seen=new Set();const deduplicated=[];fields.forEach(field=>{if(!seen.has(field.key)){seen.add(field.key);deduplicated.push(field);}});return deduplicated;};const loadSavedForms=()=>{const forms=formConfigService.getAllFormConfigs();setSavedForms(forms);};const loadInitialConfig=config=>{setFormName(config.name||'');setFormDescription(config.description||'');// Deduplicate fields when loading initial config\nconst fields=config.fields||[];const deduplicatedFields=deduplicateFields(fields);setSelectedFields(deduplicatedFields);// Set edit mode when loading existing form\nsetIsEditMode(true);setEditingFormId(config.id||config.key);if(config.type==='division'&&config.associatedId){// Load division info for hierarchy selector\nsetSelectedHierarchy({divisionId:config.associatedId});}else if(config.type==='category'&&config.associatedId){// Load category info for hierarchy selector\nsetSelectedHierarchy({categoryId:config.associatedId});}else if(config.type==='firmnature'&&config.associatedId){// Load firm nature info for hierarchy selector\nsetSelectedHierarchy({firmNatureId:config.associatedId});}// If hierarchy is stored in config, use that\nif(config.hierarchy){setSelectedHierarchy(config.hierarchy);}};// Load divisions on component mount\nuseEffect(()=>{loadDivisions();},[]);// Validate form creation rules when hierarchy changes\nuseEffect(()=>{if(selectedHierarchy.categoryId){// Skip validation in edit mode since we're updating an existing form\nif(!isEditMode){const formValidation=formConfigService.validateFormCreation(selectedHierarchy.categoryId,selectedHierarchy.firmNatureId);if(!formValidation.isValid){setErrors(prev=>({...prev,formCreation:formValidation.errors.join('. ')}));}else{setErrors(prev=>{const newErrors={...prev};delete newErrors.formCreation;return newErrors;});}}else{// In edit mode, clear any form creation errors\nsetErrors(prev=>{const newErrors={...prev};delete newErrors.formCreation;return newErrors;});}const formInfo=formConfigService.getExistingFormInfo(selectedHierarchy.categoryId,selectedHierarchy.firmNatureId);setExistingFormInfo(formInfo);}else{setExistingFormInfo(null);setErrors(prev=>{const newErrors={...prev};delete newErrors.formCreation;return newErrors;});}},[selectedHierarchy.categoryId,selectedHierarchy.firmNatureId,isEditMode]);const loadDivisions=async()=>{setLoading(prev=>({...prev,divisions:true}));try{const response=await apiService.getDivisions();setDivisions(response.data||[]);}catch(error){console.error('Error loading divisions:',error);}finally{setLoading(prev=>({...prev,divisions:false}));}};const loadCategories=async divisionId=>{if(!divisionId){setCategories([]);setFirmNatures([]);return;}setLoading(prev=>({...prev,categories:true}));try{const response=await apiService.getCategoriesByDivision(divisionId);setCategories(response.data||[]);}catch(error){console.error('Error loading categories:',error);setCategories([]);}finally{setLoading(prev=>({...prev,categories:false}));}};const loadFirmNatures=async categoryId=>{if(!categoryId){setFirmNatures([]);return;}setLoading(prev=>({...prev,firmNatures:true}));try{const response=await apiService.getFirmNaturesByCategory(categoryId);setFirmNatures(response.data||[]);}catch(error){console.error('Error loading firm natures:',error);setFirmNatures([]);}finally{setLoading(prev=>({...prev,firmNatures:false}));}};const handleDivisionChange=e=>{const divisionId=e.target.value;const division=divisions.find(d=>d.id===parseInt(divisionId));setSelectedHierarchy({divisionId:divisionId||null,categoryId:null,firmNatureId:null,division:division||null,category:null,firmNature:null});setCategories([]);setFirmNatures([]);if(divisionId){loadCategories(divisionId);}};const handleCategoryChange=e=>{const categoryId=e.target.value;const category=categories.find(c=>c.id===parseInt(categoryId));setSelectedHierarchy(prev=>({...prev,categoryId:categoryId||null,firmNatureId:null,category:category||null,firmNature:null}));setFirmNatures([]);if(categoryId){loadFirmNatures(categoryId);}};const handleFirmNatureChange=e=>{const firmNatureId=e.target.value;const firmNature=firmNatures.find(fn=>fn.id===parseInt(firmNatureId));setSelectedHierarchy(prev=>({...prev,firmNatureId:firmNatureId||null,firmNature:firmNature||null}));};const handleFieldToggle=field=>{const isSelected=selectedFields.some(f=>f.key===field.key);if(isSelected){// Remove all instances of this field key (in case there are duplicates)\nsetSelectedFields(prev=>prev.filter(f=>f.key!==field.key));}else{// Add field only if it doesn't already exist\nsetSelectedFields(prev=>{const exists=prev.some(f=>f.key===field.key);if(exists){return prev;}return[...prev,{...field}];});}};const handleFieldConfig=field=>{setConfigField(field);setShowFieldConfig(true);};const handleFieldConfigSave=updatedField=>{setSelectedFields(prev=>prev.map(f=>f.key===updatedField.key?updatedField:f));setShowFieldConfig(false);setConfigField(null);};const handleFieldMove=(fromIndex,toIndex)=>{const newFields=[...selectedFields];const[movedField]=newFields.splice(fromIndex,1);newFields.splice(toIndex,0,movedField);setSelectedFields(newFields);};const handleSelectAllFields=()=>{// Get all filtered fields (respects current section and search)\nconst fieldsToAdd=filteredFields.filter(field=>!selectedFields.some(selected=>selected.key===field.key));setSelectedFields(prev=>[...prev,...fieldsToAdd]);};const handleDeselectAllFields=()=>{// Remove all filtered fields from selection\nconst filteredFieldKeys=filteredFields.map(field=>field.key);setSelectedFields(prev=>prev.filter(field=>!filteredFieldKeys.includes(field.key)));};const handleSelectAllAvailableFields=()=>{// Select all available fields regardless of current filter\nconst fieldsToAdd=availableFields.filter(field=>!selectedFields.some(selected=>selected.key===field.key));setSelectedFields(prev=>[...prev,...fieldsToAdd]);};const handleClearAllFields=()=>{setSelectedFields([]);};const handleSave=async()=>{const validation=validateForm();if(!validation.isValid){setErrors(validation.errors);return;}setSaving(true);try{// Ensure complete hierarchy information is included\nconst completeHierarchy={...selectedHierarchy,// Include division info (required)\ndivisionId:selectedHierarchy.divisionId,division:selectedHierarchy.division,// Include category info (required)\ncategoryId:selectedHierarchy.categoryId,category:selectedHierarchy.category,// Include firm nature info if selected (required)\nfirmNatureId:selectedHierarchy.firmNatureId||null,firmNature:selectedHierarchy.firmNature||null};const config={name:formName,description:formDescription,fields:selectedFields,hierarchy:completeHierarchy,settings:{showSections:true,allowConditionalFields:true,validateOnChange:true}};let savedConfig;if(isEditMode){// In edit mode, update the existing form\n// Preserve the original type and associatedId from the form being edited\nconst originalForm=formConfigService.getAllFormConfigs().find(f=>f.id===editingFormId||f.key===editingFormId);if(originalForm){// Update the existing form with new data\nconfig.id=originalForm.id;config.key=originalForm.key;config.type=originalForm.type;config.associatedId=originalForm.associatedId;config.createdAt=originalForm.createdAt;// Preserve creation date\nconfig.updatedAt=new Date().toISOString();// Update modification date\nsavedConfig=formConfigService.saveFormConfig(originalForm.type,originalForm.associatedId,config);}else{throw new Error('Original form not found for editing');}}else{// Create new form with the most specific level selected, but include complete hierarchy\nif(selectedHierarchy.firmNatureId){savedConfig=formConfigService.saveFormConfig('firmnature',selectedHierarchy.firmNatureId,config);}else if(selectedHierarchy.categoryId){savedConfig=formConfigService.saveFormConfig('category',selectedHierarchy.categoryId,config);}else{throw new Error('Please select division, category, and firm nature');}}// Reload saved forms list\nloadSavedForms();if(onSave){onSave(savedConfig);}else{// Show success message for standalone usage\nconst message=isEditMode?'Form updated successfully!':'Form configuration saved successfully!';alert(message);// Reset form\nsetFormName('');setFormDescription('');setSelectedFields([]);setSelectedHierarchy({});setIsEditMode(false);setEditingFormId(null);}}catch(error){console.error('Error saving form:',error);setErrors({general:error.message});}finally{setSaving(false);}};const validateForm=()=>{const errors={};if(!formName.trim()){errors.formName='Form name is required';}// Division, category, and firm nature are required\nif(!selectedHierarchy.divisionId){errors.hierarchy='Please select a division';}else if(!selectedHierarchy.categoryId){errors.hierarchy='Please select a category';}else if(!selectedHierarchy.firmNatureId){errors.hierarchy='Please select a firm nature';}// Validate form creation rules (skip in edit mode)\nif(selectedHierarchy.categoryId&&!isEditMode){const formValidation=formConfigService.validateFormCreation(selectedHierarchy.categoryId,selectedHierarchy.firmNatureId);if(!formValidation.isValid){errors.formCreation=formValidation.errors.join('. ');}}if(selectedFields.length===0){errors.fields='Please select at least one field';}// Check required fields\nconst requiredFields=['name','mobileNumber','nature'];const selectedFieldKeys=selectedFields.map(f=>f.key);const missingRequired=requiredFields.filter(rf=>!selectedFieldKeys.includes(rf));if(missingRequired.length>0){errors.requiredFields=`Required fields missing: ${missingRequired.join(', ')}`;}return{isValid:Object.keys(errors).length===0,errors};};const getFilteredFields=()=>{let filtered=availableFields;// Filter by section\nif(currentSection!=='all'){filtered=filtered.filter(field=>field.section===currentSection);}// Filter by search term\nif(searchTerm){filtered=filtered.filter(field=>field.label.toLowerCase().includes(searchTerm.toLowerCase())||field.key.toLowerCase().includes(searchTerm.toLowerCase()));}return filtered;};const getSectionCounts=()=>{const counts={all:availableFields.length};Object.keys(PersonFieldDefinitions).forEach(sectionKey=>{counts[sectionKey]=availableFields.filter(f=>f.section===sectionKey).length;});return counts;};const sectionCounts=getSectionCounts();const filteredFields=getFilteredFields();return/*#__PURE__*/_jsxs(\"div\",{className:\"form-builder\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-builder-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Form Builder\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"header-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setShowPreview(true),className:\"btn btn-secondary\",disabled:selectedFields.length===0,children:\"Preview Form\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleSave,className:\"btn btn-primary\",disabled:saving||errors.formCreation,children:saving?isEditMode?'Updating...':'Saving...':isEditMode?'Update Form':'Save Form'}),onCancel&&/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:onCancel,className:\"btn btn-outline\",children:\"Cancel\"})]})]}),errors.general&&/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-error\",children:errors.general}),Object.keys(errors).length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"validation-errors\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Please fix the following issues:\"}),/*#__PURE__*/_jsx(\"ul\",{children:Object.entries(errors).map(_ref2=>{let[key,message]=_ref2;return/*#__PURE__*/_jsx(\"li\",{children:message},key);})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-builder-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"config-panel\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"config-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"section-header\",children:[/*#__PURE__*/_jsxs(\"h3\",{children:[\"Saved Forms (\",savedForms.length,\")\"]}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"btn-link\",onClick:()=>setShowSavedForms(!showSavedForms),children:showSavedForms?'Hide':'Show'})]}),showSavedForms&&/*#__PURE__*/_jsx(\"div\",{className:\"saved-forms-list\",children:savedForms.length===0?/*#__PURE__*/_jsx(\"p\",{className:\"no-forms-message\",children:\"No saved forms yet. Create your first form below!\"}):savedForms.map(form=>/*#__PURE__*/_jsxs(\"div\",{className:\"saved-form-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-item-header\",children:[/*#__PURE__*/_jsx(\"h4\",{children:form.name}),/*#__PURE__*/_jsx(\"span\",{className:\"form-type-badge\",children:form.type})]}),/*#__PURE__*/_jsx(\"p\",{className:\"form-description\",children:form.description||'No description'}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-meta\",children:[/*#__PURE__*/_jsxs(\"span\",{children:[form.summary.fieldCount,\" fields\"]}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u2022\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Updated \",new Date(form.updatedAt).toLocaleDateString()]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"btn-small btn-outline\",onClick:()=>loadInitialConfig(form),children:\"Load\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"btn-small btn-danger\",onClick:()=>{if(window.confirm('Are you sure you want to delete this form?')){formConfigService.deleteFormConfig(form.type,form.associatedId);loadSavedForms();}},children:\"Delete\"})]})]},form.key))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"config-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Form Configuration\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Form Name *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:formName,onChange:e=>setFormName(e.target.value),placeholder:\"Enter form name\",className:errors.formName?'error':''}),errors.formName&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:errors.formName})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Description\"}),/*#__PURE__*/_jsx(\"textarea\",{value:formDescription,onChange:e=>setFormDescription(e.target.value),placeholder:\"Enter form description\",rows:\"3\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"config-section\",children:[/*#__PURE__*/_jsxs(\"h3\",{children:[\"Selected Fields (\",selectedFields.length,\")\"]}),errors.fields&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:errors.fields}),errors.requiredFields&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:errors.requiredFields}),/*#__PURE__*/_jsx(\"div\",{className:\"selected-fields\",children:selectedFields.length===0?/*#__PURE__*/_jsx(\"div\",{className:\"empty-state\",children:\"No fields selected. Choose fields from the available fields panel.\"}):selectedFields.map((field,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"selected-field\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"field-info\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"field-label\",children:field.label}),/*#__PURE__*/_jsx(\"span\",{className:\"field-type\",children:field.type}),field.required&&/*#__PURE__*/_jsx(\"span\",{className:\"required-badge\",children:\"Required\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"field-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>handleFieldConfig(field),className:\"btn-icon\",title:\"Configure field\",children:\"\\u2699\\uFE0F\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>handleFieldMove(index,Math.max(0,index-1)),className:\"btn-icon\",disabled:index===0,title:\"Move up\",children:\"\\u2191\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>handleFieldMove(index,Math.min(selectedFields.length-1,index+1)),className:\"btn-icon\",disabled:index===selectedFields.length-1,title:\"Move down\",children:\"\\u2193\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>handleFieldToggle(field),className:\"btn-icon remove\",title:\"Remove field\",children:\"\\u2715\"})]})]},field.key))})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"fields-panel\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"fields-header\",children:/*#__PURE__*/_jsx(\"h3\",{children:\"Available Fields\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"hierarchy-selection\",style:{backgroundColor:'#f8f9fa',border:'1px solid #e1e5e9',borderRadius:'8px',padding:'1.5rem',margin:'1rem 0'},children:[/*#__PURE__*/_jsx(\"h4\",{style:{margin:'0 0 1rem 0',color:'#495057'},children:\"Associate with Division, Category & Firm Nature (All Required) *\"}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.875rem',color:'#6c757d',marginBottom:'1rem',padding:'0.75rem',backgroundColor:'#f8f9fa',borderRadius:'4px',border:'1px solid #e9ecef'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Form Creation Rules:\"}),/*#__PURE__*/_jsxs(\"ul\",{style:{margin:'0.5rem 0 0 1rem',paddingLeft:'1rem'},children:[/*#__PURE__*/_jsx(\"li\",{children:\"If you create a form for a category, you cannot create forms for its firm natures\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Each firm nature can have only one form\"}),/*#__PURE__*/_jsx(\"li\",{children:\"If firm natures already have forms, you cannot create a form for the parent category\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"hierarchy-dropdowns\",style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(200px, 1fr))',gap:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'500',color:'#495057'},children:\"Division *\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedHierarchy.divisionId||'',onChange:handleDivisionChange,disabled:loading.divisions,className:errors.divisionId?'error':'',style:{width:'100%',padding:'0.75rem',border:'1px solid #ced4da',borderRadius:'4px',fontSize:'1rem',backgroundColor:'white'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Division\"}),divisions.map(division=>/*#__PURE__*/_jsx(\"option\",{value:division.id,children:division.name},division.id))]}),errors.divisionId&&/*#__PURE__*/_jsx(\"div\",{style:{color:'#dc3545',fontSize:'0.875rem',marginTop:'0.25rem'},children:errors.divisionId})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'500',color:'#495057'},children:\"Category *\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedHierarchy.categoryId||'',onChange:handleCategoryChange,disabled:!selectedHierarchy.divisionId||loading.categories,className:errors.categoryId?'error':'',style:{width:'100%',padding:'0.75rem',border:'1px solid #ced4da',borderRadius:'4px',fontSize:'1rem',backgroundColor:'white'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:!selectedHierarchy.divisionId?'Select Division first':'Select Category'}),categories.map(category=>/*#__PURE__*/_jsx(\"option\",{value:category.id,children:category.name},category.id))]}),errors.categoryId&&/*#__PURE__*/_jsx(\"div\",{style:{color:'#dc3545',fontSize:'0.875rem',marginTop:'0.25rem'},children:errors.categoryId})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'500',color:'#495057'},children:\"Firm Nature (Required) *\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedHierarchy.firmNatureId||'',onChange:handleFirmNatureChange,disabled:!selectedHierarchy.categoryId||loading.firmNatures,required:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #ced4da',borderRadius:'4px',fontSize:'1rem',backgroundColor:'white'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:!selectedHierarchy.categoryId?'Select Category first':'Select Firm Nature (Required)'}),firmNatures.map(firmNature=>/*#__PURE__*/_jsx(\"option\",{value:firmNature.id,children:firmNature.name},firmNature.id))]})]})]}),errors.hierarchy&&/*#__PURE__*/_jsx(\"div\",{style:{color:'#dc3545',fontSize:'0.875rem',marginTop:'0.5rem'},children:errors.hierarchy}),errors.formCreation&&/*#__PURE__*/_jsxs(\"div\",{style:{color:'#dc3545',fontSize:'0.875rem',marginTop:'0.5rem',fontWeight:'bold'},children:[\"\\u26A0\\uFE0F \",errors.formCreation]}),existingFormInfo&&existingFormInfo.existingForms.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'1rem',padding:'0.75rem',backgroundColor:'#fff3cd',border:'1px solid #ffeaa7',borderRadius:'4px',fontSize:'0.875rem'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\uD83D\\uDCCB Existing Forms:\"}),existingFormInfo.existingForms.map((form,index)=>/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'0.5rem'},children:[/*#__PURE__*/_jsxs(\"strong\",{children:[form.type==='category'?'Category':'Firm Nature',\" Form:\"]}),\" \",form.name,form.description&&/*#__PURE__*/_jsx(\"div\",{style:{color:'#6c757d'},children:form.description})]},index))]}),existingFormInfo&&existingFormInfo.firmNaturesWithForms.length>0&&!selectedHierarchy.firmNatureId&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'1rem',padding:'0.75rem',backgroundColor:'#f8d7da',border:'1px solid #f5c6cb',borderRadius:'4px',fontSize:'0.875rem'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u26A0\\uFE0F Warning:\"}),\" This category has \",existingFormInfo.firmNaturesWithForms.length,\" firm nature form(s). You cannot create a form for this category.\"]}),selectedHierarchy.categoryId&&!errors.formCreation&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'1rem',padding:'0.75rem',backgroundColor:'#d4edda',border:'1px solid #c3e6cb',borderRadius:'4px',fontSize:'0.875rem'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u2705 Valid Selection:\"}),\" You can create a form for this \",selectedHierarchy.firmNatureId?'firm nature':'category',\".\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"fields-controls\",children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search fields...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),className:\"search-input\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"section-tabs\",children:[/*#__PURE__*/_jsxs(\"button\",{type:\"button\",onClick:()=>setCurrentSection('all'),className:`section-tab ${currentSection==='all'?'active':''}`,children:[\"All (\",sectionCounts.all,\")\"]}),Object.entries(PersonFieldDefinitions).map(_ref3=>{let[sectionKey,section]=_ref3;return/*#__PURE__*/_jsxs(\"button\",{type:\"button\",onClick:()=>setCurrentSection(sectionKey),className:`section-tab ${currentSection===sectionKey?'active':''}`,children:[section.title,\" (\",sectionCounts[sectionKey],\")\"]},sectionKey);})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bulk-selection-controls\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bulk-controls-section\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"bulk-controls-label\",children:\"Quick Actions:\"}),/*#__PURE__*/_jsxs(\"button\",{type:\"button\",onClick:handleSelectAllFields,className:\"btn btn-sm btn-outline\",title:`Select all ${filteredFields.length} filtered fields`,children:[\"Select Filtered (\",filteredFields.length,\")\"]}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleDeselectAllFields,className:\"btn btn-sm btn-outline\",title:\"Deselect all filtered fields\",children:\"Deselect Filtered\"}),/*#__PURE__*/_jsxs(\"button\",{type:\"button\",onClick:handleSelectAllAvailableFields,className:\"btn btn-sm btn-primary\",title:`Select all ${availableFields.length} available fields`,children:[\"Select All (\",availableFields.length,\")\"]}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleClearAllFields,className:\"btn btn-sm btn-outline btn-danger\",title:\"Clear all selected fields\",disabled:selectedFields.length===0,children:\"Clear All\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"selection-summary\",children:[selectedFields.length,\" of \",availableFields.length,\" fields selected\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"fields-list\",children:filteredFields.map(field=>{const isSelected=selectedFields.some(f=>f.key===field.key);return/*#__PURE__*/_jsxs(\"div\",{className:`field-item ${isSelected?'selected':''}`,onClick:()=>handleFieldToggle(field),children:[/*#__PURE__*/_jsx(\"div\",{className:\"field-checkbox\",children:/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:isSelected,onChange:()=>handleFieldToggle(field)})}),/*#__PURE__*/_jsxs(\"div\",{className:\"field-details\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"field-name\",children:field.label}),/*#__PURE__*/_jsxs(\"div\",{className:\"field-meta\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"field-type\",children:field.type}),field.required&&/*#__PURE__*/_jsx(\"span\",{className:\"required-badge\",children:\"Required\"}),field.conditional&&/*#__PURE__*/_jsx(\"span\",{className:\"conditional-badge\",children:\"Conditional\"})]})]})]},field.key);})})]})]}),showFieldConfig&&configField&&/*#__PURE__*/_jsx(FieldConfigModal,{field:configField,onSave:handleFieldConfigSave,onCancel:()=>{setShowFieldConfig(false);setConfigField(null);}}),showPreview&&/*#__PURE__*/_jsx(FormPreview,{fields:selectedFields,formName:formName,onClose:()=>setShowPreview(false)})]});};export default FormBuilder;", "map": {"version": 3, "names": ["useState", "useEffect", "PersonFieldDefinitions", "<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>s", "formConfigService", "apiService", "FieldConfigModal", "FormPreview", "jsx", "_jsx", "jsxs", "_jsxs", "FormBuilder", "_ref", "onSave", "onCancel", "initialConfig", "selectedHierarchy", "setSelectedHierarchy", "formName", "setFormName", "formDescription", "setFormDescription", "availableFields", "setAvailableFields", "<PERSON><PERSON><PERSON>s", "setSelectedFields", "currentSection", "setCurrentSection", "searchTerm", "setSearchTerm", "showPreview", "setShowPreview", "showFieldConfig", "setShowFieldConfig", "config<PERSON><PERSON>", "setConfigField", "errors", "setErrors", "saving", "setSaving", "savedForms", "setSavedForms", "showSavedForms", "setShowSavedForms", "divisions", "setDivisions", "categories", "setCategories", "firmNatures", "setFirmNatures", "loading", "setLoading", "existingFormInfo", "setExistingFormInfo", "isEditMode", "setIsEditMode", "editingFormId", "setEditingFormId", "initializeFields", "loadSavedForms", "loadInitialConfig", "allFields", "deduplicateFields", "fields", "seen", "Set", "deduplicated", "for<PERSON>ach", "field", "has", "key", "add", "push", "forms", "getAllFormConfigs", "config", "name", "description", "deduplicatedFields", "id", "type", "associatedId", "divisionId", "categoryId", "firmNatureId", "hierarchy", "loadDivisions", "formValidation", "validateFormCreation", "<PERSON><PERSON><PERSON><PERSON>", "prev", "formCreation", "join", "newErrors", "formInfo", "getExistingFormInfo", "response", "getDivisions", "data", "error", "console", "loadCategories", "getCategoriesByDivision", "loadFirmNatures", "getFirmNaturesByCategory", "handleDivisionChange", "e", "target", "value", "division", "find", "d", "parseInt", "category", "firmNature", "handleCategoryChange", "c", "handleFirmNatureChange", "fn", "handleFieldToggle", "isSelected", "some", "f", "filter", "exists", "handleFieldConfig", "handleFieldConfigSave", "updatedField", "map", "handleFieldMove", "fromIndex", "toIndex", "new<PERSON>ields", "movedField", "splice", "handleSelectAllFields", "fieldsToAdd", "filteredFields", "selected", "handleDeselectAllFields", "filteredFieldKeys", "includes", "handleSelectAllAvailableFields", "handleClearAllFields", "handleSave", "validation", "validateForm", "completeHierarchy", "settings", "showSections", "allowConditionalFields", "validateOnChange", "savedConfig", "originalForm", "createdAt", "updatedAt", "Date", "toISOString", "saveFormConfig", "Error", "message", "alert", "general", "trim", "length", "requiredFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingRequired", "rf", "Object", "keys", "getFilteredFields", "filtered", "section", "label", "toLowerCase", "getSectionCounts", "counts", "all", "sectionKey", "sectionCounts", "className", "children", "onClick", "disabled", "entries", "_ref2", "form", "summary", "fieldCount", "toLocaleDateString", "window", "confirm", "deleteFormConfig", "onChange", "placeholder", "rows", "index", "required", "title", "Math", "max", "min", "style", "backgroundColor", "border", "borderRadius", "padding", "margin", "color", "fontSize", "marginBottom", "paddingLeft", "display", "gridTemplateColumns", "gap", "fontWeight", "width", "marginTop", "existingForms", "firmNaturesWithForms", "_ref3", "checked", "conditional", "onClose"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/FormBuilder.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FieldConfigModal from './FieldConfigModal';\nimport FormPreview from './FormPreview';\nimport './FormBuilder.css';\n\nconst FormBuilder = ({ onSave, onCancel, initialConfig = null }) => {\n  const [selectedHierarchy, setSelectedHierarchy] = useState({});\n  const [formName, setFormName] = useState('');\n  const [formDescription, setFormDescription] = useState('');\n  const [availableFields, setAvailableFields] = useState([]);\n  const [selectedFields, setSelectedFields] = useState([]);\n  const [currentSection, setCurrentSection] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n  const [showFieldConfig, setShowFieldConfig] = useState(false);\n  const [configField, setConfigField] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [saving, setSaving] = useState(false);\n  const [savedForms, setSavedForms] = useState([]);\n  const [showSavedForms, setShowSavedForms] = useState(false);\n\n  // State for dropdown data\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [firmNatures, setFirmNatures] = useState([]);\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    firmNatures: false\n  });\n  const [existingFormInfo, setExistingFormInfo] = useState(null);\n  const [isEditMode, setIsEditMode] = useState(false);\n  const [editingFormId, setEditingFormId] = useState(null);\n\n  useEffect(() => {\n    initializeFields();\n    loadSavedForms();\n    if (initialConfig) {\n      loadInitialConfig(initialConfig);\n    }\n  }, [initialConfig]);\n\n  const initializeFields = () => {\n    const allFields = getAllPersonFields();\n    setAvailableFields(allFields);\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = (fields) => {\n    const seen = new Set();\n    const deduplicated = [];\n\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      }\n    });\n\n    return deduplicated;\n  };\n\n  const loadSavedForms = () => {\n    const forms = formConfigService.getAllFormConfigs();\n    setSavedForms(forms);\n  };\n\n  const loadInitialConfig = (config) => {\n    setFormName(config.name || '');\n    setFormDescription(config.description || '');\n\n    // Deduplicate fields when loading initial config\n    const fields = config.fields || [];\n    const deduplicatedFields = deduplicateFields(fields);\n    setSelectedFields(deduplicatedFields);\n\n    // Set edit mode when loading existing form\n    setIsEditMode(true);\n    setEditingFormId(config.id || config.key);\n\n    if (config.type === 'division' && config.associatedId) {\n      // Load division info for hierarchy selector\n      setSelectedHierarchy({\n        divisionId: config.associatedId\n      });\n    } else if (config.type === 'category' && config.associatedId) {\n      // Load category info for hierarchy selector\n      setSelectedHierarchy({\n        categoryId: config.associatedId\n      });\n    } else if (config.type === 'firmnature' && config.associatedId) {\n      // Load firm nature info for hierarchy selector\n      setSelectedHierarchy({\n        firmNatureId: config.associatedId\n      });\n    }\n\n    // If hierarchy is stored in config, use that\n    if (config.hierarchy) {\n      setSelectedHierarchy(config.hierarchy);\n    }\n  };\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n  }, []);\n\n  // Validate form creation rules when hierarchy changes\n  useEffect(() => {\n    if (selectedHierarchy.categoryId) {\n      // Skip validation in edit mode since we're updating an existing form\n      if (!isEditMode) {\n        const formValidation = formConfigService.validateFormCreation(\n          selectedHierarchy.categoryId,\n          selectedHierarchy.firmNatureId\n        );\n\n        if (!formValidation.isValid) {\n          setErrors(prev => ({\n            ...prev,\n            formCreation: formValidation.errors.join('. ')\n          }));\n        } else {\n          setErrors(prev => {\n            const newErrors = { ...prev };\n            delete newErrors.formCreation;\n            return newErrors;\n          });\n        }\n      } else {\n        // In edit mode, clear any form creation errors\n        setErrors(prev => {\n          const newErrors = { ...prev };\n          delete newErrors.formCreation;\n          return newErrors;\n        });\n      }\n\n      const formInfo = formConfigService.getExistingFormInfo(\n        selectedHierarchy.categoryId,\n        selectedHierarchy.firmNatureId\n      );\n      setExistingFormInfo(formInfo);\n    } else {\n      setExistingFormInfo(null);\n      setErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors.formCreation;\n        return newErrors;\n      });\n    }\n  }, [selectedHierarchy.categoryId, selectedHierarchy.firmNatureId, isEditMode]);\n\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  const loadCategories = async (divisionId) => {\n    if (!divisionId) {\n      setCategories([]);\n      setFirmNatures([]);\n      return;\n    }\n\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  const loadFirmNatures = async (categoryId) => {\n    if (!categoryId) {\n      setFirmNatures([]);\n      return;\n    }\n\n    setLoading(prev => ({ ...prev, firmNatures: true }));\n    try {\n      const response = await apiService.getFirmNaturesByCategory(categoryId);\n      setFirmNatures(response.data || []);\n    } catch (error) {\n      console.error('Error loading firm natures:', error);\n      setFirmNatures([]);\n    } finally {\n      setLoading(prev => ({ ...prev, firmNatures: false }));\n    }\n  };\n\n  const handleDivisionChange = (e) => {\n    const divisionId = e.target.value;\n    const division = divisions.find(d => d.id === parseInt(divisionId));\n\n    setSelectedHierarchy({\n      divisionId: divisionId || null,\n      categoryId: null,\n      firmNatureId: null,\n      division: division || null,\n      category: null,\n      firmNature: null\n    });\n\n    setCategories([]);\n    setFirmNatures([]);\n\n    if (divisionId) {\n      loadCategories(divisionId);\n    }\n  };\n\n  const handleCategoryChange = (e) => {\n    const categoryId = e.target.value;\n    const category = categories.find(c => c.id === parseInt(categoryId));\n\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      categoryId: categoryId || null,\n      firmNatureId: null,\n      category: category || null,\n      firmNature: null\n    }));\n\n    setFirmNatures([]);\n\n    if (categoryId) {\n      loadFirmNatures(categoryId);\n    }\n  };\n\n  const handleFirmNatureChange = (e) => {\n    const firmNatureId = e.target.value;\n    const firmNature = firmNatures.find(fn => fn.id === parseInt(firmNatureId));\n\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      firmNatureId: firmNatureId || null,\n      firmNature: firmNature || null\n    }));\n  };\n\n  const handleFieldToggle = (field) => {\n    const isSelected = selectedFields.some(f => f.key === field.key);\n\n    if (isSelected) {\n      // Remove all instances of this field key (in case there are duplicates)\n      setSelectedFields(prev => prev.filter(f => f.key !== field.key));\n    } else {\n      // Add field only if it doesn't already exist\n      setSelectedFields(prev => {\n        const exists = prev.some(f => f.key === field.key);\n        if (exists) {\n          return prev;\n        }\n        return [...prev, { ...field }];\n      });\n    }\n  };\n\n  const handleFieldConfig = (field) => {\n    setConfigField(field);\n    setShowFieldConfig(true);\n  };\n\n  const handleFieldConfigSave = (updatedField) => {\n    setSelectedFields(prev => \n      prev.map(f => f.key === updatedField.key ? updatedField : f)\n    );\n    setShowFieldConfig(false);\n    setConfigField(null);\n  };\n\n  const handleFieldMove = (fromIndex, toIndex) => {\n    const newFields = [...selectedFields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n    setSelectedFields(newFields);\n  };\n\n  const handleSelectAllFields = () => {\n    // Get all filtered fields (respects current section and search)\n    const fieldsToAdd = filteredFields.filter(field =>\n      !selectedFields.some(selected => selected.key === field.key)\n    );\n\n    setSelectedFields(prev => [...prev, ...fieldsToAdd]);\n  };\n\n  const handleDeselectAllFields = () => {\n    // Remove all filtered fields from selection\n    const filteredFieldKeys = filteredFields.map(field => field.key);\n    setSelectedFields(prev =>\n      prev.filter(field => !filteredFieldKeys.includes(field.key))\n    );\n  };\n\n  const handleSelectAllAvailableFields = () => {\n    // Select all available fields regardless of current filter\n    const fieldsToAdd = availableFields.filter(field =>\n      !selectedFields.some(selected => selected.key === field.key)\n    );\n\n    setSelectedFields(prev => [...prev, ...fieldsToAdd]);\n  };\n\n  const handleClearAllFields = () => {\n    setSelectedFields([]);\n  };\n\n  const handleSave = async () => {\n    const validation = validateForm();\n\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSaving(true);\n    try {\n      // Ensure complete hierarchy information is included\n      const completeHierarchy = {\n        ...selectedHierarchy,\n        // Include division info (required)\n        divisionId: selectedHierarchy.divisionId,\n        division: selectedHierarchy.division,\n        // Include category info (required)\n        categoryId: selectedHierarchy.categoryId,\n        category: selectedHierarchy.category,\n        // Include firm nature info if selected (required)\n        firmNatureId: selectedHierarchy.firmNatureId || null,\n        firmNature: selectedHierarchy.firmNature || null\n      };\n\n      const config = {\n        name: formName,\n        description: formDescription,\n        fields: selectedFields,\n        hierarchy: completeHierarchy,\n        settings: {\n          showSections: true,\n          allowConditionalFields: true,\n          validateOnChange: true\n        }\n      };\n\n      let savedConfig;\n\n      if (isEditMode) {\n        // In edit mode, update the existing form\n        // Preserve the original type and associatedId from the form being edited\n        const originalForm = formConfigService.getAllFormConfigs().find(f => f.id === editingFormId || f.key === editingFormId);\n        if (originalForm) {\n          // Update the existing form with new data\n          config.id = originalForm.id;\n          config.key = originalForm.key;\n          config.type = originalForm.type;\n          config.associatedId = originalForm.associatedId;\n          config.createdAt = originalForm.createdAt; // Preserve creation date\n          config.updatedAt = new Date().toISOString(); // Update modification date\n\n          savedConfig = formConfigService.saveFormConfig(originalForm.type, originalForm.associatedId, config);\n        } else {\n          throw new Error('Original form not found for editing');\n        }\n      } else {\n        // Create new form with the most specific level selected, but include complete hierarchy\n        if (selectedHierarchy.firmNatureId) {\n          savedConfig = formConfigService.saveFormConfig('firmnature', selectedHierarchy.firmNatureId, config);\n        } else if (selectedHierarchy.categoryId) {\n          savedConfig = formConfigService.saveFormConfig('category', selectedHierarchy.categoryId, config);\n        } else {\n          throw new Error('Please select division, category, and firm nature');\n        }\n      }\n\n      // Reload saved forms list\n      loadSavedForms();\n\n      if (onSave) {\n        onSave(savedConfig);\n      } else {\n        // Show success message for standalone usage\n        const message = isEditMode ? 'Form updated successfully!' : 'Form configuration saved successfully!';\n        alert(message);\n        // Reset form\n        setFormName('');\n        setFormDescription('');\n        setSelectedFields([]);\n        setSelectedHierarchy({});\n        setIsEditMode(false);\n        setEditingFormId(null);\n      }\n    } catch (error) {\n      console.error('Error saving form:', error);\n      setErrors({ general: error.message });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const validateForm = () => {\n    const errors = {};\n\n    if (!formName.trim()) {\n      errors.formName = 'Form name is required';\n    }\n\n    // Division, category, and firm nature are required\n    if (!selectedHierarchy.divisionId) {\n      errors.hierarchy = 'Please select a division';\n    } else if (!selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Please select a category';\n    } else if (!selectedHierarchy.firmNatureId) {\n      errors.hierarchy = 'Please select a firm nature';\n    }\n\n    // Validate form creation rules (skip in edit mode)\n    if (selectedHierarchy.categoryId && !isEditMode) {\n      const formValidation = formConfigService.validateFormCreation(\n        selectedHierarchy.categoryId,\n        selectedHierarchy.firmNatureId\n      );\n\n      if (!formValidation.isValid) {\n        errors.formCreation = formValidation.errors.join('. ');\n      }\n    }\n\n    if (selectedFields.length === 0) {\n      errors.fields = 'Please select at least one field';\n    }\n\n    // Check required fields\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = selectedFields.map(f => f.key);\n    const missingRequired = requiredFields.filter(rf => !selectedFieldKeys.includes(rf));\n\n    if (missingRequired.length > 0) {\n      errors.requiredFields = `Required fields missing: ${missingRequired.join(', ')}`;\n    }\n\n    return {\n      isValid: Object.keys(errors).length === 0,\n      errors\n    };\n  };\n\n  const getFilteredFields = () => {\n    let filtered = availableFields;\n\n    // Filter by section\n    if (currentSection !== 'all') {\n      filtered = filtered.filter(field => field.section === currentSection);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(field => \n        field.label.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        field.key.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    }\n\n    return filtered;\n  };\n\n  const getSectionCounts = () => {\n    const counts = { all: availableFields.length };\n    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {\n      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;\n    });\n    return counts;\n  };\n\n  const sectionCounts = getSectionCounts();\n  const filteredFields = getFilteredFields();\n\n  return (\n    <div className=\"form-builder\">\n      <div className=\"form-builder-header\">\n        <h2>Form Builder</h2>\n        <div className=\"header-actions\">\n          <button \n            type=\"button\" \n            onClick={() => setShowPreview(true)}\n            className=\"btn btn-secondary\"\n            disabled={selectedFields.length === 0}\n          >\n            Preview Form\n          </button>\n          <button\n            type=\"button\"\n            onClick={handleSave}\n            className=\"btn btn-primary\"\n            disabled={saving || errors.formCreation}\n          >\n            {saving ? (isEditMode ? 'Updating...' : 'Saving...') : (isEditMode ? 'Update Form' : 'Save Form')}\n          </button>\n          {onCancel && (\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              className=\"btn btn-outline\"\n            >\n              Cancel\n            </button>\n          )}\n        </div>\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      {/* Validation Status */}\n      {Object.keys(errors).length > 0 && (\n        <div className=\"validation-errors\">\n          <h4>Please fix the following issues:</h4>\n          <ul>\n            {Object.entries(errors).map(([key, message]) => (\n              <li key={key}>{message}</li>\n            ))}\n          </ul>\n        </div>\n      )}\n\n      <div className=\"form-builder-content\">\n        {/* Form Configuration Panel */}\n        <div className=\"config-panel\">\n          {/* Saved Forms Section */}\n          <div className=\"config-section\">\n            <div className=\"section-header\">\n              <h3>Saved Forms ({savedForms.length})</h3>\n              <button\n                type=\"button\"\n                className=\"btn-link\"\n                onClick={() => setShowSavedForms(!showSavedForms)}\n              >\n                {showSavedForms ? 'Hide' : 'Show'}\n              </button>\n            </div>\n\n            {showSavedForms && (\n              <div className=\"saved-forms-list\">\n                {savedForms.length === 0 ? (\n                  <p className=\"no-forms-message\">No saved forms yet. Create your first form below!</p>\n                ) : (\n                  savedForms.map((form) => (\n                    <div key={form.key} className=\"saved-form-item\">\n                      <div className=\"form-item-header\">\n                        <h4>{form.name}</h4>\n                        <span className=\"form-type-badge\">{form.type}</span>\n                      </div>\n                      <p className=\"form-description\">{form.description || 'No description'}</p>\n                      <div className=\"form-meta\">\n                        <span>{form.summary.fieldCount} fields</span>\n                        <span>•</span>\n                        <span>Updated {new Date(form.updatedAt).toLocaleDateString()}</span>\n                      </div>\n                      <div className=\"form-actions\">\n                        <button\n                          type=\"button\"\n                          className=\"btn-small btn-outline\"\n                          onClick={() => loadInitialConfig(form)}\n                        >\n                          Load\n                        </button>\n                        <button\n                          type=\"button\"\n                          className=\"btn-small btn-danger\"\n                          onClick={() => {\n                            if (window.confirm('Are you sure you want to delete this form?')) {\n                              formConfigService.deleteFormConfig(form.type, form.associatedId);\n                              loadSavedForms();\n                            }\n                          }}\n                        >\n                          Delete\n                        </button>\n                      </div>\n                    </div>\n                  ))\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* Form Configuration Section */}\n          <div className=\"config-section\">\n            <h3>Form Configuration</h3>\n\n            <div className=\"form-group\">\n              <label>Form Name *</label>\n              <input\n                type=\"text\"\n                value={formName}\n                onChange={(e) => setFormName(e.target.value)}\n                placeholder=\"Enter form name\"\n                className={errors.formName ? 'error' : ''}\n              />\n              {errors.formName && <div className=\"error-message\">{errors.formName}</div>}\n            </div>\n\n            <div className=\"form-group\">\n              <label>Description</label>\n              <textarea\n                value={formDescription}\n                onChange={(e) => setFormDescription(e.target.value)}\n                placeholder=\"Enter form description\"\n                rows=\"3\"\n              />\n            </div>\n\n\n          </div>\n\n          {/* Selected Fields Panel */}\n          <div className=\"config-section\">\n            <h3>Selected Fields ({selectedFields.length})</h3>\n            {errors.fields && <div className=\"error-message\">{errors.fields}</div>}\n            {errors.requiredFields && <div className=\"error-message\">{errors.requiredFields}</div>}\n            \n            <div className=\"selected-fields\">\n              {selectedFields.length === 0 ? (\n                <div className=\"empty-state\">\n                  No fields selected. Choose fields from the available fields panel.\n                </div>\n              ) : (\n                selectedFields.map((field, index) => (\n                  <div key={field.key} className=\"selected-field\">\n                    <div className=\"field-info\">\n                      <span className=\"field-label\">{field.label}</span>\n                      <span className=\"field-type\">{field.type}</span>\n                      {field.required && <span className=\"required-badge\">Required</span>}\n                    </div>\n                    <div className=\"field-actions\">\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldConfig(field)}\n                        className=\"btn-icon\"\n                        title=\"Configure field\"\n                      >\n                        ⚙️\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldMove(index, Math.max(0, index - 1))}\n                        className=\"btn-icon\"\n                        disabled={index === 0}\n                        title=\"Move up\"\n                      >\n                        ↑\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1))}\n                        className=\"btn-icon\"\n                        disabled={index === selectedFields.length - 1}\n                        title=\"Move down\"\n                      >\n                        ↓\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldToggle(field)}\n                        className=\"btn-icon remove\"\n                        title=\"Remove field\"\n                      >\n                        ✕\n                      </button>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Available Fields Panel */}\n        <div className=\"fields-panel\">\n          <div className=\"fields-header\">\n            <h3>Available Fields</h3>\n          </div>\n\n          {/* Division/Category/Firm Nature Selection */}\n          <div className=\"hierarchy-selection\" style={{\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #e1e5e9',\n            borderRadius: '8px',\n            padding: '1.5rem',\n            margin: '1rem 0'\n          }}>\n            <h4 style={{ margin: '0 0 1rem 0', color: '#495057' }}>\n              Associate with Division, Category & Firm Nature (All Required) *\n            </h4>\n            <div style={{\n              fontSize: '0.875rem',\n              color: '#6c757d',\n              marginBottom: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8f9fa',\n              borderRadius: '4px',\n              border: '1px solid #e9ecef'\n            }}>\n              <strong>Form Creation Rules:</strong>\n              <ul style={{ margin: '0.5rem 0 0 1rem', paddingLeft: '1rem' }}>\n                <li>If you create a form for a category, you cannot create forms for its firm natures</li>\n                <li>Each firm nature can have only one form</li>\n                <li>If firm natures already have forms, you cannot create a form for the parent category</li>\n              </ul>\n            </div>\n\n            <div className=\"hierarchy-dropdowns\" style={{\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '1rem'\n            }}>\n              {/* Division Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  Division *\n                </label>\n                <select\n                  value={selectedHierarchy.divisionId || ''}\n                  onChange={handleDivisionChange}\n                  disabled={loading.divisions}\n                  className={errors.divisionId ? 'error' : ''}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">Select Division</option>\n                  {divisions.map(division => (\n                    <option key={division.id} value={division.id}>\n                      {division.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.divisionId && (\n                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>\n                    {errors.divisionId}\n                  </div>\n                )}\n              </div>\n\n              {/* Category Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  Category *\n                </label>\n                <select\n                  value={selectedHierarchy.categoryId || ''}\n                  onChange={handleCategoryChange}\n                  disabled={!selectedHierarchy.divisionId || loading.categories}\n                  className={errors.categoryId ? 'error' : ''}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">\n                    {!selectedHierarchy.divisionId ? 'Select Division first' : 'Select Category'}\n                  </option>\n                  {categories.map(category => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.categoryId && (\n                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>\n                    {errors.categoryId}\n                  </div>\n                )}\n              </div>\n\n              {/* Firm Nature Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  Firm Nature (Required) *\n                </label>\n                <select\n                  value={selectedHierarchy.firmNatureId || ''}\n                  onChange={handleFirmNatureChange}\n                  disabled={!selectedHierarchy.categoryId || loading.firmNatures}\n                  required\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">\n                    {!selectedHierarchy.categoryId ? 'Select Category first' : 'Select Firm Nature (Required)'}\n                  </option>\n                  {firmNatures.map(firmNature => (\n                    <option key={firmNature.id} value={firmNature.id}>\n                      {firmNature.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            {errors.hierarchy && (\n              <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.5rem' }}>\n                {errors.hierarchy}\n              </div>\n            )}\n            {errors.formCreation && (\n              <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.5rem', fontWeight: 'bold' }}>\n                ⚠️ {errors.formCreation}\n              </div>\n            )}\n\n            {/* Existing Form Information */}\n            {existingFormInfo && existingFormInfo.existingForms.length > 0 && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#fff3cd',\n                border: '1px solid #ffeaa7',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>📋 Existing Forms:</strong>\n                {existingFormInfo.existingForms.map((form, index) => (\n                  <div key={index} style={{ marginTop: '0.5rem' }}>\n                    <strong>{form.type === 'category' ? 'Category' : 'Firm Nature'} Form:</strong> {form.name}\n                    {form.description && <div style={{ color: '#6c757d' }}>{form.description}</div>}\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {/* Firm Natures with Forms Warning */}\n            {existingFormInfo && existingFormInfo.firmNaturesWithForms.length > 0 && !selectedHierarchy.firmNatureId && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#f8d7da',\n                border: '1px solid #f5c6cb',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>⚠️ Warning:</strong> This category has {existingFormInfo.firmNaturesWithForms.length} firm nature form(s).\n                You cannot create a form for this category.\n              </div>\n            )}\n\n            {/* Success Message for Valid Selection */}\n            {selectedHierarchy.categoryId && !errors.formCreation && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#d4edda',\n                border: '1px solid #c3e6cb',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>✅ Valid Selection:</strong> You can create a form for this {selectedHierarchy.firmNatureId ? 'firm nature' : 'category'}.\n              </div>\n            )}\n          </div>\n\n          {/* Search and Filter Controls */}\n          <div className=\"fields-controls\">\n            <input\n              type=\"text\"\n              placeholder=\"Search fields...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"search-input\"\n            />\n          </div>\n\n          {/* Section Tabs */}\n          <div className=\"section-tabs\">\n            <button\n              type=\"button\"\n              onClick={() => setCurrentSection('all')}\n              className={`section-tab ${currentSection === 'all' ? 'active' : ''}`}\n            >\n              All ({sectionCounts.all})\n            </button>\n            {Object.entries(PersonFieldDefinitions).map(([sectionKey, section]) => (\n              <button\n                key={sectionKey}\n                type=\"button\"\n                onClick={() => setCurrentSection(sectionKey)}\n                className={`section-tab ${currentSection === sectionKey ? 'active' : ''}`}\n              >\n                {section.title} ({sectionCounts[sectionKey]})\n              </button>\n            ))}\n          </div>\n\n          {/* Bulk Selection Controls */}\n          <div className=\"bulk-selection-controls\">\n            <div className=\"bulk-controls-section\">\n              <span className=\"bulk-controls-label\">Quick Actions:</span>\n              <button\n                type=\"button\"\n                onClick={handleSelectAllFields}\n                className=\"btn btn-sm btn-outline\"\n                title={`Select all ${filteredFields.length} filtered fields`}\n              >\n                Select Filtered ({filteredFields.length})\n              </button>\n              <button\n                type=\"button\"\n                onClick={handleDeselectAllFields}\n                className=\"btn btn-sm btn-outline\"\n                title=\"Deselect all filtered fields\"\n              >\n                Deselect Filtered\n              </button>\n              <button\n                type=\"button\"\n                onClick={handleSelectAllAvailableFields}\n                className=\"btn btn-sm btn-primary\"\n                title={`Select all ${availableFields.length} available fields`}\n              >\n                Select All ({availableFields.length})\n              </button>\n              <button\n                type=\"button\"\n                onClick={handleClearAllFields}\n                className=\"btn btn-sm btn-outline btn-danger\"\n                title=\"Clear all selected fields\"\n                disabled={selectedFields.length === 0}\n              >\n                Clear All\n              </button>\n            </div>\n            <div className=\"selection-summary\">\n              {selectedFields.length} of {availableFields.length} fields selected\n            </div>\n          </div>\n\n          {/* Fields List */}\n          <div className=\"fields-list\">\n            {filteredFields.map(field => {\n              const isSelected = selectedFields.some(f => f.key === field.key);\n              return (\n                <div\n                  key={field.key}\n                  className={`field-item ${isSelected ? 'selected' : ''}`}\n                  onClick={() => handleFieldToggle(field)}\n                >\n                  <div className=\"field-checkbox\">\n                    <input\n                      type=\"checkbox\"\n                      checked={isSelected}\n                      onChange={() => handleFieldToggle(field)}\n                    />\n                  </div>\n                  <div className=\"field-details\">\n                    <div className=\"field-name\">{field.label}</div>\n                    <div className=\"field-meta\">\n                      <span className=\"field-type\">{field.type}</span>\n                      {field.required && <span className=\"required-badge\">Required</span>}\n                      {field.conditional && <span className=\"conditional-badge\">Conditional</span>}\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Modals */}\n      {showFieldConfig && configField && (\n        <FieldConfigModal\n          field={configField}\n          onSave={handleFieldConfigSave}\n          onCancel={() => {\n            setShowFieldConfig(false);\n            setConfigField(null);\n          }}\n        />\n      )}\n\n      {showPreview && (\n        <FormPreview\n          fields={selectedFields}\n          formName={formName}\n          onClose={() => setShowPreview(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default FormBuilder;\n"], "mappings": "AAAA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC3C,OAASC,sBAAsB,CAAEC,kBAAkB,KAAQ,iCAAiC,CAC5F,MAAO,CAAAC,iBAAiB,KAAM,kCAAkC,CAChE,MAAO,CAAAC,UAAU,KAAM,2BAA2B,CAClD,MAAO,CAAAC,gBAAgB,KAAM,oBAAoB,CACjD,MAAO,CAAAC,WAAW,KAAM,eAAe,CACvC,MAAO,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3B,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAgD,IAA/C,CAAEC,MAAM,CAAEC,QAAQ,CAAEC,aAAa,CAAG,IAAK,CAAC,CAAAH,IAAA,CAC7D,KAAM,CAACI,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC9D,KAAM,CAACmB,QAAQ,CAAEC,WAAW,CAAC,CAAGpB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACqB,eAAe,CAAEC,kBAAkB,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACuB,eAAe,CAAEC,kBAAkB,CAAC,CAAGxB,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACyB,cAAc,CAAEC,iBAAiB,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC2B,cAAc,CAAEC,iBAAiB,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAC6B,UAAU,CAAEC,aAAa,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC+B,WAAW,CAAEC,cAAc,CAAC,CAAGhC,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACiC,eAAe,CAAEC,kBAAkB,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACmC,WAAW,CAAEC,cAAc,CAAC,CAAGpC,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACqC,MAAM,CAAEC,SAAS,CAAC,CAAGtC,QAAQ,CAAC,CAAC,CAAC,CAAC,CACxC,KAAM,CAACuC,MAAM,CAAEC,SAAS,CAAC,CAAGxC,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAACyC,UAAU,CAAEC,aAAa,CAAC,CAAG1C,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC2C,cAAc,CAAEC,iBAAiB,CAAC,CAAG5C,QAAQ,CAAC,KAAK,CAAC,CAE3D;AACA,KAAM,CAAC6C,SAAS,CAAEC,YAAY,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC+C,UAAU,CAAEC,aAAa,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACiD,WAAW,CAAEC,cAAc,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACmD,OAAO,CAAEC,UAAU,CAAC,CAAGpD,QAAQ,CAAC,CACrC6C,SAAS,CAAE,KAAK,CAChBE,UAAU,CAAE,KAAK,CACjBE,WAAW,CAAE,KACf,CAAC,CAAC,CACF,KAAM,CAACI,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGtD,QAAQ,CAAC,IAAI,CAAC,CAC9D,KAAM,CAACuD,UAAU,CAAEC,aAAa,CAAC,CAAGxD,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACyD,aAAa,CAAEC,gBAAgB,CAAC,CAAG1D,QAAQ,CAAC,IAAI,CAAC,CAExDC,SAAS,CAAC,IAAM,CACd0D,gBAAgB,CAAC,CAAC,CAClBC,cAAc,CAAC,CAAC,CAChB,GAAI5C,aAAa,CAAE,CACjB6C,iBAAiB,CAAC7C,aAAa,CAAC,CAClC,CACF,CAAC,CAAE,CAACA,aAAa,CAAC,CAAC,CAEnB,KAAM,CAAA2C,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAAG,SAAS,CAAG3D,kBAAkB,CAAC,CAAC,CACtCqB,kBAAkB,CAACsC,SAAS,CAAC,CAC/B,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAIC,MAAM,EAAK,CACpC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CACtB,KAAM,CAAAC,YAAY,CAAG,EAAE,CAEvBH,MAAM,CAACI,OAAO,CAACC,KAAK,EAAI,CACtB,GAAI,CAACJ,IAAI,CAACK,GAAG,CAACD,KAAK,CAACE,GAAG,CAAC,CAAE,CACxBN,IAAI,CAACO,GAAG,CAACH,KAAK,CAACE,GAAG,CAAC,CACnBJ,YAAY,CAACM,IAAI,CAACJ,KAAK,CAAC,CAC1B,CACF,CAAC,CAAC,CAEF,MAAO,CAAAF,YAAY,CACrB,CAAC,CAED,KAAM,CAAAP,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAc,KAAK,CAAGtE,iBAAiB,CAACuE,iBAAiB,CAAC,CAAC,CACnDjC,aAAa,CAACgC,KAAK,CAAC,CACtB,CAAC,CAED,KAAM,CAAAb,iBAAiB,CAAIe,MAAM,EAAK,CACpCxD,WAAW,CAACwD,MAAM,CAACC,IAAI,EAAI,EAAE,CAAC,CAC9BvD,kBAAkB,CAACsD,MAAM,CAACE,WAAW,EAAI,EAAE,CAAC,CAE5C;AACA,KAAM,CAAAd,MAAM,CAAGY,MAAM,CAACZ,MAAM,EAAI,EAAE,CAClC,KAAM,CAAAe,kBAAkB,CAAGhB,iBAAiB,CAACC,MAAM,CAAC,CACpDtC,iBAAiB,CAACqD,kBAAkB,CAAC,CAErC;AACAvB,aAAa,CAAC,IAAI,CAAC,CACnBE,gBAAgB,CAACkB,MAAM,CAACI,EAAE,EAAIJ,MAAM,CAACL,GAAG,CAAC,CAEzC,GAAIK,MAAM,CAACK,IAAI,GAAK,UAAU,EAAIL,MAAM,CAACM,YAAY,CAAE,CACrD;AACAhE,oBAAoB,CAAC,CACnBiE,UAAU,CAAEP,MAAM,CAACM,YACrB,CAAC,CAAC,CACJ,CAAC,IAAM,IAAIN,MAAM,CAACK,IAAI,GAAK,UAAU,EAAIL,MAAM,CAACM,YAAY,CAAE,CAC5D;AACAhE,oBAAoB,CAAC,CACnBkE,UAAU,CAAER,MAAM,CAACM,YACrB,CAAC,CAAC,CACJ,CAAC,IAAM,IAAIN,MAAM,CAACK,IAAI,GAAK,YAAY,EAAIL,MAAM,CAACM,YAAY,CAAE,CAC9D;AACAhE,oBAAoB,CAAC,CACnBmE,YAAY,CAAET,MAAM,CAACM,YACvB,CAAC,CAAC,CACJ,CAEA;AACA,GAAIN,MAAM,CAACU,SAAS,CAAE,CACpBpE,oBAAoB,CAAC0D,MAAM,CAACU,SAAS,CAAC,CACxC,CACF,CAAC,CAED;AACArF,SAAS,CAAC,IAAM,CACdsF,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,EAAE,CAAC,CAEN;AACAtF,SAAS,CAAC,IAAM,CACd,GAAIgB,iBAAiB,CAACmE,UAAU,CAAE,CAChC;AACA,GAAI,CAAC7B,UAAU,CAAE,CACf,KAAM,CAAAiC,cAAc,CAAGpF,iBAAiB,CAACqF,oBAAoB,CAC3DxE,iBAAiB,CAACmE,UAAU,CAC5BnE,iBAAiB,CAACoE,YACpB,CAAC,CAED,GAAI,CAACG,cAAc,CAACE,OAAO,CAAE,CAC3BpD,SAAS,CAACqD,IAAI,GAAK,CACjB,GAAGA,IAAI,CACPC,YAAY,CAAEJ,cAAc,CAACnD,MAAM,CAACwD,IAAI,CAAC,IAAI,CAC/C,CAAC,CAAC,CAAC,CACL,CAAC,IAAM,CACLvD,SAAS,CAACqD,IAAI,EAAI,CAChB,KAAM,CAAAG,SAAS,CAAG,CAAE,GAAGH,IAAK,CAAC,CAC7B,MAAO,CAAAG,SAAS,CAACF,YAAY,CAC7B,MAAO,CAAAE,SAAS,CAClB,CAAC,CAAC,CACJ,CACF,CAAC,IAAM,CACL;AACAxD,SAAS,CAACqD,IAAI,EAAI,CAChB,KAAM,CAAAG,SAAS,CAAG,CAAE,GAAGH,IAAK,CAAC,CAC7B,MAAO,CAAAG,SAAS,CAACF,YAAY,CAC7B,MAAO,CAAAE,SAAS,CAClB,CAAC,CAAC,CACJ,CAEA,KAAM,CAAAC,QAAQ,CAAG3F,iBAAiB,CAAC4F,mBAAmB,CACpD/E,iBAAiB,CAACmE,UAAU,CAC5BnE,iBAAiB,CAACoE,YACpB,CAAC,CACD/B,mBAAmB,CAACyC,QAAQ,CAAC,CAC/B,CAAC,IAAM,CACLzC,mBAAmB,CAAC,IAAI,CAAC,CACzBhB,SAAS,CAACqD,IAAI,EAAI,CAChB,KAAM,CAAAG,SAAS,CAAG,CAAE,GAAGH,IAAK,CAAC,CAC7B,MAAO,CAAAG,SAAS,CAACF,YAAY,CAC7B,MAAO,CAAAE,SAAS,CAClB,CAAC,CAAC,CACJ,CACF,CAAC,CAAE,CAAC7E,iBAAiB,CAACmE,UAAU,CAAEnE,iBAAiB,CAACoE,YAAY,CAAE9B,UAAU,CAAC,CAAC,CAE9E,KAAM,CAAAgC,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChCnC,UAAU,CAACuC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE9C,SAAS,CAAE,IAAK,CAAC,CAAC,CAAC,CAClD,GAAI,CACF,KAAM,CAAAoD,QAAQ,CAAG,KAAM,CAAA5F,UAAU,CAAC6F,YAAY,CAAC,CAAC,CAChDpD,YAAY,CAACmD,QAAQ,CAACE,IAAI,EAAI,EAAE,CAAC,CACnC,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAClD,CAAC,OAAS,CACRhD,UAAU,CAACuC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE9C,SAAS,CAAE,KAAM,CAAC,CAAC,CAAC,CACrD,CACF,CAAC,CAED,KAAM,CAAAyD,cAAc,CAAG,KAAO,CAAAnB,UAAU,EAAK,CAC3C,GAAI,CAACA,UAAU,CAAE,CACfnC,aAAa,CAAC,EAAE,CAAC,CACjBE,cAAc,CAAC,EAAE,CAAC,CAClB,OACF,CAEAE,UAAU,CAACuC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE5C,UAAU,CAAE,IAAK,CAAC,CAAC,CAAC,CACnD,GAAI,CACF,KAAM,CAAAkD,QAAQ,CAAG,KAAM,CAAA5F,UAAU,CAACkG,uBAAuB,CAACpB,UAAU,CAAC,CACrEnC,aAAa,CAACiD,QAAQ,CAACE,IAAI,EAAI,EAAE,CAAC,CACpC,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjDpD,aAAa,CAAC,EAAE,CAAC,CACnB,CAAC,OAAS,CACRI,UAAU,CAACuC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE5C,UAAU,CAAE,KAAM,CAAC,CAAC,CAAC,CACtD,CACF,CAAC,CAED,KAAM,CAAAyD,eAAe,CAAG,KAAO,CAAApB,UAAU,EAAK,CAC5C,GAAI,CAACA,UAAU,CAAE,CACflC,cAAc,CAAC,EAAE,CAAC,CAClB,OACF,CAEAE,UAAU,CAACuC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE1C,WAAW,CAAE,IAAK,CAAC,CAAC,CAAC,CACpD,GAAI,CACF,KAAM,CAAAgD,QAAQ,CAAG,KAAM,CAAA5F,UAAU,CAACoG,wBAAwB,CAACrB,UAAU,CAAC,CACtElC,cAAc,CAAC+C,QAAQ,CAACE,IAAI,EAAI,EAAE,CAAC,CACrC,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnDlD,cAAc,CAAC,EAAE,CAAC,CACpB,CAAC,OAAS,CACRE,UAAU,CAACuC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE1C,WAAW,CAAE,KAAM,CAAC,CAAC,CAAC,CACvD,CACF,CAAC,CAED,KAAM,CAAAyD,oBAAoB,CAAIC,CAAC,EAAK,CAClC,KAAM,CAAAxB,UAAU,CAAGwB,CAAC,CAACC,MAAM,CAACC,KAAK,CACjC,KAAM,CAAAC,QAAQ,CAAGjE,SAAS,CAACkE,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAChC,EAAE,GAAKiC,QAAQ,CAAC9B,UAAU,CAAC,CAAC,CAEnEjE,oBAAoB,CAAC,CACnBiE,UAAU,CAAEA,UAAU,EAAI,IAAI,CAC9BC,UAAU,CAAE,IAAI,CAChBC,YAAY,CAAE,IAAI,CAClByB,QAAQ,CAAEA,QAAQ,EAAI,IAAI,CAC1BI,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,IACd,CAAC,CAAC,CAEFnE,aAAa,CAAC,EAAE,CAAC,CACjBE,cAAc,CAAC,EAAE,CAAC,CAElB,GAAIiC,UAAU,CAAE,CACdmB,cAAc,CAACnB,UAAU,CAAC,CAC5B,CACF,CAAC,CAED,KAAM,CAAAiC,oBAAoB,CAAIT,CAAC,EAAK,CAClC,KAAM,CAAAvB,UAAU,CAAGuB,CAAC,CAACC,MAAM,CAACC,KAAK,CACjC,KAAM,CAAAK,QAAQ,CAAGnE,UAAU,CAACgE,IAAI,CAACM,CAAC,EAAIA,CAAC,CAACrC,EAAE,GAAKiC,QAAQ,CAAC7B,UAAU,CAAC,CAAC,CAEpElE,oBAAoB,CAACyE,IAAI,GAAK,CAC5B,GAAGA,IAAI,CACPP,UAAU,CAAEA,UAAU,EAAI,IAAI,CAC9BC,YAAY,CAAE,IAAI,CAClB6B,QAAQ,CAAEA,QAAQ,EAAI,IAAI,CAC1BC,UAAU,CAAE,IACd,CAAC,CAAC,CAAC,CAEHjE,cAAc,CAAC,EAAE,CAAC,CAElB,GAAIkC,UAAU,CAAE,CACdoB,eAAe,CAACpB,UAAU,CAAC,CAC7B,CACF,CAAC,CAED,KAAM,CAAAkC,sBAAsB,CAAIX,CAAC,EAAK,CACpC,KAAM,CAAAtB,YAAY,CAAGsB,CAAC,CAACC,MAAM,CAACC,KAAK,CACnC,KAAM,CAAAM,UAAU,CAAGlE,WAAW,CAAC8D,IAAI,CAACQ,EAAE,EAAIA,EAAE,CAACvC,EAAE,GAAKiC,QAAQ,CAAC5B,YAAY,CAAC,CAAC,CAE3EnE,oBAAoB,CAACyE,IAAI,GAAK,CAC5B,GAAGA,IAAI,CACPN,YAAY,CAAEA,YAAY,EAAI,IAAI,CAClC8B,UAAU,CAAEA,UAAU,EAAI,IAC5B,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAK,iBAAiB,CAAInD,KAAK,EAAK,CACnC,KAAM,CAAAoD,UAAU,CAAGhG,cAAc,CAACiG,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACpD,GAAG,GAAKF,KAAK,CAACE,GAAG,CAAC,CAEhE,GAAIkD,UAAU,CAAE,CACd;AACA/F,iBAAiB,CAACiE,IAAI,EAAIA,IAAI,CAACiC,MAAM,CAACD,CAAC,EAAIA,CAAC,CAACpD,GAAG,GAAKF,KAAK,CAACE,GAAG,CAAC,CAAC,CAClE,CAAC,IAAM,CACL;AACA7C,iBAAiB,CAACiE,IAAI,EAAI,CACxB,KAAM,CAAAkC,MAAM,CAAGlC,IAAI,CAAC+B,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACpD,GAAG,GAAKF,KAAK,CAACE,GAAG,CAAC,CAClD,GAAIsD,MAAM,CAAE,CACV,MAAO,CAAAlC,IAAI,CACb,CACA,MAAO,CAAC,GAAGA,IAAI,CAAE,CAAE,GAAGtB,KAAM,CAAC,CAAC,CAChC,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAyD,iBAAiB,CAAIzD,KAAK,EAAK,CACnCjC,cAAc,CAACiC,KAAK,CAAC,CACrBnC,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAA6F,qBAAqB,CAAIC,YAAY,EAAK,CAC9CtG,iBAAiB,CAACiE,IAAI,EACpBA,IAAI,CAACsC,GAAG,CAACN,CAAC,EAAIA,CAAC,CAACpD,GAAG,GAAKyD,YAAY,CAACzD,GAAG,CAAGyD,YAAY,CAAGL,CAAC,CAC7D,CAAC,CACDzF,kBAAkB,CAAC,KAAK,CAAC,CACzBE,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAED,KAAM,CAAA8F,eAAe,CAAGA,CAACC,SAAS,CAAEC,OAAO,GAAK,CAC9C,KAAM,CAAAC,SAAS,CAAG,CAAC,GAAG5G,cAAc,CAAC,CACrC,KAAM,CAAC6G,UAAU,CAAC,CAAGD,SAAS,CAACE,MAAM,CAACJ,SAAS,CAAE,CAAC,CAAC,CACnDE,SAAS,CAACE,MAAM,CAACH,OAAO,CAAE,CAAC,CAAEE,UAAU,CAAC,CACxC5G,iBAAiB,CAAC2G,SAAS,CAAC,CAC9B,CAAC,CAED,KAAM,CAAAG,qBAAqB,CAAGA,CAAA,GAAM,CAClC;AACA,KAAM,CAAAC,WAAW,CAAGC,cAAc,CAACd,MAAM,CAACvD,KAAK,EAC7C,CAAC5C,cAAc,CAACiG,IAAI,CAACiB,QAAQ,EAAIA,QAAQ,CAACpE,GAAG,GAAKF,KAAK,CAACE,GAAG,CAC7D,CAAC,CAED7C,iBAAiB,CAACiE,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,GAAG8C,WAAW,CAAC,CAAC,CACtD,CAAC,CAED,KAAM,CAAAG,uBAAuB,CAAGA,CAAA,GAAM,CACpC;AACA,KAAM,CAAAC,iBAAiB,CAAGH,cAAc,CAACT,GAAG,CAAC5D,KAAK,EAAIA,KAAK,CAACE,GAAG,CAAC,CAChE7C,iBAAiB,CAACiE,IAAI,EACpBA,IAAI,CAACiC,MAAM,CAACvD,KAAK,EAAI,CAACwE,iBAAiB,CAACC,QAAQ,CAACzE,KAAK,CAACE,GAAG,CAAC,CAC7D,CAAC,CACH,CAAC,CAED,KAAM,CAAAwE,8BAA8B,CAAGA,CAAA,GAAM,CAC3C;AACA,KAAM,CAAAN,WAAW,CAAGlH,eAAe,CAACqG,MAAM,CAACvD,KAAK,EAC9C,CAAC5C,cAAc,CAACiG,IAAI,CAACiB,QAAQ,EAAIA,QAAQ,CAACpE,GAAG,GAAKF,KAAK,CAACE,GAAG,CAC7D,CAAC,CAED7C,iBAAiB,CAACiE,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,GAAG8C,WAAW,CAAC,CAAC,CACtD,CAAC,CAED,KAAM,CAAAO,oBAAoB,CAAGA,CAAA,GAAM,CACjCtH,iBAAiB,CAAC,EAAE,CAAC,CACvB,CAAC,CAED,KAAM,CAAAuH,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,KAAM,CAAAC,UAAU,CAAGC,YAAY,CAAC,CAAC,CAEjC,GAAI,CAACD,UAAU,CAACxD,OAAO,CAAE,CACvBpD,SAAS,CAAC4G,UAAU,CAAC7G,MAAM,CAAC,CAC5B,OACF,CAEAG,SAAS,CAAC,IAAI,CAAC,CACf,GAAI,CACF;AACA,KAAM,CAAA4G,iBAAiB,CAAG,CACxB,GAAGnI,iBAAiB,CACpB;AACAkE,UAAU,CAAElE,iBAAiB,CAACkE,UAAU,CACxC2B,QAAQ,CAAE7F,iBAAiB,CAAC6F,QAAQ,CACpC;AACA1B,UAAU,CAAEnE,iBAAiB,CAACmE,UAAU,CACxC8B,QAAQ,CAAEjG,iBAAiB,CAACiG,QAAQ,CACpC;AACA7B,YAAY,CAAEpE,iBAAiB,CAACoE,YAAY,EAAI,IAAI,CACpD8B,UAAU,CAAElG,iBAAiB,CAACkG,UAAU,EAAI,IAC9C,CAAC,CAED,KAAM,CAAAvC,MAAM,CAAG,CACbC,IAAI,CAAE1D,QAAQ,CACd2D,WAAW,CAAEzD,eAAe,CAC5B2C,MAAM,CAAEvC,cAAc,CACtB6D,SAAS,CAAE8D,iBAAiB,CAC5BC,QAAQ,CAAE,CACRC,YAAY,CAAE,IAAI,CAClBC,sBAAsB,CAAE,IAAI,CAC5BC,gBAAgB,CAAE,IACpB,CACF,CAAC,CAED,GAAI,CAAAC,WAAW,CAEf,GAAIlG,UAAU,CAAE,CACd;AACA;AACA,KAAM,CAAAmG,YAAY,CAAGtJ,iBAAiB,CAACuE,iBAAiB,CAAC,CAAC,CAACoC,IAAI,CAACY,CAAC,EAAIA,CAAC,CAAC3C,EAAE,GAAKvB,aAAa,EAAIkE,CAAC,CAACpD,GAAG,GAAKd,aAAa,CAAC,CACvH,GAAIiG,YAAY,CAAE,CAChB;AACA9E,MAAM,CAACI,EAAE,CAAG0E,YAAY,CAAC1E,EAAE,CAC3BJ,MAAM,CAACL,GAAG,CAAGmF,YAAY,CAACnF,GAAG,CAC7BK,MAAM,CAACK,IAAI,CAAGyE,YAAY,CAACzE,IAAI,CAC/BL,MAAM,CAACM,YAAY,CAAGwE,YAAY,CAACxE,YAAY,CAC/CN,MAAM,CAAC+E,SAAS,CAAGD,YAAY,CAACC,SAAS,CAAE;AAC3C/E,MAAM,CAACgF,SAAS,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAE;AAE7CL,WAAW,CAAGrJ,iBAAiB,CAAC2J,cAAc,CAACL,YAAY,CAACzE,IAAI,CAAEyE,YAAY,CAACxE,YAAY,CAAEN,MAAM,CAAC,CACtG,CAAC,IAAM,CACL,KAAM,IAAI,CAAAoF,KAAK,CAAC,qCAAqC,CAAC,CACxD,CACF,CAAC,IAAM,CACL;AACA,GAAI/I,iBAAiB,CAACoE,YAAY,CAAE,CAClCoE,WAAW,CAAGrJ,iBAAiB,CAAC2J,cAAc,CAAC,YAAY,CAAE9I,iBAAiB,CAACoE,YAAY,CAAET,MAAM,CAAC,CACtG,CAAC,IAAM,IAAI3D,iBAAiB,CAACmE,UAAU,CAAE,CACvCqE,WAAW,CAAGrJ,iBAAiB,CAAC2J,cAAc,CAAC,UAAU,CAAE9I,iBAAiB,CAACmE,UAAU,CAAER,MAAM,CAAC,CAClG,CAAC,IAAM,CACL,KAAM,IAAI,CAAAoF,KAAK,CAAC,mDAAmD,CAAC,CACtE,CACF,CAEA;AACApG,cAAc,CAAC,CAAC,CAEhB,GAAI9C,MAAM,CAAE,CACVA,MAAM,CAAC2I,WAAW,CAAC,CACrB,CAAC,IAAM,CACL;AACA,KAAM,CAAAQ,OAAO,CAAG1G,UAAU,CAAG,4BAA4B,CAAG,wCAAwC,CACpG2G,KAAK,CAACD,OAAO,CAAC,CACd;AACA7I,WAAW,CAAC,EAAE,CAAC,CACfE,kBAAkB,CAAC,EAAE,CAAC,CACtBI,iBAAiB,CAAC,EAAE,CAAC,CACrBR,oBAAoB,CAAC,CAAC,CAAC,CAAC,CACxBsC,aAAa,CAAC,KAAK,CAAC,CACpBE,gBAAgB,CAAC,IAAI,CAAC,CACxB,CACF,CAAE,MAAO0C,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C9D,SAAS,CAAC,CAAE6H,OAAO,CAAE/D,KAAK,CAAC6D,OAAQ,CAAC,CAAC,CACvC,CAAC,OAAS,CACRzH,SAAS,CAAC,KAAK,CAAC,CAClB,CACF,CAAC,CAED,KAAM,CAAA2G,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAA9G,MAAM,CAAG,CAAC,CAAC,CAEjB,GAAI,CAAClB,QAAQ,CAACiJ,IAAI,CAAC,CAAC,CAAE,CACpB/H,MAAM,CAAClB,QAAQ,CAAG,uBAAuB,CAC3C,CAEA;AACA,GAAI,CAACF,iBAAiB,CAACkE,UAAU,CAAE,CACjC9C,MAAM,CAACiD,SAAS,CAAG,0BAA0B,CAC/C,CAAC,IAAM,IAAI,CAACrE,iBAAiB,CAACmE,UAAU,CAAE,CACxC/C,MAAM,CAACiD,SAAS,CAAG,0BAA0B,CAC/C,CAAC,IAAM,IAAI,CAACrE,iBAAiB,CAACoE,YAAY,CAAE,CAC1ChD,MAAM,CAACiD,SAAS,CAAG,6BAA6B,CAClD,CAEA;AACA,GAAIrE,iBAAiB,CAACmE,UAAU,EAAI,CAAC7B,UAAU,CAAE,CAC/C,KAAM,CAAAiC,cAAc,CAAGpF,iBAAiB,CAACqF,oBAAoB,CAC3DxE,iBAAiB,CAACmE,UAAU,CAC5BnE,iBAAiB,CAACoE,YACpB,CAAC,CAED,GAAI,CAACG,cAAc,CAACE,OAAO,CAAE,CAC3BrD,MAAM,CAACuD,YAAY,CAAGJ,cAAc,CAACnD,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC,CACxD,CACF,CAEA,GAAIpE,cAAc,CAAC4I,MAAM,GAAK,CAAC,CAAE,CAC/BhI,MAAM,CAAC2B,MAAM,CAAG,kCAAkC,CACpD,CAEA;AACA,KAAM,CAAAsG,cAAc,CAAG,CAAC,MAAM,CAAE,cAAc,CAAE,QAAQ,CAAC,CACzD,KAAM,CAAAC,iBAAiB,CAAG9I,cAAc,CAACwG,GAAG,CAACN,CAAC,EAAIA,CAAC,CAACpD,GAAG,CAAC,CACxD,KAAM,CAAAiG,eAAe,CAAGF,cAAc,CAAC1C,MAAM,CAAC6C,EAAE,EAAI,CAACF,iBAAiB,CAACzB,QAAQ,CAAC2B,EAAE,CAAC,CAAC,CAEpF,GAAID,eAAe,CAACH,MAAM,CAAG,CAAC,CAAE,CAC9BhI,MAAM,CAACiI,cAAc,CAAG,4BAA4BE,eAAe,CAAC3E,IAAI,CAAC,IAAI,CAAC,EAAE,CAClF,CAEA,MAAO,CACLH,OAAO,CAAEgF,MAAM,CAACC,IAAI,CAACtI,MAAM,CAAC,CAACgI,MAAM,GAAK,CAAC,CACzChI,MACF,CAAC,CACH,CAAC,CAED,KAAM,CAAAuI,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,GAAI,CAAAC,QAAQ,CAAGtJ,eAAe,CAE9B;AACA,GAAII,cAAc,GAAK,KAAK,CAAE,CAC5BkJ,QAAQ,CAAGA,QAAQ,CAACjD,MAAM,CAACvD,KAAK,EAAIA,KAAK,CAACyG,OAAO,GAAKnJ,cAAc,CAAC,CACvE,CAEA;AACA,GAAIE,UAAU,CAAE,CACdgJ,QAAQ,CAAGA,QAAQ,CAACjD,MAAM,CAACvD,KAAK,EAC9BA,KAAK,CAAC0G,KAAK,CAACC,WAAW,CAAC,CAAC,CAAClC,QAAQ,CAACjH,UAAU,CAACmJ,WAAW,CAAC,CAAC,CAAC,EAC5D3G,KAAK,CAACE,GAAG,CAACyG,WAAW,CAAC,CAAC,CAAClC,QAAQ,CAACjH,UAAU,CAACmJ,WAAW,CAAC,CAAC,CAC3D,CAAC,CACH,CAEA,MAAO,CAAAH,QAAQ,CACjB,CAAC,CAED,KAAM,CAAAI,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAAC,MAAM,CAAG,CAAEC,GAAG,CAAE5J,eAAe,CAAC8I,MAAO,CAAC,CAC9CK,MAAM,CAACC,IAAI,CAACzK,sBAAsB,CAAC,CAACkE,OAAO,CAACgH,UAAU,EAAI,CACxDF,MAAM,CAACE,UAAU,CAAC,CAAG7J,eAAe,CAACqG,MAAM,CAACD,CAAC,EAAIA,CAAC,CAACmD,OAAO,GAAKM,UAAU,CAAC,CAACf,MAAM,CACnF,CAAC,CAAC,CACF,MAAO,CAAAa,MAAM,CACf,CAAC,CAED,KAAM,CAAAG,aAAa,CAAGJ,gBAAgB,CAAC,CAAC,CACxC,KAAM,CAAAvC,cAAc,CAAGkC,iBAAiB,CAAC,CAAC,CAE1C,mBACEjK,KAAA,QAAK2K,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B5K,KAAA,QAAK2K,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC9K,IAAA,OAAA8K,QAAA,CAAI,cAAY,CAAI,CAAC,cACrB5K,KAAA,QAAK2K,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B9K,IAAA,WACEwE,IAAI,CAAC,QAAQ,CACbuG,OAAO,CAAEA,CAAA,GAAMxJ,cAAc,CAAC,IAAI,CAAE,CACpCsJ,SAAS,CAAC,mBAAmB,CAC7BG,QAAQ,CAAEhK,cAAc,CAAC4I,MAAM,GAAK,CAAE,CAAAkB,QAAA,CACvC,cAED,CAAQ,CAAC,cACT9K,IAAA,WACEwE,IAAI,CAAC,QAAQ,CACbuG,OAAO,CAAEvC,UAAW,CACpBqC,SAAS,CAAC,iBAAiB,CAC3BG,QAAQ,CAAElJ,MAAM,EAAIF,MAAM,CAACuD,YAAa,CAAA2F,QAAA,CAEvChJ,MAAM,CAAIgB,UAAU,CAAG,aAAa,CAAG,WAAW,CAAKA,UAAU,CAAG,aAAa,CAAG,WAAY,CAC3F,CAAC,CACRxC,QAAQ,eACPN,IAAA,WACEwE,IAAI,CAAC,QAAQ,CACbuG,OAAO,CAAEzK,QAAS,CAClBuK,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC5B,QAED,CAAQ,CACT,EACE,CAAC,EACH,CAAC,CAELlJ,MAAM,CAAC8H,OAAO,eACb1J,IAAA,QAAK6K,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAElJ,MAAM,CAAC8H,OAAO,CAAM,CACzD,CAGAO,MAAM,CAACC,IAAI,CAACtI,MAAM,CAAC,CAACgI,MAAM,CAAG,CAAC,eAC7B1J,KAAA,QAAK2K,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC9K,IAAA,OAAA8K,QAAA,CAAI,kCAAgC,CAAI,CAAC,cACzC9K,IAAA,OAAA8K,QAAA,CACGb,MAAM,CAACgB,OAAO,CAACrJ,MAAM,CAAC,CAAC4F,GAAG,CAAC0D,KAAA,MAAC,CAACpH,GAAG,CAAE0F,OAAO,CAAC,CAAA0B,KAAA,oBACzClL,IAAA,OAAA8K,QAAA,CAAetB,OAAO,EAAb1F,GAAkB,CAAC,EAC7B,CAAC,CACA,CAAC,EACF,CACN,cAED5D,KAAA,QAAK2K,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eAEnC5K,KAAA,QAAK2K,SAAS,CAAC,cAAc,CAAAC,QAAA,eAE3B5K,KAAA,QAAK2K,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B5K,KAAA,QAAK2K,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B5K,KAAA,OAAA4K,QAAA,EAAI,eAAa,CAAC9I,UAAU,CAAC4H,MAAM,CAAC,GAAC,EAAI,CAAC,cAC1C5J,IAAA,WACEwE,IAAI,CAAC,QAAQ,CACbqG,SAAS,CAAC,UAAU,CACpBE,OAAO,CAAEA,CAAA,GAAM5I,iBAAiB,CAAC,CAACD,cAAc,CAAE,CAAA4I,QAAA,CAEjD5I,cAAc,CAAG,MAAM,CAAG,MAAM,CAC3B,CAAC,EACN,CAAC,CAELA,cAAc,eACblC,IAAA,QAAK6K,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9B9I,UAAU,CAAC4H,MAAM,GAAK,CAAC,cACtB5J,IAAA,MAAG6K,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,mDAAiD,CAAG,CAAC,CAErF9I,UAAU,CAACwF,GAAG,CAAE2D,IAAI,eAClBjL,KAAA,QAAoB2K,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC7C5K,KAAA,QAAK2K,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B9K,IAAA,OAAA8K,QAAA,CAAKK,IAAI,CAAC/G,IAAI,CAAK,CAAC,cACpBpE,IAAA,SAAM6K,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAEK,IAAI,CAAC3G,IAAI,CAAO,CAAC,EACjD,CAAC,cACNxE,IAAA,MAAG6K,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAEK,IAAI,CAAC9G,WAAW,EAAI,gBAAgB,CAAI,CAAC,cAC1EnE,KAAA,QAAK2K,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB5K,KAAA,SAAA4K,QAAA,EAAOK,IAAI,CAACC,OAAO,CAACC,UAAU,CAAC,SAAO,EAAM,CAAC,cAC7CrL,IAAA,SAAA8K,QAAA,CAAM,QAAC,CAAM,CAAC,cACd5K,KAAA,SAAA4K,QAAA,EAAM,UAAQ,CAAC,GAAI,CAAA1B,IAAI,CAAC+B,IAAI,CAAChC,SAAS,CAAC,CAACmC,kBAAkB,CAAC,CAAC,EAAO,CAAC,EACjE,CAAC,cACNpL,KAAA,QAAK2K,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9K,IAAA,WACEwE,IAAI,CAAC,QAAQ,CACbqG,SAAS,CAAC,uBAAuB,CACjCE,OAAO,CAAEA,CAAA,GAAM3H,iBAAiB,CAAC+H,IAAI,CAAE,CAAAL,QAAA,CACxC,MAED,CAAQ,CAAC,cACT9K,IAAA,WACEwE,IAAI,CAAC,QAAQ,CACbqG,SAAS,CAAC,sBAAsB,CAChCE,OAAO,CAAEA,CAAA,GAAM,CACb,GAAIQ,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,CAAE,CAChE7L,iBAAiB,CAAC8L,gBAAgB,CAACN,IAAI,CAAC3G,IAAI,CAAE2G,IAAI,CAAC1G,YAAY,CAAC,CAChEtB,cAAc,CAAC,CAAC,CAClB,CACF,CAAE,CAAA2H,QAAA,CACH,QAED,CAAQ,CAAC,EACN,CAAC,GA/BEK,IAAI,CAACrH,GAgCV,CACN,CACF,CACE,CACN,EACE,CAAC,cAGN5D,KAAA,QAAK2K,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B9K,IAAA,OAAA8K,QAAA,CAAI,oBAAkB,CAAI,CAAC,cAE3B5K,KAAA,QAAK2K,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9K,IAAA,UAAA8K,QAAA,CAAO,aAAW,CAAO,CAAC,cAC1B9K,IAAA,UACEwE,IAAI,CAAC,MAAM,CACX4B,KAAK,CAAE1F,QAAS,CAChBgL,QAAQ,CAAGxF,CAAC,EAAKvF,WAAW,CAACuF,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAC7CuF,WAAW,CAAC,iBAAiB,CAC7Bd,SAAS,CAAEjJ,MAAM,CAAClB,QAAQ,CAAG,OAAO,CAAG,EAAG,CAC3C,CAAC,CACDkB,MAAM,CAAClB,QAAQ,eAAIV,IAAA,QAAK6K,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAElJ,MAAM,CAAClB,QAAQ,CAAM,CAAC,EACvE,CAAC,cAENR,KAAA,QAAK2K,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9K,IAAA,UAAA8K,QAAA,CAAO,aAAW,CAAO,CAAC,cAC1B9K,IAAA,aACEoG,KAAK,CAAExF,eAAgB,CACvB8K,QAAQ,CAAGxF,CAAC,EAAKrF,kBAAkB,CAACqF,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CACpDuF,WAAW,CAAC,wBAAwB,CACpCC,IAAI,CAAC,GAAG,CACT,CAAC,EACC,CAAC,EAGH,CAAC,cAGN1L,KAAA,QAAK2K,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B5K,KAAA,OAAA4K,QAAA,EAAI,mBAAiB,CAAC9J,cAAc,CAAC4I,MAAM,CAAC,GAAC,EAAI,CAAC,CACjDhI,MAAM,CAAC2B,MAAM,eAAIvD,IAAA,QAAK6K,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAElJ,MAAM,CAAC2B,MAAM,CAAM,CAAC,CACrE3B,MAAM,CAACiI,cAAc,eAAI7J,IAAA,QAAK6K,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAElJ,MAAM,CAACiI,cAAc,CAAM,CAAC,cAEtF7J,IAAA,QAAK6K,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7B9J,cAAc,CAAC4I,MAAM,GAAK,CAAC,cAC1B5J,IAAA,QAAK6K,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,oEAE7B,CAAK,CAAC,CAEN9J,cAAc,CAACwG,GAAG,CAAC,CAAC5D,KAAK,CAAEiI,KAAK,gBAC9B3L,KAAA,QAAqB2K,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7C5K,KAAA,QAAK2K,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9K,IAAA,SAAM6K,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAElH,KAAK,CAAC0G,KAAK,CAAO,CAAC,cAClDtK,IAAA,SAAM6K,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAElH,KAAK,CAACY,IAAI,CAAO,CAAC,CAC/CZ,KAAK,CAACkI,QAAQ,eAAI9L,IAAA,SAAM6K,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,EAChE,CAAC,cACN5K,KAAA,QAAK2K,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B9K,IAAA,WACEwE,IAAI,CAAC,QAAQ,CACbuG,OAAO,CAAEA,CAAA,GAAM1D,iBAAiB,CAACzD,KAAK,CAAE,CACxCiH,SAAS,CAAC,UAAU,CACpBkB,KAAK,CAAC,iBAAiB,CAAAjB,QAAA,CACxB,cAED,CAAQ,CAAC,cACT9K,IAAA,WACEwE,IAAI,CAAC,QAAQ,CACbuG,OAAO,CAAEA,CAAA,GAAMtD,eAAe,CAACoE,KAAK,CAAEG,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEJ,KAAK,CAAG,CAAC,CAAC,CAAE,CAC9DhB,SAAS,CAAC,UAAU,CACpBG,QAAQ,CAAEa,KAAK,GAAK,CAAE,CACtBE,KAAK,CAAC,SAAS,CAAAjB,QAAA,CAChB,QAED,CAAQ,CAAC,cACT9K,IAAA,WACEwE,IAAI,CAAC,QAAQ,CACbuG,OAAO,CAAEA,CAAA,GAAMtD,eAAe,CAACoE,KAAK,CAAEG,IAAI,CAACE,GAAG,CAAClL,cAAc,CAAC4I,MAAM,CAAG,CAAC,CAAEiC,KAAK,CAAG,CAAC,CAAC,CAAE,CACtFhB,SAAS,CAAC,UAAU,CACpBG,QAAQ,CAAEa,KAAK,GAAK7K,cAAc,CAAC4I,MAAM,CAAG,CAAE,CAC9CmC,KAAK,CAAC,WAAW,CAAAjB,QAAA,CAClB,QAED,CAAQ,CAAC,cACT9K,IAAA,WACEwE,IAAI,CAAC,QAAQ,CACbuG,OAAO,CAAEA,CAAA,GAAMhE,iBAAiB,CAACnD,KAAK,CAAE,CACxCiH,SAAS,CAAC,iBAAiB,CAC3BkB,KAAK,CAAC,cAAc,CAAAjB,QAAA,CACrB,QAED,CAAQ,CAAC,EACN,CAAC,GAzCElH,KAAK,CAACE,GA0CX,CACN,CACF,CACE,CAAC,EACH,CAAC,EACH,CAAC,cAGN5D,KAAA,QAAK2K,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9K,IAAA,QAAK6K,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B9K,IAAA,OAAA8K,QAAA,CAAI,kBAAgB,CAAI,CAAC,CACtB,CAAC,cAGN5K,KAAA,QAAK2K,SAAS,CAAC,qBAAqB,CAACsB,KAAK,CAAE,CAC1CC,eAAe,CAAE,SAAS,CAC1BC,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,QAAQ,CACjBC,MAAM,CAAE,QACV,CAAE,CAAA1B,QAAA,eACA9K,IAAA,OAAImM,KAAK,CAAE,CAAEK,MAAM,CAAE,YAAY,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAA3B,QAAA,CAAC,kEAEvD,CAAI,CAAC,cACL5K,KAAA,QAAKiM,KAAK,CAAE,CACVO,QAAQ,CAAE,UAAU,CACpBD,KAAK,CAAE,SAAS,CAChBE,YAAY,CAAE,MAAM,CACpBJ,OAAO,CAAE,SAAS,CAClBH,eAAe,CAAE,SAAS,CAC1BE,YAAY,CAAE,KAAK,CACnBD,MAAM,CAAE,mBACV,CAAE,CAAAvB,QAAA,eACA9K,IAAA,WAAA8K,QAAA,CAAQ,sBAAoB,CAAQ,CAAC,cACrC5K,KAAA,OAAIiM,KAAK,CAAE,CAAEK,MAAM,CAAE,iBAAiB,CAAEI,WAAW,CAAE,MAAO,CAAE,CAAA9B,QAAA,eAC5D9K,IAAA,OAAA8K,QAAA,CAAI,mFAAiF,CAAI,CAAC,cAC1F9K,IAAA,OAAA8K,QAAA,CAAI,yCAAuC,CAAI,CAAC,cAChD9K,IAAA,OAAA8K,QAAA,CAAI,sFAAoF,CAAI,CAAC,EAC3F,CAAC,EACF,CAAC,cAEN5K,KAAA,QAAK2K,SAAS,CAAC,qBAAqB,CAACsB,KAAK,CAAE,CAC1CU,OAAO,CAAE,MAAM,CACfC,mBAAmB,CAAE,sCAAsC,CAC3DC,GAAG,CAAE,MACP,CAAE,CAAAjC,QAAA,eAEA5K,KAAA,QAAK2K,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9K,IAAA,UAAOmM,KAAK,CAAE,CACZU,OAAO,CAAE,OAAO,CAChBF,YAAY,CAAE,QAAQ,CACtBK,UAAU,CAAE,KAAK,CACjBP,KAAK,CAAE,SACT,CAAE,CAAA3B,QAAA,CAAC,YAEH,CAAO,CAAC,cACR5K,KAAA,WACEkG,KAAK,CAAE5F,iBAAiB,CAACkE,UAAU,EAAI,EAAG,CAC1CgH,QAAQ,CAAEzF,oBAAqB,CAC/B+E,QAAQ,CAAEtI,OAAO,CAACN,SAAU,CAC5ByI,SAAS,CAAEjJ,MAAM,CAAC8C,UAAU,CAAG,OAAO,CAAG,EAAG,CAC5CyH,KAAK,CAAE,CACLc,KAAK,CAAE,MAAM,CACbV,OAAO,CAAE,SAAS,CAClBF,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBI,QAAQ,CAAE,MAAM,CAChBN,eAAe,CAAE,OACnB,CAAE,CAAAtB,QAAA,eAEF9K,IAAA,WAAQoG,KAAK,CAAC,EAAE,CAAA0E,QAAA,CAAC,iBAAe,CAAQ,CAAC,CACxC1I,SAAS,CAACoF,GAAG,CAACnB,QAAQ,eACrBrG,IAAA,WAA0BoG,KAAK,CAAEC,QAAQ,CAAC9B,EAAG,CAAAuG,QAAA,CAC1CzE,QAAQ,CAACjC,IAAI,EADHiC,QAAQ,CAAC9B,EAEd,CACT,CAAC,EACI,CAAC,CACR3C,MAAM,CAAC8C,UAAU,eAChB1E,IAAA,QAAKmM,KAAK,CAAE,CAAEM,KAAK,CAAE,SAAS,CAAEC,QAAQ,CAAE,UAAU,CAAEQ,SAAS,CAAE,SAAU,CAAE,CAAApC,QAAA,CAC1ElJ,MAAM,CAAC8C,UAAU,CACf,CACN,EACE,CAAC,cAGNxE,KAAA,QAAK2K,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9K,IAAA,UAAOmM,KAAK,CAAE,CACZU,OAAO,CAAE,OAAO,CAChBF,YAAY,CAAE,QAAQ,CACtBK,UAAU,CAAE,KAAK,CACjBP,KAAK,CAAE,SACT,CAAE,CAAA3B,QAAA,CAAC,YAEH,CAAO,CAAC,cACR5K,KAAA,WACEkG,KAAK,CAAE5F,iBAAiB,CAACmE,UAAU,EAAI,EAAG,CAC1C+G,QAAQ,CAAE/E,oBAAqB,CAC/BqE,QAAQ,CAAE,CAACxK,iBAAiB,CAACkE,UAAU,EAAIhC,OAAO,CAACJ,UAAW,CAC9DuI,SAAS,CAAEjJ,MAAM,CAAC+C,UAAU,CAAG,OAAO,CAAG,EAAG,CAC5CwH,KAAK,CAAE,CACLc,KAAK,CAAE,MAAM,CACbV,OAAO,CAAE,SAAS,CAClBF,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBI,QAAQ,CAAE,MAAM,CAChBN,eAAe,CAAE,OACnB,CAAE,CAAAtB,QAAA,eAEF9K,IAAA,WAAQoG,KAAK,CAAC,EAAE,CAAA0E,QAAA,CACb,CAACtK,iBAAiB,CAACkE,UAAU,CAAG,uBAAuB,CAAG,iBAAiB,CACtE,CAAC,CACRpC,UAAU,CAACkF,GAAG,CAACf,QAAQ,eACtBzG,IAAA,WAA0BoG,KAAK,CAAEK,QAAQ,CAAClC,EAAG,CAAAuG,QAAA,CAC1CrE,QAAQ,CAACrC,IAAI,EADHqC,QAAQ,CAAClC,EAEd,CACT,CAAC,EACI,CAAC,CACR3C,MAAM,CAAC+C,UAAU,eAChB3E,IAAA,QAAKmM,KAAK,CAAE,CAAEM,KAAK,CAAE,SAAS,CAAEC,QAAQ,CAAE,UAAU,CAAEQ,SAAS,CAAE,SAAU,CAAE,CAAApC,QAAA,CAC1ElJ,MAAM,CAAC+C,UAAU,CACf,CACN,EACE,CAAC,cAGNzE,KAAA,QAAK2K,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9K,IAAA,UAAOmM,KAAK,CAAE,CACZU,OAAO,CAAE,OAAO,CAChBF,YAAY,CAAE,QAAQ,CACtBK,UAAU,CAAE,KAAK,CACjBP,KAAK,CAAE,SACT,CAAE,CAAA3B,QAAA,CAAC,0BAEH,CAAO,CAAC,cACR5K,KAAA,WACEkG,KAAK,CAAE5F,iBAAiB,CAACoE,YAAY,EAAI,EAAG,CAC5C8G,QAAQ,CAAE7E,sBAAuB,CACjCmE,QAAQ,CAAE,CAACxK,iBAAiB,CAACmE,UAAU,EAAIjC,OAAO,CAACF,WAAY,CAC/DsJ,QAAQ,MACRK,KAAK,CAAE,CACLc,KAAK,CAAE,MAAM,CACbV,OAAO,CAAE,SAAS,CAClBF,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBI,QAAQ,CAAE,MAAM,CAChBN,eAAe,CAAE,OACnB,CAAE,CAAAtB,QAAA,eAEF9K,IAAA,WAAQoG,KAAK,CAAC,EAAE,CAAA0E,QAAA,CACb,CAACtK,iBAAiB,CAACmE,UAAU,CAAG,uBAAuB,CAAG,+BAA+B,CACpF,CAAC,CACRnC,WAAW,CAACgF,GAAG,CAACd,UAAU,eACzB1G,IAAA,WAA4BoG,KAAK,CAAEM,UAAU,CAACnC,EAAG,CAAAuG,QAAA,CAC9CpE,UAAU,CAACtC,IAAI,EADLsC,UAAU,CAACnC,EAEhB,CACT,CAAC,EACI,CAAC,EACN,CAAC,EACH,CAAC,CAEL3C,MAAM,CAACiD,SAAS,eACf7E,IAAA,QAAKmM,KAAK,CAAE,CAAEM,KAAK,CAAE,SAAS,CAAEC,QAAQ,CAAE,UAAU,CAAEQ,SAAS,CAAE,QAAS,CAAE,CAAApC,QAAA,CACzElJ,MAAM,CAACiD,SAAS,CACd,CACN,CACAjD,MAAM,CAACuD,YAAY,eAClBjF,KAAA,QAAKiM,KAAK,CAAE,CAAEM,KAAK,CAAE,SAAS,CAAEC,QAAQ,CAAE,UAAU,CAAEQ,SAAS,CAAE,QAAQ,CAAEF,UAAU,CAAE,MAAO,CAAE,CAAAlC,QAAA,EAAC,eAC5F,CAAClJ,MAAM,CAACuD,YAAY,EACpB,CACN,CAGAvC,gBAAgB,EAAIA,gBAAgB,CAACuK,aAAa,CAACvD,MAAM,CAAG,CAAC,eAC5D1J,KAAA,QAAKiM,KAAK,CAAE,CACVe,SAAS,CAAE,MAAM,CACjBX,OAAO,CAAE,SAAS,CAClBH,eAAe,CAAE,SAAS,CAC1BC,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBI,QAAQ,CAAE,UACZ,CAAE,CAAA5B,QAAA,eACA9K,IAAA,WAAA8K,QAAA,CAAQ,8BAAkB,CAAQ,CAAC,CAClClI,gBAAgB,CAACuK,aAAa,CAAC3F,GAAG,CAAC,CAAC2D,IAAI,CAAEU,KAAK,gBAC9C3L,KAAA,QAAiBiM,KAAK,CAAE,CAAEe,SAAS,CAAE,QAAS,CAAE,CAAApC,QAAA,eAC9C5K,KAAA,WAAA4K,QAAA,EAASK,IAAI,CAAC3G,IAAI,GAAK,UAAU,CAAG,UAAU,CAAG,aAAa,CAAC,QAAM,EAAQ,CAAC,IAAC,CAAC2G,IAAI,CAAC/G,IAAI,CACxF+G,IAAI,CAAC9G,WAAW,eAAIrE,IAAA,QAAKmM,KAAK,CAAE,CAAEM,KAAK,CAAE,SAAU,CAAE,CAAA3B,QAAA,CAAEK,IAAI,CAAC9G,WAAW,CAAM,CAAC,GAFvEwH,KAGL,CACN,CAAC,EACC,CACN,CAGAjJ,gBAAgB,EAAIA,gBAAgB,CAACwK,oBAAoB,CAACxD,MAAM,CAAG,CAAC,EAAI,CAACpJ,iBAAiB,CAACoE,YAAY,eACtG1E,KAAA,QAAKiM,KAAK,CAAE,CACVe,SAAS,CAAE,MAAM,CACjBX,OAAO,CAAE,SAAS,CAClBH,eAAe,CAAE,SAAS,CAC1BC,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBI,QAAQ,CAAE,UACZ,CAAE,CAAA5B,QAAA,eACA9K,IAAA,WAAA8K,QAAA,CAAQ,uBAAW,CAAQ,CAAC,sBAAmB,CAAClI,gBAAgB,CAACwK,oBAAoB,CAACxD,MAAM,CAAC,mEAE/F,EAAK,CACN,CAGApJ,iBAAiB,CAACmE,UAAU,EAAI,CAAC/C,MAAM,CAACuD,YAAY,eACnDjF,KAAA,QAAKiM,KAAK,CAAE,CACVe,SAAS,CAAE,MAAM,CACjBX,OAAO,CAAE,SAAS,CAClBH,eAAe,CAAE,SAAS,CAC1BC,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBI,QAAQ,CAAE,UACZ,CAAE,CAAA5B,QAAA,eACA9K,IAAA,WAAA8K,QAAA,CAAQ,yBAAkB,CAAQ,CAAC,mCAAgC,CAACtK,iBAAiB,CAACoE,YAAY,CAAG,aAAa,CAAG,UAAU,CAAC,GAClI,EAAK,CACN,EACE,CAAC,cAGN5E,IAAA,QAAK6K,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B9K,IAAA,UACEwE,IAAI,CAAC,MAAM,CACXmH,WAAW,CAAC,kBAAkB,CAC9BvF,KAAK,CAAEhF,UAAW,CAClBsK,QAAQ,CAAGxF,CAAC,EAAK7E,aAAa,CAAC6E,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAC/CyE,SAAS,CAAC,cAAc,CACzB,CAAC,CACC,CAAC,cAGN3K,KAAA,QAAK2K,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B5K,KAAA,WACEsE,IAAI,CAAC,QAAQ,CACbuG,OAAO,CAAEA,CAAA,GAAM5J,iBAAiB,CAAC,KAAK,CAAE,CACxC0J,SAAS,CAAE,eAAe3J,cAAc,GAAK,KAAK,CAAG,QAAQ,CAAG,EAAE,EAAG,CAAA4J,QAAA,EACtE,OACM,CAACF,aAAa,CAACF,GAAG,CAAC,GAC1B,EAAQ,CAAC,CACRT,MAAM,CAACgB,OAAO,CAACxL,sBAAsB,CAAC,CAAC+H,GAAG,CAAC6F,KAAA,MAAC,CAAC1C,UAAU,CAAEN,OAAO,CAAC,CAAAgD,KAAA,oBAChEnN,KAAA,WAEEsE,IAAI,CAAC,QAAQ,CACbuG,OAAO,CAAEA,CAAA,GAAM5J,iBAAiB,CAACwJ,UAAU,CAAE,CAC7CE,SAAS,CAAE,eAAe3J,cAAc,GAAKyJ,UAAU,CAAG,QAAQ,CAAG,EAAE,EAAG,CAAAG,QAAA,EAEzET,OAAO,CAAC0B,KAAK,CAAC,IAAE,CAACnB,aAAa,CAACD,UAAU,CAAC,CAAC,GAC9C,GANOA,UAMC,CAAC,EACV,CAAC,EACC,CAAC,cAGNzK,KAAA,QAAK2K,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtC5K,KAAA,QAAK2K,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC9K,IAAA,SAAM6K,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,gBAAc,CAAM,CAAC,cAC3D5K,KAAA,WACEsE,IAAI,CAAC,QAAQ,CACbuG,OAAO,CAAEhD,qBAAsB,CAC/B8C,SAAS,CAAC,wBAAwB,CAClCkB,KAAK,CAAE,cAAc9D,cAAc,CAAC2B,MAAM,kBAAmB,CAAAkB,QAAA,EAC9D,mBACkB,CAAC7C,cAAc,CAAC2B,MAAM,CAAC,GAC1C,EAAQ,CAAC,cACT5J,IAAA,WACEwE,IAAI,CAAC,QAAQ,CACbuG,OAAO,CAAE5C,uBAAwB,CACjC0C,SAAS,CAAC,wBAAwB,CAClCkB,KAAK,CAAC,8BAA8B,CAAAjB,QAAA,CACrC,mBAED,CAAQ,CAAC,cACT5K,KAAA,WACEsE,IAAI,CAAC,QAAQ,CACbuG,OAAO,CAAEzC,8BAA+B,CACxCuC,SAAS,CAAC,wBAAwB,CAClCkB,KAAK,CAAE,cAAcjL,eAAe,CAAC8I,MAAM,mBAAoB,CAAAkB,QAAA,EAChE,cACa,CAAChK,eAAe,CAAC8I,MAAM,CAAC,GACtC,EAAQ,CAAC,cACT5J,IAAA,WACEwE,IAAI,CAAC,QAAQ,CACbuG,OAAO,CAAExC,oBAAqB,CAC9BsC,SAAS,CAAC,mCAAmC,CAC7CkB,KAAK,CAAC,2BAA2B,CACjCf,QAAQ,CAAEhK,cAAc,CAAC4I,MAAM,GAAK,CAAE,CAAAkB,QAAA,CACvC,WAED,CAAQ,CAAC,EACN,CAAC,cACN5K,KAAA,QAAK2K,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAC/B9J,cAAc,CAAC4I,MAAM,CAAC,MAAI,CAAC9I,eAAe,CAAC8I,MAAM,CAAC,kBACrD,EAAK,CAAC,EACH,CAAC,cAGN5J,IAAA,QAAK6K,SAAS,CAAC,aAAa,CAAAC,QAAA,CACzB7C,cAAc,CAACT,GAAG,CAAC5D,KAAK,EAAI,CAC3B,KAAM,CAAAoD,UAAU,CAAGhG,cAAc,CAACiG,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACpD,GAAG,GAAKF,KAAK,CAACE,GAAG,CAAC,CAChE,mBACE5D,KAAA,QAEE2K,SAAS,CAAE,cAAc7D,UAAU,CAAG,UAAU,CAAG,EAAE,EAAG,CACxD+D,OAAO,CAAEA,CAAA,GAAMhE,iBAAiB,CAACnD,KAAK,CAAE,CAAAkH,QAAA,eAExC9K,IAAA,QAAK6K,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B9K,IAAA,UACEwE,IAAI,CAAC,UAAU,CACf8I,OAAO,CAAEtG,UAAW,CACpB0E,QAAQ,CAAEA,CAAA,GAAM3E,iBAAiB,CAACnD,KAAK,CAAE,CAC1C,CAAC,CACC,CAAC,cACN1D,KAAA,QAAK2K,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B9K,IAAA,QAAK6K,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAElH,KAAK,CAAC0G,KAAK,CAAM,CAAC,cAC/CpK,KAAA,QAAK2K,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9K,IAAA,SAAM6K,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAElH,KAAK,CAACY,IAAI,CAAO,CAAC,CAC/CZ,KAAK,CAACkI,QAAQ,eAAI9L,IAAA,SAAM6K,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,CAClElH,KAAK,CAAC2J,WAAW,eAAIvN,IAAA,SAAM6K,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,aAAW,CAAM,CAAC,EACzE,CAAC,EACH,CAAC,GAlBDlH,KAAK,CAACE,GAmBR,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CAGLtC,eAAe,EAAIE,WAAW,eAC7B1B,IAAA,CAACH,gBAAgB,EACf+D,KAAK,CAAElC,WAAY,CACnBrB,MAAM,CAAEiH,qBAAsB,CAC9BhH,QAAQ,CAAEA,CAAA,GAAM,CACdmB,kBAAkB,CAAC,KAAK,CAAC,CACzBE,cAAc,CAAC,IAAI,CAAC,CACtB,CAAE,CACH,CACF,CAEAL,WAAW,eACVtB,IAAA,CAACF,WAAW,EACVyD,MAAM,CAAEvC,cAAe,CACvBN,QAAQ,CAAEA,QAAS,CACnB8M,OAAO,CAAEA,CAAA,GAAMjM,cAAc,CAAC,KAAK,CAAE,CACtC,CACF,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAApB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}