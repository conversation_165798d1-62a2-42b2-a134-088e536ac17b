{"ast": null, "code": "import { namedEdges, resolveEdge } from './edge.mjs';\nconst defaultOffset = [0, 0];\nfunction resolveOffset(offset, containerLength, targetLength, targetInset) {\n  let offsetDefinition = Array.isArray(offset) ? offset : defaultOffset;\n  let targetPoint = 0;\n  let containerPoint = 0;\n  if (typeof offset === \"number\") {\n    /**\n     * If we're provided offset: [0, 0.5, 1] then each number x should become\n     * [x, x], so we default to the behaviour of mapping 0 => 0 of both target\n     * and container etc.\n     */\n    offsetDefinition = [offset, offset];\n  } else if (typeof offset === \"string\") {\n    offset = offset.trim();\n    if (offset.includes(\" \")) {\n      offsetDefinition = offset.split(\" \");\n    } else {\n      /**\n       * If we're provided a definition like \"100px\" then we want to apply\n       * that only to the top of the target point, leaving the container at 0.\n       * Whereas a named offset like \"end\" should be applied to both.\n       */\n      offsetDefinition = [offset, namedEdges[offset] ? offset : `0`];\n    }\n  }\n  targetPoint = resolveEdge(offsetDefinition[0], targetLength, targetInset);\n  containerPoint = resolveEdge(offsetDefinition[1], containerLength);\n  return targetPoint - containerPoint;\n}\nexport { resolveOffset };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "resolveEdge", "defaultOffset", "resolveOffset", "offset", "containerLength", "targetLength", "targetInset", "offsetDefinition", "Array", "isArray", "targetPoint", "containerPoint", "trim", "includes", "split"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs"], "sourcesContent": ["import { namedEdges, resolveEdge } from './edge.mjs';\n\nconst defaultOffset = [0, 0];\nfunction resolveOffset(offset, containerLength, targetLength, targetInset) {\n    let offsetDefinition = Array.isArray(offset) ? offset : defaultOffset;\n    let targetPoint = 0;\n    let containerPoint = 0;\n    if (typeof offset === \"number\") {\n        /**\n         * If we're provided offset: [0, 0.5, 1] then each number x should become\n         * [x, x], so we default to the behaviour of mapping 0 => 0 of both target\n         * and container etc.\n         */\n        offsetDefinition = [offset, offset];\n    }\n    else if (typeof offset === \"string\") {\n        offset = offset.trim();\n        if (offset.includes(\" \")) {\n            offsetDefinition = offset.split(\" \");\n        }\n        else {\n            /**\n             * If we're provided a definition like \"100px\" then we want to apply\n             * that only to the top of the target point, leaving the container at 0.\n             * Whereas a named offset like \"end\" should be applied to both.\n             */\n            offsetDefinition = [offset, namedEdges[offset] ? offset : `0`];\n        }\n    }\n    targetPoint = resolveEdge(offsetDefinition[0], targetLength, targetInset);\n    containerPoint = resolveEdge(offsetDefinition[1], containerLength);\n    return targetPoint - containerPoint;\n}\n\nexport { resolveOffset };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,WAAW,QAAQ,YAAY;AAEpD,MAAMC,aAAa,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAC5B,SAASC,aAAaA,CAACC,MAAM,EAAEC,eAAe,EAAEC,YAAY,EAAEC,WAAW,EAAE;EACvE,IAAIC,gBAAgB,GAAGC,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,GAAGA,MAAM,GAAGF,aAAa;EACrE,IAAIS,WAAW,GAAG,CAAC;EACnB,IAAIC,cAAc,GAAG,CAAC;EACtB,IAAI,OAAOR,MAAM,KAAK,QAAQ,EAAE;IAC5B;AACR;AACA;AACA;AACA;IACQI,gBAAgB,GAAG,CAACJ,MAAM,EAAEA,MAAM,CAAC;EACvC,CAAC,MACI,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IACjCA,MAAM,GAAGA,MAAM,CAACS,IAAI,CAAC,CAAC;IACtB,IAAIT,MAAM,CAACU,QAAQ,CAAC,GAAG,CAAC,EAAE;MACtBN,gBAAgB,GAAGJ,MAAM,CAACW,KAAK,CAAC,GAAG,CAAC;IACxC,CAAC,MACI;MACD;AACZ;AACA;AACA;AACA;MACYP,gBAAgB,GAAG,CAACJ,MAAM,EAAEJ,UAAU,CAACI,MAAM,CAAC,GAAGA,MAAM,GAAG,GAAG,CAAC;IAClE;EACJ;EACAO,WAAW,GAAGV,WAAW,CAACO,gBAAgB,CAAC,CAAC,CAAC,EAAEF,YAAY,EAAEC,WAAW,CAAC;EACzEK,cAAc,GAAGX,WAAW,CAACO,gBAAgB,CAAC,CAAC,CAAC,EAAEH,eAAe,CAAC;EAClE,OAAOM,WAAW,GAAGC,cAAc;AACvC;AAEA,SAAST,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}