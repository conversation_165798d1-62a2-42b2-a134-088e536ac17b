// Enums
export const PersonNature = {
  Business: 1,
  Corporate: 2,
  Agriculture: 3,
  Individual: 4
};

export const PersonNatureLabels = {
  [PersonNature.Business]: 'Business',
  [PersonNature.Corporate]: 'Corporate',
  [PersonNature.Agriculture]: 'Agriculture',
  [PersonNature.Individual]: 'Individual'
};

export const Gender = {
  Male: 1,
  Female: 2,
  Other: 3
};

export const GenderLabels = {
  [Gender.Male]: 'Male',
  [Gender.Female]: 'Female',
  [Gender.Other]: 'Other'
};

export const WorkingProfile = {
  Business: 1,
  Corporate: 2,
  Agriculture: 3
};

export const WorkingProfileLabels = {
  [WorkingProfile.Business]: 'Business',
  [WorkingProfile.Corporate]: 'Corporate',
  [WorkingProfile.Agriculture]: 'Agriculture'
};

// Field definitions for form builder
export const PersonFieldDefinitions = {
  // Personal Information Section
  personalInfo: {
    title: 'Personal Information',
    fields: {
      name: {
        key: 'name',
        label: 'Full Name',
        type: 'text',
        required: true,
        validation: { maxLength: 255 },
        section: 'personalInfo'
      },
      gender: {
        key: 'gender',
        label: 'Gender',
        type: 'select',
        required: false,
        options: Object.entries(GenderLabels).map(([value, label]) => ({ value: parseInt(value), label })),
        section: 'personalInfo'
      },
      dateOfBirth: {
        key: 'dateOfBirth',
        label: 'Date of Birth',
        type: 'date',
        required: false,
        section: 'personalInfo'
      },
      isMarried: {
        key: 'isMarried',
        label: 'Is Married',
        type: 'checkbox',
        required: false,
        section: 'personalInfo'
      },
      dateOfMarriage: {
        key: 'dateOfMarriage',
        label: 'Date of Marriage',
        type: 'date',
        required: false,
        conditional: { field: 'isMarried', value: true },
        section: 'personalInfo'
      }
    }
  },

  // Contact Information Section
  contactInfo: {
    title: 'Contact Information',
    fields: {
      mobileNumber: {
        key: 'mobileNumber',
        label: 'Mobile Number',
        type: 'tel',
        required: true,
        validation: { pattern: '^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$' },
        section: 'contactInfo'
      },
      alternateNumbers: {
        key: 'alternateNumbers',
        label: 'Alternate Numbers',
        type: 'array',
        required: false,
        section: 'contactInfo'
      },
      primaryEmailId: {
        key: 'primaryEmailId',
        label: 'Primary Email',
        type: 'email',
        required: false,
        section: 'contactInfo'
      },
      alternateEmailIds: {
        key: 'alternateEmailIds',
        label: 'Alternate Emails',
        type: 'array',
        required: false,
        section: 'contactInfo'
      },
      website: {
        key: 'website',
        label: 'Website',
        type: 'url',
        required: false,
        section: 'contactInfo'
      }
    }
  },

  // Location Information Section
  locationInfo: {
    title: 'Location Information',
    fields: {
      workingState: {
        key: 'workingState',
        label: 'Working State',
        type: 'text',
        required: false,
        section: 'locationInfo'
      },
      domesticState: {
        key: 'domesticState',
        label: 'Domestic State',
        type: 'text',
        required: false,
        section: 'locationInfo'
      },
      district: {
        key: 'district',
        label: 'District',
        type: 'text',
        required: false,
        section: 'locationInfo'
      },
      address: {
        key: 'address',
        label: 'Address',
        type: 'textarea',
        required: false,
        section: 'locationInfo'
      },
      workingArea: {
        key: 'workingArea',
        label: 'Working Area',
        type: 'text',
        required: false,
        section: 'locationInfo'
      }
    }
  },

  // Business Information Section
  businessInfo: {
    title: 'Business Information',
    fields: {
      nature: {
        key: 'nature',
        label: 'Nature',
        type: 'select',
        required: true,
        options: Object.entries(PersonNatureLabels).map(([value, label]) => ({ value: parseInt(value), label })),
        section: 'businessInfo'
      },
      transactionValue: {
        key: 'transactionValue',
        label: 'Transaction Value',
        type: 'number',
        required: false,
        section: 'businessInfo'
      },
      reraRegistrationNumber: {
        key: 'reraRegistrationNumber',
        label: 'RERA Registration Number',
        type: 'text',
        required: false,
        section: 'businessInfo'
      },
      workingProfiles: {
        key: 'workingProfiles',
        label: 'Working Profiles',
        type: 'multiselect',
        required: false,
        options: Object.entries(WorkingProfileLabels).map(([value, label]) => ({ value: parseInt(value), label })),
        section: 'businessInfo'
      },
      starRating: {
        key: 'starRating',
        label: 'Star Rating',
        type: 'number',
        required: false,
        validation: { min: 1, max: 5 },
        section: 'businessInfo'
      },
      source: {
        key: 'source',
        label: 'Source',
        type: 'text',
        required: false,
        section: 'businessInfo'
      },
      remarks: {
        key: 'remarks',
        label: 'Remarks',
        type: 'textarea',
        required: false,
        section: 'businessInfo'
      }
    }
  },

  // Associate Information Section
  associateInfo: {
    title: 'Associate Information',
    fields: {
      hasAssociate: {
        key: 'hasAssociate',
        label: 'Has Associate',
        type: 'checkbox',
        required: false,
        section: 'associateInfo'
      },
      associateName: {
        key: 'associateName',
        label: 'Associate Name',
        type: 'text',
        required: false,
        conditional: { field: 'hasAssociate', value: true },
        section: 'associateInfo'
      },
      associateRelation: {
        key: 'associateRelation',
        label: 'Associate Relation',
        type: 'text',
        required: false,
        conditional: { field: 'hasAssociate', value: true },
        section: 'associateInfo'
      },
      associateMobile: {
        key: 'associateMobile',
        label: 'Associate Mobile',
        type: 'tel',
        required: false,
        conditional: { field: 'hasAssociate', value: true },
        section: 'associateInfo'
      }
    }
  },

  // Digital Presence Section
  digitalPresence: {
    title: 'Digital Presence',
    fields: {
      usingWebsite: {
        key: 'usingWebsite',
        label: 'Using Website',
        type: 'checkbox',
        required: false,
        section: 'digitalPresence'
      },
      websiteLink: {
        key: 'websiteLink',
        label: 'Website Link',
        type: 'url',
        required: false,
        conditional: { field: 'usingWebsite', value: true },
        section: 'digitalPresence'
      },
      usingCRMApp: {
        key: 'usingCRMApp',
        label: 'Using CRM App',
        type: 'checkbox',
        required: false,
        section: 'digitalPresence'
      },
      crmAppLink: {
        key: 'crmAppLink',
        label: 'CRM App Link',
        type: 'url',
        required: false,
        conditional: { field: 'usingCRMApp', value: true },
        section: 'digitalPresence'
      }
    }
  },

  // Company Information Section
  companyInfo: {
    title: 'Company Information',
    fields: {
      firmName: {
        key: 'firmName',
        label: 'Firm Name',
        type: 'text',
        required: false,
        section: 'companyInfo'
      },
      numberOfOffices: {
        key: 'numberOfOffices',
        label: 'Number of Offices',
        type: 'number',
        required: false,
        section: 'companyInfo'
      },
      numberOfBranches: {
        key: 'numberOfBranches',
        label: 'Number of Branches',
        type: 'number',
        required: false,
        section: 'companyInfo'
      },
      totalEmployeeStrength: {
        key: 'totalEmployeeStrength',
        label: 'Total Employee Strength',
        type: 'number',
        required: false,
        section: 'companyInfo'
      }
    }
  },

  // Authorized Person Section
  authorizedPerson: {
    title: 'Authorized Person',
    fields: {
      authorizedPersonName: {
        key: 'authorizedPersonName',
        label: 'Authorized Person Name',
        type: 'text',
        required: false,
        section: 'authorizedPerson'
      },
      authorizedPersonEmail: {
        key: 'authorizedPersonEmail',
        label: 'Authorized Person Email',
        type: 'email',
        required: false,
        section: 'authorizedPerson'
      },
      designation: {
        key: 'designation',
        label: 'Designation',
        type: 'text',
        required: false,
        section: 'authorizedPerson'
      }
    }
  },

  // Marketing Information Section
  marketingInfo: {
    title: 'Marketing Information',
    fields: {
      marketingContact: {
        key: 'marketingContact',
        label: 'Marketing Contact',
        type: 'text',
        required: false,
        section: 'marketingInfo'
      },
      marketingDesignation: {
        key: 'marketingDesignation',
        label: 'Marketing Designation',
        type: 'text',
        required: false,
        section: 'marketingInfo'
      },
      placeOfPosting: {
        key: 'placeOfPosting',
        label: 'Place of Posting',
        type: 'text',
        required: false,
        section: 'marketingInfo'
      },
      department: {
        key: 'department',
        label: 'Department',
        type: 'text',
        required: false,
        section: 'marketingInfo'
      }
    }
  }
};

// Get all fields as a flat array
export const getAllPersonFields = () => {
  const allFields = [];
  Object.values(PersonFieldDefinitions).forEach(section => {
    Object.values(section.fields).forEach(field => {
      allFields.push(field);
    });
  });
  return allFields;
};

// Get required fields
export const getRequiredFields = () => {
  return getAllPersonFields().filter(field => field.required);
};

// Debug function to check for duplicate field definitions
export const checkForDuplicateFields = () => {
  const fieldsByKey = new Map();
  const duplicates = [];

  Object.entries(PersonFieldDefinitions).forEach(([sectionKey, section]) => {
    Object.entries(section.fields).forEach(([fieldName, field]) => {
      const key = field.key;

      if (fieldsByKey.has(key)) {
        duplicates.push({
          key,
          sections: [fieldsByKey.get(key).section, sectionKey],
          fieldName1: fieldsByKey.get(key).fieldName,
          fieldName2: fieldName
        });
      } else {
        fieldsByKey.set(key, { section: sectionKey, fieldName });
      }
    });
  });

  if (duplicates.length > 0) {
    console.error('DUPLICATE FIELD DEFINITIONS FOUND:');
    duplicates.forEach(dup => {
      console.error(`Field key "${dup.key}" is defined in multiple sections:`, dup.sections);
    });
  } else {
    console.log('No duplicate field definitions found.');
  }

  return duplicates;
};

// Default form data structure
export const getDefaultPersonData = () => ({
  divisionId: null,
  categoryId: null,
  firmNatureId: null,
  name: '',
  mobileNumber: '',
  nature: null,
  gender: null,
  alternateNumbers: [],
  primaryEmailId: null, // Changed from '' to null to avoid validation
  alternateEmailIds: [],
  website: null, // Changed from '' to null to avoid validation
  dateOfBirth: null,
  isMarried: false,
  dateOfMarriage: null,
  workingState: '',
  domesticState: '',
  district: '',
  address: '',
  workingArea: '',
  hasAssociate: false,
  associateName: '',
  associateRelation: '',
  associateMobile: '',
  usingWebsite: false,
  websiteLink: null, // Changed from '' to null to avoid validation
  usingCRMApp: false,
  crmAppLink: null, // Changed from '' to null to avoid validation
  transactionValue: null,
  reraRegistrationNumber: '',
  workingProfiles: [],
  starRating: null,
  source: '',
  remarks: '',
  firmName: '',
  numberOfOffices: null,
  numberOfBranches: null,
  totalEmployeeStrength: null,
  authorizedPersonName: '',
  authorizedPersonEmail: null, // Changed from '' to null to avoid validation
  designation: '',
  marketingContact: '',
  marketingDesignation: '',
  placeOfPosting: '',
  department: ''
});
