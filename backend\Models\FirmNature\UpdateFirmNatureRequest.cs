using System.ComponentModel.DataAnnotations;

namespace CrmApi.Models.FirmNature
{
    public class UpdateFirmNatureRequest
    {
        [Required(ErrorMessage = "Firm nature name is required")]
        [StringLength(255, ErrorMessage = "Firm nature name cannot exceed 255 characters")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Category ID is required")]
        [Range(1, int.MaxValue, ErrorMessage = "Category ID must be a positive number")]
        public int CategoryId { get; set; }
    }
}
