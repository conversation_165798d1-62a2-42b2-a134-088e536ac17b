{"ast": null, "code": "import React,{useState,useEffect}from'react';import formConfigService from'../../services/formConfigService';import apiService from'../../services/apiService';import'./AllFormsModal.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AllFormsModal=_ref=>{let{onClose,onEditForm,onDeleteForm,isInline=false}=_ref;const[forms,setForms]=useState([]);const[divisions,setDivisions]=useState([]);const[categories,setCategories]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[deleteConfirm,setDeleteConfirm]=useState(null);useEffect(()=>{loadData();},[]);const loadData=async()=>{setLoading(true);setError(null);try{// Load divisions and categories\nconst[divisionsResponse,categoriesResponse]=await Promise.all([apiService.getDivisions(),apiService.getCategories()]);setDivisions(divisionsResponse.data||[]);setCategories(categoriesResponse.data||[]);// Load all forms from localStorage\nconst allForms=formConfigService.getAllFormConfigs();// Enrich forms with division and category information\nconst enrichedForms=allForms.map(form=>{var _form$hierarchy,_form$hierarchy2,_form$hierarchy3,_form$hierarchy4;let divisionName='General';let categoryName='Default';if((_form$hierarchy=form.hierarchy)!==null&&_form$hierarchy!==void 0&&_form$hierarchy.divisionId){var _divisionsResponse$da;const division=(_divisionsResponse$da=divisionsResponse.data)===null||_divisionsResponse$da===void 0?void 0:_divisionsResponse$da.find(d=>d.id===form.hierarchy.divisionId);if(division){divisionName=division.name;}}if((_form$hierarchy2=form.hierarchy)!==null&&_form$hierarchy2!==void 0&&_form$hierarchy2.categoryId){var _categoriesResponse$d;const category=(_categoriesResponse$d=categoriesResponse.data)===null||_categoriesResponse$d===void 0?void 0:_categoriesResponse$d.find(c=>c.id===form.hierarchy.categoryId);if(category){categoryName=category.name;}}return{...form,divisionName,categoryName,divisionId:((_form$hierarchy3=form.hierarchy)===null||_form$hierarchy3===void 0?void 0:_form$hierarchy3.divisionId)||null,categoryId:((_form$hierarchy4=form.hierarchy)===null||_form$hierarchy4===void 0?void 0:_form$hierarchy4.categoryId)||null};});setForms(enrichedForms);}catch(err){console.error('Error loading forms data:',err);setError('Failed to load forms data. Please try again.');}finally{setLoading(false);}};const handleDeleteForm=form=>{setDeleteConfirm(form);};const confirmDelete=()=>{if(deleteConfirm){try{formConfigService.deleteFormConfig(deleteConfirm.type,deleteConfirm.associatedId);setForms(forms.filter(f=>f.id!==deleteConfirm.id));setDeleteConfirm(null);if(onDeleteForm){onDeleteForm(deleteConfirm);}}catch(err){console.error('Error deleting form:',err);setError('Failed to delete form. Please try again.');}}};const cancelDelete=()=>{setDeleteConfirm(null);};const handleEditForm=form=>{if(onEditForm){onEditForm(form);}// Don't call onClose() here - let the parent component handle navigation\n// onClose() was causing the component to return to person list instead of form builder\n};// Group forms by division and then by category\nconst groupedForms=forms.reduce((acc,form)=>{const divisionKey=form.divisionId||'general';const categoryKey=form.categoryId||'default';if(!acc[divisionKey]){acc[divisionKey]={name:form.divisionName,categories:{}};}if(!acc[divisionKey].categories[categoryKey]){acc[divisionKey].categories[categoryKey]={name:form.categoryName,forms:[]};}acc[divisionKey].categories[categoryKey].forms.push(form);return acc;},{});const formatDate=dateString=>{if(!dateString)return'N/A';return new Date(dateString).toLocaleDateString();};if(loading){const content=/*#__PURE__*/_jsxs(\"div\",{className:\"modal-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"All Forms\"}),/*#__PURE__*/_jsx(\"button\",{onClick:onClose,className:\"close-button\",children:\"\\xD7\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"modal-body\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"loading-state\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Loading forms...\"})]})})]});return isInline?/*#__PURE__*/_jsx(\"div\",{className:\"all-forms-inline\",children:content}):/*#__PURE__*/_jsx(\"div\",{className:\"all-forms-modal\",children:content});}const content=/*#__PURE__*/_jsxs(\"div\",{className:\"modal-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"All Forms\"}),/*#__PURE__*/_jsx(\"button\",{onClick:onClose,className:\"close-button\",children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-body\",children:[error&&/*#__PURE__*/_jsxs(\"div\",{className:\"error-message\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"error-icon\",children:\"\\u26A0\\uFE0F\"}),/*#__PURE__*/_jsx(\"span\",{children:error})]}),forms.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"empty-state\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"empty-icon\",children:\"\\uD83D\\uDCDD\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"No Forms Found\"}),/*#__PURE__*/_jsx(\"p\",{children:\"No custom forms have been created yet. Use the Form Builder to create your first form.\"})]}):/*#__PURE__*/_jsx(\"div\",{className:\"forms-container\",children:Object.entries(groupedForms).map(_ref2=>{let[divisionKey,division]=_ref2;return/*#__PURE__*/_jsxs(\"div\",{className:\"division-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"division-header\",children:[/*#__PURE__*/_jsxs(\"h3\",{children:[\"\\uD83C\\uDFE2 \",division.name]}),/*#__PURE__*/_jsxs(\"span\",{className:\"forms-count\",children:[Object.values(division.categories).reduce((total,cat)=>total+cat.forms.length,0),\" form(s)\"]})]}),Object.entries(division.categories).map(_ref3=>{let[categoryKey,category]=_ref3;return/*#__PURE__*/_jsxs(\"div\",{className:\"category-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"category-header\",children:[/*#__PURE__*/_jsxs(\"h4\",{children:[\"\\uD83D\\uDCC2 \",category.name]}),/*#__PURE__*/_jsxs(\"span\",{className:\"category-forms-count\",children:[category.forms.length,\" form(s)\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"forms-list\",children:category.forms.map(form=>{var _form$fields;return/*#__PURE__*/_jsxs(\"div\",{className:\"form-list-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-info\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-name-section\",children:[/*#__PURE__*/_jsx(\"h5\",{className:\"form-name\",children:form.name}),/*#__PURE__*/_jsx(\"span\",{className:`type-badge ${form.type}`,children:form.type==='category'?'Category':form.type==='subcategory'?'SubCategory':'Custom'})]}),/*#__PURE__*/_jsx(\"p\",{className:\"form-description\",children:form.description||'No description'}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-meta\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"meta-item\",children:[\"\\uD83D\\uDCDD \",((_form$fields=form.fields)===null||_form$fields===void 0?void 0:_form$fields.length)||0,\" fields\"]}),/*#__PURE__*/_jsxs(\"span\",{className:\"meta-item\",children:[\"\\uD83D\\uDCC5 \",formatDate(form.updatedAt)]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-actions\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEditForm(form),className:\"btn-action edit\",title:\"Edit form\",children:\"\\u270F\\uFE0F Edit\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteForm(form),className:\"btn-action delete\",title:\"Delete form\",children:\"\\uD83D\\uDDD1\\uFE0F Delete\"})]})]},form.id);})})]},categoryKey);})]},divisionKey);})})]}),deleteConfirm&&/*#__PURE__*/_jsx(\"div\",{className:\"delete-confirm-modal\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"delete-confirm-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"delete-confirm-header\",children:/*#__PURE__*/_jsx(\"h3\",{children:\"Confirm Delete\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"delete-confirm-body\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"Are you sure you want to delete the form \",/*#__PURE__*/_jsxs(\"strong\",{children:[\"\\\"\",deleteConfirm.name,\"\\\"\"]}),\"?\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"warning-text\",children:\"This action cannot be undone.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"delete-confirm-actions\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:cancelDelete,className:\"btn-cancel\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{onClick:confirmDelete,className:\"btn-delete\",children:\"Delete\"})]})]})})]});return isInline?/*#__PURE__*/_jsx(\"div\",{className:\"all-forms-inline\",children:content}):/*#__PURE__*/_jsx(\"div\",{className:\"all-forms-modal\",children:content});};export default AllFormsModal;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "formConfigService", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "AllFormsModal", "_ref", "onClose", "onEditForm", "onDeleteForm", "isInline", "forms", "setForms", "divisions", "setDivisions", "categories", "setCategories", "loading", "setLoading", "error", "setError", "deleteConfirm", "setDeleteConfirm", "loadData", "divisionsResponse", "categoriesResponse", "Promise", "all", "getDivisions", "getCategories", "data", "allForms", "getAllFormConfigs", "enrichedForms", "map", "form", "_form$hierarchy", "_form$hierarchy2", "_form$hierarchy3", "_form$hierarchy4", "divisionName", "categoryName", "hierarchy", "divisionId", "_divisionsResponse$da", "division", "find", "d", "id", "name", "categoryId", "_categoriesResponse$d", "category", "c", "err", "console", "handleDeleteForm", "confirmDelete", "deleteFormConfig", "type", "associatedId", "filter", "f", "cancelDelete", "handleEditForm", "groupedForms", "reduce", "acc", "divisionKey", "categoryKey", "push", "formatDate", "dateString", "Date", "toLocaleDateString", "content", "className", "children", "onClick", "length", "Object", "entries", "_ref2", "values", "total", "cat", "_ref3", "_form$fields", "description", "fields", "updatedAt", "title"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/AllFormsModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport './AllFormsModal.css';\n\nconst AllFormsModal = ({ onClose, onEditForm, onDeleteForm, isInline = false }) => {\n  const [forms, setForms] = useState([]);\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [deleteConfirm, setDeleteConfirm] = useState(null);\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      // Load divisions and categories\n      const [divisionsResponse, categoriesResponse] = await Promise.all([\n        apiService.getDivisions(),\n        apiService.getCategories()\n      ]);\n\n      setDivisions(divisionsResponse.data || []);\n      setCategories(categoriesResponse.data || []);\n\n      // Load all forms from localStorage\n      const allForms = formConfigService.getAllFormConfigs();\n\n      // Enrich forms with division and category information\n      const enrichedForms = allForms.map(form => {\n        let divisionName = 'General';\n        let categoryName = 'Default';\n\n        if (form.hierarchy?.divisionId) {\n          const division = divisionsResponse.data?.find(d => d.id === form.hierarchy.divisionId);\n          if (division) {\n            divisionName = division.name;\n          }\n        }\n\n        if (form.hierarchy?.categoryId) {\n          const category = categoriesResponse.data?.find(c => c.id === form.hierarchy.categoryId);\n          if (category) {\n            categoryName = category.name;\n          }\n        }\n\n        return {\n          ...form,\n          divisionName,\n          categoryName,\n          divisionId: form.hierarchy?.divisionId || null,\n          categoryId: form.hierarchy?.categoryId || null\n        };\n      });\n\n      setForms(enrichedForms);\n    } catch (err) {\n      console.error('Error loading forms data:', err);\n      setError('Failed to load forms data. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteForm = (form) => {\n    setDeleteConfirm(form);\n  };\n\n  const confirmDelete = () => {\n    if (deleteConfirm) {\n      try {\n        formConfigService.deleteFormConfig(deleteConfirm.type, deleteConfirm.associatedId);\n        setForms(forms.filter(f => f.id !== deleteConfirm.id));\n        setDeleteConfirm(null);\n        if (onDeleteForm) {\n          onDeleteForm(deleteConfirm);\n        }\n      } catch (err) {\n        console.error('Error deleting form:', err);\n        setError('Failed to delete form. Please try again.');\n      }\n    }\n  };\n\n  const cancelDelete = () => {\n    setDeleteConfirm(null);\n  };\n\n  const handleEditForm = (form) => {\n    if (onEditForm) {\n      onEditForm(form);\n    }\n    // Don't call onClose() here - let the parent component handle navigation\n    // onClose() was causing the component to return to person list instead of form builder\n  };\n\n  // Group forms by division and then by category\n  const groupedForms = forms.reduce((acc, form) => {\n    const divisionKey = form.divisionId || 'general';\n    const categoryKey = form.categoryId || 'default';\n    \n    if (!acc[divisionKey]) {\n      acc[divisionKey] = {\n        name: form.divisionName,\n        categories: {}\n      };\n    }\n    \n    if (!acc[divisionKey].categories[categoryKey]) {\n      acc[divisionKey].categories[categoryKey] = {\n        name: form.categoryName,\n        forms: []\n      };\n    }\n    \n    acc[divisionKey].categories[categoryKey].forms.push(form);\n    \n    return acc;\n  }, {});\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  if (loading) {\n    const content = (\n      <div className=\"modal-content\">\n        <div className=\"modal-header\">\n          <h2>All Forms</h2>\n          <button onClick={onClose} className=\"close-button\">×</button>\n        </div>\n        <div className=\"modal-body\">\n          <div className=\"loading-state\">\n            <div className=\"spinner\"></div>\n            <p>Loading forms...</p>\n          </div>\n        </div>\n      </div>\n    );\n\n    return isInline ? (\n      <div className=\"all-forms-inline\">\n        {content}\n      </div>\n    ) : (\n      <div className=\"all-forms-modal\">\n        {content}\n      </div>\n    );\n  }\n\n  const content = (\n    <div className=\"modal-content\">\n      <div className=\"modal-header\">\n        <h2>All Forms</h2>\n        <button onClick={onClose} className=\"close-button\">×</button>\n      </div>\n\n      <div className=\"modal-body\">\n        {error && (\n          <div className=\"error-message\">\n            <span className=\"error-icon\">⚠️</span>\n            <span>{error}</span>\n          </div>\n        )}\n\n        {forms.length === 0 ? (\n          <div className=\"empty-state\">\n            <div className=\"empty-icon\">📝</div>\n            <h3>No Forms Found</h3>\n            <p>No custom forms have been created yet. Use the Form Builder to create your first form.</p>\n          </div>\n        ) : (\n          <div className=\"forms-container\">\n            {Object.entries(groupedForms).map(([divisionKey, division]) => (\n              <div key={divisionKey} className=\"division-section\">\n                <div className=\"division-header\">\n                  <h3>🏢 {division.name}</h3>\n                  <span className=\"forms-count\">\n                    {Object.values(division.categories).reduce((total, cat) => total + cat.forms.length, 0)} form(s)\n                  </span>\n                </div>\n\n                {Object.entries(division.categories).map(([categoryKey, category]) => (\n                  <div key={categoryKey} className=\"category-section\">\n                    <div className=\"category-header\">\n                      <h4>📂 {category.name}</h4>\n                      <span className=\"category-forms-count\">{category.forms.length} form(s)</span>\n                    </div>\n\n                    <div className=\"forms-list\">\n                      {category.forms.map(form => (\n                        <div key={form.id} className=\"form-list-item\">\n                          <div className=\"form-info\">\n                            <div className=\"form-name-section\">\n                              <h5 className=\"form-name\">{form.name}</h5>\n                              <span className={`type-badge ${form.type}`}>\n                                {form.type === 'category' ? 'Category' :\n                                 form.type === 'subcategory' ? 'SubCategory' :\n                                 'Custom'}\n                              </span>\n                            </div>\n                            <p className=\"form-description\">{form.description || 'No description'}</p>\n                            <div className=\"form-meta\">\n                              <span className=\"meta-item\">📝 {form.fields?.length || 0} fields</span>\n                              <span className=\"meta-item\">📅 {formatDate(form.updatedAt)}</span>\n                            </div>\n                          </div>\n                          <div className=\"form-actions\">\n                            <button\n                              onClick={() => handleEditForm(form)}\n                              className=\"btn-action edit\"\n                              title=\"Edit form\"\n                            >\n                              ✏️ Edit\n                            </button>\n                            <button\n                              onClick={() => handleDeleteForm(form)}\n                              className=\"btn-action delete\"\n                              title=\"Delete form\"\n                            >\n                              🗑️ Delete\n                            </button>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Delete Confirmation Modal */}\n      {deleteConfirm && (\n        <div className=\"delete-confirm-modal\">\n          <div className=\"delete-confirm-content\">\n            <div className=\"delete-confirm-header\">\n              <h3>Confirm Delete</h3>\n            </div>\n            <div className=\"delete-confirm-body\">\n              <p>Are you sure you want to delete the form <strong>\"{deleteConfirm.name}\"</strong>?</p>\n              <p className=\"warning-text\">This action cannot be undone.</p>\n            </div>\n            <div className=\"delete-confirm-actions\">\n              <button onClick={cancelDelete} className=\"btn-cancel\">Cancel</button>\n              <button onClick={confirmDelete} className=\"btn-delete\">Delete</button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  return isInline ? (\n    <div className=\"all-forms-inline\">\n      {content}\n    </div>\n  ) : (\n    <div className=\"all-forms-modal\">\n      {content}\n    </div>\n  );\n};\n\nexport default AllFormsModal;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,iBAAiB,KAAM,kCAAkC,CAChE,MAAO,CAAAC,UAAU,KAAM,2BAA2B,CAClD,MAAO,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE7B,KAAM,CAAAC,aAAa,CAAGC,IAAA,EAA6D,IAA5D,CAAEC,OAAO,CAAEC,UAAU,CAAEC,YAAY,CAAEC,QAAQ,CAAG,KAAM,CAAC,CAAAJ,IAAA,CAC5E,KAAM,CAACK,KAAK,CAAEC,QAAQ,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACgB,SAAS,CAAEC,YAAY,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACkB,UAAU,CAAEC,aAAa,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACoB,OAAO,CAAEC,UAAU,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACsB,KAAK,CAAEC,QAAQ,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACwB,aAAa,CAAEC,gBAAgB,CAAC,CAAGzB,QAAQ,CAAC,IAAI,CAAC,CAExDC,SAAS,CAAC,IAAM,CACdyB,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3BL,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACF;AACA,KAAM,CAACI,iBAAiB,CAAEC,kBAAkB,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CAChE3B,UAAU,CAAC4B,YAAY,CAAC,CAAC,CACzB5B,UAAU,CAAC6B,aAAa,CAAC,CAAC,CAC3B,CAAC,CAEFf,YAAY,CAACU,iBAAiB,CAACM,IAAI,EAAI,EAAE,CAAC,CAC1Cd,aAAa,CAACS,kBAAkB,CAACK,IAAI,EAAI,EAAE,CAAC,CAE5C;AACA,KAAM,CAAAC,QAAQ,CAAGhC,iBAAiB,CAACiC,iBAAiB,CAAC,CAAC,CAEtD;AACA,KAAM,CAAAC,aAAa,CAAGF,QAAQ,CAACG,GAAG,CAACC,IAAI,EAAI,KAAAC,eAAA,CAAAC,gBAAA,CAAAC,gBAAA,CAAAC,gBAAA,CACzC,GAAI,CAAAC,YAAY,CAAG,SAAS,CAC5B,GAAI,CAAAC,YAAY,CAAG,SAAS,CAE5B,IAAAL,eAAA,CAAID,IAAI,CAACO,SAAS,UAAAN,eAAA,WAAdA,eAAA,CAAgBO,UAAU,CAAE,KAAAC,qBAAA,CAC9B,KAAM,CAAAC,QAAQ,EAAAD,qBAAA,CAAGpB,iBAAiB,CAACM,IAAI,UAAAc,qBAAA,iBAAtBA,qBAAA,CAAwBE,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,EAAE,GAAKb,IAAI,CAACO,SAAS,CAACC,UAAU,CAAC,CACtF,GAAIE,QAAQ,CAAE,CACZL,YAAY,CAAGK,QAAQ,CAACI,IAAI,CAC9B,CACF,CAEA,IAAAZ,gBAAA,CAAIF,IAAI,CAACO,SAAS,UAAAL,gBAAA,WAAdA,gBAAA,CAAgBa,UAAU,CAAE,KAAAC,qBAAA,CAC9B,KAAM,CAAAC,QAAQ,EAAAD,qBAAA,CAAG1B,kBAAkB,CAACK,IAAI,UAAAqB,qBAAA,iBAAvBA,qBAAA,CAAyBL,IAAI,CAACO,CAAC,EAAIA,CAAC,CAACL,EAAE,GAAKb,IAAI,CAACO,SAAS,CAACQ,UAAU,CAAC,CACvF,GAAIE,QAAQ,CAAE,CACZX,YAAY,CAAGW,QAAQ,CAACH,IAAI,CAC9B,CACF,CAEA,MAAO,CACL,GAAGd,IAAI,CACPK,YAAY,CACZC,YAAY,CACZE,UAAU,CAAE,EAAAL,gBAAA,CAAAH,IAAI,CAACO,SAAS,UAAAJ,gBAAA,iBAAdA,gBAAA,CAAgBK,UAAU,GAAI,IAAI,CAC9CO,UAAU,CAAE,EAAAX,gBAAA,CAAAJ,IAAI,CAACO,SAAS,UAAAH,gBAAA,iBAAdA,gBAAA,CAAgBW,UAAU,GAAI,IAC5C,CAAC,CACH,CAAC,CAAC,CAEFtC,QAAQ,CAACqB,aAAa,CAAC,CACzB,CAAE,MAAOqB,GAAG,CAAE,CACZC,OAAO,CAACpC,KAAK,CAAC,2BAA2B,CAAEmC,GAAG,CAAC,CAC/ClC,QAAQ,CAAC,8CAA8C,CAAC,CAC1D,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAsC,gBAAgB,CAAIrB,IAAI,EAAK,CACjCb,gBAAgB,CAACa,IAAI,CAAC,CACxB,CAAC,CAED,KAAM,CAAAsB,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAIpC,aAAa,CAAE,CACjB,GAAI,CACFtB,iBAAiB,CAAC2D,gBAAgB,CAACrC,aAAa,CAACsC,IAAI,CAAEtC,aAAa,CAACuC,YAAY,CAAC,CAClFhD,QAAQ,CAACD,KAAK,CAACkD,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACd,EAAE,GAAK3B,aAAa,CAAC2B,EAAE,CAAC,CAAC,CACtD1B,gBAAgB,CAAC,IAAI,CAAC,CACtB,GAAIb,YAAY,CAAE,CAChBA,YAAY,CAACY,aAAa,CAAC,CAC7B,CACF,CAAE,MAAOiC,GAAG,CAAE,CACZC,OAAO,CAACpC,KAAK,CAAC,sBAAsB,CAAEmC,GAAG,CAAC,CAC1ClC,QAAQ,CAAC,0CAA0C,CAAC,CACtD,CACF,CACF,CAAC,CAED,KAAM,CAAA2C,YAAY,CAAGA,CAAA,GAAM,CACzBzC,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED,KAAM,CAAA0C,cAAc,CAAI7B,IAAI,EAAK,CAC/B,GAAI3B,UAAU,CAAE,CACdA,UAAU,CAAC2B,IAAI,CAAC,CAClB,CACA;AACA;AACF,CAAC,CAED;AACA,KAAM,CAAA8B,YAAY,CAAGtD,KAAK,CAACuD,MAAM,CAAC,CAACC,GAAG,CAAEhC,IAAI,GAAK,CAC/C,KAAM,CAAAiC,WAAW,CAAGjC,IAAI,CAACQ,UAAU,EAAI,SAAS,CAChD,KAAM,CAAA0B,WAAW,CAAGlC,IAAI,CAACe,UAAU,EAAI,SAAS,CAEhD,GAAI,CAACiB,GAAG,CAACC,WAAW,CAAC,CAAE,CACrBD,GAAG,CAACC,WAAW,CAAC,CAAG,CACjBnB,IAAI,CAAEd,IAAI,CAACK,YAAY,CACvBzB,UAAU,CAAE,CAAC,CACf,CAAC,CACH,CAEA,GAAI,CAACoD,GAAG,CAACC,WAAW,CAAC,CAACrD,UAAU,CAACsD,WAAW,CAAC,CAAE,CAC7CF,GAAG,CAACC,WAAW,CAAC,CAACrD,UAAU,CAACsD,WAAW,CAAC,CAAG,CACzCpB,IAAI,CAAEd,IAAI,CAACM,YAAY,CACvB9B,KAAK,CAAE,EACT,CAAC,CACH,CAEAwD,GAAG,CAACC,WAAW,CAAC,CAACrD,UAAU,CAACsD,WAAW,CAAC,CAAC1D,KAAK,CAAC2D,IAAI,CAACnC,IAAI,CAAC,CAEzD,MAAO,CAAAgC,GAAG,CACZ,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN,KAAM,CAAAI,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAI,CAACA,UAAU,CAAE,MAAO,KAAK,CAC7B,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CAClD,CAAC,CAED,GAAIzD,OAAO,CAAE,CACX,KAAM,CAAA0D,OAAO,cACXvE,KAAA,QAAKwE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BzE,KAAA,QAAKwE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B3E,IAAA,OAAA2E,QAAA,CAAI,WAAS,CAAI,CAAC,cAClB3E,IAAA,WAAQ4E,OAAO,CAAEvE,OAAQ,CAACqE,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,MAAC,CAAQ,CAAC,EAC1D,CAAC,cACN3E,IAAA,QAAK0E,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBzE,KAAA,QAAKwE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B3E,IAAA,QAAK0E,SAAS,CAAC,SAAS,CAAM,CAAC,cAC/B1E,IAAA,MAAA2E,QAAA,CAAG,kBAAgB,CAAG,CAAC,EACpB,CAAC,CACH,CAAC,EACH,CACN,CAED,MAAO,CAAAnE,QAAQ,cACbR,IAAA,QAAK0E,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9BF,OAAO,CACL,CAAC,cAENzE,IAAA,QAAK0E,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7BF,OAAO,CACL,CACN,CACH,CAEA,KAAM,CAAAA,OAAO,cACXvE,KAAA,QAAKwE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BzE,KAAA,QAAKwE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B3E,IAAA,OAAA2E,QAAA,CAAI,WAAS,CAAI,CAAC,cAClB3E,IAAA,WAAQ4E,OAAO,CAAEvE,OAAQ,CAACqE,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,MAAC,CAAQ,CAAC,EAC1D,CAAC,cAENzE,KAAA,QAAKwE,SAAS,CAAC,YAAY,CAAAC,QAAA,EACxB1D,KAAK,eACJf,KAAA,QAAKwE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B3E,IAAA,SAAM0E,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cACtC3E,IAAA,SAAA2E,QAAA,CAAO1D,KAAK,CAAO,CAAC,EACjB,CACN,CAEAR,KAAK,CAACoE,MAAM,GAAK,CAAC,cACjB3E,KAAA,QAAKwE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3E,IAAA,QAAK0E,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACpC3E,IAAA,OAAA2E,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvB3E,IAAA,MAAA2E,QAAA,CAAG,wFAAsF,CAAG,CAAC,EAC1F,CAAC,cAEN3E,IAAA,QAAK0E,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7BG,MAAM,CAACC,OAAO,CAAChB,YAAY,CAAC,CAAC/B,GAAG,CAACgD,KAAA,MAAC,CAACd,WAAW,CAAEvB,QAAQ,CAAC,CAAAqC,KAAA,oBACxD9E,KAAA,QAAuBwE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eACjDzE,KAAA,QAAKwE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BzE,KAAA,OAAAyE,QAAA,EAAI,eAAG,CAAChC,QAAQ,CAACI,IAAI,EAAK,CAAC,cAC3B7C,KAAA,SAAMwE,SAAS,CAAC,aAAa,CAAAC,QAAA,EAC1BG,MAAM,CAACG,MAAM,CAACtC,QAAQ,CAAC9B,UAAU,CAAC,CAACmD,MAAM,CAAC,CAACkB,KAAK,CAAEC,GAAG,GAAKD,KAAK,CAAGC,GAAG,CAAC1E,KAAK,CAACoE,MAAM,CAAE,CAAC,CAAC,CAAC,UAC1F,EAAM,CAAC,EACJ,CAAC,CAELC,MAAM,CAACC,OAAO,CAACpC,QAAQ,CAAC9B,UAAU,CAAC,CAACmB,GAAG,CAACoD,KAAA,MAAC,CAACjB,WAAW,CAAEjB,QAAQ,CAAC,CAAAkC,KAAA,oBAC/DlF,KAAA,QAAuBwE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eACjDzE,KAAA,QAAKwE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BzE,KAAA,OAAAyE,QAAA,EAAI,eAAG,CAACzB,QAAQ,CAACH,IAAI,EAAK,CAAC,cAC3B7C,KAAA,SAAMwE,SAAS,CAAC,sBAAsB,CAAAC,QAAA,EAAEzB,QAAQ,CAACzC,KAAK,CAACoE,MAAM,CAAC,UAAQ,EAAM,CAAC,EAC1E,CAAC,cAEN7E,IAAA,QAAK0E,SAAS,CAAC,YAAY,CAAAC,QAAA,CACxBzB,QAAQ,CAACzC,KAAK,CAACuB,GAAG,CAACC,IAAI,OAAAoD,YAAA,oBACtBnF,KAAA,QAAmBwE,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC3CzE,KAAA,QAAKwE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBzE,KAAA,QAAKwE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC3E,IAAA,OAAI0E,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAE1C,IAAI,CAACc,IAAI,CAAK,CAAC,cAC1C/C,IAAA,SAAM0E,SAAS,CAAE,cAAczC,IAAI,CAACwB,IAAI,EAAG,CAAAkB,QAAA,CACxC1C,IAAI,CAACwB,IAAI,GAAK,UAAU,CAAG,UAAU,CACrCxB,IAAI,CAACwB,IAAI,GAAK,aAAa,CAAG,aAAa,CAC3C,QAAQ,CACL,CAAC,EACJ,CAAC,cACNzD,IAAA,MAAG0E,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAE1C,IAAI,CAACqD,WAAW,EAAI,gBAAgB,CAAI,CAAC,cAC1EpF,KAAA,QAAKwE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBzE,KAAA,SAAMwE,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,eAAG,CAAC,EAAAU,YAAA,CAAApD,IAAI,CAACsD,MAAM,UAAAF,YAAA,iBAAXA,YAAA,CAAaR,MAAM,GAAI,CAAC,CAAC,SAAO,EAAM,CAAC,cACvE3E,KAAA,SAAMwE,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,eAAG,CAACN,UAAU,CAACpC,IAAI,CAACuD,SAAS,CAAC,EAAO,CAAC,EAC/D,CAAC,EACH,CAAC,cACNtF,KAAA,QAAKwE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B3E,IAAA,WACE4E,OAAO,CAAEA,CAAA,GAAMd,cAAc,CAAC7B,IAAI,CAAE,CACpCyC,SAAS,CAAC,iBAAiB,CAC3Be,KAAK,CAAC,WAAW,CAAAd,QAAA,CAClB,mBAED,CAAQ,CAAC,cACT3E,IAAA,WACE4E,OAAO,CAAEA,CAAA,GAAMtB,gBAAgB,CAACrB,IAAI,CAAE,CACtCyC,SAAS,CAAC,mBAAmB,CAC7Be,KAAK,CAAC,aAAa,CAAAd,QAAA,CACpB,2BAED,CAAQ,CAAC,EACN,CAAC,GA/BE1C,IAAI,CAACa,EAgCV,CAAC,EACP,CAAC,CACC,CAAC,GA1CEqB,WA2CL,CAAC,EACP,CAAC,GArDMD,WAsDL,CAAC,EACP,CAAC,CACC,CACN,EACE,CAAC,CAGL/C,aAAa,eACZnB,IAAA,QAAK0E,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnCzE,KAAA,QAAKwE,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC3E,IAAA,QAAK0E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpC3E,IAAA,OAAA2E,QAAA,CAAI,gBAAc,CAAI,CAAC,CACpB,CAAC,cACNzE,KAAA,QAAKwE,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCzE,KAAA,MAAAyE,QAAA,EAAG,2CAAyC,cAAAzE,KAAA,WAAAyE,QAAA,EAAQ,IAAC,CAACxD,aAAa,CAAC4B,IAAI,CAAC,IAAC,EAAQ,CAAC,IAAC,EAAG,CAAC,cACxF/C,IAAA,MAAG0E,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,+BAA6B,CAAG,CAAC,EAC1D,CAAC,cACNzE,KAAA,QAAKwE,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC3E,IAAA,WAAQ4E,OAAO,CAAEf,YAAa,CAACa,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAM,CAAQ,CAAC,cACrE3E,IAAA,WAAQ4E,OAAO,CAAErB,aAAc,CAACmB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAM,CAAQ,CAAC,EACnE,CAAC,EACH,CAAC,CACH,CACN,EACE,CACN,CAED,MAAO,CAAAnE,QAAQ,cACbR,IAAA,QAAK0E,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9BF,OAAO,CACL,CAAC,cAENzE,IAAA,QAAK0E,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7BF,OAAO,CACL,CACN,CACH,CAAC,CAED,cAAe,CAAAtE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}