{"ast": null, "code": "import React,{useState,useEffect}from'react';import axios from'axios';import Pagination from'./Pagination';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const API_BASE_URL='http://localhost:5000/api';const DivisionSetup=()=>{const[divisions,setDivisions]=useState([]);const[newDivision,setNewDivision]=useState('');const[loading,setLoading]=useState(false);const[currentPage,setCurrentPage]=useState(1);const[itemsPerPage]=useState(10);useEffect(()=>{fetchDivisions();},[]);const fetchDivisions=async()=>{try{const response=await axios.get(`${API_BASE_URL}/divisions`);setDivisions(response.data);}catch(error){console.error('Error fetching divisions:',error);}};const handleSubmit=async e=>{e.preventDefault();if(!newDivision.trim())return;setLoading(true);try{await axios.post(`${API_BASE_URL}/divisions`,{name:newDivision});setNewDivision('');fetchDivisions();}catch(error){console.error('Error creating division:',error);alert('Error creating division. Please try again.');}finally{setLoading(false);}};const handleDelete=async id=>{if(window.confirm('Are you sure you want to delete this division?')){try{await axios.delete(`${API_BASE_URL}/divisions/${id}`);fetchDivisions();}catch(error){console.error('Error deleting division:',error);alert('Error deleting division. Please try again.');}}};// Calculate pagination\nconst totalItems=divisions.length;const startIndex=(currentPage-1)*itemsPerPage;const endIndex=startIndex+itemsPerPage;const currentItems=divisions.slice(startIndex,endIndex);const handlePageChange=page=>{setCurrentPage(page);};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Division Setup\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Add New Division\"}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"divisionName\",children:\"Division Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"divisionName\",className:\"form-control\",value:newDivision,onChange:e=>setNewDivision(e.target.value),placeholder:\"Enter division name\",required:true})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"btn btn-primary\",disabled:loading,children:loading?'Adding...':'Add Division'})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Existing Divisions\"}),divisions.length===0?/*#__PURE__*/_jsx(\"p\",{children:\"No divisions found. Add your first division above.\"}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"table\",{className:\"table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Name\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Created At\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:currentItems.map(division=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:division.id}),/*#__PURE__*/_jsx(\"td\",{children:division.name}),/*#__PURE__*/_jsx(\"td\",{children:new Date(division.createdAt).toLocaleDateString()}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"button\",{className:\"btn btn-danger btn-sm\",onClick:()=>handleDelete(division.id),children:\"Delete\"})})]},division.id))})]}),/*#__PURE__*/_jsx(Pagination,{currentPage:currentPage,totalItems:totalItems,itemsPerPage:itemsPerPage,onPageChange:handlePageChange})]})]})]});};export default DivisionSetup;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Pagination", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "API_BASE_URL", "DivisionSetup", "divisions", "setDivisions", "newDivision", "setNewDivision", "loading", "setLoading", "currentPage", "setCurrentPage", "itemsPerPage", "fetchDivisions", "response", "get", "data", "error", "console", "handleSubmit", "e", "preventDefault", "trim", "post", "name", "alert", "handleDelete", "id", "window", "confirm", "delete", "totalItems", "length", "startIndex", "endIndex", "currentItems", "slice", "handlePageChange", "page", "children", "className", "onSubmit", "htmlFor", "type", "value", "onChange", "target", "placeholder", "required", "disabled", "map", "division", "Date", "createdAt", "toLocaleDateString", "onClick", "onPageChange"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/DivisionSetup.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport Pagination from './Pagination';\r\n\r\nconst API_BASE_URL = 'http://localhost:5000/api';\r\n\r\nconst DivisionSetup = () => {\r\n  const [divisions, setDivisions] = useState([]);\r\n  const [newDivision, setNewDivision] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [itemsPerPage] = useState(10);\r\n\r\n  useEffect(() => {\r\n    fetchDivisions();\r\n  }, []);\r\n\r\n  const fetchDivisions = async () => {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/divisions`);\r\n      setDivisions(response.data);\r\n    } catch (error) {\r\n      console.error('Error fetching divisions:', error);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    if (!newDivision.trim()) return;\r\n\r\n    setLoading(true);\r\n    try {\r\n      await axios.post(`${API_BASE_URL}/divisions`, {\r\n        name: newDivision\r\n      });\r\n      setNewDivision('');\r\n      fetchDivisions();\r\n    } catch (error) {\r\n      console.error('Error creating division:', error);\r\n      alert('Error creating division. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (id) => {\r\n    if (window.confirm('Are you sure you want to delete this division?')) {\r\n      try {\r\n        await axios.delete(`${API_BASE_URL}/divisions/${id}`);\r\n        fetchDivisions();\r\n      } catch (error) {\r\n        console.error('Error deleting division:', error);\r\n        alert('Error deleting division. Please try again.');\r\n      }\r\n    }\r\n  };\r\n\r\n  // Calculate pagination\r\n  const totalItems = divisions.length;\r\n  const startIndex = (currentPage - 1) * itemsPerPage;\r\n  const endIndex = startIndex + itemsPerPage;\r\n  const currentItems = divisions.slice(startIndex, endIndex);\r\n\r\n  const handlePageChange = (page) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <h1>Division Setup</h1>\r\n      \r\n      <div className=\"card\">\r\n        <h3>Add New Division</h3>\r\n        <form onSubmit={handleSubmit}>\r\n          <div className=\"form-group\">\r\n            <label htmlFor=\"divisionName\">Division Name</label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"divisionName\"\r\n              className=\"form-control\"\r\n              value={newDivision}\r\n              onChange={(e) => setNewDivision(e.target.value)}\r\n              placeholder=\"Enter division name\"\r\n              required\r\n            />\r\n          </div>\r\n          <button type=\"submit\" className=\"btn btn-primary\" disabled={loading}>\r\n            {loading ? 'Adding...' : 'Add Division'}\r\n          </button>\r\n        </form>\r\n      </div>\r\n\r\n      <div className=\"card\">\r\n        <h3>Existing Divisions</h3>\r\n        {divisions.length === 0 ? (\r\n          <p>No divisions found. Add your first division above.</p>\r\n        ) : (\r\n          <>\r\n            <table className=\"table\">\r\n              <thead>\r\n                <tr>\r\n                  <th>ID</th>\r\n                  <th>Name</th>\r\n                  <th>Created At</th>\r\n                  <th>Actions</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {currentItems.map((division) => (\r\n                  <tr key={division.id}>\r\n                    <td>{division.id}</td>\r\n                    <td>{division.name}</td>\r\n                    <td>{new Date(division.createdAt).toLocaleDateString()}</td>\r\n                    <td>\r\n                      <button\r\n                        className=\"btn btn-danger btn-sm\"\r\n                        onClick={() => handleDelete(division.id)}\r\n                      >\r\n                        Delete\r\n                      </button>\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n            <Pagination\r\n              currentPage={currentPage}\r\n              totalItems={totalItems}\r\n              itemsPerPage={itemsPerPage}\r\n              onPageChange={handlePageChange}\r\n            />\r\n          </>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DivisionSetup;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,UAAU,KAAM,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEtC,KAAM,CAAAC,YAAY,CAAG,2BAA2B,CAEhD,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGb,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACc,WAAW,CAAEC,cAAc,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACgB,OAAO,CAAEC,UAAU,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACkB,WAAW,CAAEC,cAAc,CAAC,CAAGnB,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAACoB,YAAY,CAAC,CAAGpB,QAAQ,CAAC,EAAE,CAAC,CAEnCC,SAAS,CAAC,IAAM,CACdoB,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAApB,KAAK,CAACqB,GAAG,CAAC,GAAGb,YAAY,YAAY,CAAC,CAC7DG,YAAY,CAACS,QAAQ,CAACE,IAAI,CAAC,CAC7B,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACnD,CACF,CAAC,CAED,KAAM,CAAAE,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAChCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI,CAACf,WAAW,CAACgB,IAAI,CAAC,CAAC,CAAE,OAEzBb,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAf,KAAK,CAAC6B,IAAI,CAAC,GAAGrB,YAAY,YAAY,CAAE,CAC5CsB,IAAI,CAAElB,WACR,CAAC,CAAC,CACFC,cAAc,CAAC,EAAE,CAAC,CAClBM,cAAc,CAAC,CAAC,CAClB,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDQ,KAAK,CAAC,4CAA4C,CAAC,CACrD,CAAC,OAAS,CACRhB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAiB,YAAY,CAAG,KAAO,CAAAC,EAAE,EAAK,CACjC,GAAIC,MAAM,CAACC,OAAO,CAAC,gDAAgD,CAAC,CAAE,CACpE,GAAI,CACF,KAAM,CAAAnC,KAAK,CAACoC,MAAM,CAAC,GAAG5B,YAAY,cAAcyB,EAAE,EAAE,CAAC,CACrDd,cAAc,CAAC,CAAC,CAClB,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDQ,KAAK,CAAC,4CAA4C,CAAC,CACrD,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAM,UAAU,CAAG3B,SAAS,CAAC4B,MAAM,CACnC,KAAM,CAAAC,UAAU,CAAG,CAACvB,WAAW,CAAG,CAAC,EAAIE,YAAY,CACnD,KAAM,CAAAsB,QAAQ,CAAGD,UAAU,CAAGrB,YAAY,CAC1C,KAAM,CAAAuB,YAAY,CAAG/B,SAAS,CAACgC,KAAK,CAACH,UAAU,CAAEC,QAAQ,CAAC,CAE1D,KAAM,CAAAG,gBAAgB,CAAIC,IAAI,EAAK,CACjC3B,cAAc,CAAC2B,IAAI,CAAC,CACtB,CAAC,CAED,mBACEvC,KAAA,QAAAwC,QAAA,eACE1C,IAAA,OAAA0C,QAAA,CAAI,gBAAc,CAAI,CAAC,cAEvBxC,KAAA,QAAKyC,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnB1C,IAAA,OAAA0C,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzBxC,KAAA,SAAM0C,QAAQ,CAAEtB,YAAa,CAAAoB,QAAA,eAC3BxC,KAAA,QAAKyC,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzB1C,IAAA,UAAO6C,OAAO,CAAC,cAAc,CAAAH,QAAA,CAAC,eAAa,CAAO,CAAC,cACnD1C,IAAA,UACE8C,IAAI,CAAC,MAAM,CACXhB,EAAE,CAAC,cAAc,CACjBa,SAAS,CAAC,cAAc,CACxBI,KAAK,CAAEtC,WAAY,CACnBuC,QAAQ,CAAGzB,CAAC,EAAKb,cAAc,CAACa,CAAC,CAAC0B,MAAM,CAACF,KAAK,CAAE,CAChDG,WAAW,CAAC,qBAAqB,CACjCC,QAAQ,MACT,CAAC,EACC,CAAC,cACNnD,IAAA,WAAQ8C,IAAI,CAAC,QAAQ,CAACH,SAAS,CAAC,iBAAiB,CAACS,QAAQ,CAAEzC,OAAQ,CAAA+B,QAAA,CACjE/B,OAAO,CAAG,WAAW,CAAG,cAAc,CACjC,CAAC,EACL,CAAC,EACJ,CAAC,cAENT,KAAA,QAAKyC,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnB1C,IAAA,OAAA0C,QAAA,CAAI,oBAAkB,CAAI,CAAC,CAC1BnC,SAAS,CAAC4B,MAAM,GAAK,CAAC,cACrBnC,IAAA,MAAA0C,QAAA,CAAG,oDAAkD,CAAG,CAAC,cAEzDxC,KAAA,CAAAE,SAAA,EAAAsC,QAAA,eACExC,KAAA,UAAOyC,SAAS,CAAC,OAAO,CAAAD,QAAA,eACtB1C,IAAA,UAAA0C,QAAA,cACExC,KAAA,OAAAwC,QAAA,eACE1C,IAAA,OAAA0C,QAAA,CAAI,IAAE,CAAI,CAAC,cACX1C,IAAA,OAAA0C,QAAA,CAAI,MAAI,CAAI,CAAC,cACb1C,IAAA,OAAA0C,QAAA,CAAI,YAAU,CAAI,CAAC,cACnB1C,IAAA,OAAA0C,QAAA,CAAI,SAAO,CAAI,CAAC,EACd,CAAC,CACA,CAAC,cACR1C,IAAA,UAAA0C,QAAA,CACGJ,YAAY,CAACe,GAAG,CAAEC,QAAQ,eACzBpD,KAAA,OAAAwC,QAAA,eACE1C,IAAA,OAAA0C,QAAA,CAAKY,QAAQ,CAACxB,EAAE,CAAK,CAAC,cACtB9B,IAAA,OAAA0C,QAAA,CAAKY,QAAQ,CAAC3B,IAAI,CAAK,CAAC,cACxB3B,IAAA,OAAA0C,QAAA,CAAK,GAAI,CAAAa,IAAI,CAACD,QAAQ,CAACE,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAAK,CAAC,cAC5DzD,IAAA,OAAA0C,QAAA,cACE1C,IAAA,WACE2C,SAAS,CAAC,uBAAuB,CACjCe,OAAO,CAAEA,CAAA,GAAM7B,YAAY,CAACyB,QAAQ,CAACxB,EAAE,CAAE,CAAAY,QAAA,CAC1C,QAED,CAAQ,CAAC,CACP,CAAC,GAXEY,QAAQ,CAACxB,EAYd,CACL,CAAC,CACG,CAAC,EACH,CAAC,cACR9B,IAAA,CAACF,UAAU,EACTe,WAAW,CAAEA,WAAY,CACzBqB,UAAU,CAAEA,UAAW,CACvBnB,YAAY,CAAEA,YAAa,CAC3B4C,YAAY,CAAEnB,gBAAiB,CAChC,CAAC,EACF,CACH,EACE,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}