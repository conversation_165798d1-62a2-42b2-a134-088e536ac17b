-- =====================================================
-- Simple Migration Script: Sub Category to Firm Nature (MySQL)
-- Description: Direct SQL commands to rename sub_categories to firm_natures
-- Database: MySQL
-- Author: CRM System Migration
-- Date: 2025-01-01
-- =====================================================

-- IMPORTANT: Run CheckCurrentTables_MySQL.sql first to verify your table structure!

USE data_crm;

-- =====================================================
-- STEP 1: CREATE BACKUP TABLES
-- =====================================================

-- Backup sub_categories table (if it exists)
CREATE TABLE IF NOT EXISTS sub_categories_backup AS 
SELECT * FROM sub_categories;

-- Backup persons table structure
CREATE TABLE IF NOT EXISTS persons_structure_backup LIKE persons;

SELECT 'Backup tables created successfully' AS Status;

-- =====================================================
-- STEP 2: DROP FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Drop foreign key constraint from persons to sub_categories
-- Note: Replace 'your_fk_constraint_name' with the actual constraint name from your database
-- You can find the constraint name by running: 
-- SHOW CREATE TABLE persons;

-- Example (uncomment and modify the constraint name):
-- ALTER TABLE persons DROP FOREIGN KEY fk_persons_sub_categories;

SELECT 'Foreign key constraints dropped (modify constraint names as needed)' AS Status;

-- =====================================================
-- STEP 3: RENAME TABLE
-- =====================================================

-- Rename sub_categories table to firm_natures
RENAME TABLE sub_categories TO firm_natures;

SELECT 'Table renamed: sub_categories -> firm_natures' AS Status;

-- =====================================================
-- STEP 4: RENAME COLUMN IN PERSONS TABLE
-- =====================================================

-- Rename sub_category_id column to firm_nature_id
ALTER TABLE persons CHANGE COLUMN sub_category_id firm_nature_id INT NULL;

SELECT 'Column renamed: sub_category_id -> firm_nature_id' AS Status;

-- =====================================================
-- STEP 5: RECREATE INDEXES
-- =====================================================

-- Create indexes on firm_natures table
CREATE INDEX IX_firm_natures_category_id ON firm_natures(category_id);
CREATE INDEX IX_firm_natures_category_id_name ON firm_natures(category_id, name);

-- Create index on persons table for firm_nature_id
CREATE INDEX IX_persons_firm_nature_id ON persons(firm_nature_id);

SELECT 'Indexes created successfully' AS Status;

-- =====================================================
-- STEP 6: RECREATE FOREIGN KEY CONSTRAINT
-- =====================================================

-- Add foreign key constraint from persons to firm_natures
ALTER TABLE persons 
ADD CONSTRAINT fk_persons_firm_natures 
FOREIGN KEY (firm_nature_id) REFERENCES firm_natures(id)
ON DELETE SET NULL ON UPDATE CASCADE;

SELECT 'Foreign key constraint recreated' AS Status;

-- =====================================================
-- STEP 7: CREATE PARTITIONING (OPTIONAL)
-- =====================================================

-- Note: This step requires recreating the persons table with partitioning
-- Uncomment the following section if you want partitioning:

/*
-- Create temporary table to hold persons data
CREATE TABLE persons_temp AS SELECT * FROM persons;

-- Drop the original persons table
DROP TABLE persons;

-- Recreate persons table with partitioning
CREATE TABLE persons (
    id INT NOT NULL AUTO_INCREMENT,
    division_id INT NOT NULL,
    category_id INT NOT NULL,
    firm_nature_id INT NULL,
    name VARCHAR(255) NOT NULL,
    mobile_number VARCHAR(15) NOT NULL,
    nature INT NOT NULL,
    -- Add all other columns from your original persons table here
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE,
    PRIMARY KEY (id, firm_nature_id),
    INDEX IX_persons_firm_nature_id (firm_nature_id),
    INDEX IX_persons_division_id (division_id),
    INDEX IX_persons_category_id (category_id)
)
PARTITION BY HASH(IFNULL(firm_nature_id, 0))
PARTITIONS 16;

-- Insert data back from temporary table
INSERT INTO persons SELECT * FROM persons_temp;

-- Drop temporary table
DROP TABLE persons_temp;

-- Recreate foreign key constraints
ALTER TABLE persons 
ADD CONSTRAINT fk_persons_firm_natures 
FOREIGN KEY (firm_nature_id) REFERENCES firm_natures(id);

SELECT 'Partitioning created successfully' AS Status;
*/

-- =====================================================
-- STEP 8: VERIFICATION
-- =====================================================

-- Verify the migration
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'SUCCESS: firm_natures table exists'
        ELSE 'ERROR: firm_natures table missing'
    END AS firm_natures_check
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'firm_natures';

SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'SUCCESS: firm_nature_id column exists'
        ELSE 'ERROR: firm_nature_id column missing'
    END AS firm_nature_id_check
FROM information_schema.columns 
WHERE table_schema = 'data_crm' AND table_name = 'persons' AND column_name = 'firm_nature_id';

-- Show record counts
SELECT 
    (SELECT COUNT(*) FROM firm_natures) AS firm_natures_count,
    (SELECT COUNT(*) FROM persons) AS persons_count;

SELECT '==================================================' AS Status;
SELECT 'MIGRATION COMPLETED!' AS Status;
SELECT '==================================================' AS Status;
SELECT 'Next steps:' AS Status;
SELECT '1. Update your application code to use firm_natures and firm_nature_id' AS Status;
SELECT '2. Test the application thoroughly' AS Status;
SELECT '3. Remove backup tables when confident migration is successful' AS Status;
