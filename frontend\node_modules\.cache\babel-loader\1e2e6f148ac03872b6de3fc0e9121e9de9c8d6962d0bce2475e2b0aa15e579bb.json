{"ast": null, "code": "import React from'react';import'./FormField.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FormField=_ref=>{let{field,value,onChange,error}=_ref;const handleChange=e=>{const{type,checked,value:inputValue}=e.target;if(type==='checkbox'){onChange(checked);}else if(type==='number'){onChange(inputValue?parseFloat(inputValue):null);}else{onChange(inputValue);}};const handleArrayChange=e=>{const inputValue=e.target.value;const arrayValue=inputValue.split(',').map(item=>item.trim()).filter(item=>item);onChange(arrayValue);};const handleMultiSelectChange=e=>{const selectedOptions=Array.from(e.target.selectedOptions,option=>isNaN(option.value)?option.value:parseInt(option.value));onChange(selectedOptions);};const renderField=()=>{var _field$validation,_field$validation2,_field$validation3,_field$validation4,_field$validation5,_field$validation6,_field$validation7,_field$validation8,_field$options,_field$options2,_field$options3;const commonProps={id:field.key,name:field.key,placeholder:field.placeholder||'',className:`form-input ${error?'error':''}`,'aria-describedby':error?`${field.key}-error`:undefined};switch(field.type){case'text':case'email':case'tel':case'url':return/*#__PURE__*/_jsx(\"input\",{...commonProps,type:field.type,value:value||'',onChange:handleChange,maxLength:(_field$validation=field.validation)===null||_field$validation===void 0?void 0:_field$validation.maxLength,minLength:(_field$validation2=field.validation)===null||_field$validation2===void 0?void 0:_field$validation2.minLength,pattern:(_field$validation3=field.validation)===null||_field$validation3===void 0?void 0:_field$validation3.pattern,required:field.required});case'textarea':return/*#__PURE__*/_jsx(\"textarea\",{...commonProps,value:value||'',onChange:handleChange,rows:field.rows||3,maxLength:(_field$validation4=field.validation)===null||_field$validation4===void 0?void 0:_field$validation4.maxLength,minLength:(_field$validation5=field.validation)===null||_field$validation5===void 0?void 0:_field$validation5.minLength,required:field.required});case'number':return/*#__PURE__*/_jsx(\"input\",{...commonProps,type:\"number\",value:value||'',onChange:handleChange,min:(_field$validation6=field.validation)===null||_field$validation6===void 0?void 0:_field$validation6.min,max:(_field$validation7=field.validation)===null||_field$validation7===void 0?void 0:_field$validation7.max,step:((_field$validation8=field.validation)===null||_field$validation8===void 0?void 0:_field$validation8.step)||'any',required:field.required});case'date':return/*#__PURE__*/_jsx(\"input\",{...commonProps,type:\"date\",value:value||'',onChange:handleChange,required:field.required});case'checkbox':return/*#__PURE__*/_jsxs(\"div\",{className:\"checkbox-wrapper\",children:[/*#__PURE__*/_jsx(\"input\",{...commonProps,type:\"checkbox\",checked:value||false,onChange:handleChange,className:\"form-checkbox\"}),/*#__PURE__*/_jsx(\"label\",{htmlFor:field.key,className:\"checkbox-label\",children:field.label})]});case'select':return/*#__PURE__*/_jsxs(\"select\",{...commonProps,value:value||'',onChange:handleChange,required:field.required,children:[/*#__PURE__*/_jsxs(\"option\",{value:\"\",children:[\"Select \",field.label]}),(_field$options=field.options)===null||_field$options===void 0?void 0:_field$options.map(option=>/*#__PURE__*/_jsx(\"option\",{value:option.value,children:option.label},option.value))]});case'multiselect':return/*#__PURE__*/_jsx(\"select\",{...commonProps,multiple:true,value:Array.isArray(value)?value:[],onChange:handleMultiSelectChange,size:Math.min(((_field$options2=field.options)===null||_field$options2===void 0?void 0:_field$options2.length)||5,5),children:(_field$options3=field.options)===null||_field$options3===void 0?void 0:_field$options3.map(option=>/*#__PURE__*/_jsx(\"option\",{value:option.value,children:option.label},option.value))});case'array':return/*#__PURE__*/_jsx(\"input\",{...commonProps,type:\"text\",value:Array.isArray(value)?value.join(', '):value||'',onChange:handleArrayChange,placeholder:field.placeholder||'Enter values separated by commas'});default:return/*#__PURE__*/_jsx(\"input\",{...commonProps,type:\"text\",value:value||'',onChange:handleChange,required:field.required});}};// Don't render label for checkbox as it's handled inside the field\nconst shouldRenderLabel=field.type!=='checkbox';return/*#__PURE__*/_jsxs(\"div\",{className:`form-field ${field.type==='checkbox'?'checkbox-field':''}`,children:[shouldRenderLabel&&/*#__PURE__*/_jsxs(\"label\",{htmlFor:field.key,className:\"form-label\",children:[field.label,field.required&&/*#__PURE__*/_jsx(\"span\",{className:\"required-indicator\",children:\"*\"})]}),renderField(),field.helpText&&/*#__PURE__*/_jsx(\"div\",{className:\"help-text\",children:field.helpText}),error&&/*#__PURE__*/_jsx(\"div\",{id:`${field.key}-error`,className:\"error-message\",role:\"alert\",children:error})]});};export default FormField;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "FormField", "_ref", "field", "value", "onChange", "error", "handleChange", "e", "type", "checked", "inputValue", "target", "parseFloat", "handleArrayChange", "arrayValue", "split", "map", "item", "trim", "filter", "handleMultiSelectChange", "selectedOptions", "Array", "from", "option", "isNaN", "parseInt", "renderField", "_field$validation", "_field$validation2", "_field$validation3", "_field$validation4", "_field$validation5", "_field$validation6", "_field$validation7", "_field$validation8", "_field$options", "_field$options2", "_field$options3", "commonProps", "id", "key", "name", "placeholder", "className", "undefined", "max<PERSON><PERSON><PERSON>", "validation", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "required", "rows", "min", "max", "step", "children", "htmlFor", "label", "options", "multiple", "isArray", "size", "Math", "length", "join", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helpText", "role"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/FormField.js"], "sourcesContent": ["import React from 'react';\nimport './FormField.css';\n\nconst FormField = ({ field, value, onChange, error }) => {\n  const handleChange = (e) => {\n    const { type, checked, value: inputValue } = e.target;\n    \n    if (type === 'checkbox') {\n      onChange(checked);\n    } else if (type === 'number') {\n      onChange(inputValue ? parseFloat(inputValue) : null);\n    } else {\n      onChange(inputValue);\n    }\n  };\n\n  const handleArrayChange = (e) => {\n    const inputValue = e.target.value;\n    const arrayValue = inputValue.split(',').map(item => item.trim()).filter(item => item);\n    onChange(arrayValue);\n  };\n\n  const handleMultiSelectChange = (e) => {\n    const selectedOptions = Array.from(e.target.selectedOptions, option => \n      isNaN(option.value) ? option.value : parseInt(option.value)\n    );\n    onChange(selectedOptions);\n  };\n\n  const renderField = () => {\n    const commonProps = {\n      id: field.key,\n      name: field.key,\n      placeholder: field.placeholder || '',\n      className: `form-input ${error ? 'error' : ''}`,\n      'aria-describedby': error ? `${field.key}-error` : undefined\n    };\n\n    switch (field.type) {\n      case 'text':\n      case 'email':\n      case 'tel':\n      case 'url':\n        return (\n          <input\n            {...commonProps}\n            type={field.type}\n            value={value || ''}\n            onChange={handleChange}\n            maxLength={field.validation?.maxLength}\n            minLength={field.validation?.minLength}\n            pattern={field.validation?.pattern}\n            required={field.required}\n          />\n        );\n\n      case 'textarea':\n        return (\n          <textarea\n            {...commonProps}\n            value={value || ''}\n            onChange={handleChange}\n            rows={field.rows || 3}\n            maxLength={field.validation?.maxLength}\n            minLength={field.validation?.minLength}\n            required={field.required}\n          />\n        );\n\n      case 'number':\n        return (\n          <input\n            {...commonProps}\n            type=\"number\"\n            value={value || ''}\n            onChange={handleChange}\n            min={field.validation?.min}\n            max={field.validation?.max}\n            step={field.validation?.step || 'any'}\n            required={field.required}\n          />\n        );\n\n      case 'date':\n        return (\n          <input\n            {...commonProps}\n            type=\"date\"\n            value={value || ''}\n            onChange={handleChange}\n            required={field.required}\n          />\n        );\n\n      case 'checkbox':\n        return (\n          <div className=\"checkbox-wrapper\">\n            <input\n              {...commonProps}\n              type=\"checkbox\"\n              checked={value || false}\n              onChange={handleChange}\n              className=\"form-checkbox\"\n            />\n            <label htmlFor={field.key} className=\"checkbox-label\">\n              {field.label}\n            </label>\n          </div>\n        );\n\n      case 'select':\n        return (\n          <select\n            {...commonProps}\n            value={value || ''}\n            onChange={handleChange}\n            required={field.required}\n          >\n            <option value=\"\">Select {field.label}</option>\n            {field.options?.map(option => (\n              <option key={option.value} value={option.value}>\n                {option.label}\n              </option>\n            ))}\n          </select>\n        );\n\n      case 'multiselect':\n        return (\n          <select\n            {...commonProps}\n            multiple\n            value={Array.isArray(value) ? value : []}\n            onChange={handleMultiSelectChange}\n            size={Math.min(field.options?.length || 5, 5)}\n          >\n            {field.options?.map(option => (\n              <option key={option.value} value={option.value}>\n                {option.label}\n              </option>\n            ))}\n          </select>\n        );\n\n      case 'array':\n        return (\n          <input\n            {...commonProps}\n            type=\"text\"\n            value={Array.isArray(value) ? value.join(', ') : value || ''}\n            onChange={handleArrayChange}\n            placeholder={field.placeholder || 'Enter values separated by commas'}\n          />\n        );\n\n      default:\n        return (\n          <input\n            {...commonProps}\n            type=\"text\"\n            value={value || ''}\n            onChange={handleChange}\n            required={field.required}\n          />\n        );\n    }\n  };\n\n  // Don't render label for checkbox as it's handled inside the field\n  const shouldRenderLabel = field.type !== 'checkbox';\n\n  return (\n    <div className={`form-field ${field.type === 'checkbox' ? 'checkbox-field' : ''}`}>\n      {shouldRenderLabel && (\n        <label htmlFor={field.key} className=\"form-label\">\n          {field.label}\n          {field.required && <span className=\"required-indicator\">*</span>}\n        </label>\n      )}\n      \n      {renderField()}\n      \n      {field.helpText && (\n        <div className=\"help-text\">{field.helpText}</div>\n      )}\n      \n      {error && (\n        <div id={`${field.key}-error`} className=\"error-message\" role=\"alert\">\n          {error}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FormField;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzB,KAAM,CAAAC,SAAS,CAAGC,IAAA,EAAuC,IAAtC,CAAEC,KAAK,CAAEC,KAAK,CAAEC,QAAQ,CAAEC,KAAM,CAAC,CAAAJ,IAAA,CAClD,KAAM,CAAAK,YAAY,CAAIC,CAAC,EAAK,CAC1B,KAAM,CAAEC,IAAI,CAAEC,OAAO,CAAEN,KAAK,CAAEO,UAAW,CAAC,CAAGH,CAAC,CAACI,MAAM,CAErD,GAAIH,IAAI,GAAK,UAAU,CAAE,CACvBJ,QAAQ,CAACK,OAAO,CAAC,CACnB,CAAC,IAAM,IAAID,IAAI,GAAK,QAAQ,CAAE,CAC5BJ,QAAQ,CAACM,UAAU,CAAGE,UAAU,CAACF,UAAU,CAAC,CAAG,IAAI,CAAC,CACtD,CAAC,IAAM,CACLN,QAAQ,CAACM,UAAU,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAAG,iBAAiB,CAAIN,CAAC,EAAK,CAC/B,KAAM,CAAAG,UAAU,CAAGH,CAAC,CAACI,MAAM,CAACR,KAAK,CACjC,KAAM,CAAAW,UAAU,CAAGJ,UAAU,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAACF,IAAI,EAAIA,IAAI,CAAC,CACtFb,QAAQ,CAACU,UAAU,CAAC,CACtB,CAAC,CAED,KAAM,CAAAM,uBAAuB,CAAIb,CAAC,EAAK,CACrC,KAAM,CAAAc,eAAe,CAAGC,KAAK,CAACC,IAAI,CAAChB,CAAC,CAACI,MAAM,CAACU,eAAe,CAAEG,MAAM,EACjEC,KAAK,CAACD,MAAM,CAACrB,KAAK,CAAC,CAAGqB,MAAM,CAACrB,KAAK,CAAGuB,QAAQ,CAACF,MAAM,CAACrB,KAAK,CAC5D,CAAC,CACDC,QAAQ,CAACiB,eAAe,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAM,WAAW,CAAGA,CAAA,GAAM,KAAAC,iBAAA,CAAAC,kBAAA,CAAAC,kBAAA,CAAAC,kBAAA,CAAAC,kBAAA,CAAAC,kBAAA,CAAAC,kBAAA,CAAAC,kBAAA,CAAAC,cAAA,CAAAC,eAAA,CAAAC,eAAA,CACxB,KAAM,CAAAC,WAAW,CAAG,CAClBC,EAAE,CAAEtC,KAAK,CAACuC,GAAG,CACbC,IAAI,CAAExC,KAAK,CAACuC,GAAG,CACfE,WAAW,CAAEzC,KAAK,CAACyC,WAAW,EAAI,EAAE,CACpCC,SAAS,CAAE,cAAcvC,KAAK,CAAG,OAAO,CAAG,EAAE,EAAE,CAC/C,kBAAkB,CAAEA,KAAK,CAAG,GAAGH,KAAK,CAACuC,GAAG,QAAQ,CAAGI,SACrD,CAAC,CAED,OAAQ3C,KAAK,CAACM,IAAI,EAChB,IAAK,MAAM,CACX,IAAK,OAAO,CACZ,IAAK,KAAK,CACV,IAAK,KAAK,CACR,mBACEX,IAAA,aACM0C,WAAW,CACf/B,IAAI,CAAEN,KAAK,CAACM,IAAK,CACjBL,KAAK,CAAEA,KAAK,EAAI,EAAG,CACnBC,QAAQ,CAAEE,YAAa,CACvBwC,SAAS,EAAAlB,iBAAA,CAAE1B,KAAK,CAAC6C,UAAU,UAAAnB,iBAAA,iBAAhBA,iBAAA,CAAkBkB,SAAU,CACvCE,SAAS,EAAAnB,kBAAA,CAAE3B,KAAK,CAAC6C,UAAU,UAAAlB,kBAAA,iBAAhBA,kBAAA,CAAkBmB,SAAU,CACvCC,OAAO,EAAAnB,kBAAA,CAAE5B,KAAK,CAAC6C,UAAU,UAAAjB,kBAAA,iBAAhBA,kBAAA,CAAkBmB,OAAQ,CACnCC,QAAQ,CAAEhD,KAAK,CAACgD,QAAS,CAC1B,CAAC,CAGN,IAAK,UAAU,CACb,mBACErD,IAAA,gBACM0C,WAAW,CACfpC,KAAK,CAAEA,KAAK,EAAI,EAAG,CACnBC,QAAQ,CAAEE,YAAa,CACvB6C,IAAI,CAAEjD,KAAK,CAACiD,IAAI,EAAI,CAAE,CACtBL,SAAS,EAAAf,kBAAA,CAAE7B,KAAK,CAAC6C,UAAU,UAAAhB,kBAAA,iBAAhBA,kBAAA,CAAkBe,SAAU,CACvCE,SAAS,EAAAhB,kBAAA,CAAE9B,KAAK,CAAC6C,UAAU,UAAAf,kBAAA,iBAAhBA,kBAAA,CAAkBgB,SAAU,CACvCE,QAAQ,CAAEhD,KAAK,CAACgD,QAAS,CAC1B,CAAC,CAGN,IAAK,QAAQ,CACX,mBACErD,IAAA,aACM0C,WAAW,CACf/B,IAAI,CAAC,QAAQ,CACbL,KAAK,CAAEA,KAAK,EAAI,EAAG,CACnBC,QAAQ,CAAEE,YAAa,CACvB8C,GAAG,EAAAnB,kBAAA,CAAE/B,KAAK,CAAC6C,UAAU,UAAAd,kBAAA,iBAAhBA,kBAAA,CAAkBmB,GAAI,CAC3BC,GAAG,EAAAnB,kBAAA,CAAEhC,KAAK,CAAC6C,UAAU,UAAAb,kBAAA,iBAAhBA,kBAAA,CAAkBmB,GAAI,CAC3BC,IAAI,CAAE,EAAAnB,kBAAA,CAAAjC,KAAK,CAAC6C,UAAU,UAAAZ,kBAAA,iBAAhBA,kBAAA,CAAkBmB,IAAI,GAAI,KAAM,CACtCJ,QAAQ,CAAEhD,KAAK,CAACgD,QAAS,CAC1B,CAAC,CAGN,IAAK,MAAM,CACT,mBACErD,IAAA,aACM0C,WAAW,CACf/B,IAAI,CAAC,MAAM,CACXL,KAAK,CAAEA,KAAK,EAAI,EAAG,CACnBC,QAAQ,CAAEE,YAAa,CACvB4C,QAAQ,CAAEhD,KAAK,CAACgD,QAAS,CAC1B,CAAC,CAGN,IAAK,UAAU,CACb,mBACEnD,KAAA,QAAK6C,SAAS,CAAC,kBAAkB,CAAAW,QAAA,eAC/B1D,IAAA,aACM0C,WAAW,CACf/B,IAAI,CAAC,UAAU,CACfC,OAAO,CAAEN,KAAK,EAAI,KAAM,CACxBC,QAAQ,CAAEE,YAAa,CACvBsC,SAAS,CAAC,eAAe,CAC1B,CAAC,cACF/C,IAAA,UAAO2D,OAAO,CAAEtD,KAAK,CAACuC,GAAI,CAACG,SAAS,CAAC,gBAAgB,CAAAW,QAAA,CAClDrD,KAAK,CAACuD,KAAK,CACP,CAAC,EACL,CAAC,CAGV,IAAK,QAAQ,CACX,mBACE1D,KAAA,cACMwC,WAAW,CACfpC,KAAK,CAAEA,KAAK,EAAI,EAAG,CACnBC,QAAQ,CAAEE,YAAa,CACvB4C,QAAQ,CAAEhD,KAAK,CAACgD,QAAS,CAAAK,QAAA,eAEzBxD,KAAA,WAAQI,KAAK,CAAC,EAAE,CAAAoD,QAAA,EAAC,SAAO,CAACrD,KAAK,CAACuD,KAAK,EAAS,CAAC,EAAArB,cAAA,CAC7ClC,KAAK,CAACwD,OAAO,UAAAtB,cAAA,iBAAbA,cAAA,CAAepB,GAAG,CAACQ,MAAM,eACxB3B,IAAA,WAA2BM,KAAK,CAAEqB,MAAM,CAACrB,KAAM,CAAAoD,QAAA,CAC5C/B,MAAM,CAACiC,KAAK,EADFjC,MAAM,CAACrB,KAEZ,CACT,CAAC,EACI,CAAC,CAGb,IAAK,aAAa,CAChB,mBACEN,IAAA,cACM0C,WAAW,CACfoB,QAAQ,MACRxD,KAAK,CAAEmB,KAAK,CAACsC,OAAO,CAACzD,KAAK,CAAC,CAAGA,KAAK,CAAG,EAAG,CACzCC,QAAQ,CAAEgB,uBAAwB,CAClCyC,IAAI,CAAEC,IAAI,CAACV,GAAG,CAAC,EAAAf,eAAA,CAAAnC,KAAK,CAACwD,OAAO,UAAArB,eAAA,iBAAbA,eAAA,CAAe0B,MAAM,GAAI,CAAC,CAAE,CAAC,CAAE,CAAAR,QAAA,EAAAjB,eAAA,CAE7CpC,KAAK,CAACwD,OAAO,UAAApB,eAAA,iBAAbA,eAAA,CAAetB,GAAG,CAACQ,MAAM,eACxB3B,IAAA,WAA2BM,KAAK,CAAEqB,MAAM,CAACrB,KAAM,CAAAoD,QAAA,CAC5C/B,MAAM,CAACiC,KAAK,EADFjC,MAAM,CAACrB,KAEZ,CACT,CAAC,CACI,CAAC,CAGb,IAAK,OAAO,CACV,mBACEN,IAAA,aACM0C,WAAW,CACf/B,IAAI,CAAC,MAAM,CACXL,KAAK,CAAEmB,KAAK,CAACsC,OAAO,CAACzD,KAAK,CAAC,CAAGA,KAAK,CAAC6D,IAAI,CAAC,IAAI,CAAC,CAAG7D,KAAK,EAAI,EAAG,CAC7DC,QAAQ,CAAES,iBAAkB,CAC5B8B,WAAW,CAAEzC,KAAK,CAACyC,WAAW,EAAI,kCAAmC,CACtE,CAAC,CAGN,QACE,mBACE9C,IAAA,aACM0C,WAAW,CACf/B,IAAI,CAAC,MAAM,CACXL,KAAK,CAAEA,KAAK,EAAI,EAAG,CACnBC,QAAQ,CAAEE,YAAa,CACvB4C,QAAQ,CAAEhD,KAAK,CAACgD,QAAS,CAC1B,CAAC,CAER,CACF,CAAC,CAED;AACA,KAAM,CAAAe,iBAAiB,CAAG/D,KAAK,CAACM,IAAI,GAAK,UAAU,CAEnD,mBACET,KAAA,QAAK6C,SAAS,CAAE,cAAc1C,KAAK,CAACM,IAAI,GAAK,UAAU,CAAG,gBAAgB,CAAG,EAAE,EAAG,CAAA+C,QAAA,EAC/EU,iBAAiB,eAChBlE,KAAA,UAAOyD,OAAO,CAAEtD,KAAK,CAACuC,GAAI,CAACG,SAAS,CAAC,YAAY,CAAAW,QAAA,EAC9CrD,KAAK,CAACuD,KAAK,CACXvD,KAAK,CAACgD,QAAQ,eAAIrD,IAAA,SAAM+C,SAAS,CAAC,oBAAoB,CAAAW,QAAA,CAAC,GAAC,CAAM,CAAC,EAC3D,CACR,CAEA5B,WAAW,CAAC,CAAC,CAEbzB,KAAK,CAACgE,QAAQ,eACbrE,IAAA,QAAK+C,SAAS,CAAC,WAAW,CAAAW,QAAA,CAAErD,KAAK,CAACgE,QAAQ,CAAM,CACjD,CAEA7D,KAAK,eACJR,IAAA,QAAK2C,EAAE,CAAE,GAAGtC,KAAK,CAACuC,GAAG,QAAS,CAACG,SAAS,CAAC,eAAe,CAACuB,IAAI,CAAC,OAAO,CAAAZ,QAAA,CAClElD,KAAK,CACH,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}