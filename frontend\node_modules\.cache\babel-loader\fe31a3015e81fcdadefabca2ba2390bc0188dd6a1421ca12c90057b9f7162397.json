{"ast": null, "code": "import { transform } from '../utils/transform.mjs';\nimport { useCombineMotionValues } from './use-combine-values.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { useComputed } from './use-computed.mjs';\nfunction useTransform(input, inputRangeOrTransformer, outputRange, options) {\n  if (typeof input === \"function\") {\n    return useComputed(input);\n  }\n  const transformer = typeof inputRangeOrTransformer === \"function\" ? inputRangeOrTransformer : transform(inputRangeOrTransformer, outputRange, options);\n  return Array.isArray(input) ? useListTransform(input, transformer) : useListTransform([input], _ref => {\n    let [latest] = _ref;\n    return transformer(latest);\n  });\n}\nfunction useListTransform(values, transformer) {\n  const latest = useConstant(() => []);\n  return useCombineMotionValues(values, () => {\n    latest.length = 0;\n    const numValues = values.length;\n    for (let i = 0; i < numValues; i++) {\n      latest[i] = values[i].get();\n    }\n    return transformer(latest);\n  });\n}\nexport { useTransform };", "map": {"version": 3, "names": ["transform", "useCombineMotionValues", "useConstant", "useComputed", "useTransform", "input", "inputRangeOrTransformer", "outputRange", "options", "transformer", "Array", "isArray", "useListTransform", "_ref", "latest", "values", "length", "numValues", "i", "get"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/value/use-transform.mjs"], "sourcesContent": ["import { transform } from '../utils/transform.mjs';\nimport { useCombineMotionValues } from './use-combine-values.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { useComputed } from './use-computed.mjs';\n\nfunction useTransform(input, inputRangeOrTransformer, outputRange, options) {\n    if (typeof input === \"function\") {\n        return useComputed(input);\n    }\n    const transformer = typeof inputRangeOrTransformer === \"function\"\n        ? inputRangeOrTransformer\n        : transform(inputRangeOrTransformer, outputRange, options);\n    return Array.isArray(input)\n        ? useListTransform(input, transformer)\n        : useListTransform([input], ([latest]) => transformer(latest));\n}\nfunction useListTransform(values, transformer) {\n    const latest = useConstant(() => []);\n    return useCombineMotionValues(values, () => {\n        latest.length = 0;\n        const numValues = values.length;\n        for (let i = 0; i < numValues; i++) {\n            latest[i] = values[i].get();\n        }\n        return transformer(latest);\n    });\n}\n\nexport { useTransform };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,SAASC,sBAAsB,QAAQ,0BAA0B;AACjE,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,WAAW,QAAQ,oBAAoB;AAEhD,SAASC,YAAYA,CAACC,KAAK,EAAEC,uBAAuB,EAAEC,WAAW,EAAEC,OAAO,EAAE;EACxE,IAAI,OAAOH,KAAK,KAAK,UAAU,EAAE;IAC7B,OAAOF,WAAW,CAACE,KAAK,CAAC;EAC7B;EACA,MAAMI,WAAW,GAAG,OAAOH,uBAAuB,KAAK,UAAU,GAC3DA,uBAAuB,GACvBN,SAAS,CAACM,uBAAuB,EAAEC,WAAW,EAAEC,OAAO,CAAC;EAC9D,OAAOE,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GACrBO,gBAAgB,CAACP,KAAK,EAAEI,WAAW,CAAC,GACpCG,gBAAgB,CAAC,CAACP,KAAK,CAAC,EAAEQ,IAAA;IAAA,IAAC,CAACC,MAAM,CAAC,GAAAD,IAAA;IAAA,OAAKJ,WAAW,CAACK,MAAM,CAAC;EAAA,EAAC;AACtE;AACA,SAASF,gBAAgBA,CAACG,MAAM,EAAEN,WAAW,EAAE;EAC3C,MAAMK,MAAM,GAAGZ,WAAW,CAAC,MAAM,EAAE,CAAC;EACpC,OAAOD,sBAAsB,CAACc,MAAM,EAAE,MAAM;IACxCD,MAAM,CAACE,MAAM,GAAG,CAAC;IACjB,MAAMC,SAAS,GAAGF,MAAM,CAACC,MAAM;IAC/B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,EAAEC,CAAC,EAAE,EAAE;MAChCJ,MAAM,CAACI,CAAC,CAAC,GAAGH,MAAM,CAACG,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC;IAC/B;IACA,OAAOV,WAAW,CAACK,MAAM,CAAC;EAC9B,CAAC,CAAC;AACN;AAEA,SAASV,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}