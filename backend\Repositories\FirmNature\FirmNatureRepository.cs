using Microsoft.EntityFrameworkCore;
using CrmApi.Data;
using CrmApi.Models.FirmNature;

namespace CrmApi.Repositories.FirmNature
{
    public class FirmNatureRepository : IFirmNatureRepository
    {
        private readonly CrmDbContext _context;

        public FirmNatureRepository(CrmDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Models.FirmNature.FirmNature>> GetAllAsync()
        {
            return await _context.FirmNatures.ToListAsync();
        }

        public async Task<IEnumerable<Models.FirmNature.FirmNature>> GetAllWithRelationsAsync()
        {
            return await _context.FirmNatures
                .Include(fn => fn.Category)
                .ThenInclude(c => c.Division)
                .ToListAsync();
        }

        public async Task<Models.FirmNature.FirmNature?> GetByIdAsync(int id)
        {
            return await _context.FirmNatures.FindAsync(id);
        }

        public async Task<Models.FirmNature.FirmNature?> GetByIdWithRelationsAsync(int id)
        {
            return await _context.FirmNatures
                .Include(fn => fn.Category)
                .ThenInclude(c => c.Division)
                .FirstOrDefaultAsync(fn => fn.Id == id);
        }

        public async Task<IEnumerable<Models.FirmNature.FirmNature>> GetByCategoryIdAsync(int categoryId)
        {
            return await _context.FirmNatures
                .Where(fn => fn.CategoryId == categoryId)
                .ToListAsync();
        }

        public async Task<Models.FirmNature.FirmNature> CreateAsync(Models.FirmNature.FirmNature firmNature)
        {
            firmNature.CreatedAt = DateTime.UtcNow;
            firmNature.UpdatedAt = DateTime.UtcNow;

            _context.FirmNatures.Add(firmNature);
            await _context.SaveChangesAsync();
            return firmNature;
        }

        public async Task<Models.FirmNature.FirmNature> UpdateAsync(Models.FirmNature.FirmNature firmNature)
        {
            firmNature.UpdatedAt = DateTime.UtcNow;
            _context.Entry(firmNature).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return firmNature;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var firmNature = await _context.FirmNatures.FindAsync(id);
            if (firmNature == null)
                return false;

            _context.FirmNatures.Remove(firmNature);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.FirmNatures.AnyAsync(fn => fn.Id == id);
        }

        public async Task<bool> NameExistsInCategoryAsync(string name, int categoryId, int? excludeId = null)
        {
            var query = _context.FirmNatures.Where(fn => fn.Name == name && fn.CategoryId == categoryId);
            if (excludeId.HasValue)
                query = query.Where(fn => fn.Id != excludeId.Value);

            return await query.AnyAsync();
        }
    }
}
