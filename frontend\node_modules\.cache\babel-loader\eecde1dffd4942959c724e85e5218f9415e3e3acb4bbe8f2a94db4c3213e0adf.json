{"ast": null, "code": "import { useRef } from 'react';\nimport { useIsomorphicLayoutEffect } from './use-isomorphic-effect.mjs';\nfunction useIsMounted() {\n  const isMounted = useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n  return isMounted;\n}\nexport { useIsMounted };", "map": {"version": 3, "names": ["useRef", "useIsomorphicLayoutEffect", "useIsMounted", "isMounted", "current"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/utils/use-is-mounted.mjs"], "sourcesContent": ["import { useRef } from 'react';\nimport { useIsomorphicLayoutEffect } from './use-isomorphic-effect.mjs';\n\nfunction useIsMounted() {\n    const isMounted = useRef(false);\n    useIsomorphicLayoutEffect(() => {\n        isMounted.current = true;\n        return () => {\n            isMounted.current = false;\n        };\n    }, []);\n    return isMounted;\n}\n\nexport { useIsMounted };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,yBAAyB,QAAQ,6BAA6B;AAEvE,SAASC,YAAYA,CAAA,EAAG;EACpB,MAAMC,SAAS,GAAGH,MAAM,CAAC,KAAK,CAAC;EAC/BC,yBAAyB,CAAC,MAAM;IAC5BE,SAAS,CAACC,OAAO,GAAG,IAAI;IACxB,OAAO,MAAM;MACTD,SAAS,CAACC,OAAO,GAAG,KAAK;IAC7B,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EACN,OAAOD,SAAS;AACpB;AAEA,SAASD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}