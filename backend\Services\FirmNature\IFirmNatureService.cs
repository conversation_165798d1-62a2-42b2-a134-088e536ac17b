using CrmApi.Models.FirmNature;

namespace CrmApi.Services.FirmNature
{
    public interface IFirmNatureService
    {
        Task<IEnumerable<FirmNatureResponse>> GetAllFirmNaturesAsync();
        Task<FirmNatureResponse?> GetFirmNatureByIdAsync(int id);
        Task<IEnumerable<FirmNatureResponse>> GetFirmNaturesByCategoryAsync(int categoryId);
        Task<FirmNatureResponse> CreateFirmNatureAsync(CreateFirmNatureRequest request);
        Task<FirmNatureResponse> UpdateFirmNatureAsync(int id, UpdateFirmNatureRequest request);
        Task<bool> DeleteFirmNatureAsync(int id);
    }
}
