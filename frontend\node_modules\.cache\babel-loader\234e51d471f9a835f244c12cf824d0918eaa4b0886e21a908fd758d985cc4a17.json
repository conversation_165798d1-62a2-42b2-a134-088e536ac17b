{"ast": null, "code": "import React from'react';import{Link,useLocation}from'react-router-dom';import{motion}from'framer-motion';import{FiHome,FiLayers,FiLogOut,FiUser,FiBarChart2,FiGrid,FiUsers,FiUpload,FiSettings}from'react-icons/fi';import{useAuth}from'../context/AuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Navbar=()=>{const location=useLocation();const{user,logout}=useAuth();const menuItems=[{path:'/',label:'Dashboard',icon:FiBarChart2},{path:'/divisions',label:'Division Setup',icon:FiGrid},{path:'/categories',label:'Categories',icon:FiLayers},{path:'/persons-view',label:'View Persons',icon:FiUsers},{path:'/persons',label:'Person Management',icon:FiUser},{path:'/import',label:'Import Persons',icon:FiUpload},{path:'/form-builder',label:'Form Builder',icon:FiSettings}];const handleLogout=()=>{logout();};return/*#__PURE__*/_jsxs(motion.div,{className:\"admin-sidebar\",initial:{x:-250},animate:{x:0},transition:{duration:0.3},children:[/*#__PURE__*/_jsx(\"div\",{className:\"sidebar-header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"admin-profile\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"profile-avatar\",children:/*#__PURE__*/_jsx(FiUser,{size:24})}),/*#__PURE__*/_jsxs(\"div\",{className:\"profile-info\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Admin Panel\"}),/*#__PURE__*/_jsxs(\"p\",{children:[user===null||user===void 0?void 0:user.firstName,\" \",user===null||user===void 0?void 0:user.lastName]})]})]})}),/*#__PURE__*/_jsx(\"nav\",{className:\"sidebar-nav\",children:menuItems.map((item,index)=>{const Icon=item.icon;const isActive=location.pathname===item.path;return/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:index*0.1},children:/*#__PURE__*/_jsxs(Link,{to:item.path,className:`sidebar-link ${isActive?'active':''}`,children:[/*#__PURE__*/_jsx(Icon,{className:\"sidebar-icon\"}),/*#__PURE__*/_jsx(\"span\",{children:item.label})]})},item.path);})}),/*#__PURE__*/_jsx(\"div\",{className:\"sidebar-footer\",children:/*#__PURE__*/_jsxs(motion.button,{className:\"logout-btn\",onClick:handleLogout,whileHover:{scale:1.05},whileTap:{scale:0.95},children:[/*#__PURE__*/_jsx(FiLogOut,{className:\"sidebar-icon\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Logout\"})]})})]});};export default Navbar;", "map": {"version": 3, "names": ["React", "Link", "useLocation", "motion", "FiHome", "FiLayers", "FiLogOut", "FiUser", "FiBarChart2", "<PERSON><PERSON><PERSON>", "FiUsers", "FiUpload", "FiSettings", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON>", "location", "user", "logout", "menuItems", "path", "label", "icon", "handleLogout", "div", "className", "initial", "x", "animate", "transition", "duration", "children", "size", "firstName", "lastName", "map", "item", "index", "Icon", "isActive", "pathname", "opacity", "delay", "to", "button", "onClick", "whileHover", "scale", "whileTap"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/Navbar.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport { motion } from 'framer-motion';\r\nimport { FiHome, FiLayers, FiLogOut, FiUser, FiBarChart2, FiGrid, FiUsers, FiUpload, FiSettings } from 'react-icons/fi';\r\nimport { useAuth } from '../context/AuthContext';\r\n\r\nconst Navbar = () => {\r\n  const location = useLocation();\r\n  const { user, logout } = useAuth();\r\n\r\n  const menuItems = [\r\n    { path: '/', label: 'Dashboard', icon: FiBarChart2 },\r\n    { path: '/divisions', label: 'Division Setup', icon: FiGrid },\r\n    { path: '/categories', label: 'Categories', icon: FiLayers },\r\n    { path: '/persons-view', label: 'View Persons', icon: FiUsers },\r\n    { path: '/persons', label: 'Person Management', icon: FiUser },\r\n    { path: '/import', label: 'Import Persons', icon: FiUpload },\r\n    { path: '/form-builder', label: 'Form Builder', icon: FiSettings }\r\n  ];\r\n\r\n  const handleLogout = () => {\r\n    logout();\r\n  };\r\n\r\n  return (\r\n    <motion.div \r\n      className=\"admin-sidebar\"\r\n      initial={{ x: -250 }}\r\n      animate={{ x: 0 }}\r\n      transition={{ duration: 0.3 }}\r\n    >\r\n      <div className=\"sidebar-header\">\r\n        <div className=\"admin-profile\">\r\n          <div className=\"profile-avatar\">\r\n            <FiUser size={24} />\r\n          </div>\r\n          <div className=\"profile-info\">\r\n            <h3>Admin Panel</h3>\r\n            <p>{user?.firstName} {user?.lastName}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <nav className=\"sidebar-nav\">\r\n        {menuItems.map((item, index) => {\r\n          const Icon = item.icon;\r\n          const isActive = location.pathname === item.path;\r\n          \r\n          return (\r\n            <motion.div\r\n              key={item.path}\r\n              initial={{ opacity: 0, x: -20 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ delay: index * 0.1 }}\r\n            >\r\n              <Link \r\n                to={item.path} \r\n                className={`sidebar-link ${isActive ? 'active' : ''}`}\r\n              >\r\n                <Icon className=\"sidebar-icon\" />\r\n                <span>{item.label}</span>\r\n              </Link>\r\n            </motion.div>\r\n          );\r\n        })}\r\n      </nav>\r\n\r\n      <div className=\"sidebar-footer\">\r\n        <motion.button\r\n          className=\"logout-btn\"\r\n          onClick={handleLogout}\r\n          whileHover={{ scale: 1.05 }}\r\n          whileTap={{ scale: 0.95 }}\r\n        >\r\n          <FiLogOut className=\"sidebar-icon\" />\r\n          <span>Logout</span>\r\n        </motion.button>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport default Navbar;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,MAAM,CAAEC,QAAQ,CAAEC,QAAQ,CAAEC,MAAM,CAAEC,WAAW,CAAEC,MAAM,CAAEC,OAAO,CAAEC,QAAQ,CAAEC,UAAU,KAAQ,gBAAgB,CACvH,OAASC,OAAO,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjD,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,KAAM,CAAAC,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEkB,IAAI,CAAEC,MAAO,CAAC,CAAGR,OAAO,CAAC,CAAC,CAElC,KAAM,CAAAS,SAAS,CAAG,CAChB,CAAEC,IAAI,CAAE,GAAG,CAAEC,KAAK,CAAE,WAAW,CAAEC,IAAI,CAAEjB,WAAY,CAAC,CACpD,CAAEe,IAAI,CAAE,YAAY,CAAEC,KAAK,CAAE,gBAAgB,CAAEC,IAAI,CAAEhB,MAAO,CAAC,CAC7D,CAAEc,IAAI,CAAE,aAAa,CAAEC,KAAK,CAAE,YAAY,CAAEC,IAAI,CAAEpB,QAAS,CAAC,CAC5D,CAAEkB,IAAI,CAAE,eAAe,CAAEC,KAAK,CAAE,cAAc,CAAEC,IAAI,CAAEf,OAAQ,CAAC,CAC/D,CAAEa,IAAI,CAAE,UAAU,CAAEC,KAAK,CAAE,mBAAmB,CAAEC,IAAI,CAAElB,MAAO,CAAC,CAC9D,CAAEgB,IAAI,CAAE,SAAS,CAAEC,KAAK,CAAE,gBAAgB,CAAEC,IAAI,CAAEd,QAAS,CAAC,CAC5D,CAAEY,IAAI,CAAE,eAAe,CAAEC,KAAK,CAAE,cAAc,CAAEC,IAAI,CAAEb,UAAW,CAAC,CACnE,CAED,KAAM,CAAAc,YAAY,CAAGA,CAAA,GAAM,CACzBL,MAAM,CAAC,CAAC,CACV,CAAC,CAED,mBACEJ,KAAA,CAACd,MAAM,CAACwB,GAAG,EACTC,SAAS,CAAC,eAAe,CACzBC,OAAO,CAAE,CAAEC,CAAC,CAAE,CAAC,GAAI,CAAE,CACrBC,OAAO,CAAE,CAAED,CAAC,CAAE,CAAE,CAAE,CAClBE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAC,QAAA,eAE9BnB,IAAA,QAAKa,SAAS,CAAC,gBAAgB,CAAAM,QAAA,cAC7BjB,KAAA,QAAKW,SAAS,CAAC,eAAe,CAAAM,QAAA,eAC5BnB,IAAA,QAAKa,SAAS,CAAC,gBAAgB,CAAAM,QAAA,cAC7BnB,IAAA,CAACR,MAAM,EAAC4B,IAAI,CAAE,EAAG,CAAE,CAAC,CACjB,CAAC,cACNlB,KAAA,QAAKW,SAAS,CAAC,cAAc,CAAAM,QAAA,eAC3BnB,IAAA,OAAAmB,QAAA,CAAI,aAAW,CAAI,CAAC,cACpBjB,KAAA,MAAAiB,QAAA,EAAId,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEgB,SAAS,CAAC,GAAC,CAAChB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEiB,QAAQ,EAAI,CAAC,EACtC,CAAC,EACH,CAAC,CACH,CAAC,cAENtB,IAAA,QAAKa,SAAS,CAAC,aAAa,CAAAM,QAAA,CACzBZ,SAAS,CAACgB,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CAC9B,KAAM,CAAAC,IAAI,CAAGF,IAAI,CAACd,IAAI,CACtB,KAAM,CAAAiB,QAAQ,CAAGvB,QAAQ,CAACwB,QAAQ,GAAKJ,IAAI,CAAChB,IAAI,CAEhD,mBACER,IAAA,CAACZ,MAAM,CAACwB,GAAG,EAETE,OAAO,CAAE,CAAEe,OAAO,CAAE,CAAC,CAAEd,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCC,OAAO,CAAE,CAAEa,OAAO,CAAE,CAAC,CAAEd,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEa,KAAK,CAAEL,KAAK,CAAG,GAAI,CAAE,CAAAN,QAAA,cAEnCjB,KAAA,CAAChB,IAAI,EACH6C,EAAE,CAAEP,IAAI,CAAChB,IAAK,CACdK,SAAS,CAAE,gBAAgBc,QAAQ,CAAG,QAAQ,CAAG,EAAE,EAAG,CAAAR,QAAA,eAEtDnB,IAAA,CAAC0B,IAAI,EAACb,SAAS,CAAC,cAAc,CAAE,CAAC,cACjCb,IAAA,SAAAmB,QAAA,CAAOK,IAAI,CAACf,KAAK,CAAO,CAAC,EACrB,CAAC,EAXFe,IAAI,CAAChB,IAYA,CAAC,CAEjB,CAAC,CAAC,CACC,CAAC,cAENR,IAAA,QAAKa,SAAS,CAAC,gBAAgB,CAAAM,QAAA,cAC7BjB,KAAA,CAACd,MAAM,CAAC4C,MAAM,EACZnB,SAAS,CAAC,YAAY,CACtBoB,OAAO,CAAEtB,YAAa,CACtBuB,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAAAhB,QAAA,eAE1BnB,IAAA,CAACT,QAAQ,EAACsB,SAAS,CAAC,cAAc,CAAE,CAAC,cACrCb,IAAA,SAAAmB,QAAA,CAAM,QAAM,CAAM,CAAC,EACN,CAAC,CACb,CAAC,EACI,CAAC,CAEjB,CAAC,CAED,cAAe,CAAAhB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}