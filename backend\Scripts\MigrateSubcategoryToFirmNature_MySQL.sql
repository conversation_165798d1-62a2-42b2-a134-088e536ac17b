-- =====================================================
-- Migration Script: Subcategory to Firm Nature (MySQL)
-- Description: Rename subcategories table to firm_natures and update all related references
-- Database: MySQL
-- Author: CRM System Migration
-- Date: 2025-01-01
-- =====================================================

USE data_crm;

SELECT 'Starting migration from Subcategory to Firm Nature...' AS Status;

-- =====================================================
-- STEP 1: BACKUP EXISTING DATA (Optional but recommended)
-- =====================================================

SELECT 'Step 1: Creating backup tables...' AS Status;

-- Backup sub_categories table
DROP TABLE IF EXISTS sub_categories_backup;
CREATE TABLE sub_categories_backup AS SELECT * FROM sub_categories;
SELECT 'Backup of sub_categories table created as sub_categories_backup' AS Status;

-- Backup persons table structure for reference
DROP TABLE IF EXISTS persons_structure_backup;
CREATE TABLE persons_structure_backup LIKE persons;
SELECT 'Backup of persons table structure created as persons_structure_backup' AS Status;

-- =====================================================
-- STEP 2: DROP FOREIGN KEY CONSTRAINTS
-- =====================================================

SELECT 'Step 2: Dropping foreign key constraints...' AS Status;

-- Drop foreign key constraint from persons table
SET @sql = (SELECT CONCAT('ALTER TABLE persons DROP FOREIGN KEY ', CONSTRAINT_NAME)
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = 'data_crm'
            AND TABLE_NAME = 'persons'
            AND COLUMN_NAME = 'sub_category_id'
            AND REFERENCED_TABLE_NAME IS NOT NULL);

SET @sql = IFNULL(@sql, 'SELECT "No FK constraint found for sub_category_id" AS Status');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT 'Dropped foreign key constraint on sub_category_id' AS Status;

-- =====================================================
-- STEP 3: DROP EXISTING INDEXES
-- =====================================================

SELECT 'Step 3: Dropping existing indexes...' AS Status;

-- Drop indexes on sub_categories table
DROP INDEX IF EXISTS IX_sub_categories_category_id ON sub_categories;
DROP INDEX IF EXISTS IX_sub_categories_category_id_name ON sub_categories;
SELECT 'Dropped indexes on sub_categories table' AS Status;

-- Drop indexes on persons table related to sub_category
DROP INDEX IF EXISTS IX_persons_sub_category_id ON persons;
SELECT 'Dropped sub_category_id index on persons table' AS Status;

-- =====================================================
-- STEP 4: RENAME SUB_CATEGORIES TABLE TO FIRM_NATURES
-- =====================================================

SELECT 'Step 4: Renaming sub_categories table to firm_natures...' AS Status;

-- Rename the table
RENAME TABLE sub_categories TO firm_natures;
SELECT 'Renamed sub_categories table to firm_natures' AS Status;

-- =====================================================
-- STEP 5: RENAME COLUMN IN PERSONS TABLE
-- =====================================================

SELECT 'Step 5: Renaming sub_category_id column to firm_nature_id in persons table...' AS Status;

-- Rename the column
ALTER TABLE persons CHANGE COLUMN sub_category_id firm_nature_id INT NULL;
SELECT 'Renamed sub_category_id column to firm_nature_id in persons table' AS Status;

-- =====================================================
-- STEP 6: CREATE NEW INDEXES
-- =====================================================

SELECT 'Step 6: Creating new indexes...' AS Status;

-- Create indexes on firm_natures table
CREATE INDEX IX_firm_natures_category_id ON firm_natures(category_id);
CREATE INDEX IX_firm_natures_category_id_name ON firm_natures(category_id, name);
SELECT 'Created indexes on firm_natures table' AS Status;

-- Create index on persons table for firm_nature_id
CREATE INDEX IX_persons_firm_nature_id ON persons(firm_nature_id);
SELECT 'Created firm_nature_id index on persons table' AS Status;

-- =====================================================
-- STEP 7: RECREATE FOREIGN KEY CONSTRAINT
-- =====================================================

SELECT 'Step 7: Creating new foreign key constraint...' AS Status;

-- Add foreign key constraint from persons to firm_natures
ALTER TABLE persons 
ADD CONSTRAINT fk_persons_firm_natures 
FOREIGN KEY (firm_nature_id) REFERENCES firm_natures(id)
ON DELETE SET NULL ON UPDATE CASCADE;

SELECT 'Created foreign key constraint fk_persons_firm_natures' AS Status;

-- =====================================================
-- STEP 8: CREATE PARTITIONING ON FIRM_NATURE_ID
-- =====================================================

SELECT 'Step 8: Setting up partitioning on firm_nature_id...' AS Status;

-- First, we need to drop the existing persons table and recreate it with partitioning
-- This is necessary because MySQL requires partitioning to be defined at table creation

-- Create temporary table to hold persons data
CREATE TABLE persons_temp AS SELECT * FROM persons;
SELECT 'Created temporary table with persons data' AS Status;

-- Drop the original persons table
DROP TABLE persons;
SELECT 'Dropped original persons table' AS Status;

-- Recreate persons table with partitioning
CREATE TABLE persons (
    id INT NOT NULL AUTO_INCREMENT,
    division_id INT NOT NULL,
    category_id INT NOT NULL,
    firm_nature_id INT NULL,
    name VARCHAR(255) NOT NULL,
    mobile_number VARCHAR(15) NOT NULL,
    nature INT NOT NULL,
    gender INT NULL,
    alternate_numbers JSON NULL,
    primary_email_id VARCHAR(255) NULL,
    alternate_email_ids JSON NULL,
    website VARCHAR(500) NULL,
    date_of_birth DATETIME NULL,
    is_married BOOLEAN NULL,
    date_of_marriage DATETIME NULL,
    working_state INT NULL,
    district VARCHAR(100) NULL,
    pin_code VARCHAR(10) NULL,
    address_line_1 VARCHAR(500) NULL,
    address_line_2 VARCHAR(500) NULL,
    landmark VARCHAR(255) NULL,
    star_rating INT NULL,
    remarks TEXT NULL,
    firm_name VARCHAR(255) NULL,
    number_of_offices INT NULL,
    number_of_branches INT NULL,
    total_employee_strength INT NULL,
    authorized_person_name VARCHAR(255) NULL,
    authorized_person_email VARCHAR(255) NULL,
    designation VARCHAR(100) NULL,
    marketing_contact VARCHAR(255) NULL,
    marketing_designation VARCHAR(100) NULL,
    place_of_posting VARCHAR(255) NULL,
    department VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP NULL,
    PRIMARY KEY (id, firm_nature_id),
    INDEX idx_division_id (division_id),
    INDEX idx_category_id (category_id),
    INDEX idx_firm_nature_id (firm_nature_id),
    INDEX idx_mobile_number (mobile_number),
    INDEX idx_name (name),
    INDEX idx_primary_email_id (primary_email_id),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_created_at (created_at),
    INDEX idx_composite_division_category (division_id, category_id),
    INDEX idx_composite_mobile_division_category (mobile_number, division_id, category_id),
    UNIQUE KEY uk_mobile_division_category (mobile_number, division_id, category_id, is_deleted)
)
PARTITION BY HASH(IFNULL(firm_nature_id, 0))
PARTITIONS 16;

SELECT 'Created partitioned persons table' AS Status;

-- Insert data back from temporary table
INSERT INTO persons SELECT * FROM persons_temp;
SELECT 'Restored data to partitioned persons table' AS Status;

-- Drop temporary table
DROP TABLE persons_temp;
SELECT 'Dropped temporary table' AS Status;

-- =====================================================
-- STEP 9: RECREATE FOREIGN KEY CONSTRAINTS
-- =====================================================

SELECT 'Step 9: Creating foreign key constraints...' AS Status;

-- Add foreign key constraints
ALTER TABLE persons 
ADD CONSTRAINT fk_persons_divisions 
FOREIGN KEY (division_id) REFERENCES divisions(id);

ALTER TABLE persons 
ADD CONSTRAINT fk_persons_categories 
FOREIGN KEY (category_id) REFERENCES categories(id);

ALTER TABLE persons 
ADD CONSTRAINT fk_persons_firm_natures 
FOREIGN KEY (firm_nature_id) REFERENCES firm_natures(id)
ON DELETE SET NULL ON UPDATE CASCADE;

SELECT 'Created all foreign key constraints' AS Status;

-- =====================================================
-- STEP 10: CREATE ADDITIONAL PARTITIONED INDEXES
-- =====================================================

SELECT 'Step 10: Creating additional partitioned indexes...' AS Status;

-- Create additional indexes for better performance
CREATE INDEX idx_persons_name_firm_nature ON persons(name, firm_nature_id, division_id);
CREATE INDEX idx_persons_created_at_firm_nature ON persons(created_at, firm_nature_id, is_deleted);
CREATE INDEX idx_persons_nature_firm_nature ON persons(nature, firm_nature_id);

SELECT 'Created additional partitioned indexes' AS Status;

-- =====================================================
-- STEP 11: UPDATE STATISTICS (MySQL equivalent)
-- =====================================================

SELECT 'Step 11: Analyzing tables for statistics...' AS Status;

-- Analyze tables to update statistics
ANALYZE TABLE firm_natures;
ANALYZE TABLE persons;

SELECT 'Updated table statistics' AS Status;

-- =====================================================
-- STEP 12: VERIFICATION
-- =====================================================

SELECT 'Step 12: Verifying migration...' AS Status;

-- Verify table exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'firm_natures table exists'
        ELSE 'ERROR: firm_natures table does not exist'
    END AS verification_result
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'firm_natures';

-- Verify column exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'firm_nature_id column exists in persons table'
        ELSE 'ERROR: firm_nature_id column does not exist in persons table'
    END AS verification_result
FROM information_schema.columns 
WHERE table_schema = 'data_crm' AND table_name = 'persons' AND column_name = 'firm_nature_id';

-- Verify foreign key constraint
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'fk_persons_firm_natures constraint exists'
        ELSE 'ERROR: fk_persons_firm_natures constraint does not exist'
    END AS verification_result
FROM information_schema.key_column_usage 
WHERE table_schema = 'data_crm' 
AND table_name = 'persons' 
AND column_name = 'firm_nature_id' 
AND referenced_table_name = 'firm_natures';

-- Verify partitioning
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('Persons table has ', COUNT(*), ' partitions')
        ELSE 'ERROR: Persons table is not partitioned'
    END AS verification_result
FROM information_schema.partitions 
WHERE table_schema = 'data_crm' AND table_name = 'persons' AND partition_name IS NOT NULL;

-- Show record counts
SELECT 
    (SELECT COUNT(*) FROM firm_natures) AS firm_nature_count,
    (SELECT COUNT(*) FROM persons) AS persons_count;

-- =====================================================
-- FINAL STATUS
-- =====================================================

SELECT '==================================================' AS Status;
SELECT 'MIGRATION COMPLETED SUCCESSFULLY!' AS Status;
SELECT '==================================================' AS Status;
SELECT '' AS Status;
SELECT 'Summary of changes:' AS Status;
SELECT '✓ Renamed subcategories table to firm_natures' AS Status;
SELECT '✓ Renamed subcategory_id column to firm_nature_id' AS Status;
SELECT '✓ Updated all foreign key constraints' AS Status;
SELECT '✓ Recreated all indexes with new names' AS Status;
SELECT '✓ Created partitioning on firm_nature_id (16 partitions)' AS Status;
SELECT '✓ Created additional indexes for performance' AS Status;
SELECT '✓ Updated table statistics' AS Status;
SELECT '' AS Status;
SELECT 'Next steps:' AS Status;
SELECT '1. Update your application code to use new table/column names' AS Status;
SELECT '2. Update any stored procedures or views' AS Status;
SELECT '3. Test the application thoroughly' AS Status;
SELECT '4. Remove backup tables when confident migration is successful' AS Status;
SELECT '' AS Status;
