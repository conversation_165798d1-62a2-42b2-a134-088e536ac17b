using CrmApi.Models.FirmNature;
using CrmApi.Models.Category;
using CrmApi.Models.Division;
using CrmApi.Repositories.FirmNature;
using CrmApi.Repositories.Category;
using CrmApi.Exceptions;

namespace CrmApi.Services.FirmNature
{
    public class FirmNatureService : IFirmNatureService
    {
        private readonly IFirmNatureRepository _firmNatureRepository;
        private readonly ICategoryRepository _categoryRepository;

        public FirmNatureService(IFirmNatureRepository firmNatureRepository, ICategoryRepository categoryRepository)
        {
            _firmNatureRepository = firmNatureRepository;
            _categoryRepository = categoryRepository;
        }

        public async Task<IEnumerable<FirmNatureResponse>> GetAllFirmNaturesAsync()
        {
            var firmNatures = await _firmNatureRepository.GetAllWithRelationsAsync();
            return firmNatures.Select(MapToResponse);
        }

        public async Task<FirmNatureResponse?> GetFirmNatureByIdAsync(int id)
        {
            var firmNature = await _firmNatureRepository.GetByIdWithRelationsAsync(id);
            return firmNature != null ? MapToResponse(firmNature) : null;
        }

        public async Task<IEnumerable<FirmNatureResponse>> GetFirmNaturesByCategoryAsync(int categoryId)
        {
            var firmNatures = await _firmNatureRepository.GetByCategoryIdAsync(categoryId);
            return firmNatures.Select(fn => new FirmNatureResponse
            {
                Id = fn.Id,
                CategoryId = fn.CategoryId,
                Name = fn.Name,
                CreatedAt = fn.CreatedAt,
                UpdatedAt = fn.UpdatedAt
            });
        }

        public async Task<FirmNatureResponse> CreateFirmNatureAsync(CreateFirmNatureRequest request)
        {
            // Check if category exists
            if (!await _categoryRepository.ExistsAsync(request.CategoryId))
                throw new NotFoundException($"Category with ID {request.CategoryId} not found");

            // Check if name already exists in category
            if (await _firmNatureRepository.NameExistsInCategoryAsync(request.Name, request.CategoryId))
                throw new BusinessException($"Firm Nature with name '{request.Name}' already exists in this category");

            var firmNature = new Models.FirmNature.FirmNature
            {
                Name = request.Name,
                CategoryId = request.CategoryId
            };

            var createdFirmNature = await _firmNatureRepository.CreateAsync(firmNature);

            // Get the firm nature with relations for response
            var firmNatureWithRelations = await _firmNatureRepository.GetByIdWithRelationsAsync(createdFirmNature.Id);
            return MapToResponse(firmNatureWithRelations!);
        }

        public async Task<FirmNatureResponse> UpdateFirmNatureAsync(int id, UpdateFirmNatureRequest request)
        {
            var firmNature = await _firmNatureRepository.GetByIdAsync(id);
            if (firmNature == null)
                throw new NotFoundException($"Firm Nature with ID {id} not found");

            // Check if category exists
            if (!await _categoryRepository.ExistsAsync(request.CategoryId))
                throw new NotFoundException($"Category with ID {request.CategoryId} not found");

            // Check name uniqueness in category
            if (await _firmNatureRepository.NameExistsInCategoryAsync(request.Name, request.CategoryId, id))
                throw new BusinessException($"Firm Nature with name '{request.Name}' already exists in this category");

            firmNature.Name = request.Name;
            firmNature.CategoryId = request.CategoryId;

            var updatedFirmNature = await _firmNatureRepository.UpdateAsync(firmNature);

            // Get the firm nature with relations for response
            var firmNatureWithRelations = await _firmNatureRepository.GetByIdWithRelationsAsync(updatedFirmNature.Id);
            return MapToResponse(firmNatureWithRelations!);
        }

        public async Task<bool> DeleteFirmNatureAsync(int id)
        {
            if (!await _firmNatureRepository.ExistsAsync(id))
                throw new NotFoundException($"Firm Nature with ID {id} not found");

            return await _firmNatureRepository.DeleteAsync(id);
        }

        private FirmNatureResponse MapToResponse(Models.FirmNature.FirmNature firmNature)
        {
            return new FirmNatureResponse
            {
                Id = firmNature.Id,
                CategoryId = firmNature.CategoryId,
                Name = firmNature.Name,
                CreatedAt = firmNature.CreatedAt,
                UpdatedAt = firmNature.UpdatedAt,
                Category = firmNature.Category != null ? new CategoryResponse
                {
                    Id = firmNature.Category.Id,
                    DivisionId = firmNature.Category.DivisionId,
                    Name = firmNature.Category.Name,
                    CreatedAt = firmNature.Category.CreatedAt,
                    UpdatedAt = firmNature.Category.UpdatedAt,
                    Division = firmNature.Category.Division != null ? new DivisionResponse
                    {
                        Id = firmNature.Category.Division.Id,
                        Name = firmNature.Category.Division.Name,
                        CreatedAt = firmNature.Category.Division.CreatedAt,
                        UpdatedAt = firmNature.Category.Division.UpdatedAt
                    } : null
                } : null
            };
        }
    }
}
