{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\FormBuilder.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FieldConfigModal from './FieldConfigModal';\nimport FormPreview from './FormPreview';\nimport './FormBuilder.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FormBuilder = ({\n  onSave,\n  onCancel,\n  initialConfig = null\n}) => {\n  _s();\n  const [selectedHierarchy, setSelectedHierarchy] = useState({});\n  const [formName, setFormName] = useState('');\n  const [formDescription, setFormDescription] = useState('');\n  const [availableFields, setAvailableFields] = useState([]);\n  const [selectedFields, setSelectedFields] = useState([]);\n  const [currentSection, setCurrentSection] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n  const [showFieldConfig, setShowFieldConfig] = useState(false);\n  const [configField, setConfigField] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [saving, setSaving] = useState(false);\n  const [savedForms, setSavedForms] = useState([]);\n  const [showSavedForms, setShowSavedForms] = useState(false);\n\n  // State for dropdown data\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [firmNatures, setFirmNatures] = useState([]);\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    firmNatures: false\n  });\n  const [existingFormInfo, setExistingFormInfo] = useState(null);\n  const [isEditMode, setIsEditMode] = useState(false);\n  const [editingFormId, setEditingFormId] = useState(null);\n  useEffect(() => {\n    initializeFields();\n    loadSavedForms();\n    if (initialConfig) {\n      loadInitialConfig(initialConfig);\n    }\n  }, [initialConfig]);\n  const initializeFields = () => {\n    const allFields = getAllPersonFields();\n    setAvailableFields(allFields);\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = fields => {\n    const seen = new Set();\n    const deduplicated = [];\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      }\n    });\n    return deduplicated;\n  };\n  const loadSavedForms = () => {\n    const forms = formConfigService.getAllFormConfigs();\n    setSavedForms(forms);\n  };\n  const loadInitialConfig = config => {\n    setFormName(config.name || '');\n    setFormDescription(config.description || '');\n\n    // Deduplicate fields when loading initial config\n    const fields = config.fields || [];\n    const deduplicatedFields = deduplicateFields(fields);\n    setSelectedFields(deduplicatedFields);\n\n    // Set edit mode when loading existing form\n    setIsEditMode(true);\n    setEditingFormId(config.id || config.key);\n    if (config.type === 'division' && config.associatedId) {\n      // Load division info for hierarchy selector\n      setSelectedHierarchy({\n        divisionId: config.associatedId\n      });\n    } else if (config.type === 'category' && config.associatedId) {\n      // Load category info for hierarchy selector\n      setSelectedHierarchy({\n        categoryId: config.associatedId\n      });\n    } else if (config.type === 'subcategory' && config.associatedId) {\n      // Load subcategory info for hierarchy selector\n      setSelectedHierarchy({\n        subCategoryId: config.associatedId\n      });\n    }\n\n    // If hierarchy is stored in config, use that\n    if (config.hierarchy) {\n      setSelectedHierarchy(config.hierarchy);\n    }\n  };\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n  }, []);\n\n  // Validate form creation rules when hierarchy changes\n  useEffect(() => {\n    if (selectedHierarchy.categoryId) {\n      // Skip validation in edit mode since we're updating an existing form\n      if (!isEditMode) {\n        const formValidation = formConfigService.validateFormCreation(selectedHierarchy.categoryId, selectedHierarchy.subCategoryId);\n        if (!formValidation.isValid) {\n          setErrors(prev => ({\n            ...prev,\n            formCreation: formValidation.errors.join('. ')\n          }));\n        } else {\n          setErrors(prev => {\n            const newErrors = {\n              ...prev\n            };\n            delete newErrors.formCreation;\n            return newErrors;\n          });\n        }\n      } else {\n        // In edit mode, clear any form creation errors\n        setErrors(prev => {\n          const newErrors = {\n            ...prev\n          };\n          delete newErrors.formCreation;\n          return newErrors;\n        });\n      }\n      const formInfo = formConfigService.getExistingFormInfo(selectedHierarchy.categoryId, selectedHierarchy.subCategoryId);\n      setExistingFormInfo(formInfo);\n    } else {\n      setExistingFormInfo(null);\n      setErrors(prev => {\n        const newErrors = {\n          ...prev\n        };\n        delete newErrors.formCreation;\n        return newErrors;\n      });\n    }\n  }, [selectedHierarchy.categoryId, selectedHierarchy.subCategoryId, isEditMode]);\n  const loadDivisions = async () => {\n    setLoading(prev => ({\n      ...prev,\n      divisions: true\n    }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        divisions: false\n      }));\n    }\n  };\n  const loadCategories = async divisionId => {\n    if (!divisionId) {\n      setCategories([]);\n      setFirmNatures([]);\n      return;\n    }\n    setLoading(prev => ({\n      ...prev,\n      categories: true\n    }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        categories: false\n      }));\n    }\n  };\n  const loadFirmNatures = async categoryId => {\n    if (!categoryId) {\n      setFirmNatures([]);\n      return;\n    }\n    setLoading(prev => ({\n      ...prev,\n      firmNatures: true\n    }));\n    try {\n      const response = await apiService.getFirmNaturesByCategory(categoryId);\n      setFirmNatures(response.data || []);\n    } catch (error) {\n      console.error('Error loading firm natures:', error);\n      setFirmNatures([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        firmNatures: false\n      }));\n    }\n  };\n  const handleDivisionChange = e => {\n    const divisionId = e.target.value;\n    const division = divisions.find(d => d.id === parseInt(divisionId));\n    setSelectedHierarchy({\n      divisionId: divisionId || null,\n      categoryId: null,\n      firmNatureId: null,\n      division: division || null,\n      category: null,\n      firmNature: null\n    });\n    setCategories([]);\n    setFirmNatures([]);\n    if (divisionId) {\n      loadCategories(divisionId);\n    }\n  };\n  const handleCategoryChange = e => {\n    const categoryId = e.target.value;\n    const category = categories.find(c => c.id === parseInt(categoryId));\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      categoryId: categoryId || null,\n      firmNatureId: null,\n      category: category || null,\n      firmNature: null\n    }));\n    setFirmNatures([]);\n    if (categoryId) {\n      loadFirmNatures(categoryId);\n    }\n  };\n  const handleSubCategoryChange = e => {\n    const subCategoryId = e.target.value;\n    const subCategory = subCategories.find(sc => sc.id === parseInt(subCategoryId));\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      subCategoryId: subCategoryId || null,\n      subCategory: subCategory || null\n    }));\n  };\n  const handleFieldToggle = field => {\n    const isSelected = selectedFields.some(f => f.key === field.key);\n    if (isSelected) {\n      // Remove all instances of this field key (in case there are duplicates)\n      setSelectedFields(prev => prev.filter(f => f.key !== field.key));\n    } else {\n      // Add field only if it doesn't already exist\n      setSelectedFields(prev => {\n        const exists = prev.some(f => f.key === field.key);\n        if (exists) {\n          return prev;\n        }\n        return [...prev, {\n          ...field\n        }];\n      });\n    }\n  };\n  const handleFieldConfig = field => {\n    setConfigField(field);\n    setShowFieldConfig(true);\n  };\n  const handleFieldConfigSave = updatedField => {\n    setSelectedFields(prev => prev.map(f => f.key === updatedField.key ? updatedField : f));\n    setShowFieldConfig(false);\n    setConfigField(null);\n  };\n  const handleFieldMove = (fromIndex, toIndex) => {\n    const newFields = [...selectedFields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n    setSelectedFields(newFields);\n  };\n  const handleSelectAllFields = () => {\n    // Get all filtered fields (respects current section and search)\n    const fieldsToAdd = filteredFields.filter(field => !selectedFields.some(selected => selected.key === field.key));\n    setSelectedFields(prev => [...prev, ...fieldsToAdd]);\n  };\n  const handleDeselectAllFields = () => {\n    // Remove all filtered fields from selection\n    const filteredFieldKeys = filteredFields.map(field => field.key);\n    setSelectedFields(prev => prev.filter(field => !filteredFieldKeys.includes(field.key)));\n  };\n  const handleSelectAllAvailableFields = () => {\n    // Select all available fields regardless of current filter\n    const fieldsToAdd = availableFields.filter(field => !selectedFields.some(selected => selected.key === field.key));\n    setSelectedFields(prev => [...prev, ...fieldsToAdd]);\n  };\n  const handleClearAllFields = () => {\n    setSelectedFields([]);\n  };\n  const handleSave = async () => {\n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n    setSaving(true);\n    try {\n      // Ensure complete hierarchy information is included\n      const completeHierarchy = {\n        ...selectedHierarchy,\n        // Include division info (required)\n        divisionId: selectedHierarchy.divisionId,\n        division: selectedHierarchy.division,\n        // Include category info (required)\n        categoryId: selectedHierarchy.categoryId,\n        category: selectedHierarchy.category,\n        // Include subcategory info if selected (optional)\n        subCategoryId: selectedHierarchy.subCategoryId || null,\n        subCategory: selectedHierarchy.subCategory || null\n      };\n      const config = {\n        name: formName,\n        description: formDescription,\n        fields: selectedFields,\n        hierarchy: completeHierarchy,\n        settings: {\n          showSections: true,\n          allowConditionalFields: true,\n          validateOnChange: true\n        }\n      };\n      let savedConfig;\n      if (isEditMode) {\n        // In edit mode, update the existing form\n        // Preserve the original type and associatedId from the form being edited\n        const originalForm = formConfigService.getAllFormConfigs().find(f => f.id === editingFormId || f.key === editingFormId);\n        if (originalForm) {\n          // Update the existing form with new data\n          config.id = originalForm.id;\n          config.key = originalForm.key;\n          config.type = originalForm.type;\n          config.associatedId = originalForm.associatedId;\n          config.createdAt = originalForm.createdAt; // Preserve creation date\n          config.updatedAt = new Date().toISOString(); // Update modification date\n\n          savedConfig = formConfigService.saveFormConfig(originalForm.type, originalForm.associatedId, config);\n        } else {\n          throw new Error('Original form not found for editing');\n        }\n      } else {\n        // Create new form with the most specific level selected, but include complete hierarchy\n        if (selectedHierarchy.subCategoryId) {\n          savedConfig = formConfigService.saveFormConfig('subcategory', selectedHierarchy.subCategoryId, config);\n        } else if (selectedHierarchy.categoryId) {\n          savedConfig = formConfigService.saveFormConfig('category', selectedHierarchy.categoryId, config);\n        } else {\n          throw new Error('Please select both division and category');\n        }\n      }\n\n      // Reload saved forms list\n      loadSavedForms();\n      if (onSave) {\n        onSave(savedConfig);\n      } else {\n        // Show success message for standalone usage\n        const message = isEditMode ? 'Form updated successfully!' : 'Form configuration saved successfully!';\n        alert(message);\n        // Reset form\n        setFormName('');\n        setFormDescription('');\n        setSelectedFields([]);\n        setSelectedHierarchy({});\n        setIsEditMode(false);\n        setEditingFormId(null);\n      }\n    } catch (error) {\n      console.error('Error saving form:', error);\n      setErrors({\n        general: error.message\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n  const validateForm = () => {\n    const errors = {};\n    if (!formName.trim()) {\n      errors.formName = 'Form name is required';\n    }\n\n    // Division and category are required, subcategory is optional\n    if (!selectedHierarchy.divisionId) {\n      errors.hierarchy = 'Please select a division';\n    } else if (!selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Please select a category';\n    } else if (selectedHierarchy.subCategoryId && !selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Cannot select subcategory without selecting category';\n    }\n\n    // Validate form creation rules (skip in edit mode)\n    if (selectedHierarchy.categoryId && !isEditMode) {\n      const formValidation = formConfigService.validateFormCreation(selectedHierarchy.categoryId, selectedHierarchy.subCategoryId);\n      if (!formValidation.isValid) {\n        errors.formCreation = formValidation.errors.join('. ');\n      }\n    }\n    if (selectedFields.length === 0) {\n      errors.fields = 'Please select at least one field';\n    }\n\n    // Check required fields\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = selectedFields.map(f => f.key);\n    const missingRequired = requiredFields.filter(rf => !selectedFieldKeys.includes(rf));\n    if (missingRequired.length > 0) {\n      errors.requiredFields = `Required fields missing: ${missingRequired.join(', ')}`;\n    }\n    return {\n      isValid: Object.keys(errors).length === 0,\n      errors\n    };\n  };\n  const getFilteredFields = () => {\n    let filtered = availableFields;\n\n    // Filter by section\n    if (currentSection !== 'all') {\n      filtered = filtered.filter(field => field.section === currentSection);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(field => field.label.toLowerCase().includes(searchTerm.toLowerCase()) || field.key.toLowerCase().includes(searchTerm.toLowerCase()));\n    }\n    return filtered;\n  };\n  const getSectionCounts = () => {\n    const counts = {\n      all: availableFields.length\n    };\n    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {\n      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;\n    });\n    return counts;\n  };\n  const sectionCounts = getSectionCounts();\n  const filteredFields = getFilteredFields();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-builder\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-builder-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Form Builder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => setShowPreview(true),\n          className: \"btn btn-secondary\",\n          disabled: selectedFields.length === 0,\n          children: \"Preview Form\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleSave,\n          className: \"btn btn-primary\",\n          disabled: saving || errors.formCreation,\n          children: saving ? isEditMode ? 'Updating...' : 'Saving...' : isEditMode ? 'Update Form' : 'Save Form'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this), onCancel && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 9\n    }, this), Object.keys(errors).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"validation-errors\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Please fix the following issues:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: Object.entries(errors).map(([key, message]) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: message\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 533,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-builder-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"config-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Saved Forms (\", savedForms.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn-link\",\n              onClick: () => setShowSavedForms(!showSavedForms),\n              children: showSavedForms ? 'Hide' : 'Show'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 13\n          }, this), showSavedForms && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"saved-forms-list\",\n            children: savedForms.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-forms-message\",\n              children: \"No saved forms yet. Create your first form below!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 19\n            }, this) : savedForms.map(form => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"saved-form-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-item-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: form.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"form-type-badge\",\n                  children: form.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-description\",\n                children: form.description || 'No description'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [form.summary.fieldCount, \" fields\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Updated \", new Date(form.updatedAt).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn-small btn-outline\",\n                  onClick: () => loadInitialConfig(form),\n                  children: \"Load\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn-small btn-danger\",\n                  onClick: () => {\n                    if (window.confirm('Are you sure you want to delete this form?')) {\n                      formConfigService.deleteFormConfig(form.type, form.associatedId);\n                      loadSavedForms();\n                    }\n                  },\n                  children: \"Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 23\n              }, this)]\n            }, form.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Form Configuration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Form Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formName,\n              onChange: e => setFormName(e.target.value),\n              placeholder: \"Enter form name\",\n              className: errors.formName ? 'error' : ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 15\n            }, this), errors.formName && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"error-message\",\n              children: errors.formName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: formDescription,\n              onChange: e => setFormDescription(e.target.value),\n              placeholder: \"Enter form description\",\n              rows: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Selected Fields (\", selectedFields.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 13\n          }, this), errors.fields && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.fields\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 31\n          }, this), errors.requiredFields && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.requiredFields\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 39\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-fields\",\n            children: selectedFields.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: \"No fields selected. Choose fields from the available fields panel.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 17\n            }, this) : selectedFields.map((field, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"selected-field\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"field-label\",\n                  children: field.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"field-type\",\n                  children: field.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 23\n                }, this), field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"required-badge\",\n                  children: \"Required\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldConfig(field),\n                  className: \"btn-icon\",\n                  title: \"Configure field\",\n                  children: \"\\u2699\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldMove(index, Math.max(0, index - 1)),\n                  className: \"btn-icon\",\n                  disabled: index === 0,\n                  title: \"Move up\",\n                  children: \"\\u2191\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1)),\n                  className: \"btn-icon\",\n                  disabled: index === selectedFields.length - 1,\n                  title: \"Move down\",\n                  children: \"\\u2193\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldToggle(field),\n                  className: \"btn-icon remove\",\n                  title: \"Remove field\",\n                  children: \"\\u2715\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 21\n              }, this)]\n            }, field.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fields-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Available Fields\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 697,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hierarchy-selection\",\n          style: {\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #e1e5e9',\n            borderRadius: '8px',\n            padding: '1.5rem',\n            margin: '1rem 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 1rem 0',\n              color: '#495057'\n            },\n            children: \"Associate with Division & Category (Required) / SubCategory (Optional) *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6c757d',\n              marginBottom: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8f9fa',\n              borderRadius: '4px',\n              border: '1px solid #e9ecef'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Form Creation Rules:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              style: {\n                margin: '0.5rem 0 0 1rem',\n                paddingLeft: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"If you create a form for a category, you cannot create forms for its subcategories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Each subcategory can have only one form\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"If subcategories already have forms, you cannot create a form for the parent category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hierarchy-dropdowns\",\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"Division *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.divisionId || '',\n                onChange: handleDivisionChange,\n                disabled: loading.divisions,\n                className: errors.divisionId ? 'error' : '',\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Division\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 758,\n                  columnNumber: 19\n                }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: division.id,\n                  children: division.name\n                }, division.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 760,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 17\n              }, this), errors.divisionId && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#dc3545',\n                  fontSize: '0.875rem',\n                  marginTop: '0.25rem'\n                },\n                children: errors.divisionId\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"Category *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.categoryId || '',\n                onChange: handleCategoryChange,\n                disabled: !selectedHierarchy.divisionId || loading.categories,\n                className: errors.categoryId ? 'error' : '',\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: !selectedHierarchy.divisionId ? 'Select Division first' : 'Select Category'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 17\n              }, this), errors.categoryId && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#dc3545',\n                  fontSize: '0.875rem',\n                  marginTop: '0.25rem'\n                },\n                children: errors.categoryId\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"SubCategory (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.subCategoryId || '',\n                onChange: handleSubCategoryChange,\n                disabled: !selectedHierarchy.categoryId || loading.subCategories,\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: !selectedHierarchy.categoryId ? 'Select Category first' : 'Select SubCategory (Optional)'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 19\n                }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: subCategory.id,\n                  children: subCategory.name\n                }, subCategory.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 839,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 13\n          }, this), errors.hierarchy && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#dc3545',\n              fontSize: '0.875rem',\n              marginTop: '0.5rem'\n            },\n            children: errors.hierarchy\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 15\n          }, this), errors.formCreation && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#dc3545',\n              fontSize: '0.875rem',\n              marginTop: '0.5rem',\n              fontWeight: 'bold'\n            },\n            children: [\"\\u26A0\\uFE0F \", errors.formCreation]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 15\n          }, this), existingFormInfo && existingFormInfo.existingForms.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#fff3cd',\n              border: '1px solid #ffeaa7',\n              borderRadius: '4px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\uD83D\\uDCCB Existing Forms:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 17\n            }, this), existingFormInfo.existingForms.map((form, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [form.type === 'category' ? 'Category' : 'SubCategory', \" Form:\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 871,\n                columnNumber: 21\n              }, this), \" \", form.name, form.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#6c757d'\n                },\n                children: form.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 42\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 870,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 860,\n            columnNumber: 15\n          }, this), existingFormInfo && existingFormInfo.subCategoriesWithForms.length > 0 && !selectedHierarchy.subCategoryId && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8d7da',\n              border: '1px solid #f5c6cb',\n              borderRadius: '4px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u26A0\\uFE0F Warning:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 17\n            }, this), \" This category has \", existingFormInfo.subCategoriesWithForms.length, \" subcategory form(s). You cannot create a form for this category.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 880,\n            columnNumber: 15\n          }, this), selectedHierarchy.categoryId && !errors.formCreation && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#d4edda',\n              border: '1px solid #c3e6cb',\n              borderRadius: '4px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u2705 Valid Selection:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 903,\n              columnNumber: 17\n            }, this), \" You can create a form for this \", selectedHierarchy.subCategoryId ? 'subcategory' : 'category', \".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 895,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-controls\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search fields...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 909,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-tabs\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setCurrentSection('all'),\n            className: `section-tab ${currentSection === 'all' ? 'active' : ''}`,\n            children: [\"All (\", sectionCounts.all, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 921,\n            columnNumber: 13\n          }, this), Object.entries(PersonFieldDefinitions).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setCurrentSection(sectionKey),\n            className: `section-tab ${currentSection === sectionKey ? 'active' : ''}`,\n            children: [section.title, \" (\", sectionCounts[sectionKey], \")\"]\n          }, sectionKey, true, {\n            fileName: _jsxFileName,\n            lineNumber: 929,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 920,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bulk-selection-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bulk-controls-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bulk-controls-label\",\n              children: \"Quick Actions:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 943,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleSelectAllFields,\n              className: \"btn btn-sm btn-outline\",\n              title: `Select all ${filteredFields.length} filtered fields`,\n              children: [\"Select Filtered (\", filteredFields.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 944,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleDeselectAllFields,\n              className: \"btn btn-sm btn-outline\",\n              title: \"Deselect all filtered fields\",\n              children: \"Deselect Filtered\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleSelectAllAvailableFields,\n              className: \"btn btn-sm btn-primary\",\n              title: `Select all ${availableFields.length} available fields`,\n              children: [\"Select All (\", availableFields.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleClearAllFields,\n              className: \"btn btn-sm btn-outline btn-danger\",\n              title: \"Clear all selected fields\",\n              disabled: selectedFields.length === 0,\n              children: \"Clear All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 942,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selection-summary\",\n            children: [selectedFields.length, \" of \", availableFields.length, \" fields selected\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 978,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 941,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-list\",\n          children: filteredFields.map(field => {\n            const isSelected = selectedFields.some(f => f.key === field.key);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `field-item ${isSelected ? 'selected' : ''}`,\n              onClick: () => handleFieldToggle(field),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-checkbox\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: isSelected,\n                  onChange: () => handleFieldToggle(field)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 994,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 993,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"field-name\",\n                  children: field.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1001,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"field-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"field-type\",\n                    children: field.type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1003,\n                    columnNumber: 23\n                  }, this), field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"required-badge\",\n                    children: \"Required\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1004,\n                    columnNumber: 42\n                  }, this), field.conditional && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"conditional-badge\",\n                    children: \"Conditional\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1005,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1002,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1000,\n                columnNumber: 19\n              }, this)]\n            }, field.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 988,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 984,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 543,\n      columnNumber: 7\n    }, this), showFieldConfig && configField && /*#__PURE__*/_jsxDEV(FieldConfigModal, {\n      field: configField,\n      onSave: handleFieldConfigSave,\n      onCancel: () => {\n        setShowFieldConfig(false);\n        setConfigField(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1017,\n      columnNumber: 9\n    }, this), showPreview && /*#__PURE__*/_jsxDEV(FormPreview, {\n      fields: selectedFields,\n      formName: formName,\n      onClose: () => setShowPreview(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1028,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 495,\n    columnNumber: 5\n  }, this);\n};\n_s(FormBuilder, \"y3bzwENh58K6Al/BOkHOsxoa9Ss=\");\n_c = FormBuilder;\nexport default FormBuilder;\nvar _c;\n$RefreshReg$(_c, \"FormBuilder\");", "map": {"version": 3, "names": ["useState", "useEffect", "PersonFieldDefinitions", "<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>s", "formConfigService", "apiService", "FieldConfigModal", "FormPreview", "jsxDEV", "_jsxDEV", "FormBuilder", "onSave", "onCancel", "initialConfig", "_s", "selectedHierarchy", "setSelectedHierarchy", "formName", "setFormName", "formDescription", "setFormDescription", "availableFields", "setAvailableFields", "<PERSON><PERSON><PERSON>s", "setSelectedFields", "currentSection", "setCurrentSection", "searchTerm", "setSearchTerm", "showPreview", "setShowPreview", "showFieldConfig", "setShowFieldConfig", "config<PERSON><PERSON>", "setConfigField", "errors", "setErrors", "saving", "setSaving", "savedForms", "setSavedForms", "showSavedForms", "setShowSavedForms", "divisions", "setDivisions", "categories", "setCategories", "firmNatures", "setFirmNatures", "loading", "setLoading", "existingFormInfo", "setExistingFormInfo", "isEditMode", "setIsEditMode", "editingFormId", "setEditingFormId", "initializeFields", "loadSavedForms", "loadInitialConfig", "allFields", "deduplicateFields", "fields", "seen", "Set", "deduplicated", "for<PERSON>ach", "field", "has", "key", "add", "push", "forms", "getAllFormConfigs", "config", "name", "description", "deduplicatedFields", "id", "type", "associatedId", "divisionId", "categoryId", "subCategoryId", "hierarchy", "loadDivisions", "formValidation", "validateFormCreation", "<PERSON><PERSON><PERSON><PERSON>", "prev", "formCreation", "join", "newErrors", "formInfo", "getExistingFormInfo", "response", "getDivisions", "data", "error", "console", "loadCategories", "getCategoriesByDivision", "loadFirmNatures", "getFirmNaturesByCategory", "handleDivisionChange", "e", "target", "value", "division", "find", "d", "parseInt", "firmNatureId", "category", "firmNature", "handleCategoryChange", "c", "handleSubCategoryChange", "subCategory", "subCategories", "sc", "handleFieldToggle", "isSelected", "some", "f", "filter", "exists", "handleFieldConfig", "handleFieldConfigSave", "updatedField", "map", "handleFieldMove", "fromIndex", "toIndex", "new<PERSON>ields", "movedField", "splice", "handleSelectAllFields", "fieldsToAdd", "filteredFields", "selected", "handleDeselectAllFields", "filteredFieldKeys", "includes", "handleSelectAllAvailableFields", "handleClearAllFields", "handleSave", "validation", "validateForm", "completeHierarchy", "settings", "showSections", "allowConditionalFields", "validateOnChange", "savedConfig", "originalForm", "createdAt", "updatedAt", "Date", "toISOString", "saveFormConfig", "Error", "message", "alert", "general", "trim", "length", "requiredFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingRequired", "rf", "Object", "keys", "getFilteredFields", "filtered", "section", "label", "toLowerCase", "getSectionCounts", "counts", "all", "sectionKey", "sectionCounts", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "entries", "form", "summary", "fieldCount", "toLocaleDateString", "window", "confirm", "deleteFormConfig", "onChange", "placeholder", "rows", "index", "required", "title", "Math", "max", "min", "style", "backgroundColor", "border", "borderRadius", "padding", "margin", "color", "fontSize", "marginBottom", "paddingLeft", "display", "gridTemplateColumns", "gap", "fontWeight", "width", "marginTop", "existingForms", "subCategoriesWithForms", "checked", "conditional", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/FormBuilder.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FieldConfigModal from './FieldConfigModal';\nimport FormPreview from './FormPreview';\nimport './FormBuilder.css';\n\nconst FormBuilder = ({ onSave, onCancel, initialConfig = null }) => {\n  const [selectedHierarchy, setSelectedHierarchy] = useState({});\n  const [formName, setFormName] = useState('');\n  const [formDescription, setFormDescription] = useState('');\n  const [availableFields, setAvailableFields] = useState([]);\n  const [selectedFields, setSelectedFields] = useState([]);\n  const [currentSection, setCurrentSection] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n  const [showFieldConfig, setShowFieldConfig] = useState(false);\n  const [configField, setConfigField] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [saving, setSaving] = useState(false);\n  const [savedForms, setSavedForms] = useState([]);\n  const [showSavedForms, setShowSavedForms] = useState(false);\n\n  // State for dropdown data\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [firmNatures, setFirmNatures] = useState([]);\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    firmNatures: false\n  });\n  const [existingFormInfo, setExistingFormInfo] = useState(null);\n  const [isEditMode, setIsEditMode] = useState(false);\n  const [editingFormId, setEditingFormId] = useState(null);\n\n  useEffect(() => {\n    initializeFields();\n    loadSavedForms();\n    if (initialConfig) {\n      loadInitialConfig(initialConfig);\n    }\n  }, [initialConfig]);\n\n  const initializeFields = () => {\n    const allFields = getAllPersonFields();\n    setAvailableFields(allFields);\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = (fields) => {\n    const seen = new Set();\n    const deduplicated = [];\n\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      }\n    });\n\n    return deduplicated;\n  };\n\n  const loadSavedForms = () => {\n    const forms = formConfigService.getAllFormConfigs();\n    setSavedForms(forms);\n  };\n\n  const loadInitialConfig = (config) => {\n    setFormName(config.name || '');\n    setFormDescription(config.description || '');\n\n    // Deduplicate fields when loading initial config\n    const fields = config.fields || [];\n    const deduplicatedFields = deduplicateFields(fields);\n    setSelectedFields(deduplicatedFields);\n\n    // Set edit mode when loading existing form\n    setIsEditMode(true);\n    setEditingFormId(config.id || config.key);\n\n    if (config.type === 'division' && config.associatedId) {\n      // Load division info for hierarchy selector\n      setSelectedHierarchy({\n        divisionId: config.associatedId\n      });\n    } else if (config.type === 'category' && config.associatedId) {\n      // Load category info for hierarchy selector\n      setSelectedHierarchy({\n        categoryId: config.associatedId\n      });\n    } else if (config.type === 'subcategory' && config.associatedId) {\n      // Load subcategory info for hierarchy selector\n      setSelectedHierarchy({\n        subCategoryId: config.associatedId\n      });\n    }\n\n    // If hierarchy is stored in config, use that\n    if (config.hierarchy) {\n      setSelectedHierarchy(config.hierarchy);\n    }\n  };\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n  }, []);\n\n  // Validate form creation rules when hierarchy changes\n  useEffect(() => {\n    if (selectedHierarchy.categoryId) {\n      // Skip validation in edit mode since we're updating an existing form\n      if (!isEditMode) {\n        const formValidation = formConfigService.validateFormCreation(\n          selectedHierarchy.categoryId,\n          selectedHierarchy.subCategoryId\n        );\n\n        if (!formValidation.isValid) {\n          setErrors(prev => ({\n            ...prev,\n            formCreation: formValidation.errors.join('. ')\n          }));\n        } else {\n          setErrors(prev => {\n            const newErrors = { ...prev };\n            delete newErrors.formCreation;\n            return newErrors;\n          });\n        }\n      } else {\n        // In edit mode, clear any form creation errors\n        setErrors(prev => {\n          const newErrors = { ...prev };\n          delete newErrors.formCreation;\n          return newErrors;\n        });\n      }\n\n      const formInfo = formConfigService.getExistingFormInfo(\n        selectedHierarchy.categoryId,\n        selectedHierarchy.subCategoryId\n      );\n      setExistingFormInfo(formInfo);\n    } else {\n      setExistingFormInfo(null);\n      setErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors.formCreation;\n        return newErrors;\n      });\n    }\n  }, [selectedHierarchy.categoryId, selectedHierarchy.subCategoryId, isEditMode]);\n\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  const loadCategories = async (divisionId) => {\n    if (!divisionId) {\n      setCategories([]);\n      setFirmNatures([]);\n      return;\n    }\n\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  const loadFirmNatures = async (categoryId) => {\n    if (!categoryId) {\n      setFirmNatures([]);\n      return;\n    }\n\n    setLoading(prev => ({ ...prev, firmNatures: true }));\n    try {\n      const response = await apiService.getFirmNaturesByCategory(categoryId);\n      setFirmNatures(response.data || []);\n    } catch (error) {\n      console.error('Error loading firm natures:', error);\n      setFirmNatures([]);\n    } finally {\n      setLoading(prev => ({ ...prev, firmNatures: false }));\n    }\n  };\n\n  const handleDivisionChange = (e) => {\n    const divisionId = e.target.value;\n    const division = divisions.find(d => d.id === parseInt(divisionId));\n\n    setSelectedHierarchy({\n      divisionId: divisionId || null,\n      categoryId: null,\n      firmNatureId: null,\n      division: division || null,\n      category: null,\n      firmNature: null\n    });\n\n    setCategories([]);\n    setFirmNatures([]);\n\n    if (divisionId) {\n      loadCategories(divisionId);\n    }\n  };\n\n  const handleCategoryChange = (e) => {\n    const categoryId = e.target.value;\n    const category = categories.find(c => c.id === parseInt(categoryId));\n\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      categoryId: categoryId || null,\n      firmNatureId: null,\n      category: category || null,\n      firmNature: null\n    }));\n\n    setFirmNatures([]);\n\n    if (categoryId) {\n      loadFirmNatures(categoryId);\n    }\n  };\n\n  const handleSubCategoryChange = (e) => {\n    const subCategoryId = e.target.value;\n    const subCategory = subCategories.find(sc => sc.id === parseInt(subCategoryId));\n\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      subCategoryId: subCategoryId || null,\n      subCategory: subCategory || null\n    }));\n  };\n\n  const handleFieldToggle = (field) => {\n    const isSelected = selectedFields.some(f => f.key === field.key);\n\n    if (isSelected) {\n      // Remove all instances of this field key (in case there are duplicates)\n      setSelectedFields(prev => prev.filter(f => f.key !== field.key));\n    } else {\n      // Add field only if it doesn't already exist\n      setSelectedFields(prev => {\n        const exists = prev.some(f => f.key === field.key);\n        if (exists) {\n          return prev;\n        }\n        return [...prev, { ...field }];\n      });\n    }\n  };\n\n  const handleFieldConfig = (field) => {\n    setConfigField(field);\n    setShowFieldConfig(true);\n  };\n\n  const handleFieldConfigSave = (updatedField) => {\n    setSelectedFields(prev => \n      prev.map(f => f.key === updatedField.key ? updatedField : f)\n    );\n    setShowFieldConfig(false);\n    setConfigField(null);\n  };\n\n  const handleFieldMove = (fromIndex, toIndex) => {\n    const newFields = [...selectedFields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n    setSelectedFields(newFields);\n  };\n\n  const handleSelectAllFields = () => {\n    // Get all filtered fields (respects current section and search)\n    const fieldsToAdd = filteredFields.filter(field =>\n      !selectedFields.some(selected => selected.key === field.key)\n    );\n\n    setSelectedFields(prev => [...prev, ...fieldsToAdd]);\n  };\n\n  const handleDeselectAllFields = () => {\n    // Remove all filtered fields from selection\n    const filteredFieldKeys = filteredFields.map(field => field.key);\n    setSelectedFields(prev =>\n      prev.filter(field => !filteredFieldKeys.includes(field.key))\n    );\n  };\n\n  const handleSelectAllAvailableFields = () => {\n    // Select all available fields regardless of current filter\n    const fieldsToAdd = availableFields.filter(field =>\n      !selectedFields.some(selected => selected.key === field.key)\n    );\n\n    setSelectedFields(prev => [...prev, ...fieldsToAdd]);\n  };\n\n  const handleClearAllFields = () => {\n    setSelectedFields([]);\n  };\n\n  const handleSave = async () => {\n    const validation = validateForm();\n\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSaving(true);\n    try {\n      // Ensure complete hierarchy information is included\n      const completeHierarchy = {\n        ...selectedHierarchy,\n        // Include division info (required)\n        divisionId: selectedHierarchy.divisionId,\n        division: selectedHierarchy.division,\n        // Include category info (required)\n        categoryId: selectedHierarchy.categoryId,\n        category: selectedHierarchy.category,\n        // Include subcategory info if selected (optional)\n        subCategoryId: selectedHierarchy.subCategoryId || null,\n        subCategory: selectedHierarchy.subCategory || null\n      };\n\n      const config = {\n        name: formName,\n        description: formDescription,\n        fields: selectedFields,\n        hierarchy: completeHierarchy,\n        settings: {\n          showSections: true,\n          allowConditionalFields: true,\n          validateOnChange: true\n        }\n      };\n\n      let savedConfig;\n\n      if (isEditMode) {\n        // In edit mode, update the existing form\n        // Preserve the original type and associatedId from the form being edited\n        const originalForm = formConfigService.getAllFormConfigs().find(f => f.id === editingFormId || f.key === editingFormId);\n        if (originalForm) {\n          // Update the existing form with new data\n          config.id = originalForm.id;\n          config.key = originalForm.key;\n          config.type = originalForm.type;\n          config.associatedId = originalForm.associatedId;\n          config.createdAt = originalForm.createdAt; // Preserve creation date\n          config.updatedAt = new Date().toISOString(); // Update modification date\n\n          savedConfig = formConfigService.saveFormConfig(originalForm.type, originalForm.associatedId, config);\n        } else {\n          throw new Error('Original form not found for editing');\n        }\n      } else {\n        // Create new form with the most specific level selected, but include complete hierarchy\n        if (selectedHierarchy.subCategoryId) {\n          savedConfig = formConfigService.saveFormConfig('subcategory', selectedHierarchy.subCategoryId, config);\n        } else if (selectedHierarchy.categoryId) {\n          savedConfig = formConfigService.saveFormConfig('category', selectedHierarchy.categoryId, config);\n        } else {\n          throw new Error('Please select both division and category');\n        }\n      }\n\n      // Reload saved forms list\n      loadSavedForms();\n\n      if (onSave) {\n        onSave(savedConfig);\n      } else {\n        // Show success message for standalone usage\n        const message = isEditMode ? 'Form updated successfully!' : 'Form configuration saved successfully!';\n        alert(message);\n        // Reset form\n        setFormName('');\n        setFormDescription('');\n        setSelectedFields([]);\n        setSelectedHierarchy({});\n        setIsEditMode(false);\n        setEditingFormId(null);\n      }\n    } catch (error) {\n      console.error('Error saving form:', error);\n      setErrors({ general: error.message });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const validateForm = () => {\n    const errors = {};\n\n    if (!formName.trim()) {\n      errors.formName = 'Form name is required';\n    }\n\n    // Division and category are required, subcategory is optional\n    if (!selectedHierarchy.divisionId) {\n      errors.hierarchy = 'Please select a division';\n    } else if (!selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Please select a category';\n    } else if (selectedHierarchy.subCategoryId && !selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Cannot select subcategory without selecting category';\n    }\n\n    // Validate form creation rules (skip in edit mode)\n    if (selectedHierarchy.categoryId && !isEditMode) {\n      const formValidation = formConfigService.validateFormCreation(\n        selectedHierarchy.categoryId,\n        selectedHierarchy.subCategoryId\n      );\n\n      if (!formValidation.isValid) {\n        errors.formCreation = formValidation.errors.join('. ');\n      }\n    }\n\n    if (selectedFields.length === 0) {\n      errors.fields = 'Please select at least one field';\n    }\n\n    // Check required fields\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = selectedFields.map(f => f.key);\n    const missingRequired = requiredFields.filter(rf => !selectedFieldKeys.includes(rf));\n\n    if (missingRequired.length > 0) {\n      errors.requiredFields = `Required fields missing: ${missingRequired.join(', ')}`;\n    }\n\n    return {\n      isValid: Object.keys(errors).length === 0,\n      errors\n    };\n  };\n\n  const getFilteredFields = () => {\n    let filtered = availableFields;\n\n    // Filter by section\n    if (currentSection !== 'all') {\n      filtered = filtered.filter(field => field.section === currentSection);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(field => \n        field.label.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        field.key.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    }\n\n    return filtered;\n  };\n\n  const getSectionCounts = () => {\n    const counts = { all: availableFields.length };\n    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {\n      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;\n    });\n    return counts;\n  };\n\n  const sectionCounts = getSectionCounts();\n  const filteredFields = getFilteredFields();\n\n  return (\n    <div className=\"form-builder\">\n      <div className=\"form-builder-header\">\n        <h2>Form Builder</h2>\n        <div className=\"header-actions\">\n          <button \n            type=\"button\" \n            onClick={() => setShowPreview(true)}\n            className=\"btn btn-secondary\"\n            disabled={selectedFields.length === 0}\n          >\n            Preview Form\n          </button>\n          <button\n            type=\"button\"\n            onClick={handleSave}\n            className=\"btn btn-primary\"\n            disabled={saving || errors.formCreation}\n          >\n            {saving ? (isEditMode ? 'Updating...' : 'Saving...') : (isEditMode ? 'Update Form' : 'Save Form')}\n          </button>\n          {onCancel && (\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              className=\"btn btn-outline\"\n            >\n              Cancel\n            </button>\n          )}\n        </div>\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      {/* Validation Status */}\n      {Object.keys(errors).length > 0 && (\n        <div className=\"validation-errors\">\n          <h4>Please fix the following issues:</h4>\n          <ul>\n            {Object.entries(errors).map(([key, message]) => (\n              <li key={key}>{message}</li>\n            ))}\n          </ul>\n        </div>\n      )}\n\n      <div className=\"form-builder-content\">\n        {/* Form Configuration Panel */}\n        <div className=\"config-panel\">\n          {/* Saved Forms Section */}\n          <div className=\"config-section\">\n            <div className=\"section-header\">\n              <h3>Saved Forms ({savedForms.length})</h3>\n              <button\n                type=\"button\"\n                className=\"btn-link\"\n                onClick={() => setShowSavedForms(!showSavedForms)}\n              >\n                {showSavedForms ? 'Hide' : 'Show'}\n              </button>\n            </div>\n\n            {showSavedForms && (\n              <div className=\"saved-forms-list\">\n                {savedForms.length === 0 ? (\n                  <p className=\"no-forms-message\">No saved forms yet. Create your first form below!</p>\n                ) : (\n                  savedForms.map((form) => (\n                    <div key={form.key} className=\"saved-form-item\">\n                      <div className=\"form-item-header\">\n                        <h4>{form.name}</h4>\n                        <span className=\"form-type-badge\">{form.type}</span>\n                      </div>\n                      <p className=\"form-description\">{form.description || 'No description'}</p>\n                      <div className=\"form-meta\">\n                        <span>{form.summary.fieldCount} fields</span>\n                        <span>•</span>\n                        <span>Updated {new Date(form.updatedAt).toLocaleDateString()}</span>\n                      </div>\n                      <div className=\"form-actions\">\n                        <button\n                          type=\"button\"\n                          className=\"btn-small btn-outline\"\n                          onClick={() => loadInitialConfig(form)}\n                        >\n                          Load\n                        </button>\n                        <button\n                          type=\"button\"\n                          className=\"btn-small btn-danger\"\n                          onClick={() => {\n                            if (window.confirm('Are you sure you want to delete this form?')) {\n                              formConfigService.deleteFormConfig(form.type, form.associatedId);\n                              loadSavedForms();\n                            }\n                          }}\n                        >\n                          Delete\n                        </button>\n                      </div>\n                    </div>\n                  ))\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* Form Configuration Section */}\n          <div className=\"config-section\">\n            <h3>Form Configuration</h3>\n\n            <div className=\"form-group\">\n              <label>Form Name *</label>\n              <input\n                type=\"text\"\n                value={formName}\n                onChange={(e) => setFormName(e.target.value)}\n                placeholder=\"Enter form name\"\n                className={errors.formName ? 'error' : ''}\n              />\n              {errors.formName && <div className=\"error-message\">{errors.formName}</div>}\n            </div>\n\n            <div className=\"form-group\">\n              <label>Description</label>\n              <textarea\n                value={formDescription}\n                onChange={(e) => setFormDescription(e.target.value)}\n                placeholder=\"Enter form description\"\n                rows=\"3\"\n              />\n            </div>\n\n\n          </div>\n\n          {/* Selected Fields Panel */}\n          <div className=\"config-section\">\n            <h3>Selected Fields ({selectedFields.length})</h3>\n            {errors.fields && <div className=\"error-message\">{errors.fields}</div>}\n            {errors.requiredFields && <div className=\"error-message\">{errors.requiredFields}</div>}\n            \n            <div className=\"selected-fields\">\n              {selectedFields.length === 0 ? (\n                <div className=\"empty-state\">\n                  No fields selected. Choose fields from the available fields panel.\n                </div>\n              ) : (\n                selectedFields.map((field, index) => (\n                  <div key={field.key} className=\"selected-field\">\n                    <div className=\"field-info\">\n                      <span className=\"field-label\">{field.label}</span>\n                      <span className=\"field-type\">{field.type}</span>\n                      {field.required && <span className=\"required-badge\">Required</span>}\n                    </div>\n                    <div className=\"field-actions\">\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldConfig(field)}\n                        className=\"btn-icon\"\n                        title=\"Configure field\"\n                      >\n                        ⚙️\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldMove(index, Math.max(0, index - 1))}\n                        className=\"btn-icon\"\n                        disabled={index === 0}\n                        title=\"Move up\"\n                      >\n                        ↑\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1))}\n                        className=\"btn-icon\"\n                        disabled={index === selectedFields.length - 1}\n                        title=\"Move down\"\n                      >\n                        ↓\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldToggle(field)}\n                        className=\"btn-icon remove\"\n                        title=\"Remove field\"\n                      >\n                        ✕\n                      </button>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Available Fields Panel */}\n        <div className=\"fields-panel\">\n          <div className=\"fields-header\">\n            <h3>Available Fields</h3>\n          </div>\n\n          {/* Division/Category/SubCategory Selection */}\n          <div className=\"hierarchy-selection\" style={{\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #e1e5e9',\n            borderRadius: '8px',\n            padding: '1.5rem',\n            margin: '1rem 0'\n          }}>\n            <h4 style={{ margin: '0 0 1rem 0', color: '#495057' }}>\n              Associate with Division & Category (Required) / SubCategory (Optional) *\n            </h4>\n            <div style={{\n              fontSize: '0.875rem',\n              color: '#6c757d',\n              marginBottom: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8f9fa',\n              borderRadius: '4px',\n              border: '1px solid #e9ecef'\n            }}>\n              <strong>Form Creation Rules:</strong>\n              <ul style={{ margin: '0.5rem 0 0 1rem', paddingLeft: '1rem' }}>\n                <li>If you create a form for a category, you cannot create forms for its subcategories</li>\n                <li>Each subcategory can have only one form</li>\n                <li>If subcategories already have forms, you cannot create a form for the parent category</li>\n              </ul>\n            </div>\n\n            <div className=\"hierarchy-dropdowns\" style={{\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '1rem'\n            }}>\n              {/* Division Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  Division *\n                </label>\n                <select\n                  value={selectedHierarchy.divisionId || ''}\n                  onChange={handleDivisionChange}\n                  disabled={loading.divisions}\n                  className={errors.divisionId ? 'error' : ''}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">Select Division</option>\n                  {divisions.map(division => (\n                    <option key={division.id} value={division.id}>\n                      {division.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.divisionId && (\n                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>\n                    {errors.divisionId}\n                  </div>\n                )}\n              </div>\n\n              {/* Category Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  Category *\n                </label>\n                <select\n                  value={selectedHierarchy.categoryId || ''}\n                  onChange={handleCategoryChange}\n                  disabled={!selectedHierarchy.divisionId || loading.categories}\n                  className={errors.categoryId ? 'error' : ''}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">\n                    {!selectedHierarchy.divisionId ? 'Select Division first' : 'Select Category'}\n                  </option>\n                  {categories.map(category => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.categoryId && (\n                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>\n                    {errors.categoryId}\n                  </div>\n                )}\n              </div>\n\n              {/* SubCategory Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  SubCategory (Optional)\n                </label>\n                <select\n                  value={selectedHierarchy.subCategoryId || ''}\n                  onChange={handleSubCategoryChange}\n                  disabled={!selectedHierarchy.categoryId || loading.subCategories}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">\n                    {!selectedHierarchy.categoryId ? 'Select Category first' : 'Select SubCategory (Optional)'}\n                  </option>\n                  {subCategories.map(subCategory => (\n                    <option key={subCategory.id} value={subCategory.id}>\n                      {subCategory.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            {errors.hierarchy && (\n              <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.5rem' }}>\n                {errors.hierarchy}\n              </div>\n            )}\n            {errors.formCreation && (\n              <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.5rem', fontWeight: 'bold' }}>\n                ⚠️ {errors.formCreation}\n              </div>\n            )}\n\n            {/* Existing Form Information */}\n            {existingFormInfo && existingFormInfo.existingForms.length > 0 && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#fff3cd',\n                border: '1px solid #ffeaa7',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>📋 Existing Forms:</strong>\n                {existingFormInfo.existingForms.map((form, index) => (\n                  <div key={index} style={{ marginTop: '0.5rem' }}>\n                    <strong>{form.type === 'category' ? 'Category' : 'SubCategory'} Form:</strong> {form.name}\n                    {form.description && <div style={{ color: '#6c757d' }}>{form.description}</div>}\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {/* Subcategories with Forms Warning */}\n            {existingFormInfo && existingFormInfo.subCategoriesWithForms.length > 0 && !selectedHierarchy.subCategoryId && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#f8d7da',\n                border: '1px solid #f5c6cb',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>⚠️ Warning:</strong> This category has {existingFormInfo.subCategoriesWithForms.length} subcategory form(s).\n                You cannot create a form for this category.\n              </div>\n            )}\n\n            {/* Success Message for Valid Selection */}\n            {selectedHierarchy.categoryId && !errors.formCreation && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#d4edda',\n                border: '1px solid #c3e6cb',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>✅ Valid Selection:</strong> You can create a form for this {selectedHierarchy.subCategoryId ? 'subcategory' : 'category'}.\n              </div>\n            )}\n          </div>\n\n          {/* Search and Filter Controls */}\n          <div className=\"fields-controls\">\n            <input\n              type=\"text\"\n              placeholder=\"Search fields...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"search-input\"\n            />\n          </div>\n\n          {/* Section Tabs */}\n          <div className=\"section-tabs\">\n            <button\n              type=\"button\"\n              onClick={() => setCurrentSection('all')}\n              className={`section-tab ${currentSection === 'all' ? 'active' : ''}`}\n            >\n              All ({sectionCounts.all})\n            </button>\n            {Object.entries(PersonFieldDefinitions).map(([sectionKey, section]) => (\n              <button\n                key={sectionKey}\n                type=\"button\"\n                onClick={() => setCurrentSection(sectionKey)}\n                className={`section-tab ${currentSection === sectionKey ? 'active' : ''}`}\n              >\n                {section.title} ({sectionCounts[sectionKey]})\n              </button>\n            ))}\n          </div>\n\n          {/* Bulk Selection Controls */}\n          <div className=\"bulk-selection-controls\">\n            <div className=\"bulk-controls-section\">\n              <span className=\"bulk-controls-label\">Quick Actions:</span>\n              <button\n                type=\"button\"\n                onClick={handleSelectAllFields}\n                className=\"btn btn-sm btn-outline\"\n                title={`Select all ${filteredFields.length} filtered fields`}\n              >\n                Select Filtered ({filteredFields.length})\n              </button>\n              <button\n                type=\"button\"\n                onClick={handleDeselectAllFields}\n                className=\"btn btn-sm btn-outline\"\n                title=\"Deselect all filtered fields\"\n              >\n                Deselect Filtered\n              </button>\n              <button\n                type=\"button\"\n                onClick={handleSelectAllAvailableFields}\n                className=\"btn btn-sm btn-primary\"\n                title={`Select all ${availableFields.length} available fields`}\n              >\n                Select All ({availableFields.length})\n              </button>\n              <button\n                type=\"button\"\n                onClick={handleClearAllFields}\n                className=\"btn btn-sm btn-outline btn-danger\"\n                title=\"Clear all selected fields\"\n                disabled={selectedFields.length === 0}\n              >\n                Clear All\n              </button>\n            </div>\n            <div className=\"selection-summary\">\n              {selectedFields.length} of {availableFields.length} fields selected\n            </div>\n          </div>\n\n          {/* Fields List */}\n          <div className=\"fields-list\">\n            {filteredFields.map(field => {\n              const isSelected = selectedFields.some(f => f.key === field.key);\n              return (\n                <div\n                  key={field.key}\n                  className={`field-item ${isSelected ? 'selected' : ''}`}\n                  onClick={() => handleFieldToggle(field)}\n                >\n                  <div className=\"field-checkbox\">\n                    <input\n                      type=\"checkbox\"\n                      checked={isSelected}\n                      onChange={() => handleFieldToggle(field)}\n                    />\n                  </div>\n                  <div className=\"field-details\">\n                    <div className=\"field-name\">{field.label}</div>\n                    <div className=\"field-meta\">\n                      <span className=\"field-type\">{field.type}</span>\n                      {field.required && <span className=\"required-badge\">Required</span>}\n                      {field.conditional && <span className=\"conditional-badge\">Conditional</span>}\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Modals */}\n      {showFieldConfig && configField && (\n        <FieldConfigModal\n          field={configField}\n          onSave={handleFieldConfigSave}\n          onCancel={() => {\n            setShowFieldConfig(false);\n            setConfigField(null);\n          }}\n        />\n      )}\n\n      {showPreview && (\n        <FormPreview\n          fields={selectedFields}\n          formName={formName}\n          onClose={() => setShowPreview(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default FormBuilder;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,iCAAiC;AAC5F,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACqC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC;IACrC2C,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAE,KAAK;IACjBE,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAExDC,SAAS,CAAC,MAAM;IACdwD,gBAAgB,CAAC,CAAC;IAClBC,cAAc,CAAC,CAAC;IAChB,IAAI7C,aAAa,EAAE;MACjB8C,iBAAiB,CAAC9C,aAAa,CAAC;IAClC;EACF,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,MAAM4C,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMG,SAAS,GAAGzD,kBAAkB,CAAC,CAAC;IACtCmB,kBAAkB,CAACsC,SAAS,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAIC,MAAM,IAAK;IACpC,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,EAAE;IAEvBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAI;MACtB,IAAI,CAACJ,IAAI,CAACK,GAAG,CAACD,KAAK,CAACE,GAAG,CAAC,EAAE;QACxBN,IAAI,CAACO,GAAG,CAACH,KAAK,CAACE,GAAG,CAAC;QACnBJ,YAAY,CAACM,IAAI,CAACJ,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IAEF,OAAOF,YAAY;EACrB,CAAC;EAED,MAAMP,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMc,KAAK,GAAGpE,iBAAiB,CAACqE,iBAAiB,CAAC,CAAC;IACnDjC,aAAa,CAACgC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMb,iBAAiB,GAAIe,MAAM,IAAK;IACpCxD,WAAW,CAACwD,MAAM,CAACC,IAAI,IAAI,EAAE,CAAC;IAC9BvD,kBAAkB,CAACsD,MAAM,CAACE,WAAW,IAAI,EAAE,CAAC;;IAE5C;IACA,MAAMd,MAAM,GAAGY,MAAM,CAACZ,MAAM,IAAI,EAAE;IAClC,MAAMe,kBAAkB,GAAGhB,iBAAiB,CAACC,MAAM,CAAC;IACpDtC,iBAAiB,CAACqD,kBAAkB,CAAC;;IAErC;IACAvB,aAAa,CAAC,IAAI,CAAC;IACnBE,gBAAgB,CAACkB,MAAM,CAACI,EAAE,IAAIJ,MAAM,CAACL,GAAG,CAAC;IAEzC,IAAIK,MAAM,CAACK,IAAI,KAAK,UAAU,IAAIL,MAAM,CAACM,YAAY,EAAE;MACrD;MACAhE,oBAAoB,CAAC;QACnBiE,UAAU,EAAEP,MAAM,CAACM;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIN,MAAM,CAACK,IAAI,KAAK,UAAU,IAAIL,MAAM,CAACM,YAAY,EAAE;MAC5D;MACAhE,oBAAoB,CAAC;QACnBkE,UAAU,EAAER,MAAM,CAACM;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIN,MAAM,CAACK,IAAI,KAAK,aAAa,IAAIL,MAAM,CAACM,YAAY,EAAE;MAC/D;MACAhE,oBAAoB,CAAC;QACnBmE,aAAa,EAAET,MAAM,CAACM;MACxB,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIN,MAAM,CAACU,SAAS,EAAE;MACpBpE,oBAAoB,CAAC0D,MAAM,CAACU,SAAS,CAAC;IACxC;EACF,CAAC;;EAED;EACAnF,SAAS,CAAC,MAAM;IACdoF,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApF,SAAS,CAAC,MAAM;IACd,IAAIc,iBAAiB,CAACmE,UAAU,EAAE;MAChC;MACA,IAAI,CAAC7B,UAAU,EAAE;QACf,MAAMiC,cAAc,GAAGlF,iBAAiB,CAACmF,oBAAoB,CAC3DxE,iBAAiB,CAACmE,UAAU,EAC5BnE,iBAAiB,CAACoE,aACpB,CAAC;QAED,IAAI,CAACG,cAAc,CAACE,OAAO,EAAE;UAC3BpD,SAAS,CAACqD,IAAI,KAAK;YACjB,GAAGA,IAAI;YACPC,YAAY,EAAEJ,cAAc,CAACnD,MAAM,CAACwD,IAAI,CAAC,IAAI;UAC/C,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACLvD,SAAS,CAACqD,IAAI,IAAI;YAChB,MAAMG,SAAS,GAAG;cAAE,GAAGH;YAAK,CAAC;YAC7B,OAAOG,SAAS,CAACF,YAAY;YAC7B,OAAOE,SAAS;UAClB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACAxD,SAAS,CAACqD,IAAI,IAAI;UAChB,MAAMG,SAAS,GAAG;YAAE,GAAGH;UAAK,CAAC;UAC7B,OAAOG,SAAS,CAACF,YAAY;UAC7B,OAAOE,SAAS;QAClB,CAAC,CAAC;MACJ;MAEA,MAAMC,QAAQ,GAAGzF,iBAAiB,CAAC0F,mBAAmB,CACpD/E,iBAAiB,CAACmE,UAAU,EAC5BnE,iBAAiB,CAACoE,aACpB,CAAC;MACD/B,mBAAmB,CAACyC,QAAQ,CAAC;IAC/B,CAAC,MAAM;MACLzC,mBAAmB,CAAC,IAAI,CAAC;MACzBhB,SAAS,CAACqD,IAAI,IAAI;QAChB,MAAMG,SAAS,GAAG;UAAE,GAAGH;QAAK,CAAC;QAC7B,OAAOG,SAAS,CAACF,YAAY;QAC7B,OAAOE,SAAS;MAClB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC7E,iBAAiB,CAACmE,UAAU,EAAEnE,iBAAiB,CAACoE,aAAa,EAAE9B,UAAU,CAAC,CAAC;EAE/E,MAAMgC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCnC,UAAU,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9C,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAClD,IAAI;MACF,MAAMoD,QAAQ,GAAG,MAAM1F,UAAU,CAAC2F,YAAY,CAAC,CAAC;MAChDpD,YAAY,CAACmD,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRhD,UAAU,CAACuC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE9C,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;EAED,MAAMyD,cAAc,GAAG,MAAOnB,UAAU,IAAK;IAC3C,IAAI,CAACA,UAAU,EAAE;MACfnC,aAAa,CAAC,EAAE,CAAC;MACjBE,cAAc,CAAC,EAAE,CAAC;MAClB;IACF;IAEAE,UAAU,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE5C,UAAU,EAAE;IAAK,CAAC,CAAC,CAAC;IACnD,IAAI;MACF,MAAMkD,QAAQ,GAAG,MAAM1F,UAAU,CAACgG,uBAAuB,CAACpB,UAAU,CAAC;MACrEnC,aAAa,CAACiD,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDpD,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRI,UAAU,CAACuC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE5C,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAMyD,eAAe,GAAG,MAAOpB,UAAU,IAAK;IAC5C,IAAI,CAACA,UAAU,EAAE;MACflC,cAAc,CAAC,EAAE,CAAC;MAClB;IACF;IAEAE,UAAU,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1C,WAAW,EAAE;IAAK,CAAC,CAAC,CAAC;IACpD,IAAI;MACF,MAAMgD,QAAQ,GAAG,MAAM1F,UAAU,CAACkG,wBAAwB,CAACrB,UAAU,CAAC;MACtElC,cAAc,CAAC+C,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACrC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDlD,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,SAAS;MACRE,UAAU,CAACuC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE1C,WAAW,EAAE;MAAM,CAAC,CAAC,CAAC;IACvD;EACF,CAAC;EAED,MAAMyD,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMxB,UAAU,GAAGwB,CAAC,CAACC,MAAM,CAACC,KAAK;IACjC,MAAMC,QAAQ,GAAGjE,SAAS,CAACkE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAKiC,QAAQ,CAAC9B,UAAU,CAAC,CAAC;IAEnEjE,oBAAoB,CAAC;MACnBiE,UAAU,EAAEA,UAAU,IAAI,IAAI;MAC9BC,UAAU,EAAE,IAAI;MAChB8B,YAAY,EAAE,IAAI;MAClBJ,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BK,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC,CAAC;IAEFpE,aAAa,CAAC,EAAE,CAAC;IACjBE,cAAc,CAAC,EAAE,CAAC;IAElB,IAAIiC,UAAU,EAAE;MACdmB,cAAc,CAACnB,UAAU,CAAC;IAC5B;EACF,CAAC;EAED,MAAMkC,oBAAoB,GAAIV,CAAC,IAAK;IAClC,MAAMvB,UAAU,GAAGuB,CAAC,CAACC,MAAM,CAACC,KAAK;IACjC,MAAMM,QAAQ,GAAGpE,UAAU,CAACgE,IAAI,CAACO,CAAC,IAAIA,CAAC,CAACtC,EAAE,KAAKiC,QAAQ,CAAC7B,UAAU,CAAC,CAAC;IAEpElE,oBAAoB,CAACyE,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACPP,UAAU,EAAEA,UAAU,IAAI,IAAI;MAC9B8B,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BC,UAAU,EAAE;IACd,CAAC,CAAC,CAAC;IAEHlE,cAAc,CAAC,EAAE,CAAC;IAElB,IAAIkC,UAAU,EAAE;MACdoB,eAAe,CAACpB,UAAU,CAAC;IAC7B;EACF,CAAC;EAED,MAAMmC,uBAAuB,GAAIZ,CAAC,IAAK;IACrC,MAAMtB,aAAa,GAAGsB,CAAC,CAACC,MAAM,CAACC,KAAK;IACpC,MAAMW,WAAW,GAAGC,aAAa,CAACV,IAAI,CAACW,EAAE,IAAIA,EAAE,CAAC1C,EAAE,KAAKiC,QAAQ,CAAC5B,aAAa,CAAC,CAAC;IAE/EnE,oBAAoB,CAACyE,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACPN,aAAa,EAAEA,aAAa,IAAI,IAAI;MACpCmC,WAAW,EAAEA,WAAW,IAAI;IAC9B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,iBAAiB,GAAItD,KAAK,IAAK;IACnC,MAAMuD,UAAU,GAAGnG,cAAc,CAACoG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC;IAEhE,IAAIqD,UAAU,EAAE;MACd;MACAlG,iBAAiB,CAACiE,IAAI,IAAIA,IAAI,CAACoC,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACvD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC,CAAC;IAClE,CAAC,MAAM;MACL;MACA7C,iBAAiB,CAACiE,IAAI,IAAI;QACxB,MAAMqC,MAAM,GAAGrC,IAAI,CAACkC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC;QAClD,IAAIyD,MAAM,EAAE;UACV,OAAOrC,IAAI;QACb;QACA,OAAO,CAAC,GAAGA,IAAI,EAAE;UAAE,GAAGtB;QAAM,CAAC,CAAC;MAChC,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM4D,iBAAiB,GAAI5D,KAAK,IAAK;IACnCjC,cAAc,CAACiC,KAAK,CAAC;IACrBnC,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMgG,qBAAqB,GAAIC,YAAY,IAAK;IAC9CzG,iBAAiB,CAACiE,IAAI,IACpBA,IAAI,CAACyC,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACvD,GAAG,KAAK4D,YAAY,CAAC5D,GAAG,GAAG4D,YAAY,GAAGL,CAAC,CAC7D,CAAC;IACD5F,kBAAkB,CAAC,KAAK,CAAC;IACzBE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMiG,eAAe,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IAC9C,MAAMC,SAAS,GAAG,CAAC,GAAG/G,cAAc,CAAC;IACrC,MAAM,CAACgH,UAAU,CAAC,GAAGD,SAAS,CAACE,MAAM,CAACJ,SAAS,EAAE,CAAC,CAAC;IACnDE,SAAS,CAACE,MAAM,CAACH,OAAO,EAAE,CAAC,EAAEE,UAAU,CAAC;IACxC/G,iBAAiB,CAAC8G,SAAS,CAAC;EAC9B,CAAC;EAED,MAAMG,qBAAqB,GAAGA,CAAA,KAAM;IAClC;IACA,MAAMC,WAAW,GAAGC,cAAc,CAACd,MAAM,CAAC1D,KAAK,IAC7C,CAAC5C,cAAc,CAACoG,IAAI,CAACiB,QAAQ,IAAIA,QAAQ,CAACvE,GAAG,KAAKF,KAAK,CAACE,GAAG,CAC7D,CAAC;IAED7C,iBAAiB,CAACiE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGiD,WAAW,CAAC,CAAC;EACtD,CAAC;EAED,MAAMG,uBAAuB,GAAGA,CAAA,KAAM;IACpC;IACA,MAAMC,iBAAiB,GAAGH,cAAc,CAACT,GAAG,CAAC/D,KAAK,IAAIA,KAAK,CAACE,GAAG,CAAC;IAChE7C,iBAAiB,CAACiE,IAAI,IACpBA,IAAI,CAACoC,MAAM,CAAC1D,KAAK,IAAI,CAAC2E,iBAAiB,CAACC,QAAQ,CAAC5E,KAAK,CAACE,GAAG,CAAC,CAC7D,CAAC;EACH,CAAC;EAED,MAAM2E,8BAA8B,GAAGA,CAAA,KAAM;IAC3C;IACA,MAAMN,WAAW,GAAGrH,eAAe,CAACwG,MAAM,CAAC1D,KAAK,IAC9C,CAAC5C,cAAc,CAACoG,IAAI,CAACiB,QAAQ,IAAIA,QAAQ,CAACvE,GAAG,KAAKF,KAAK,CAACE,GAAG,CAC7D,CAAC;IAED7C,iBAAiB,CAACiE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGiD,WAAW,CAAC,CAAC;EACtD,CAAC;EAED,MAAMO,oBAAoB,GAAGA,CAAA,KAAM;IACjCzH,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC;EAED,MAAM0H,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMC,UAAU,GAAGC,YAAY,CAAC,CAAC;IAEjC,IAAI,CAACD,UAAU,CAAC3D,OAAO,EAAE;MACvBpD,SAAS,CAAC+G,UAAU,CAAChH,MAAM,CAAC;MAC5B;IACF;IAEAG,SAAS,CAAC,IAAI,CAAC;IACf,IAAI;MACF;MACA,MAAM+G,iBAAiB,GAAG;QACxB,GAAGtI,iBAAiB;QACpB;QACAkE,UAAU,EAAElE,iBAAiB,CAACkE,UAAU;QACxC2B,QAAQ,EAAE7F,iBAAiB,CAAC6F,QAAQ;QACpC;QACA1B,UAAU,EAAEnE,iBAAiB,CAACmE,UAAU;QACxC+B,QAAQ,EAAElG,iBAAiB,CAACkG,QAAQ;QACpC;QACA9B,aAAa,EAAEpE,iBAAiB,CAACoE,aAAa,IAAI,IAAI;QACtDmC,WAAW,EAAEvG,iBAAiB,CAACuG,WAAW,IAAI;MAChD,CAAC;MAED,MAAM5C,MAAM,GAAG;QACbC,IAAI,EAAE1D,QAAQ;QACd2D,WAAW,EAAEzD,eAAe;QAC5B2C,MAAM,EAAEvC,cAAc;QACtB6D,SAAS,EAAEiE,iBAAiB;QAC5BC,QAAQ,EAAE;UACRC,YAAY,EAAE,IAAI;UAClBC,sBAAsB,EAAE,IAAI;UAC5BC,gBAAgB,EAAE;QACpB;MACF,CAAC;MAED,IAAIC,WAAW;MAEf,IAAIrG,UAAU,EAAE;QACd;QACA;QACA,MAAMsG,YAAY,GAAGvJ,iBAAiB,CAACqE,iBAAiB,CAAC,CAAC,CAACoC,IAAI,CAACe,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAKvB,aAAa,IAAIqE,CAAC,CAACvD,GAAG,KAAKd,aAAa,CAAC;QACvH,IAAIoG,YAAY,EAAE;UAChB;UACAjF,MAAM,CAACI,EAAE,GAAG6E,YAAY,CAAC7E,EAAE;UAC3BJ,MAAM,CAACL,GAAG,GAAGsF,YAAY,CAACtF,GAAG;UAC7BK,MAAM,CAACK,IAAI,GAAG4E,YAAY,CAAC5E,IAAI;UAC/BL,MAAM,CAACM,YAAY,GAAG2E,YAAY,CAAC3E,YAAY;UAC/CN,MAAM,CAACkF,SAAS,GAAGD,YAAY,CAACC,SAAS,CAAC,CAAC;UAC3ClF,MAAM,CAACmF,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;;UAE7CL,WAAW,GAAGtJ,iBAAiB,CAAC4J,cAAc,CAACL,YAAY,CAAC5E,IAAI,EAAE4E,YAAY,CAAC3E,YAAY,EAAEN,MAAM,CAAC;QACtG,CAAC,MAAM;UACL,MAAM,IAAIuF,KAAK,CAAC,qCAAqC,CAAC;QACxD;MACF,CAAC,MAAM;QACL;QACA,IAAIlJ,iBAAiB,CAACoE,aAAa,EAAE;UACnCuE,WAAW,GAAGtJ,iBAAiB,CAAC4J,cAAc,CAAC,aAAa,EAAEjJ,iBAAiB,CAACoE,aAAa,EAAET,MAAM,CAAC;QACxG,CAAC,MAAM,IAAI3D,iBAAiB,CAACmE,UAAU,EAAE;UACvCwE,WAAW,GAAGtJ,iBAAiB,CAAC4J,cAAc,CAAC,UAAU,EAAEjJ,iBAAiB,CAACmE,UAAU,EAAER,MAAM,CAAC;QAClG,CAAC,MAAM;UACL,MAAM,IAAIuF,KAAK,CAAC,0CAA0C,CAAC;QAC7D;MACF;;MAEA;MACAvG,cAAc,CAAC,CAAC;MAEhB,IAAI/C,MAAM,EAAE;QACVA,MAAM,CAAC+I,WAAW,CAAC;MACrB,CAAC,MAAM;QACL;QACA,MAAMQ,OAAO,GAAG7G,UAAU,GAAG,4BAA4B,GAAG,wCAAwC;QACpG8G,KAAK,CAACD,OAAO,CAAC;QACd;QACAhJ,WAAW,CAAC,EAAE,CAAC;QACfE,kBAAkB,CAAC,EAAE,CAAC;QACtBI,iBAAiB,CAAC,EAAE,CAAC;QACrBR,oBAAoB,CAAC,CAAC,CAAC,CAAC;QACxBsC,aAAa,CAAC,KAAK,CAAC;QACpBE,gBAAgB,CAAC,IAAI,CAAC;MACxB;IACF,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C9D,SAAS,CAAC;QAAEgI,OAAO,EAAElE,KAAK,CAACgE;MAAQ,CAAC,CAAC;IACvC,CAAC,SAAS;MACR5H,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAM8G,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMjH,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAAClB,QAAQ,CAACoJ,IAAI,CAAC,CAAC,EAAE;MACpBlI,MAAM,CAAClB,QAAQ,GAAG,uBAAuB;IAC3C;;IAEA;IACA,IAAI,CAACF,iBAAiB,CAACkE,UAAU,EAAE;MACjC9C,MAAM,CAACiD,SAAS,GAAG,0BAA0B;IAC/C,CAAC,MAAM,IAAI,CAACrE,iBAAiB,CAACmE,UAAU,EAAE;MACxC/C,MAAM,CAACiD,SAAS,GAAG,0BAA0B;IAC/C,CAAC,MAAM,IAAIrE,iBAAiB,CAACoE,aAAa,IAAI,CAACpE,iBAAiB,CAACmE,UAAU,EAAE;MAC3E/C,MAAM,CAACiD,SAAS,GAAG,sDAAsD;IAC3E;;IAEA;IACA,IAAIrE,iBAAiB,CAACmE,UAAU,IAAI,CAAC7B,UAAU,EAAE;MAC/C,MAAMiC,cAAc,GAAGlF,iBAAiB,CAACmF,oBAAoB,CAC3DxE,iBAAiB,CAACmE,UAAU,EAC5BnE,iBAAiB,CAACoE,aACpB,CAAC;MAED,IAAI,CAACG,cAAc,CAACE,OAAO,EAAE;QAC3BrD,MAAM,CAACuD,YAAY,GAAGJ,cAAc,CAACnD,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC;MACxD;IACF;IAEA,IAAIpE,cAAc,CAAC+I,MAAM,KAAK,CAAC,EAAE;MAC/BnI,MAAM,CAAC2B,MAAM,GAAG,kCAAkC;IACpD;;IAEA;IACA,MAAMyG,cAAc,GAAG,CAAC,MAAM,EAAE,cAAc,EAAE,QAAQ,CAAC;IACzD,MAAMC,iBAAiB,GAAGjJ,cAAc,CAAC2G,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACvD,GAAG,CAAC;IACxD,MAAMoG,eAAe,GAAGF,cAAc,CAAC1C,MAAM,CAAC6C,EAAE,IAAI,CAACF,iBAAiB,CAACzB,QAAQ,CAAC2B,EAAE,CAAC,CAAC;IAEpF,IAAID,eAAe,CAACH,MAAM,GAAG,CAAC,EAAE;MAC9BnI,MAAM,CAACoI,cAAc,GAAG,4BAA4BE,eAAe,CAAC9E,IAAI,CAAC,IAAI,CAAC,EAAE;IAClF;IAEA,OAAO;MACLH,OAAO,EAAEmF,MAAM,CAACC,IAAI,CAACzI,MAAM,CAAC,CAACmI,MAAM,KAAK,CAAC;MACzCnI;IACF,CAAC;EACH,CAAC;EAED,MAAM0I,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIC,QAAQ,GAAGzJ,eAAe;;IAE9B;IACA,IAAII,cAAc,KAAK,KAAK,EAAE;MAC5BqJ,QAAQ,GAAGA,QAAQ,CAACjD,MAAM,CAAC1D,KAAK,IAAIA,KAAK,CAAC4G,OAAO,KAAKtJ,cAAc,CAAC;IACvE;;IAEA;IACA,IAAIE,UAAU,EAAE;MACdmJ,QAAQ,GAAGA,QAAQ,CAACjD,MAAM,CAAC1D,KAAK,IAC9BA,KAAK,CAAC6G,KAAK,CAACC,WAAW,CAAC,CAAC,CAAClC,QAAQ,CAACpH,UAAU,CAACsJ,WAAW,CAAC,CAAC,CAAC,IAC5D9G,KAAK,CAACE,GAAG,CAAC4G,WAAW,CAAC,CAAC,CAAClC,QAAQ,CAACpH,UAAU,CAACsJ,WAAW,CAAC,CAAC,CAC3D,CAAC;IACH;IAEA,OAAOH,QAAQ;EACjB,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,MAAM,GAAG;MAAEC,GAAG,EAAE/J,eAAe,CAACiJ;IAAO,CAAC;IAC9CK,MAAM,CAACC,IAAI,CAAC1K,sBAAsB,CAAC,CAACgE,OAAO,CAACmH,UAAU,IAAI;MACxDF,MAAM,CAACE,UAAU,CAAC,GAAGhK,eAAe,CAACwG,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACmD,OAAO,KAAKM,UAAU,CAAC,CAACf,MAAM;IACnF,CAAC,CAAC;IACF,OAAOa,MAAM;EACf,CAAC;EAED,MAAMG,aAAa,GAAGJ,gBAAgB,CAAC,CAAC;EACxC,MAAMvC,cAAc,GAAGkC,iBAAiB,CAAC,CAAC;EAE1C,oBACEpK,OAAA;IAAK8K,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B/K,OAAA;MAAK8K,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC/K,OAAA;QAAA+K,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrBnL,OAAA;QAAK8K,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B/K,OAAA;UACEsE,IAAI,EAAC,QAAQ;UACb8G,OAAO,EAAEA,CAAA,KAAM/J,cAAc,CAAC,IAAI,CAAE;UACpCyJ,SAAS,EAAC,mBAAmB;UAC7BO,QAAQ,EAAEvK,cAAc,CAAC+I,MAAM,KAAK,CAAE;UAAAkB,QAAA,EACvC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnL,OAAA;UACEsE,IAAI,EAAC,QAAQ;UACb8G,OAAO,EAAE3C,UAAW;UACpBqC,SAAS,EAAC,iBAAiB;UAC3BO,QAAQ,EAAEzJ,MAAM,IAAIF,MAAM,CAACuD,YAAa;UAAA8F,QAAA,EAEvCnJ,MAAM,GAAIgB,UAAU,GAAG,aAAa,GAAG,WAAW,GAAKA,UAAU,GAAG,aAAa,GAAG;QAAY;UAAAoI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F,CAAC,EACRhL,QAAQ,iBACPH,OAAA;UACEsE,IAAI,EAAC,QAAQ;UACb8G,OAAO,EAAEjL,QAAS;UAClB2K,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELzJ,MAAM,CAACiI,OAAO,iBACb3J,OAAA;MAAK8K,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAErJ,MAAM,CAACiI;IAAO;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,EAGAjB,MAAM,CAACC,IAAI,CAACzI,MAAM,CAAC,CAACmI,MAAM,GAAG,CAAC,iBAC7B7J,OAAA;MAAK8K,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC/K,OAAA;QAAA+K,QAAA,EAAI;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzCnL,OAAA;QAAA+K,QAAA,EACGb,MAAM,CAACoB,OAAO,CAAC5J,MAAM,CAAC,CAAC+F,GAAG,CAAC,CAAC,CAAC7D,GAAG,EAAE6F,OAAO,CAAC,kBACzCzJ,OAAA;UAAA+K,QAAA,EAAetB;QAAO,GAAb7F,GAAG;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACN,eAEDnL,OAAA;MAAK8K,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnC/K,OAAA;QAAK8K,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAE3B/K,OAAA;UAAK8K,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B/K,OAAA;YAAK8K,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B/K,OAAA;cAAA+K,QAAA,GAAI,eAAa,EAACjJ,UAAU,CAAC+H,MAAM,EAAC,GAAC;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1CnL,OAAA;cACEsE,IAAI,EAAC,QAAQ;cACbwG,SAAS,EAAC,UAAU;cACpBM,OAAO,EAAEA,CAAA,KAAMnJ,iBAAiB,CAAC,CAACD,cAAc,CAAE;cAAA+I,QAAA,EAEjD/I,cAAc,GAAG,MAAM,GAAG;YAAM;cAAAgJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELnJ,cAAc,iBACbhC,OAAA;YAAK8K,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9BjJ,UAAU,CAAC+H,MAAM,KAAK,CAAC,gBACtB7J,OAAA;cAAG8K,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,GAErFrJ,UAAU,CAAC2F,GAAG,CAAE8D,IAAI,iBAClBvL,OAAA;cAAoB8K,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC7C/K,OAAA;gBAAK8K,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B/K,OAAA;kBAAA+K,QAAA,EAAKQ,IAAI,CAACrH;gBAAI;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpBnL,OAAA;kBAAM8K,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAEQ,IAAI,CAACjH;gBAAI;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACNnL,OAAA;gBAAG8K,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEQ,IAAI,CAACpH,WAAW,IAAI;cAAgB;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1EnL,OAAA;gBAAK8K,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB/K,OAAA;kBAAA+K,QAAA,GAAOQ,IAAI,CAACC,OAAO,CAACC,UAAU,EAAC,SAAO;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7CnL,OAAA;kBAAA+K,QAAA,EAAM;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACdnL,OAAA;kBAAA+K,QAAA,GAAM,UAAQ,EAAC,IAAI1B,IAAI,CAACkC,IAAI,CAACnC,SAAS,CAAC,CAACsC,kBAAkB,CAAC,CAAC;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNnL,OAAA;gBAAK8K,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B/K,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACbwG,SAAS,EAAC,uBAAuB;kBACjCM,OAAO,EAAEA,CAAA,KAAMlI,iBAAiB,CAACqI,IAAI,CAAE;kBAAAR,QAAA,EACxC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnL,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACbwG,SAAS,EAAC,sBAAsB;kBAChCM,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIO,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;sBAChEjM,iBAAiB,CAACkM,gBAAgB,CAACN,IAAI,CAACjH,IAAI,EAAEiH,IAAI,CAAChH,YAAY,CAAC;sBAChEtB,cAAc,CAAC,CAAC;oBAClB;kBACF,CAAE;kBAAA8H,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GA/BEI,IAAI,CAAC3H,GAAG;cAAAoH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCb,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNnL,OAAA;UAAK8K,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B/K,OAAA;YAAA+K,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE3BnL,OAAA;YAAK8K,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB/K,OAAA;cAAA+K,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1BnL,OAAA;cACEsE,IAAI,EAAC,MAAM;cACX4B,KAAK,EAAE1F,QAAS;cAChBsL,QAAQ,EAAG9F,CAAC,IAAKvF,WAAW,CAACuF,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;cAC7C6F,WAAW,EAAC,iBAAiB;cAC7BjB,SAAS,EAAEpJ,MAAM,CAAClB,QAAQ,GAAG,OAAO,GAAG;YAAG;cAAAwK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,EACDzJ,MAAM,CAAClB,QAAQ,iBAAIR,OAAA;cAAK8K,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAErJ,MAAM,CAAClB;YAAQ;cAAAwK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAENnL,OAAA;YAAK8K,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB/K,OAAA;cAAA+K,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1BnL,OAAA;cACEkG,KAAK,EAAExF,eAAgB;cACvBoL,QAAQ,EAAG9F,CAAC,IAAKrF,kBAAkB,CAACqF,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;cACpD6F,WAAW,EAAC,wBAAwB;cACpCC,IAAI,EAAC;YAAG;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGH,CAAC,eAGNnL,OAAA;UAAK8K,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B/K,OAAA;YAAA+K,QAAA,GAAI,mBAAiB,EAACjK,cAAc,CAAC+I,MAAM,EAAC,GAAC;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACjDzJ,MAAM,CAAC2B,MAAM,iBAAIrD,OAAA;YAAK8K,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAErJ,MAAM,CAAC2B;UAAM;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACrEzJ,MAAM,CAACoI,cAAc,iBAAI9J,OAAA;YAAK8K,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAErJ,MAAM,CAACoI;UAAc;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEtFnL,OAAA;YAAK8K,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BjK,cAAc,CAAC+I,MAAM,KAAK,CAAC,gBAC1B7J,OAAA;cAAK8K,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAENrK,cAAc,CAAC2G,GAAG,CAAC,CAAC/D,KAAK,EAAEuI,KAAK,kBAC9BjM,OAAA;cAAqB8K,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7C/K,OAAA;gBAAK8K,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/K,OAAA;kBAAM8K,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAErH,KAAK,CAAC6G;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDnL,OAAA;kBAAM8K,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAErH,KAAK,CAACY;gBAAI;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC/CzH,KAAK,CAACwI,QAAQ,iBAAIlM,OAAA;kBAAM8K,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACNnL,OAAA;gBAAK8K,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B/K,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACb8G,OAAO,EAAEA,CAAA,KAAM9D,iBAAiB,CAAC5D,KAAK,CAAE;kBACxCoH,SAAS,EAAC,UAAU;kBACpBqB,KAAK,EAAC,iBAAiB;kBAAApB,QAAA,EACxB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnL,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACb8G,OAAO,EAAEA,CAAA,KAAM1D,eAAe,CAACuE,KAAK,EAAEG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,KAAK,GAAG,CAAC,CAAC,CAAE;kBAC9DnB,SAAS,EAAC,UAAU;kBACpBO,QAAQ,EAAEY,KAAK,KAAK,CAAE;kBACtBE,KAAK,EAAC,SAAS;kBAAApB,QAAA,EAChB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnL,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACb8G,OAAO,EAAEA,CAAA,KAAM1D,eAAe,CAACuE,KAAK,EAAEG,IAAI,CAACE,GAAG,CAACxL,cAAc,CAAC+I,MAAM,GAAG,CAAC,EAAEoC,KAAK,GAAG,CAAC,CAAC,CAAE;kBACtFnB,SAAS,EAAC,UAAU;kBACpBO,QAAQ,EAAEY,KAAK,KAAKnL,cAAc,CAAC+I,MAAM,GAAG,CAAE;kBAC9CsC,KAAK,EAAC,WAAW;kBAAApB,QAAA,EAClB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnL,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACb8G,OAAO,EAAEA,CAAA,KAAMpE,iBAAiB,CAACtD,KAAK,CAAE;kBACxCoH,SAAS,EAAC,iBAAiB;kBAC3BqB,KAAK,EAAC,cAAc;kBAAApB,QAAA,EACrB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAzCEzH,KAAK,CAACE,GAAG;cAAAoH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0Cd,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnL,OAAA;QAAK8K,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B/K,OAAA;UAAK8K,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B/K,OAAA;YAAA+K,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAGNnL,OAAA;UAAK8K,SAAS,EAAC,qBAAqB;UAACyB,KAAK,EAAE;YAC1CC,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,QAAQ;YACjBC,MAAM,EAAE;UACV,CAAE;UAAA7B,QAAA,gBACA/K,OAAA;YAAIuM,KAAK,EAAE;cAAEK,MAAM,EAAE,YAAY;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAA9B,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLnL,OAAA;YAAKuM,KAAK,EAAE;cACVO,QAAQ,EAAE,UAAU;cACpBD,KAAK,EAAE,SAAS;cAChBE,YAAY,EAAE,MAAM;cACpBJ,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BE,YAAY,EAAE,KAAK;cACnBD,MAAM,EAAE;YACV,CAAE;YAAA1B,QAAA,gBACA/K,OAAA;cAAA+K,QAAA,EAAQ;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCnL,OAAA;cAAIuM,KAAK,EAAE;gBAAEK,MAAM,EAAE,iBAAiB;gBAAEI,WAAW,EAAE;cAAO,CAAE;cAAAjC,QAAA,gBAC5D/K,OAAA;gBAAA+K,QAAA,EAAI;cAAkF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3FnL,OAAA;gBAAA+K,QAAA,EAAI;cAAuC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDnL,OAAA;gBAAA+K,QAAA,EAAI;cAAqF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENnL,OAAA;YAAK8K,SAAS,EAAC,qBAAqB;YAACyB,KAAK,EAAE;cAC1CU,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE,sCAAsC;cAC3DC,GAAG,EAAE;YACP,CAAE;YAAApC,QAAA,gBAEA/K,OAAA;cAAK8K,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/K,OAAA;gBAAOuM,KAAK,EAAE;kBACZU,OAAO,EAAE,OAAO;kBAChBF,YAAY,EAAE,QAAQ;kBACtBK,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAA9B,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRnL,OAAA;gBACEkG,KAAK,EAAE5F,iBAAiB,CAACkE,UAAU,IAAI,EAAG;gBAC1CsH,QAAQ,EAAE/F,oBAAqB;gBAC/BsF,QAAQ,EAAE7I,OAAO,CAACN,SAAU;gBAC5B4I,SAAS,EAAEpJ,MAAM,CAAC8C,UAAU,GAAG,OAAO,GAAG,EAAG;gBAC5C+H,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACbV,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBI,QAAQ,EAAE,MAAM;kBAChBN,eAAe,EAAE;gBACnB,CAAE;gBAAAzB,QAAA,gBAEF/K,OAAA;kBAAQkG,KAAK,EAAC,EAAE;kBAAA6E,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACxCjJ,SAAS,CAACuF,GAAG,CAACtB,QAAQ,iBACrBnG,OAAA;kBAA0BkG,KAAK,EAAEC,QAAQ,CAAC9B,EAAG;kBAAA0G,QAAA,EAC1C5E,QAAQ,CAACjC;gBAAI,GADHiC,QAAQ,CAAC9B,EAAE;kBAAA2G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACRzJ,MAAM,CAAC8C,UAAU,iBAChBxE,OAAA;gBAAKuM,KAAK,EAAE;kBAAEM,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,UAAU;kBAAEQ,SAAS,EAAE;gBAAU,CAAE;gBAAAvC,QAAA,EAC1ErJ,MAAM,CAAC8C;cAAU;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNnL,OAAA;cAAK8K,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/K,OAAA;gBAAOuM,KAAK,EAAE;kBACZU,OAAO,EAAE,OAAO;kBAChBF,YAAY,EAAE,QAAQ;kBACtBK,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAA9B,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRnL,OAAA;gBACEkG,KAAK,EAAE5F,iBAAiB,CAACmE,UAAU,IAAI,EAAG;gBAC1CqH,QAAQ,EAAEpF,oBAAqB;gBAC/B2E,QAAQ,EAAE,CAAC/K,iBAAiB,CAACkE,UAAU,IAAIhC,OAAO,CAACJ,UAAW;gBAC9D0I,SAAS,EAAEpJ,MAAM,CAAC+C,UAAU,GAAG,OAAO,GAAG,EAAG;gBAC5C8H,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACbV,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBI,QAAQ,EAAE,MAAM;kBAChBN,eAAe,EAAE;gBACnB,CAAE;gBAAAzB,QAAA,gBAEF/K,OAAA;kBAAQkG,KAAK,EAAC,EAAE;kBAAA6E,QAAA,EACb,CAACzK,iBAAiB,CAACkE,UAAU,GAAG,uBAAuB,GAAG;gBAAiB;kBAAAwG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,EACR/I,UAAU,CAACqF,GAAG,CAACjB,QAAQ,iBACtBxG,OAAA;kBAA0BkG,KAAK,EAAEM,QAAQ,CAACnC,EAAG;kBAAA0G,QAAA,EAC1CvE,QAAQ,CAACtC;gBAAI,GADHsC,QAAQ,CAACnC,EAAE;kBAAA2G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACRzJ,MAAM,CAAC+C,UAAU,iBAChBzE,OAAA;gBAAKuM,KAAK,EAAE;kBAAEM,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,UAAU;kBAAEQ,SAAS,EAAE;gBAAU,CAAE;gBAAAvC,QAAA,EAC1ErJ,MAAM,CAAC+C;cAAU;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNnL,OAAA;cAAK8K,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/K,OAAA;gBAAOuM,KAAK,EAAE;kBACZU,OAAO,EAAE,OAAO;kBAChBF,YAAY,EAAE,QAAQ;kBACtBK,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAA9B,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRnL,OAAA;gBACEkG,KAAK,EAAE5F,iBAAiB,CAACoE,aAAa,IAAI,EAAG;gBAC7CoH,QAAQ,EAAElF,uBAAwB;gBAClCyE,QAAQ,EAAE,CAAC/K,iBAAiB,CAACmE,UAAU,IAAIjC,OAAO,CAACsE,aAAc;gBACjEyF,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACbV,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBI,QAAQ,EAAE,MAAM;kBAChBN,eAAe,EAAE;gBACnB,CAAE;gBAAAzB,QAAA,gBAEF/K,OAAA;kBAAQkG,KAAK,EAAC,EAAE;kBAAA6E,QAAA,EACb,CAACzK,iBAAiB,CAACmE,UAAU,GAAG,uBAAuB,GAAG;gBAA+B;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,EACRrE,aAAa,CAACW,GAAG,CAACZ,WAAW,iBAC5B7G,OAAA;kBAA6BkG,KAAK,EAAEW,WAAW,CAACxC,EAAG;kBAAA0G,QAAA,EAChDlE,WAAW,CAAC3C;gBAAI,GADN2C,WAAW,CAACxC,EAAE;kBAAA2G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEnB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELzJ,MAAM,CAACiD,SAAS,iBACf3E,OAAA;YAAKuM,KAAK,EAAE;cAAEM,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEQ,SAAS,EAAE;YAAS,CAAE;YAAAvC,QAAA,EACzErJ,MAAM,CAACiD;UAAS;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CACN,EACAzJ,MAAM,CAACuD,YAAY,iBAClBjF,OAAA;YAAKuM,KAAK,EAAE;cAAEM,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEQ,SAAS,EAAE,QAAQ;cAAEF,UAAU,EAAE;YAAO,CAAE;YAAArC,QAAA,GAAC,eAC5F,EAACrJ,MAAM,CAACuD,YAAY;UAAA;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CACN,EAGAzI,gBAAgB,IAAIA,gBAAgB,CAAC6K,aAAa,CAAC1D,MAAM,GAAG,CAAC,iBAC5D7J,OAAA;YAAKuM,KAAK,EAAE;cACVe,SAAS,EAAE,MAAM;cACjBX,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBI,QAAQ,EAAE;YACZ,CAAE;YAAA/B,QAAA,gBACA/K,OAAA;cAAA+K,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAClCzI,gBAAgB,CAAC6K,aAAa,CAAC9F,GAAG,CAAC,CAAC8D,IAAI,EAAEU,KAAK,kBAC9CjM,OAAA;cAAiBuM,KAAK,EAAE;gBAAEe,SAAS,EAAE;cAAS,CAAE;cAAAvC,QAAA,gBAC9C/K,OAAA;gBAAA+K,QAAA,GAASQ,IAAI,CAACjH,IAAI,KAAK,UAAU,GAAG,UAAU,GAAG,aAAa,EAAC,QAAM;cAAA;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACI,IAAI,CAACrH,IAAI,EACxFqH,IAAI,CAACpH,WAAW,iBAAInE,OAAA;gBAAKuM,KAAK,EAAE;kBAAEM,KAAK,EAAE;gBAAU,CAAE;gBAAA9B,QAAA,EAAEQ,IAAI,CAACpH;cAAW;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAFvEc,KAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAGAzI,gBAAgB,IAAIA,gBAAgB,CAAC8K,sBAAsB,CAAC3D,MAAM,GAAG,CAAC,IAAI,CAACvJ,iBAAiB,CAACoE,aAAa,iBACzG1E,OAAA;YAAKuM,KAAK,EAAE;cACVe,SAAS,EAAE,MAAM;cACjBX,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBI,QAAQ,EAAE;YACZ,CAAE;YAAA/B,QAAA,gBACA/K,OAAA;cAAA+K,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,uBAAmB,EAACzI,gBAAgB,CAAC8K,sBAAsB,CAAC3D,MAAM,EAAC,mEAEjG;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,EAGA7K,iBAAiB,CAACmE,UAAU,IAAI,CAAC/C,MAAM,CAACuD,YAAY,iBACnDjF,OAAA;YAAKuM,KAAK,EAAE;cACVe,SAAS,EAAE,MAAM;cACjBX,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBI,QAAQ,EAAE;YACZ,CAAE;YAAA/B,QAAA,gBACA/K,OAAA;cAAA+K,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,oCAAgC,EAAC7K,iBAAiB,CAACoE,aAAa,GAAG,aAAa,GAAG,UAAU,EAAC,GACnI;UAAA;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNnL,OAAA;UAAK8K,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B/K,OAAA;YACEsE,IAAI,EAAC,MAAM;YACXyH,WAAW,EAAC,kBAAkB;YAC9B7F,KAAK,EAAEhF,UAAW;YAClB4K,QAAQ,EAAG9F,CAAC,IAAK7E,aAAa,CAAC6E,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;YAC/C4E,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNnL,OAAA;UAAK8K,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B/K,OAAA;YACEsE,IAAI,EAAC,QAAQ;YACb8G,OAAO,EAAEA,CAAA,KAAMnK,iBAAiB,CAAC,KAAK,CAAE;YACxC6J,SAAS,EAAE,eAAe9J,cAAc,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAA+J,QAAA,GACtE,OACM,EAACF,aAAa,CAACF,GAAG,EAAC,GAC1B;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACRjB,MAAM,CAACoB,OAAO,CAAC7L,sBAAsB,CAAC,CAACgI,GAAG,CAAC,CAAC,CAACmD,UAAU,EAAEN,OAAO,CAAC,kBAChEtK,OAAA;YAEEsE,IAAI,EAAC,QAAQ;YACb8G,OAAO,EAAEA,CAAA,KAAMnK,iBAAiB,CAAC2J,UAAU,CAAE;YAC7CE,SAAS,EAAE,eAAe9J,cAAc,KAAK4J,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAAG,QAAA,GAEzET,OAAO,CAAC6B,KAAK,EAAC,IAAE,EAACtB,aAAa,CAACD,UAAU,CAAC,EAAC,GAC9C;UAAA,GANOA,UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMT,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNnL,OAAA;UAAK8K,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC/K,OAAA;YAAK8K,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpC/K,OAAA;cAAM8K,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3DnL,OAAA;cACEsE,IAAI,EAAC,QAAQ;cACb8G,OAAO,EAAEpD,qBAAsB;cAC/B8C,SAAS,EAAC,wBAAwB;cAClCqB,KAAK,EAAE,cAAcjE,cAAc,CAAC2B,MAAM,kBAAmB;cAAAkB,QAAA,GAC9D,mBACkB,EAAC7C,cAAc,CAAC2B,MAAM,EAAC,GAC1C;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnL,OAAA;cACEsE,IAAI,EAAC,QAAQ;cACb8G,OAAO,EAAEhD,uBAAwB;cACjC0C,SAAS,EAAC,wBAAwB;cAClCqB,KAAK,EAAC,8BAA8B;cAAApB,QAAA,EACrC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnL,OAAA;cACEsE,IAAI,EAAC,QAAQ;cACb8G,OAAO,EAAE7C,8BAA+B;cACxCuC,SAAS,EAAC,wBAAwB;cAClCqB,KAAK,EAAE,cAAcvL,eAAe,CAACiJ,MAAM,mBAAoB;cAAAkB,QAAA,GAChE,cACa,EAACnK,eAAe,CAACiJ,MAAM,EAAC,GACtC;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnL,OAAA;cACEsE,IAAI,EAAC,QAAQ;cACb8G,OAAO,EAAE5C,oBAAqB;cAC9BsC,SAAS,EAAC,mCAAmC;cAC7CqB,KAAK,EAAC,2BAA2B;cACjCd,QAAQ,EAAEvK,cAAc,CAAC+I,MAAM,KAAK,CAAE;cAAAkB,QAAA,EACvC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNnL,OAAA;YAAK8K,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAC/BjK,cAAc,CAAC+I,MAAM,EAAC,MAAI,EAACjJ,eAAe,CAACiJ,MAAM,EAAC,kBACrD;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnL,OAAA;UAAK8K,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzB7C,cAAc,CAACT,GAAG,CAAC/D,KAAK,IAAI;YAC3B,MAAMuD,UAAU,GAAGnG,cAAc,CAACoG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC;YAChE,oBACE5D,OAAA;cAEE8K,SAAS,EAAE,cAAc7D,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;cACxDmE,OAAO,EAAEA,CAAA,KAAMpE,iBAAiB,CAACtD,KAAK,CAAE;cAAAqH,QAAA,gBAExC/K,OAAA;gBAAK8K,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7B/K,OAAA;kBACEsE,IAAI,EAAC,UAAU;kBACfmJ,OAAO,EAAExG,UAAW;kBACpB6E,QAAQ,EAAEA,CAAA,KAAM9E,iBAAiB,CAACtD,KAAK;gBAAE;kBAAAsH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAK8K,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B/K,OAAA;kBAAK8K,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAErH,KAAK,CAAC6G;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/CnL,OAAA;kBAAK8K,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB/K,OAAA;oBAAM8K,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAErH,KAAK,CAACY;kBAAI;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC/CzH,KAAK,CAACwI,QAAQ,iBAAIlM,OAAA;oBAAM8K,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAClEzH,KAAK,CAACgK,WAAW,iBAAI1N,OAAA;oBAAM8K,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAlBDzH,KAAK,CAACE,GAAG;cAAAoH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBX,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL7J,eAAe,IAAIE,WAAW,iBAC7BxB,OAAA,CAACH,gBAAgB;MACf6D,KAAK,EAAElC,WAAY;MACnBtB,MAAM,EAAEqH,qBAAsB;MAC9BpH,QAAQ,EAAEA,CAAA,KAAM;QACdoB,kBAAkB,CAAC,KAAK,CAAC;QACzBE,cAAc,CAAC,IAAI,CAAC;MACtB;IAAE;MAAAuJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEA/J,WAAW,iBACVpB,OAAA,CAACF,WAAW;MACVuD,MAAM,EAAEvC,cAAe;MACvBN,QAAQ,EAAEA,QAAS;MACnBmN,OAAO,EAAEA,CAAA,KAAMtM,cAAc,CAAC,KAAK;IAAE;MAAA2J,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9K,EAAA,CAngCIJ,WAAW;AAAA2N,EAAA,GAAX3N,WAAW;AAqgCjB,eAAeA,WAAW;AAAC,IAAA2N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}