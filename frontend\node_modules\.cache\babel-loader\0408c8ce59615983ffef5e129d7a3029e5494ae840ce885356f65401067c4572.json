{"ast": null, "code": "import React,{useState,useEffect}from'react';import axios from'axios';import Pagination from'./Pagination';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const API_BASE_URL='http://localhost:5000/api';const CategoryManagement=()=>{const[divisions,setDivisions]=useState([]);const[categories,setCategories]=useState([]);const[allCategories,setAllCategories]=useState([]);const[firmNatures,setFirmNatures]=useState([]);const[selectedDivision,setSelectedDivision]=useState('');const[selectedCategory,setSelectedCategory]=useState('');const[newCategory,setNewCategory]=useState('');const[newFirmNature,setNewFirmNature]=useState('');const[loading,setLoading]=useState(false);const[currentPage,setCurrentPage]=useState(1);const[itemsPerPage]=useState(10);useEffect(()=>{fetchDivisions();fetchAllCategories();},[]);useEffect(()=>{if(selectedDivision){fetchCategories(selectedDivision);setSelectedCategory('');// Reset category selection when division changes\n}else{setCategories([]);setSelectedCategory('');}},[selectedDivision]);useEffect(()=>{if(selectedCategory){fetchFirmNatures(selectedCategory);}},[selectedCategory]);const fetchDivisions=async()=>{try{const response=await axios.get(`${API_BASE_URL}/divisions`);setDivisions(response.data);}catch(error){console.error('Error fetching divisions:',error);}};const fetchCategories=async divisionId=>{try{const response=await axios.get(`${API_BASE_URL}/categories/division/${divisionId}`);setCategories(response.data);}catch(error){console.error('Error fetching categories:',error);}};const fetchAllCategories=async()=>{try{const response=await axios.get(`${API_BASE_URL}/categories`);setAllCategories(response.data);}catch(error){console.error('Error fetching all categories:',error);}};const fetchFirmNatures=async categoryId=>{try{const response=await axios.get(`${API_BASE_URL}/categories/${categoryId}`);setFirmNatures(response.data.firmNatures||[]);}catch(error){console.error('Error fetching firm natures:',error);}};const handleCategorySubmit=async e=>{e.preventDefault();if(!newCategory.trim()||!selectedDivision)return;setLoading(true);try{await axios.post(`${API_BASE_URL}/categories`,{Name:newCategory.trim(),DivisionId:parseInt(selectedDivision)});setNewCategory('');fetchCategories(selectedDivision);fetchAllCategories();// Refresh the overview\nalert('Category created successfully!');}catch(error){var _error$response,_error$response$data,_error$response2,_error$response2$data;console.error('Error creating category:',error);if((_error$response=error.response)!==null&&_error$response!==void 0&&(_error$response$data=_error$response.data)!==null&&_error$response$data!==void 0&&_error$response$data.errors){const errorMessages=Object.values(error.response.data.errors).flat().join(', ');alert(`Error creating category: ${errorMessages}`);}else if((_error$response2=error.response)!==null&&_error$response2!==void 0&&(_error$response2$data=_error$response2.data)!==null&&_error$response2$data!==void 0&&_error$response2$data.message){alert(`Error creating category: ${error.response.data.message}`);}else{alert('Error creating category. Please try again.');}}finally{setLoading(false);}};const handleFirmNatureSubmit=async e=>{e.preventDefault();if(!newFirmNature.trim()||!selectedCategory)return;setLoading(true);try{await axios.post(`${API_BASE_URL}/firmnatures`,{Name:newFirmNature.trim(),CategoryId:parseInt(selectedCategory)});setNewFirmNature('');fetchFirmNatures(selectedCategory);fetchAllCategories();// Refresh the overview\nalert('Firm nature created successfully!');}catch(error){var _error$response3,_error$response3$data,_error$response4,_error$response4$data;console.error('Error creating firm nature:',error);if((_error$response3=error.response)!==null&&_error$response3!==void 0&&(_error$response3$data=_error$response3.data)!==null&&_error$response3$data!==void 0&&_error$response3$data.errors){const errorMessages=Object.values(error.response.data.errors).flat().join(', ');alert(`Error creating firm nature: ${errorMessages}`);}else if((_error$response4=error.response)!==null&&_error$response4!==void 0&&(_error$response4$data=_error$response4.data)!==null&&_error$response4$data!==void 0&&_error$response4$data.message){alert(`Error creating firm nature: ${error.response.data.message}`);}else{alert('Error creating firm nature. Please try again.');}}finally{setLoading(false);}};// Calculate pagination for categories overview\nconst totalItems=allCategories.length;const startIndex=(currentPage-1)*itemsPerPage;const endIndex=startIndex+itemsPerPage;const currentItems=allCategories.slice(startIndex,endIndex);const handlePageChange=page=>{setCurrentPage(page);};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Category Management\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Add Category\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"divisionSelect\",children:\"Select Division\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"divisionSelect\",className:\"form-control\",value:selectedDivision,onChange:e=>setSelectedDivision(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Division\"}),divisions.map(division=>/*#__PURE__*/_jsx(\"option\",{value:division.id,children:division.name},division.id))]})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleCategorySubmit,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"categoryName\",children:\"Category Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"categoryName\",className:\"form-control\",value:newCategory,onChange:e=>setNewCategory(e.target.value),placeholder:\"Enter category name\",required:true})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"btn btn-primary\",disabled:loading||!selectedDivision,children:loading?'Adding...':'Add Category'})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Add Firm Nature\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"categorySelect\",children:\"Select Category\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"categorySelect\",className:\"form-control\",value:selectedCategory,onChange:e=>setSelectedCategory(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Category\"}),categories.map(category=>/*#__PURE__*/_jsx(\"option\",{value:category.id,children:category.name},category.id))]})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleFirmNatureSubmit,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"firmNatureName\",children:\"Firm Nature Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"firmNatureName\",className:\"form-control\",value:newFirmNature,onChange:e=>setNewFirmNature(e.target.value),placeholder:\"Enter firm nature name\",required:true})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"btn btn-primary\",disabled:loading||!selectedCategory,children:loading?'Adding...':'Add Firm Nature'})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Categories Overview\"}),allCategories.length===0?/*#__PURE__*/_jsx(\"p\",{children:\"No categories found. Add some categories to see them here.\"}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"table\",{className:\"table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Division\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Category\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Firm Natures\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:currentItems.map(category=>{var _category$division;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:((_category$division=category.division)===null||_category$division===void 0?void 0:_category$division.name)||'Unknown Division'}),/*#__PURE__*/_jsx(\"td\",{children:category.name}),/*#__PURE__*/_jsx(\"td\",{children:category.firmNatures&&category.firmNatures.length>0?category.firmNatures.map(fn=>fn.name).join(', '):'No firm natures'})]},category.id);})})]}),/*#__PURE__*/_jsx(Pagination,{currentPage:currentPage,totalItems:totalItems,itemsPerPage:itemsPerPage,onPageChange:handlePageChange})]})]})]});};export default CategoryManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Pagination", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "API_BASE_URL", "CategoryManagement", "divisions", "setDivisions", "categories", "setCategories", "allCategories", "setAllCategories", "firmNatures", "setFirmNatures", "selectedDivision", "setSelectedDivision", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "newCategory", "setNewCategory", "newFirmNature", "setNewFirmNature", "loading", "setLoading", "currentPage", "setCurrentPage", "itemsPerPage", "fetchDivisions", "fetchAllCategories", "fetchCategories", "fetchFirmNatures", "response", "get", "data", "error", "console", "divisionId", "categoryId", "handleCategorySubmit", "e", "preventDefault", "trim", "post", "Name", "DivisionId", "parseInt", "alert", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "errors", "errorMessages", "Object", "values", "flat", "join", "message", "handleFirmNatureSubmit", "CategoryId", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data", "totalItems", "length", "startIndex", "endIndex", "currentItems", "slice", "handlePageChange", "page", "children", "className", "htmlFor", "id", "value", "onChange", "target", "map", "division", "name", "onSubmit", "type", "placeholder", "required", "disabled", "category", "_category$division", "fn", "onPageChange"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/CategoryManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport Pagination from './Pagination';\r\n\r\nconst API_BASE_URL = 'http://localhost:5000/api';\r\n\r\nconst CategoryManagement = () => {\r\n  const [divisions, setDivisions] = useState([]);\r\n  const [categories, setCategories] = useState([]);\r\n  const [allCategories, setAllCategories] = useState([]);\r\n  const [firmNatures, setFirmNatures] = useState([]);\r\n  const [selectedDivision, setSelectedDivision] = useState('');\r\n  const [selectedCategory, setSelectedCategory] = useState('');\r\n  const [newCategory, setNewCategory] = useState('');\r\n  const [newFirmNature, setNewFirmNature] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [itemsPerPage] = useState(10);\r\n\r\n  useEffect(() => {\r\n    fetchDivisions();\r\n    fetchAllCategories();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (selectedDivision) {\r\n      fetchCategories(selectedDivision);\r\n      setSelectedCategory(''); // Reset category selection when division changes\r\n    } else {\r\n      setCategories([]);\r\n      setSelectedCategory('');\r\n    }\r\n  }, [selectedDivision]);\r\n\r\n  useEffect(() => {\r\n    if (selectedCategory) {\r\n      fetchFirmNatures(selectedCategory);\r\n    }\r\n  }, [selectedCategory]);\r\n\r\n  const fetchDivisions = async () => {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/divisions`);\r\n      setDivisions(response.data);\r\n    } catch (error) {\r\n      console.error('Error fetching divisions:', error);\r\n    }\r\n  };\r\n\r\n  const fetchCategories = async (divisionId) => {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/categories/division/${divisionId}`);\r\n      setCategories(response.data);\r\n    } catch (error) {\r\n      console.error('Error fetching categories:', error);\r\n    }\r\n  };\r\n\r\n  const fetchAllCategories = async () => {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/categories`);\r\n      setAllCategories(response.data);\r\n    } catch (error) {\r\n      console.error('Error fetching all categories:', error);\r\n    }\r\n  };\r\n\r\n  const fetchFirmNatures = async (categoryId) => {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/categories/${categoryId}`);\r\n      setFirmNatures(response.data.firmNatures || []);\r\n    } catch (error) {\r\n      console.error('Error fetching firm natures:', error);\r\n    }\r\n  };\r\n\r\n  const handleCategorySubmit = async (e) => {\r\n    e.preventDefault();\r\n    if (!newCategory.trim() || !selectedDivision) return;\r\n\r\n    setLoading(true);\r\n    try {\r\n      await axios.post(`${API_BASE_URL}/categories`, {\r\n        Name: newCategory.trim(),\r\n        DivisionId: parseInt(selectedDivision)\r\n      });\r\n      setNewCategory('');\r\n      fetchCategories(selectedDivision);\r\n      fetchAllCategories(); // Refresh the overview\r\n      alert('Category created successfully!');\r\n    } catch (error) {\r\n      console.error('Error creating category:', error);\r\n      if (error.response?.data?.errors) {\r\n        const errorMessages = Object.values(error.response.data.errors).flat().join(', ');\r\n        alert(`Error creating category: ${errorMessages}`);\r\n      } else if (error.response?.data?.message) {\r\n        alert(`Error creating category: ${error.response.data.message}`);\r\n      } else {\r\n        alert('Error creating category. Please try again.');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleFirmNatureSubmit = async (e) => {\r\n    e.preventDefault();\r\n    if (!newFirmNature.trim() || !selectedCategory) return;\r\n\r\n    setLoading(true);\r\n    try {\r\n      await axios.post(`${API_BASE_URL}/firmnatures`, {\r\n        Name: newFirmNature.trim(),\r\n        CategoryId: parseInt(selectedCategory)\r\n      });\r\n      setNewFirmNature('');\r\n      fetchFirmNatures(selectedCategory);\r\n      fetchAllCategories(); // Refresh the overview\r\n      alert('Firm nature created successfully!');\r\n    } catch (error) {\r\n      console.error('Error creating firm nature:', error);\r\n      if (error.response?.data?.errors) {\r\n        const errorMessages = Object.values(error.response.data.errors).flat().join(', ');\r\n        alert(`Error creating firm nature: ${errorMessages}`);\r\n      } else if (error.response?.data?.message) {\r\n        alert(`Error creating firm nature: ${error.response.data.message}`);\r\n      } else {\r\n        alert('Error creating firm nature. Please try again.');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Calculate pagination for categories overview\r\n  const totalItems = allCategories.length;\r\n  const startIndex = (currentPage - 1) * itemsPerPage;\r\n  const endIndex = startIndex + itemsPerPage;\r\n  const currentItems = allCategories.slice(startIndex, endIndex);\r\n\r\n  const handlePageChange = (page) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <h1>Category Management</h1>\r\n\r\n      <div className=\"row\">\r\n        <div className=\"col\">\r\n          <div className=\"card\">\r\n            <h3>Add Category</h3>\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"divisionSelect\">Select Division</label>\r\n              <select\r\n                id=\"divisionSelect\"\r\n                className=\"form-control\"\r\n                value={selectedDivision}\r\n                onChange={(e) => setSelectedDivision(e.target.value)}\r\n              >\r\n                <option value=\"\">Select Division</option>\r\n                {divisions.map((division) => (\r\n                  <option key={division.id} value={division.id}>\r\n                    {division.name}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            <form onSubmit={handleCategorySubmit}>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"categoryName\">Category Name</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"categoryName\"\r\n                  className=\"form-control\"\r\n                  value={newCategory}\r\n                  onChange={(e) => setNewCategory(e.target.value)}\r\n                  placeholder=\"Enter category name\"\r\n                  required\r\n                />\r\n              </div>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary\"\r\n                disabled={loading || !selectedDivision}\r\n              >\r\n                {loading ? 'Adding...' : 'Add Category'}\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"col\">\r\n          <div className=\"card\">\r\n            <h3>Add Firm Nature</h3>\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"categorySelect\">Select Category</label>\r\n              <select\r\n                id=\"categorySelect\"\r\n                className=\"form-control\"\r\n                value={selectedCategory}\r\n                onChange={(e) => setSelectedCategory(e.target.value)}\r\n              >\r\n                <option value=\"\">Select Category</option>\r\n                {categories.map((category) => (\r\n                  <option key={category.id} value={category.id}>\r\n                    {category.name}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            <form onSubmit={handleFirmNatureSubmit}>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"firmNatureName\">Firm Nature Name</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"firmNatureName\"\r\n                  className=\"form-control\"\r\n                  value={newFirmNature}\r\n                  onChange={(e) => setNewFirmNature(e.target.value)}\r\n                  placeholder=\"Enter firm nature name\"\r\n                  required\r\n                />\r\n              </div>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary\"\r\n                disabled={loading || !selectedCategory}\r\n              >\r\n                {loading ? 'Adding...' : 'Add Firm Nature'}\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"card\">\r\n        <h3>Categories Overview</h3>\r\n        {allCategories.length === 0 ? (\r\n          <p>No categories found. Add some categories to see them here.</p>\r\n        ) : (\r\n          <>\r\n            <table className=\"table\">\r\n              <thead>\r\n                <tr>\r\n                  <th>Division</th>\r\n                  <th>Category</th>\r\n                  <th>Firm Natures</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {currentItems.map((category) => (\r\n                  <tr key={category.id}>\r\n                    <td>{category.division?.name || 'Unknown Division'}</td>\r\n                    <td>{category.name}</td>\r\n                    <td>\r\n                      {category.firmNatures && category.firmNatures.length > 0\r\n                        ? category.firmNatures.map(fn => fn.name).join(', ')\r\n                        : 'No firm natures'\r\n                      }\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n            <Pagination\r\n              currentPage={currentPage}\r\n              totalItems={totalItems}\r\n              itemsPerPage={itemsPerPage}\r\n              onPageChange={handlePageChange}\r\n            />\r\n          </>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CategoryManagement;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,UAAU,KAAM,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEtC,KAAM,CAAAC,YAAY,CAAG,2BAA2B,CAEhD,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGb,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACc,UAAU,CAAEC,aAAa,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACgB,aAAa,CAAEC,gBAAgB,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACkB,WAAW,CAAEC,cAAc,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACoB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACsB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvB,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACwB,WAAW,CAAEC,cAAc,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC0B,aAAa,CAAEC,gBAAgB,CAAC,CAAG3B,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC4B,OAAO,CAAEC,UAAU,CAAC,CAAG7B,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC8B,WAAW,CAAEC,cAAc,CAAC,CAAG/B,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAACgC,YAAY,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAEnCC,SAAS,CAAC,IAAM,CACdgC,cAAc,CAAC,CAAC,CAChBC,kBAAkB,CAAC,CAAC,CACtB,CAAC,CAAE,EAAE,CAAC,CAENjC,SAAS,CAAC,IAAM,CACd,GAAImB,gBAAgB,CAAE,CACpBe,eAAe,CAACf,gBAAgB,CAAC,CACjCG,mBAAmB,CAAC,EAAE,CAAC,CAAE;AAC3B,CAAC,IAAM,CACLR,aAAa,CAAC,EAAE,CAAC,CACjBQ,mBAAmB,CAAC,EAAE,CAAC,CACzB,CACF,CAAC,CAAE,CAACH,gBAAgB,CAAC,CAAC,CAEtBnB,SAAS,CAAC,IAAM,CACd,GAAIqB,gBAAgB,CAAE,CACpBc,gBAAgB,CAACd,gBAAgB,CAAC,CACpC,CACF,CAAC,CAAE,CAACA,gBAAgB,CAAC,CAAC,CAEtB,KAAM,CAAAW,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACF,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAACoC,GAAG,CAAC,GAAG5B,YAAY,YAAY,CAAC,CAC7DG,YAAY,CAACwB,QAAQ,CAACE,IAAI,CAAC,CAC7B,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACnD,CACF,CAAC,CAED,KAAM,CAAAL,eAAe,CAAG,KAAO,CAAAO,UAAU,EAAK,CAC5C,GAAI,CACF,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAACoC,GAAG,CAAC,GAAG5B,YAAY,wBAAwBgC,UAAU,EAAE,CAAC,CACrF3B,aAAa,CAACsB,QAAQ,CAACE,IAAI,CAAC,CAC9B,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CACpD,CACF,CAAC,CAED,KAAM,CAAAN,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACF,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAACoC,GAAG,CAAC,GAAG5B,YAAY,aAAa,CAAC,CAC9DO,gBAAgB,CAACoB,QAAQ,CAACE,IAAI,CAAC,CACjC,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACxD,CACF,CAAC,CAED,KAAM,CAAAJ,gBAAgB,CAAG,KAAO,CAAAO,UAAU,EAAK,CAC7C,GAAI,CACF,KAAM,CAAAN,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAACoC,GAAG,CAAC,GAAG5B,YAAY,eAAeiC,UAAU,EAAE,CAAC,CAC5ExB,cAAc,CAACkB,QAAQ,CAACE,IAAI,CAACrB,WAAW,EAAI,EAAE,CAAC,CACjD,CAAE,MAAOsB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CACF,CAAC,CAED,KAAM,CAAAI,oBAAoB,CAAG,KAAO,CAAAC,CAAC,EAAK,CACxCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI,CAACtB,WAAW,CAACuB,IAAI,CAAC,CAAC,EAAI,CAAC3B,gBAAgB,CAAE,OAE9CS,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAA3B,KAAK,CAAC8C,IAAI,CAAC,GAAGtC,YAAY,aAAa,CAAE,CAC7CuC,IAAI,CAAEzB,WAAW,CAACuB,IAAI,CAAC,CAAC,CACxBG,UAAU,CAAEC,QAAQ,CAAC/B,gBAAgB,CACvC,CAAC,CAAC,CACFK,cAAc,CAAC,EAAE,CAAC,CAClBU,eAAe,CAACf,gBAAgB,CAAC,CACjCc,kBAAkB,CAAC,CAAC,CAAE;AACtBkB,KAAK,CAAC,gCAAgC,CAAC,CACzC,CAAE,MAAOZ,KAAK,CAAE,KAAAa,eAAA,CAAAC,oBAAA,CAAAC,gBAAA,CAAAC,qBAAA,CACdf,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,IAAAa,eAAA,CAAIb,KAAK,CAACH,QAAQ,UAAAgB,eAAA,YAAAC,oBAAA,CAAdD,eAAA,CAAgBd,IAAI,UAAAe,oBAAA,WAApBA,oBAAA,CAAsBG,MAAM,CAAE,CAChC,KAAM,CAAAC,aAAa,CAAGC,MAAM,CAACC,MAAM,CAACpB,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACkB,MAAM,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CACjFV,KAAK,CAAC,4BAA4BM,aAAa,EAAE,CAAC,CACpD,CAAC,IAAM,KAAAH,gBAAA,CAAIf,KAAK,CAACH,QAAQ,UAAAkB,gBAAA,YAAAC,qBAAA,CAAdD,gBAAA,CAAgBhB,IAAI,UAAAiB,qBAAA,WAApBA,qBAAA,CAAsBO,OAAO,CAAE,CACxCX,KAAK,CAAC,4BAA4BZ,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACwB,OAAO,EAAE,CAAC,CAClE,CAAC,IAAM,CACLX,KAAK,CAAC,4CAA4C,CAAC,CACrD,CACF,CAAC,OAAS,CACRvB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAmC,sBAAsB,CAAG,KAAO,CAAAnB,CAAC,EAAK,CAC1CA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI,CAACpB,aAAa,CAACqB,IAAI,CAAC,CAAC,EAAI,CAACzB,gBAAgB,CAAE,OAEhDO,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAA3B,KAAK,CAAC8C,IAAI,CAAC,GAAGtC,YAAY,cAAc,CAAE,CAC9CuC,IAAI,CAAEvB,aAAa,CAACqB,IAAI,CAAC,CAAC,CAC1BkB,UAAU,CAAEd,QAAQ,CAAC7B,gBAAgB,CACvC,CAAC,CAAC,CACFK,gBAAgB,CAAC,EAAE,CAAC,CACpBS,gBAAgB,CAACd,gBAAgB,CAAC,CAClCY,kBAAkB,CAAC,CAAC,CAAE;AACtBkB,KAAK,CAAC,mCAAmC,CAAC,CAC5C,CAAE,MAAOZ,KAAK,CAAE,KAAA0B,gBAAA,CAAAC,qBAAA,CAAAC,gBAAA,CAAAC,qBAAA,CACd5B,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,IAAA0B,gBAAA,CAAI1B,KAAK,CAACH,QAAQ,UAAA6B,gBAAA,YAAAC,qBAAA,CAAdD,gBAAA,CAAgB3B,IAAI,UAAA4B,qBAAA,WAApBA,qBAAA,CAAsBV,MAAM,CAAE,CAChC,KAAM,CAAAC,aAAa,CAAGC,MAAM,CAACC,MAAM,CAACpB,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACkB,MAAM,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CACjFV,KAAK,CAAC,+BAA+BM,aAAa,EAAE,CAAC,CACvD,CAAC,IAAM,KAAAU,gBAAA,CAAI5B,KAAK,CAACH,QAAQ,UAAA+B,gBAAA,YAAAC,qBAAA,CAAdD,gBAAA,CAAgB7B,IAAI,UAAA8B,qBAAA,WAApBA,qBAAA,CAAsBN,OAAO,CAAE,CACxCX,KAAK,CAAC,+BAA+BZ,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACwB,OAAO,EAAE,CAAC,CACrE,CAAC,IAAM,CACLX,KAAK,CAAC,+CAA+C,CAAC,CACxD,CACF,CAAC,OAAS,CACRvB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAyC,UAAU,CAAGtD,aAAa,CAACuD,MAAM,CACvC,KAAM,CAAAC,UAAU,CAAG,CAAC1C,WAAW,CAAG,CAAC,EAAIE,YAAY,CACnD,KAAM,CAAAyC,QAAQ,CAAGD,UAAU,CAAGxC,YAAY,CAC1C,KAAM,CAAA0C,YAAY,CAAG1D,aAAa,CAAC2D,KAAK,CAACH,UAAU,CAAEC,QAAQ,CAAC,CAE9D,KAAM,CAAAG,gBAAgB,CAAIC,IAAI,EAAK,CACjC9C,cAAc,CAAC8C,IAAI,CAAC,CACtB,CAAC,CAED,mBACEtE,KAAA,QAAAuE,QAAA,eACEzE,IAAA,OAAAyE,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAE5BvE,KAAA,QAAKwE,SAAS,CAAC,KAAK,CAAAD,QAAA,eAClBzE,IAAA,QAAK0E,SAAS,CAAC,KAAK,CAAAD,QAAA,cAClBvE,KAAA,QAAKwE,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBzE,IAAA,OAAAyE,QAAA,CAAI,cAAY,CAAI,CAAC,cACrBvE,KAAA,QAAKwE,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBzE,IAAA,UAAO2E,OAAO,CAAC,gBAAgB,CAAAF,QAAA,CAAC,iBAAe,CAAO,CAAC,cACvDvE,KAAA,WACE0E,EAAE,CAAC,gBAAgB,CACnBF,SAAS,CAAC,cAAc,CACxBG,KAAK,CAAE9D,gBAAiB,CACxB+D,QAAQ,CAAGtC,CAAC,EAAKxB,mBAAmB,CAACwB,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE,CAAAJ,QAAA,eAErDzE,IAAA,WAAQ6E,KAAK,CAAC,EAAE,CAAAJ,QAAA,CAAC,iBAAe,CAAQ,CAAC,CACxClE,SAAS,CAACyE,GAAG,CAAEC,QAAQ,eACtBjF,IAAA,WAA0B6E,KAAK,CAAEI,QAAQ,CAACL,EAAG,CAAAH,QAAA,CAC1CQ,QAAQ,CAACC,IAAI,EADHD,QAAQ,CAACL,EAEd,CACT,CAAC,EACI,CAAC,EACN,CAAC,cAEN1E,KAAA,SAAMiF,QAAQ,CAAE5C,oBAAqB,CAAAkC,QAAA,eACnCvE,KAAA,QAAKwE,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBzE,IAAA,UAAO2E,OAAO,CAAC,cAAc,CAAAF,QAAA,CAAC,eAAa,CAAO,CAAC,cACnDzE,IAAA,UACEoF,IAAI,CAAC,MAAM,CACXR,EAAE,CAAC,cAAc,CACjBF,SAAS,CAAC,cAAc,CACxBG,KAAK,CAAE1D,WAAY,CACnB2D,QAAQ,CAAGtC,CAAC,EAAKpB,cAAc,CAACoB,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE,CAChDQ,WAAW,CAAC,qBAAqB,CACjCC,QAAQ,MACT,CAAC,EACC,CAAC,cACNtF,IAAA,WACEoF,IAAI,CAAC,QAAQ,CACbV,SAAS,CAAC,iBAAiB,CAC3Ba,QAAQ,CAAEhE,OAAO,EAAI,CAACR,gBAAiB,CAAA0D,QAAA,CAEtClD,OAAO,CAAG,WAAW,CAAG,cAAc,CACjC,CAAC,EACL,CAAC,EACJ,CAAC,CACH,CAAC,cAENvB,IAAA,QAAK0E,SAAS,CAAC,KAAK,CAAAD,QAAA,cAClBvE,KAAA,QAAKwE,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBzE,IAAA,OAAAyE,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxBvE,KAAA,QAAKwE,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBzE,IAAA,UAAO2E,OAAO,CAAC,gBAAgB,CAAAF,QAAA,CAAC,iBAAe,CAAO,CAAC,cACvDvE,KAAA,WACE0E,EAAE,CAAC,gBAAgB,CACnBF,SAAS,CAAC,cAAc,CACxBG,KAAK,CAAE5D,gBAAiB,CACxB6D,QAAQ,CAAGtC,CAAC,EAAKtB,mBAAmB,CAACsB,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE,CAAAJ,QAAA,eAErDzE,IAAA,WAAQ6E,KAAK,CAAC,EAAE,CAAAJ,QAAA,CAAC,iBAAe,CAAQ,CAAC,CACxChE,UAAU,CAACuE,GAAG,CAAEQ,QAAQ,eACvBxF,IAAA,WAA0B6E,KAAK,CAAEW,QAAQ,CAACZ,EAAG,CAAAH,QAAA,CAC1Ce,QAAQ,CAACN,IAAI,EADHM,QAAQ,CAACZ,EAEd,CACT,CAAC,EACI,CAAC,EACN,CAAC,cAEN1E,KAAA,SAAMiF,QAAQ,CAAExB,sBAAuB,CAAAc,QAAA,eACrCvE,KAAA,QAAKwE,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBzE,IAAA,UAAO2E,OAAO,CAAC,gBAAgB,CAAAF,QAAA,CAAC,kBAAgB,CAAO,CAAC,cACxDzE,IAAA,UACEoF,IAAI,CAAC,MAAM,CACXR,EAAE,CAAC,gBAAgB,CACnBF,SAAS,CAAC,cAAc,CACxBG,KAAK,CAAExD,aAAc,CACrByD,QAAQ,CAAGtC,CAAC,EAAKlB,gBAAgB,CAACkB,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE,CAClDQ,WAAW,CAAC,wBAAwB,CACpCC,QAAQ,MACT,CAAC,EACC,CAAC,cACNtF,IAAA,WACEoF,IAAI,CAAC,QAAQ,CACbV,SAAS,CAAC,iBAAiB,CAC3Ba,QAAQ,CAAEhE,OAAO,EAAI,CAACN,gBAAiB,CAAAwD,QAAA,CAEtClD,OAAO,CAAG,WAAW,CAAG,iBAAiB,CACpC,CAAC,EACL,CAAC,EACJ,CAAC,CACH,CAAC,EACH,CAAC,cAENrB,KAAA,QAAKwE,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBzE,IAAA,OAAAyE,QAAA,CAAI,qBAAmB,CAAI,CAAC,CAC3B9D,aAAa,CAACuD,MAAM,GAAK,CAAC,cACzBlE,IAAA,MAAAyE,QAAA,CAAG,4DAA0D,CAAG,CAAC,cAEjEvE,KAAA,CAAAE,SAAA,EAAAqE,QAAA,eACEvE,KAAA,UAAOwE,SAAS,CAAC,OAAO,CAAAD,QAAA,eACtBzE,IAAA,UAAAyE,QAAA,cACEvE,KAAA,OAAAuE,QAAA,eACEzE,IAAA,OAAAyE,QAAA,CAAI,UAAQ,CAAI,CAAC,cACjBzE,IAAA,OAAAyE,QAAA,CAAI,UAAQ,CAAI,CAAC,cACjBzE,IAAA,OAAAyE,QAAA,CAAI,cAAY,CAAI,CAAC,EACnB,CAAC,CACA,CAAC,cACRzE,IAAA,UAAAyE,QAAA,CACGJ,YAAY,CAACW,GAAG,CAAEQ,QAAQ,OAAAC,kBAAA,oBACzBvF,KAAA,OAAAuE,QAAA,eACEzE,IAAA,OAAAyE,QAAA,CAAK,EAAAgB,kBAAA,CAAAD,QAAQ,CAACP,QAAQ,UAAAQ,kBAAA,iBAAjBA,kBAAA,CAAmBP,IAAI,GAAI,kBAAkB,CAAK,CAAC,cACxDlF,IAAA,OAAAyE,QAAA,CAAKe,QAAQ,CAACN,IAAI,CAAK,CAAC,cACxBlF,IAAA,OAAAyE,QAAA,CACGe,QAAQ,CAAC3E,WAAW,EAAI2E,QAAQ,CAAC3E,WAAW,CAACqD,MAAM,CAAG,CAAC,CACpDsB,QAAQ,CAAC3E,WAAW,CAACmE,GAAG,CAACU,EAAE,EAAIA,EAAE,CAACR,IAAI,CAAC,CAACzB,IAAI,CAAC,IAAI,CAAC,CAClD,iBAAiB,CAEnB,CAAC,GARE+B,QAAQ,CAACZ,EASd,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,cACR5E,IAAA,CAACF,UAAU,EACT2B,WAAW,CAAEA,WAAY,CACzBwC,UAAU,CAAEA,UAAW,CACvBtC,YAAY,CAAEA,YAAa,CAC3BgE,YAAY,CAAEpB,gBAAiB,CAChC,CAAC,EACF,CACH,EACE,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAjE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}