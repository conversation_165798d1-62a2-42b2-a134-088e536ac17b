import React, { useState, useEffect } from 'react';
import HierarchicalSelector from '../forms/HierarchicalSelector';
import './DivisionCategorySelection.css';

const DivisionCategorySelection = ({ onSelectionComplete, onBack, error }) => {
  const [selectedDivision, setSelectedDivision] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedFirmNature, setSelectedFirmNature] = useState('');
  const [selectionData, setSelectionData] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const validateSelection = () => {
    const errors = {};
    
    if (!selectedDivision) {
      errors.division = 'Division is required for import';
    }
    
    if (!selectedCategory) {
      errors.category = 'Category is required for import';
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleContinue = () => {
    if (validateSelection()) {
      const selection = {
        divisionId: parseInt(selectedDivision),
        categoryId: parseInt(selectedCategory),
        firmNatureId: selectedFirmNature ? parseInt(selectedFirmNature) : null
      };
      onSelectionComplete(selection);
    }
  };

  const handleSelectionChange = (selection) => {
    setSelectedDivision(selection.divisionId || '');
    setSelectedCategory(selection.categoryId || '');
    setSelectedFirmNature(selection.firmNatureId || '');
    setSelectionData(selection);

    // Clear validation errors when user makes changes
    if (validationErrors.division && selection.divisionId) {
      setValidationErrors(prev => ({ ...prev, division: null }));
    }
    if (validationErrors.category && selection.categoryId) {
      setValidationErrors(prev => ({ ...prev, category: null }));
    }
  };

  return (
    <div className="division-category-selection">
      <div className="selection-header">
        <h3>Select Division and Category for Import</h3>
        <p className="selection-description">
          All records in your Excel file will be assigned to the division and category you select below. 
          These fields are mandatory for all imported person records.
        </p>
      </div>

      {error && (
        <div className="error-message">
          <span className="error-icon">⚠️</span>
          {error}
        </div>
      )}

      <div className="selection-form">
        <div className="selection-section">
          <h4>Required Fields</h4>
          <div className="required-notice">
            <span className="required-icon">*</span>
            <span>Division and Category are mandatory for all imported records</span>
          </div>
          
          <HierarchicalSelector
            initialSelection={{
              divisionId: selectedDivision,
              categoryId: selectedCategory,
              firmNatureId: selectedFirmNature
            }}
            onSelectionChange={handleSelectionChange}
            required={true}
            showLabels={true}
            disabled={isLoading}
          />
        </div>

        <div className="selection-info">
          <h4>What this means:</h4>
          <ul>
            <li>All persons imported from your Excel file will be assigned to the selected division and category</li>
            <li>If your Excel file contains division/category columns, those values will be validated against your selection</li>
            <li>If your Excel file doesn't contain division/category columns, all records will use your selection</li>
            <li>Firm Nature is required and must be selected</li>
          </ul>
        </div>

        <div className="selection-actions">
          <button 
            type="button" 
            onClick={onBack}
            className="btn btn-secondary"
            disabled={isLoading}
          >
            ← Back
          </button>
          
          <button 
            type="button" 
            onClick={handleContinue}
            className="btn btn-primary"
            disabled={isLoading || !selectedDivision || !selectedCategory}
          >
            Continue to File Upload →
          </button>
        </div>
      </div>

      {/* Selection Summary */}
      {selectedDivision && selectedCategory && selectionData && (
        <div className="selection-summary">
          <h4>Selected Configuration:</h4>
          <div className="summary-item">
            <strong>Division:</strong> {selectionData.division?.name || 'Loading...'}
          </div>
          <div className="summary-item">
            <strong>Category:</strong> {selectionData.category?.name || 'Loading...'}
          </div>
          {selectedFirmNature && selectionData.firmNature && (
            <div className="summary-item">
              <strong>Firm Nature:</strong> {selectionData.firmNature.name}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DivisionCategorySelection;
