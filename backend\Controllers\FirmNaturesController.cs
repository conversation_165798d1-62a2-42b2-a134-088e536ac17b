using Microsoft.AspNetCore.Mvc;
using CrmApi.Models.FirmNature;
using CrmApi.Services.FirmNature;

namespace CrmApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class FirmNaturesController : ControllerBase
    {
        private readonly IFirmNatureService _firmNatureService;

        public FirmNaturesController(IFirmNatureService firmNatureService)
        {
            _firmNatureService = firmNatureService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<FirmNatureResponse>>> GetFirmNatures()
        {
            var firmNatures = await _firmNatureService.GetAllFirmNaturesAsync();
            return Ok(firmNatures);
        }

        [HttpGet("category/{categoryId}")]
        public async Task<ActionResult<IEnumerable<FirmNatureResponse>>> GetFirmNaturesByCategory(int categoryId)
        {
            var firmNatures = await _firmNatureService.GetFirmNaturesByCategoryAsync(categoryId);
            return Ok(firmNatures);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<FirmNatureResponse>> GetFirmNature(int id)
        {
            var firmNature = await _firmNatureService.GetFirmNatureByIdAsync(id);

            if (firmNature == null)
            {
                return NotFound();
            }

            return Ok(firmNature);
        }

        [HttpPost]
        public async Task<ActionResult<FirmNatureResponse>> PostFirmNature([FromBody] CreateFirmNatureRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var firmNature = await _firmNatureService.CreateFirmNatureAsync(request);
            return CreatedAtAction("GetFirmNature", new { id = firmNature.Id }, firmNature);
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<FirmNatureResponse>> PutFirmNature(int id, [FromBody] UpdateFirmNatureRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var firmNature = await _firmNatureService.UpdateFirmNatureAsync(id, request);
            return Ok(firmNature);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteFirmNature(int id)
        {
            var result = await _firmNatureService.DeleteFirmNatureAsync(id);
            if (!result)
            {
                return NotFound();
            }

            return NoContent();
        }
    }
}