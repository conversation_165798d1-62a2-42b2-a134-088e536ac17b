{"version": 3, "file": "static/css/main.a62febab.css", "mappings": "AAAA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,MACE,uBAAwB,CACxB,sBAAuB,CACvB,yBAA0B,CAC1B,uBAAwB,CACxB,sBAAuB,CACvB,uBAAwB,CACxB,oBAAqB,CACrB,oBAAqB,CACrB,qBAAsB,CACtB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,YAAgB,CAChB,uDAA4E,CAC5E,6DAAkF,CAClF,+DAAoF,CACpF,iEACF,CAEA,KAGE,kCAAmC,CACnC,iCAAkC,CAClC,kBAA2B,CAA3B,0BAA2B,CAJ3B,yIACgF,CAIhF,gBACF,CAGA,iBAGE,kBAAmB,CADnB,YAAa,CAEb,sBAAuB,CAHvB,gBAAiB,CAKjB,eAAgB,CADhB,iBAEF,CAEA,kBAME,kDAA6D,CAC7D,UACF,CAEA,2CALE,QAAS,CAFT,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAiBF,CATA,yBAQE,wCAAyC,CADzC,+dAAwb,CANxb,UAQF,CAEA,eAME,oBAA8B,CAD9B,QAAS,CAFT,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAKN,UACF,CAEA,YAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAOrC,sBAA0C,CAL1C,kBAAmB,CAInB,gEAA4B,CAA5B,2BAA4B,CAD5B,eAAgB,CAFhB,YAAa,CACb,UAIF,CAEA,cAEE,kBAAmB,CADnB,iBAEF,CAEA,YACE,kBACF,CAEA,WAME,kBAAmB,CAHnB,kDAAiF,CAAjF,8EAAiF,CACjF,iBAAkB,CAMlB,8DAA4B,CAA5B,2BAA4B,CAD5B,UAAY,CAJZ,YAAa,CAHb,WAAY,CAKZ,sBAAuB,CACvB,aAAc,CAPd,UAUF,CAEA,iBAGE,aAAsB,CAAtB,qBAAsB,CAFtB,cAAe,CACf,eAAgB,CAEhB,iBACF,CAEA,gBACE,aAAsB,CAAtB,qBAAsB,CACtB,eACF,CAEA,YACE,kBACF,CAMA,eAGE,kBAAmB,CADnB,YAAa,CADb,iBAGF,CAEA,YAGE,aAAsB,CAAtB,qBAAsB,CADtB,SAAU,CADV,iBAAkB,CAGlB,SACF,CAEA,YAOE,eAAwB,CAAxB,uBAAwB,CAJxB,wBAAiC,CAAjC,gCAAiC,CACjC,kBAAmB,CACnB,cAAe,CAHf,2BAA4B,CAI5B,uBAEF,CAEA,kBAEE,oBAAkC,CAAlC,iCAAkC,CAClC,8BACF,CAEA,iBAGE,eAAgB,CAChB,WAAY,CAIZ,iBAAkB,CAHlB,aAAsB,CAAtB,qBAAsB,CACtB,cAAe,CACf,WAAY,CANZ,iBAAkB,CAClB,UAAW,CAOX,oBACF,CAEA,uBACE,aAA2B,CAA3B,0BACF,CAEA,WAYE,kBAAmB,CATnB,kDAAiF,CAAjF,8EAAiF,CAEjF,WAAY,CACZ,kBAAmB,CAFnB,UAAY,CAKZ,cAAe,CAEf,YAAa,CAJb,cAAe,CACf,eAAgB,CAKhB,sBAAuB,CACvB,eAAgB,CAZhB,YAAa,CAQb,uBAAyB,CATzB,UAcF,CAEA,iBAEE,8DAA4B,CAA5B,2BAA4B,CAD5B,0BAEF,CAEA,oBAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAEA,cAEE,aAAsB,CAAtB,qBAAsB,CACtB,eAAiB,CAFjB,iBAGF,CAEA,iBAME,sCAAuC,CADvC,0BAAuB,CAAvB,qBAAuB,CAHvB,WAAY,CADZ,UAMF,CAGA,eAME,kDAA6D,CAG7D,gEAA4B,CAA5B,2BAA4B,CAF5B,aAAsB,CAAtB,qBAAsB,CAFtB,YAAa,CAHb,MAAO,CAQP,eAAgB,CAThB,cAAe,CAEf,KAAM,CACN,WAAY,CAIZ,YAGF,CAEA,gBAEE,iCAAiD,CADjD,iBAEF,CAEA,eAGE,QACF,CAEA,+BAJE,kBAAmB,CADnB,YAcF,CATA,gBAGE,kDAAiF,CAAjF,8EAAiF,CACjF,iBAAkB,CAIlB,4DAA4B,CAA5B,2BAA4B,CAN5B,WAAY,CAKZ,sBAAuB,CANvB,UAQF,CAEA,iBACE,gBAAiB,CACjB,eAAgB,CAChB,iBACF,CAEA,gBAEE,aAAsB,CAAtB,qBAAsB,CADtB,gBAEF,CAEA,aACE,cACF,CAEA,cAEE,kBAAmB,CAMnB,kBAAmB,CAJnB,aAAsB,CAAtB,qBAAsB,CAHtB,YAAa,CASb,eAAgB,CAPhB,QAAS,CAIT,aAAc,CADd,iBAAkB,CADlB,oBAAqB,CAIrB,uBAEF,CAEA,oBACE,oBAAoC,CACpC,aAAsB,CAAtB,qBAAsB,CACtB,oBAAqB,CACrB,yBACF,CAEA,qBACE,kDAAiF,CAAjF,8EAAiF,CAEjF,4DAA4B,CAA5B,2BAA4B,CAD5B,UAEF,CAEA,cACE,cACF,CAEA,gBAEE,WAAY,CACZ,MAAO,CAEP,cAAe,CAJf,iBAAkB,CAGlB,OAEF,CAEA,YAGE,kBAAmB,CAEnB,oBAAoC,CAEpC,0BAA0C,CAE1C,kBAAmB,CAHnB,aAAc,CAId,cAAe,CARf,YAAa,CAUb,eAAgB,CARhB,QAAS,CAIT,iBAAkB,CAGlB,uBAAyB,CAVzB,UAYF,CAEA,kBACE,oBAAoC,CACpC,0BACF,CAGA,cAIE,kBAA2B,CAA3B,0BAA2B,CAH3B,iBAAkB,CAElB,gBAAiB,CADjB,YAGF,CAGA,MACE,eAAiB,CAKjB,wBAAiC,CAAjC,gCAAiC,CAJjC,kBAAmB,CACnB,sDAA4B,CAA5B,2BAA4B,CAE5B,kBAAmB,CADnB,YAAa,CAGb,uBACF,CAEA,YACE,4DAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAEA,2BACE,aAAsB,CAAtB,qBAAsB,CACtB,kBACF,CAEA,SACE,cAAe,CACf,eACF,CAEA,SACE,iBAAkB,CAClB,eACF,CAGA,YACE,kBACF,CAEA,kBAIE,aAAsB,CAAtB,qBAAsB,CACtB,eAAiB,CAHjB,iBAIF,CAEA,cAOE,eAAiB,CAJjB,wBAAiC,CAAjC,gCAAiC,CACjC,kBAAmB,CACnB,cAAe,CAHf,iBAAkB,CAIlB,uBAAyB,CALzB,UAOF,CAEA,oBAEE,oBAAkC,CAAlC,iCAAkC,CAClC,8BAA8C,CAF9C,YAGF,CAGA,KAGE,kBAAmB,CAEnB,cAAe,CACf,eAAgB,CAIhB,sBAAuB,CAGvB,eAAgB,CADhB,uBAEF,CAEA,WAEE,4DAA4B,CAA5B,2BAA4B,CAD5B,0BAEF,CAEA,aACE,kDAAiF,CAAjF,8EAEF,CAEA,aACE,kBAAgC,CAAhC,+BAAgC,CAChC,UACF,CAEA,YACE,kBAA+B,CAA/B,8BAA+B,CAC/B,UACF,CAEA,QAEE,cAAe,CACf,eAAgB,CAFhB,gBAGF,CAGA,OAIE,eAAiB,CAFjB,wBAAyB,CAGzB,kBAAmB,CAEnB,sDAA4B,CAA5B,2BAA4B,CAJ5B,eAAgB,CAGhB,eAAgB,CALhB,UAOF,CAEA,oBAIE,+BAAwC,CAAxC,uCAAwC,CAFxC,YAAa,CACb,eAEF,CAEA,UAGE,aAAsB,CAAtB,qBAAsB,CACtB,eAAiB,CAFjB,eAGF,CAEA,gCANE,yBAQF,CAGA,aAEE,OACF,CAEA,MAGE,aAAsB,CAAtB,qBAAsB,CAFtB,cAAe,CACf,cAAe,CAEf,uBACF,CAMA,yBAHE,aAMF,CAHA,YAEE,oBACF,CAGA,aAEE,kBAAmB,CADnB,YAAa,CAEb,QAAS,CACT,kBACF,CAEA,mBACE,QACF,CAEA,qBAQE,kBAAmB,CALnB,WAAY,CACZ,iBAAkB,CAClB,cAAe,CAEf,YAAa,CADb,cAAe,CAJf,WAAY,CAOZ,sBAAuB,CACvB,uBAAyB,CATzB,UAUF,CAEA,SACE,kBAAgC,CAAhC,+BAAgC,CAChC,UACF,CAEA,YACE,kBAA+B,CAA/B,8BAA+B,CAC/B,UACF,CAEA,iCACE,oBACF,CAGA,KACE,YAAa,CAEb,cAAe,CADf,QAEF,CAEA,KACE,QAAO,CACP,eACF,CAGA,mBAME,kBAA2B,CAA3B,0BAA2B,CAD3B,gBAEF,CAEA,uBAME,sCAAuC,CADvC,wBAAsC,CADtC,iBAAkB,CAClB,wBAAsC,CAAtC,gCAAsC,CAAtC,qCAAsC,CAHtC,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CASA,iBACE,MACE,uBACF,CACA,IACE,2BACF,CACF,CAGA,yBACE,eAEE,WAAY,CACZ,iBAAkB,CAFlB,UAGF,CAEA,cACE,aAAc,CACd,YACF,CAEA,KACE,qBAAsB,CACtB,QACF,CAEA,KACE,cACF,CACF,CAEA,WAEE,kBAA2B,CAA3B,0BAA2B,CAC3B,gBAAiB,CAFjB,SAGF,CAEA,mBAGE,kBAAmB,CAGnB,aAAsB,CAAtB,qBAAsB,CALtB,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,eAEF,CAEA,kBAQE,kBAAmB,CAPnB,eAAiB,CAGjB,kBAAmB,CACnB,sDAA4B,CAA5B,2BAA4B,CAC5B,YAAa,CAGb,cAAe,CACf,QAAS,CAHT,6BAA8B,CAJ9B,kBAAmB,CADnB,YASF,CAEA,oBAGE,aAAsB,CAAtB,qBAAsB,CAFtB,gBAAiB,CACjB,eAAgB,CAEhB,iBACF,CAEA,mBACE,aAAsB,CAAtB,qBAAsB,CACtB,gBACF,CAEA,oBAGE,kBAAmB,CAFnB,YAAa,CACb,QAEF,CAEA,kBACE,eACF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAEb,OAAQ,CACR,kBACF,CAOA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,aAME,kBAAmB,CALnB,eAAiB,CAQjB,wBAAiC,CAAjC,gCAAiC,CANjC,kBAAmB,CACnB,sDAA4B,CAA5B,2BAA4B,CAC5B,YAAa,CAEb,QAAS,CALT,YAAa,CAMb,uBAEF,CAEA,mBACE,4DAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAEA,aAKE,kBAAmB,CAFnB,kBAAmB,CAKnB,sDAA4B,CAA5B,2BAA4B,CAD5B,UAAY,CAHZ,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CALvB,UAQF,CAEA,mBAGE,aAAsB,CAAtB,qBAAsB,CAFtB,cAAe,CACf,eAAgB,CAEhB,iBACF,CAEA,kBACE,aAAsB,CAAtB,qBAAsB,CACtB,eAAiB,CACjB,eACF,CAGA,aAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,YACE,eAAiB,CAGjB,wBAAiC,CAAjC,gCAAiC,CAFjC,kBAAmB,CACnB,sDAA4B,CAA5B,2BAA4B,CAE5B,eAAgB,CAChB,uBACF,CAEA,kBACE,4DAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAEA,cAGE,yBAA0B,CAD1B,+BAAwC,CAAxC,uCAAwC,CADxC,iBAGF,CAEA,iBAKE,kBAAmB,CAFnB,aAAsB,CAAtB,qBAAsB,CACtB,YAAa,CAHb,gBAAiB,CACjB,eAAgB,CAIhB,QACF,CAEA,iBAEE,YAAa,CADb,YAAa,CAEb,iBACF,CAGA,aAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,YACE,eAAiB,CAGjB,wBAAiC,CAAjC,gCAAiC,CAFjC,kBAAmB,CACnB,sDAA4B,CAA5B,2BAA4B,CAE5B,eAAgB,CAChB,uBACF,CAEA,kBACE,4DAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAEA,cAGE,yBAA0B,CAD1B,+BAAwC,CAAxC,uCAAwC,CADxC,iBAGF,CAEA,iBAKE,kBAAmB,CAFnB,aAAsB,CAAtB,qBAAsB,CACtB,YAAa,CAHb,gBAAiB,CACjB,eAAgB,CAIhB,QACF,CAEA,iBACE,gBAAiB,CACjB,eACF,CAEA,iBAEE,wBAAyB,CADzB,UAEF,CAEA,oBACE,yBAA0B,CAM1B,+BAAwC,CAAxC,uCAAwC,CADxC,eAAiB,CAFjB,eAAgB,CAIhB,eAAgB,CALhB,eAAgB,CAMhB,KAAM,CACN,UACF,CAEA,wCARE,aAAsB,CAAtB,qBAAsB,CAHtB,iBAeF,CAJA,oBAEE,+BAAwC,CAAxC,uCAEF,CAEA,gCACE,yBACF,CAEA,gBACE,aAAc,CACd,gBAAiB,CACjB,kBACF,CAEA,gBAOE,kBAAmB,CALnB,kBAA2B,CAA3B,0BAA2B,CAE3B,kBAAmB,CAEnB,YAAa,CAHb,WAAY,CAKZ,eAAgB,CAHhB,eAAgB,CAJhB,iBAQF,CAEA,iBACE,iDAAgF,CAAhF,6EAAgF,CAEhF,kBAAmB,CADnB,WAAY,CAEZ,yBACF,CAEA,qBAKE,aAAsB,CAAtB,qBAAsB,CAFtB,eAAiB,CACjB,eAAgB,CAHhB,iBAAkB,CAClB,SAAU,CAIV,SACF,CAGA,oBACE,eAAiB,CAIjB,wBAAiC,CAAjC,gCAAiC,CAHjC,kBAAmB,CAEnB,sDAA4B,CAA5B,2BAA4B,CAD5B,YAGF,CAEA,uBAME,kBAAmB,CAHnB,aAAsB,CAAtB,qBAAsB,CAEtB,YAAa,CAJb,gBAAiB,CACjB,eAAgB,CAKhB,QAAS,CAHT,kBAIF,CAEA,iBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,gBAKE,kBAAmB,CAJnB,yBAA0B,CAO1B,sBAA6B,CAN7B,kBAAmB,CAEnB,YAAa,CAEb,QAAS,CAHT,YAAa,CAIb,uBAEF,CAEA,sBACE,eAAiB,CACjB,oBAAkC,CAAlC,iCAAkC,CAClC,sDAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAEA,gBAOE,kBAAmB,CAJnB,kDAAiF,CAAjF,8EAAiF,CAEjF,iBAAkB,CADlB,UAAY,CAEZ,YAAa,CAIb,gBAAiB,CADjB,eAAgB,CAPhB,WAAY,CAMZ,sBAAuB,CAPvB,UAUF,CAEA,mBAGE,aAAsB,CAAtB,qBAAsB,CAFtB,gBAAiB,CACjB,eAAgB,CAEhB,iBACF,CAEA,kBACE,aAAsB,CAAtB,qBAAsB,CACtB,eAAiB,CACjB,iBACF,CAEA,kBACE,aAAc,CACd,gBAAiB,CACjB,kBACF,CAGA,0BAKE,0BACE,yBACF,CACF,CAEA,yBACE,kBAEE,sBAAuB,CADvB,qBAEF,CAEA,oBAGE,cAAe,CADf,0BAA2B,CAD3B,UAGF,CAMA,2BACE,yBACF,CAEA,iBACE,YACF,CAMA,8BACE,yBACF,CAEA,oBACE,cACF,CACF,CAEA,yBACE,WACE,YACF,CAMA,+BACE,YACF,CAEA,iBAEE,YAAa,CADb,YAEF,CAEA,wCAEE,iBACF,CACF,CAGA,eAEE,kBAAmB,CAGnB,aAAsB,CAAtB,qBAAsB,CAJtB,YAAa,CAGb,YAAa,CADb,sBAGF,CAGA,oCACE,SACF,CAEA,0CACE,kBAA2B,CAA3B,0BAA2B,CAC3B,iBACF,CAEA,0CACE,kBAA2B,CAA3B,0BAA2B,CAC3B,iBACF,CAEA,gDACE,kBAA2B,CAA3B,0BACF,CAEA,kBACE,eAAiB,CAKjB,wBAAiC,CAAjC,gCAAiC,CAJjC,kBAAmB,CAGnB,sDAA4B,CAA5B,2BAA4B,CAD5B,kBAAmB,CADnB,YAAa,CAIb,uBACF,CAEA,wBACE,4DAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAEA,iBACE,kBACF,CAEA,oBAKE,kBAAmB,CAFnB,aAAsB,CAAtB,qBAAsB,CACtB,YAAa,CAHb,gBAAiB,CACjB,eAAgB,CAIhB,QACF,CAEA,kBACE,gBACF,CAEA,eAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAEF,CAEA,eAKE,kBAAmB,CAJnB,kDAAqD,CAOrD,sBAA6B,CAN7B,kBAAmB,CAEnB,YAAa,CAEb,QAAS,CAIT,eAAgB,CAPhB,YAAa,CAMb,iBAAkB,CAFlB,uBAIF,CAEA,sBAOE,6EAA0H,CAC1H,yBAA0B,CAF1B,QAAS,CALT,UAAW,CAGX,MAAO,CAKP,mBAAoB,CAPpB,iBAAkB,CAGlB,OAAQ,CAFR,KAOF,CAEA,qBAEE,oBAAqB,CACrB,+BAA8C,CAF9C,0BAGF,CAEA,eAEE,4BAA6B,CAD7B,gBAEF,CAEA,kBACE,kBACE,uBACF,CACA,IACE,2BACF,CACA,IACE,0BACF,CACF,CAEA,eACE,QACF,CAEA,kBAGE,aAAsB,CAAtB,qBAAsB,CAFtB,gBAAiB,CACjB,eAAgB,CAEhB,iBACF,CAEA,iBACE,aAAsB,CAAtB,qBAAsB,CACtB,eAAiB,CACjB,iBACF,CAEA,cACE,oBAAmC,CAGnC,kBAAmB,CAFnB,aAAc,CAGd,eAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,iBACE,aAAc,CACd,gBAAiB,CACjB,kBACF,CAEA,cAGE,kBAAmB,CAInB,aAAsB,CAAtB,qBAAsB,CANtB,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,iBAAkB,CAClB,iBAEF,CAEA,mBACE,cAAe,CACf,kBAAmB,CACnB,UACF,CAEA,gBAIE,aAAsB,CAAtB,qBAAsB,CAHtB,gBAAiB,CACjB,eAAgB,CAChB,iBAEF,CAEA,mBAEE,aAAsB,CAAtB,qBAAsB,CADtB,eAEF,CAGA,yBACE,eACE,yBACF,CAEA,eACE,YACF,CAEA,eACE,cACF,CAEA,kBACE,gBACF,CACF,CAEA,yBACE,kBACE,YACF,CAEA,oBACE,gBACF,CAEA,eACE,qBAAsB,CAEtB,QAAS,CADT,iBAEF,CAEA,cACE,iBACF,CACF,CAEA,qBAGE,kBAAmB,CAInB,4BAAqC,CAArC,oCAAqC,CANrC,YAAa,CAGb,QAAS,CAFT,sBAAuB,CAGvB,eAAgB,CAChB,gBAEF,CAEA,gBAGE,wBAAiC,CAAjC,gCAAiC,CAEjC,aAAsB,CAAtB,qBAAsB,CAEtB,cAAe,CAGf,cAAe,CATf,gBAAiB,CAQjB,uBAEF,CAEA,qCACE,kBAAgC,CAAhC,+BAAgC,CAEhC,oBAAkC,CAAlC,iCAAkC,CADlC,UAAY,CAEZ,0BACF,CAEA,yBAGE,kBAA2B,CAA3B,0BAA2B,CAC3B,aAAsB,CAAtB,qBACF,CAEA,oBACE,YAAa,CACb,OACF,CAEA,mBAYE,kBAAmB,CATnB,eAAiB,CACjB,wBAAiC,CAAjC,gCAAiC,CACjC,iBAAkB,CAClB,aAAsB,CAAtB,qBAAsB,CACtB,cAAe,CAIf,YAAa,CAHb,cAAe,CACf,eAAgB,CAPhB,WAAY,CAWZ,sBAAuB,CAHvB,uBAAyB,CATzB,UAaF,CAEA,yBACE,kBAAgC,CAAhC,+BAAgC,CAGhC,0BACF,CAEA,mDAJE,oBAAkC,CAAlC,iCAAkC,CADlC,UAUF,CALA,0BACE,kDAAiF,CAAjF,8EAAiF,CAGjF,sDAA4B,CAA5B,2BACF,CAGA,yBACE,qBACE,cAAe,CACf,OACF,CAEA,gBAEE,cAAe,CACf,cAAe,CAFf,gBAGF,CAEA,mBAGE,cAAe,CADf,WAAY,CADZ,UAGF,CACF,CAEA,yBACE,qBACE,OACF,CAEA,oBACE,OACF,CAEA,gBAEE,cAAe,CACf,cAAe,CAFf,gBAGF,CAEA,mBAGE,cAAe,CADf,WAAY,CADZ,UAGF,CACF,CCjzCA,MACE,2BAA4B,CAC5B,6BAA8B,CAC9B,6BAA8B,CAC9B,gCAAiC,CACjC,gCAAiC,CACjC,8BAA+B,CAC/B,sCAAsD,CAEtD,qDAAsD,CACtD,2DAA4D,CAC5D,2DAA4D,CAC5D,uDAAwD,CAExD,4BAA6B,CAC7B,gCAAiC,CACjC,gCAAiC,CACjC,iCAAkC,CAClC,iCAAkC,CAClC,uBAAwB,CAExB,mCAAoC,CACpC,+BAAgC,CAGhC,+BAAgC,CAChC,kCAAmC,CACnC,kCAAmC,CACnC,gCAAiC,CAEjC,gCAAiC,CACjC,2CAA4C,CAG5C,uGAUA,sCAAuC,CACvC,yDAA0D,CAC1D,+DAAgE,CAChE,+DAAgE,CAChE,2DCXF,CCxCA,2BAME,qBAAsB,CACtB,UAAW,CAHX,WAAY,CADZ,cAAe,CADf,wFAA6D,CAG7D,6CAAkC,CAJlC,4CDiDF,CC1CE,qCAEE,QAAS,CADT,OD6CJ,CC1CE,uCAEE,QAAS,CADT,OAAQ,CAER,0BD4CJ,CC1CE,sCAEE,SAAU,CADV,OD6CJ,CC1CE,wCACE,UAAW,CACX,QD4CJ,CC1CE,0CACE,UAAW,CACX,QAAS,CACT,0BD4CJ,CC1CE,yCACE,UAAW,CACX,SD4CJ,CCxCA,yCACE,2BAGE,MAAO,CACP,QAAS,CAFT,SAAU,CADV,WD8CF,CC1CE,kHAGE,KAAM,CACN,uBD0CJ,CCxCE,2HAGE,QAAS,CACT,uBDwCJ,CCtCE,gCAEE,SAAa,CADb,ODyCJ,CACF,CEjGA,iBAME,iBAAkB,CAClB,wDAA6E,CAJ7E,qBAAsB,CAUtB,cAAe,CACf,aAAc,CANd,YAAa,CAIb,8DAAwC,CAHxC,6BAA8B,CAL9B,kBAAmB,CAMnB,4DAA4C,CAR5C,2DAA4C,CAS5C,eAAgB,CANhB,WAAY,CAJZ,iBAAkB,CAelB,SFmGF,CElGE,sBACE,aFoGJ,CElGE,iCACE,cFoGJ,CElGE,sBAKE,kBAAmB,CADnB,YAAa,CAFb,aAAc,CADd,aAAc,CAEd,WFsGJ,CEnGI,qCAEE,SADA,qBFsGN,CElGE,sBAIE,YAAa,CADb,aAAc,CAFd,sBAAuB,CACvB,UFsGJ,CEhGA,mBAEE,sBAAwB,CADxB,wBFoGF,CEhGA,wBAEE,sBAAwB,CADxB,wBFoGF,CEhGA,yCACE,iBAEE,eAAgB,CADhB,eFoGF,CACF,CG1JE,6BACE,wDAAsC,CACtC,gDH4JJ,CGtJE,uFACE,sDAAuC,CACvC,oDH4JJ,CG1JE,sDAEE,wDAAsC,CADtC,gDH6JJ,CG1JE,yDAEE,2DAAyC,CADzC,mDH6JJ,CG1JE,yDAEE,2DAAyC,CADzC,mDH6JJ,CG1JE,uDAEE,yDAAuC,CADvC,iDH6JJ,CGvJE,qCACE,iIH0JJ,CGxJE,oCACE,iEH0JJ,CGxJE,8BACE,iEH0JJ,CGxJE,iCACE,oEH0JJ,CGxJE,iCACE,oEH0JJ,CGxJE,+BACE,kEH0JJ,CGxJE,uRAIE,iEHuJJ,CI7MA,wBASE,qBAAsB,CAPtB,gBAAuB,CAEvB,WAAY,CAHZ,UAAW,CAKX,cAAe,CACf,UAAY,CAJZ,YAAa,CAEb,SAAU,CAGV,mBJiNF,CI9ME,+BACE,UAAW,CACX,UJgNJ,CI7ME,4BACE,iBAAkB,CAClB,WAAY,CACZ,UJ+MJ,CI5ME,4DAEE,SJ6MJ,CKrOA,mCACE,GACE,mBLwOF,CKtOA,GACE,mBLwOF,CACF,CKrOA,wBAEE,QAAS,CAGT,UAAW,CAFX,MAAO,CAIP,UAAY,CANZ,iBAAkB,CAOlB,qBAAsB,CAJtB,UAAW,CAEX,4CLyOF,CKrOE,kCACE,mDLuOJ,CKpOE,oCACE,wBLsOJ,CKnOE,6BAEE,SAAa,CADb,OAAQ,CAER,sBLqOJ,CMnQA,mBAQE,8CAFA,8EAAsD,CADtD,kBAAmB,CAEnB,2EAAiD,CAJjD,qBAAsB,CADtB,WAAY,CADZ,UN6QF,CO1QA,mCACE,kBAJA,uDPkRA,COvQA,GACE,SAAU,CACV,iCPyQF,COvQA,IACE,SAAU,CACV,gCPyQF,COvQA,IACE,+BPyQF,COvQA,IACE,+BPyQF,COvQA,GACE,cPyQF,CACF,COtQA,oCACE,IACE,SAAU,CACV,gCPwQF,COtQA,GACE,SAAU,CACV,iCPwQF,CACF,COrQA,kCACE,kBA1CA,uDPkTA,COjQA,GACE,SAAU,CACV,kCPmQF,COjQA,IACE,SAAU,CACV,+BPmQF,COjQA,IACE,gCPmQF,COjQA,IACE,8BPmQF,COjQA,GACE,cPmQF,CACF,COhQA,mCACE,IACE,SAAU,CACV,+BPkQF,COhQA,GACE,SAAU,CACV,kCPkQF,CACF,CO/PA,gCACE,kBAhFA,uDPkVA,CO3PA,GACE,SAAU,CACV,iCP6PF,CO3PA,IACE,SAAU,CACV,gCP6PF,CO3PA,IACE,+BP6PF,CO3PA,IACE,+BP6PF,CO3PA,GACE,uBP6PF,CACF,CO1PA,iCACE,IACE,gCP4PF,CO1PA,QAEE,SAAU,CACV,+BP2PF,COzPA,GACE,SAAU,CACV,kCP2PF,CACF,COxPA,kCACE,kBA1HA,uDPqXA,COpPA,GACE,SAAU,CACV,kCPsPF,COpPA,IACE,SAAU,CACV,+BPsPF,COpPA,IACE,gCPsPF,COpPA,IACE,8BPsPF,COpPA,GACE,cPsPF,CACF,COnPA,mCACE,IACE,+BPqPF,COnPA,QAEE,SAAU,CACV,gCPoPF,COlPA,GACE,SAAU,CACV,iCPoPF,CACF,COhPE,uEAEE,qCPiPJ,CO/OE,yEAEE,sCPgPJ,CO9OE,oCACE,qCPgPJ,CO9OE,uCACE,mCPgPJ,CO3OE,qEAEE,sCP6OJ,CO3OE,uEAEE,uCP4OJ,CO1OE,mCACE,oCP4OJ,CO1OE,sCACE,sCP4OJ,CQ9aA,4BACE,GACE,SAAU,CACV,2BRibF,CQ/aA,IACE,SRibF,CACF,CQ9aA,6BACE,GACE,SRgbF,CQ9aA,IACE,SAAU,CACV,2BRgbF,CQ9aA,GACE,SRgbF,CACF,CQ7aA,sBACE,+BR+aF,CQ5aA,qBACE,gCR+aF,CS3cA,4BACE,GAEE,iCAAkC,CAClC,SAAU,CAFV,2CTgdF,CS5cA,IAEE,iCAAkC,CADlC,4CT+cF,CS5cA,IAEE,SAAU,CADV,2CT+cF,CS5cA,IACE,2CT8cF,CS5cA,GACE,4BT8cF,CACF,CS3cA,6BACE,GACE,4BT6cF,CS3cA,IAEE,SAAU,CADV,4CT8cF,CS3cA,GAEE,SAAU,CADV,2CT8cF,CACF,CS1cA,sBACE,+BT4cF,CSzcA,qBACE,gCT4cF,CUjfA,kCACE,GACE,+BAAkC,CAClC,kBVofF,CUlfA,GARA,uBV6fA,CACF,CUjfA,iCACE,GACE,gCAAmC,CACnC,kBVmfF,CUjfA,GAlBA,uBVsgBA,CACF,CUhfA,+BACE,GACE,+BAAkC,CAClC,kBVkfF,CUhfA,GA5BA,uBV+gBA,CACF,CU/eA,iCACE,GACE,gCAAmC,CACnC,kBVifF,CU/eA,GAtCA,uBVwhBA,CACF,CU9eA,mCACE,GA5CA,uBV6hBA,CU9eA,GAEE,+BAAkC,CADlC,iBVifF,CACF,CU7eA,kCACE,GAtDA,uBVsiBA,CU7eA,GAEE,gCAAmC,CADnC,iBVgfF,CACF,CU5eA,kCACE,GAhEA,uBV+iBA,CU5eA,GAEE,gCAAmC,CADnC,iBV+eF,CACF,CU3eA,gCACE,GA1EA,uBVwjBA,CU3eA,GAEE,iCAAoC,CADpC,iBV8eF,CACF,CUzeE,qEAEE,oCV0eJ,CUxeE,uEAEE,qCVyeJ,CUveE,mCACE,oCVyeJ,CUveE,sCACE,kCVyeJ,CUpeE,mEAEE,qCVseJ,CUpeE,qEAEE,sCVqeJ,CUneE,kCACE,mCVqeJ,CUneE,qCACE,qCVqeJ,CWvlBA,0BACE,GACE,sBX0lBF,CWxlBA,GACE,uBX0lBF,CACF,CY/lBA,2BAGE,kBAAmB,CAFnB,YAAa,CACb,sBAAuB,CAGvB,aAAc,CADd,cAEF,CAEA,sBAEE,kBAAmB,CAEnB,eAAmB,CAInB,wBAAyB,CAFzB,kBAAmB,CACnB,8BAAyC,CAJzC,QAAS,CAET,qBAIF,CAEA,iBAEE,kBAAmB,CADnB,YAEF,CAEA,cAEE,aAAc,CADd,iBAAmB,CAEnB,eAAgB,CAChB,kBACF,CAQA,uCAJE,kBAAmB,CADnB,YAAa,CAEb,UAQF,CALA,kBAIE,cACF,CAEA,gBAEE,kBAAmB,CAMnB,eAAmB,CADnB,wBAAyB,CAKzB,iBAAkB,CAXlB,YAAa,CAUb,eAAgB,CANhB,WAAY,CAFZ,sBAAuB,CACvB,cAAe,CAEf,aAAe,CAUf,oBAAqB,CAFrB,8BAAgC,CAChC,wBAAiB,CAAjB,gBAEF,CAEA,qCACE,kBAAmB,CACnB,oBAAqB,CAErB,8BAAwC,CADxC,0BAEF,CAEA,sBAEE,8BAA8C,CAD9C,YAEF,CAEA,uBACE,kDAA6D,CAC7D,oBAAqB,CAGrB,8BAA6C,CAD7C,eAAgB,CAEhB,0BACF,CAEA,yBACE,kBAAmB,CACnB,oBAAqB,CAIrB,eAAgB,CAHhB,aAAc,CACd,kBAAmB,CACnB,cAEF,CAEA,qBAEE,gBAAuB,CADvB,WAAY,CAGZ,aAAc,CADd,cAEF,CAEA,2BACE,gBAAuB,CAEvB,eAAgB,CADhB,cAEF,CAEA,kCAEE,iBAAkB,CAClB,eAAgB,CAChB,cACF,CAEA,iBACE,mBACF,CAEA,iBACE,kBACF,CAGA,yBACE,sBACE,qBAAsB,CACtB,UAAY,CACZ,YACF,CAEA,iBACE,OACF,CAEA,qBACE,OACF,CAMA,8BAHE,gBAOF,CAJA,gBAEE,WAAY,CADZ,cAGF,CAEA,kCAEE,cAAe,CACf,cACF,CACF,CAEA,yBACE,2BAEE,cAAgB,CADhB,eAEF,CAEA,sBAEE,SAAW,CADX,cAEF,CAEA,qBACE,WACF,CAEA,kBACE,WAAa,CACb,eACF,CAEA,gBAGE,gBAAkB,CADlB,WAAY,CADZ,cAGF,CACF,CAGA,uBACE,sCACF,CAEA,2BACE,GACE,mCACF,CACA,IACE,sCACF,CACA,GACE,mCACF,CACF,CAGA,mCACE,sBACE,kBAAmB,CACnB,oBAAqB,CACrB,0BACF,CAEA,cACE,aACF,CAEA,gBACE,kBAAmB,CACnB,oBAAqB,CACrB,aACF,CAEA,qCACE,kBAAmB,CACnB,oBACF,CAEA,yBACE,kBAAmB,CACnB,oBAAqB,CACrB,aACF,CACF,CCnOA,eASE,kBAAmB,CAHnB,0BAAoC,CADpC,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YACF,CAEA,eAEE,iBAAkB,CAClB,8BAAwC,CAExC,eAAgB,CADhB,SAMF,CAWA,iBAEE,aAAc,CADd,QAEF,CA4BA,cAME,wBAAyB,CADzB,4BAA6B,CAJ7B,YAAa,CAEb,SAAW,CADX,wBAAyB,CAEzB,cAGF,CAaA,0DAKE,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,cAAgB,CADhB,UAKF,CAEA,4EAIE,oBAAqB,CACrB,gCAAgD,CAFhD,YAGF,CAEA,iCAEE,kBAAoB,CADpB,UAEF,CAEA,gBAGE,4BAA6B,CAF7B,iBAAkB,CAClB,kBAEF,CAEA,mBAEE,aAAc,CACd,cAAe,CAFf,eAGF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,SACF,CAEA,aAGE,kBAAmB,CAFnB,YAAa,CACb,SAEF,CAEA,mBACE,QACF,CAEA,YACE,wBAAyB,CAEzB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,iBAAmB,CAHnB,oBAIF,CAEA,kBACE,wBACF,CAEA,SACE,wBAAyB,CAEzB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,iBAAmB,CACnB,gBAAkB,CAJlB,kBAKF,CAEA,eACE,wBACF,CAqCA,yBACE,eAEE,WAAY,CADZ,SAEF,CAEA,wCAGE,YACF,CAEA,aAEE,mBACF,CAEA,2BAJE,qBAMF,CACF,CC5NA,YACE,oBACF,CAEA,2BACE,kBACF,CAEA,YAIE,aAAc,CAHd,aAAc,CAId,iBAAmB,CAFnB,eAAgB,CADhB,mBAIF,CAEA,oBAEE,kBACF,CAEA,YAOE,qBAAuB,CAJvB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,cAAgB,CAIhB,oEAAwE,CALxE,UAOF,CAEA,kBAEE,oBAAqB,CACrB,gCAAgD,CAFhD,YAGF,CAEA,qBACE,wBAAyB,CACzB,SACF,CAEA,kBACE,oBACF,CAEA,wBACE,oBAAqB,CACrB,gCACF,CAGA,oBAEE,eAAgB,CADhB,eAEF,CAGA,kBACE,cACF,CAEA,4BACE,WAAY,CACZ,gBACF,CAEA,mCACE,aACF,CAEA,2CACE,wBAAyB,CACzB,UACF,CAGA,kBAEE,kBAAmB,CADnB,YAAa,CAEb,SACF,CAEA,eAIE,oBAAqB,CADrB,cAAe,CADf,aAAc,CADd,sBAIF,CAEA,gBAEE,aAAc,CACd,cAAe,CAEf,iBAAmB,CAJnB,eAAgB,CAGhB,QAEF,CAEA,sBACE,aACF,CAGA,WAGE,aAAc,CADd,iBAAmB,CAEnB,iBAAkB,CAHlB,iBAIF,CAkBA,8BACE,yBACF,CAEA,kHAEE,uBAAwB,CACxB,QACF,CAGA,4BACE,cACF,CAEA,+DAGE,iBAAkB,CAFlB,cAAe,CACf,cAEF,CAEA,qEACE,wBACF,CAGA,wDAEE,qBACF,CAGA,2BACE,mBACF,CAGA,sCACE,qBAAsB,CACtB,eACF,CAGA,0BACE,yBAA0B,CAC1B,kBACF,CAEA,6BACE,yBAA0B,CAC1B,kBACF,CAGA,yBACE,YAEE,gBAAiB,CADjB,eAEF,CAEA,eAEE,aAAc,CADd,sBAEF,CAEA,gBACE,cACF,CAEA,4BACE,gBACF,CACF,CAGA,+BACE,YACE,gBACF,CAEA,kBACE,gBACF,CAEA,eACE,eACF,CACF,CAGA,uCACE,YACE,eACF,CACF,CC5NA,sBASE,kBAAmB,CAHnB,0BAAoC,CADpC,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAQP,YAAa,CAVb,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,oBACE,qBAAuB,CACvB,iBAAkB,CAClB,8BAAwC,CAKxC,YAAa,CACb,qBAAsB,CAHtB,eAAgB,CADhB,gBAAiB,CAEjB,eAAgB,CAHhB,UAMF,CAEA,gBAGE,sBAAuB,CAGvB,wBAAyB,CADzB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,cAGF,CAEA,kBAEE,aAAc,CADd,gBAEF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,SACF,CAQA,aACE,YAAa,CAEb,cAAe,CADf,QAEF,CAEA,MAGE,wBAAyB,CAEzB,iBAAkB,CAJlB,iBAAmB,CAGnB,oBAEF,CAsBA,iBACE,QAAO,CACP,eAAgB,CAChB,cACF,CAEA,gBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAElB,oBAAqB,CADrB,YAEF,CAEA,kBAEE,aAAc,CACd,iBAAmB,CAFnB,QAGF,CAEA,cACE,kBACF,CAEA,iBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAElB,oBAAqB,CADrB,cAEF,CAEA,oBAKE,+BAAgC,CAHhC,aAAc,CAId,oBAAqB,CAHrB,iBAAkB,CAFlB,iBAAoB,CAGpB,oBAGF,CAEA,gBAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,wDAEF,CAEA,eACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAElB,oBAAqB,CADrB,cAEF,CAEA,kBAEE,aAAc,CADd,eAEF,CAEA,cAEE,wDAEF,CAEA,cAGE,kBAAmB,CAEnB,qBAAuB,CAEvB,wBAAyB,CADzB,iBAAkB,CALlB,YAAa,CACb,6BAA8B,CAE9B,aAIF,CAEA,eAEE,aAAc,CACd,iBAAmB,CAFnB,eAGF,CAEA,eAEE,aAAc,CACd,cAAe,CAFf,eAGF,CAEA,YACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cACF,CAEA,eAEE,aAAc,CADd,eAEF,CAEA,aAGE,eAAY,CAFZ,YAAa,CAEb,UAAY,CADZ,yDAEF,CAEA,YAKE,qBAAuB,CAHvB,6BAOF,CAEA,oBACE,6BACF,CAEA,mBACE,6BAA8B,CAC9B,UACF,CAaA,YAGE,wBAGF,CAoBA,0BArBE,iBAAkB,CAJlB,gBAAkB,CAGlB,uBA6BF,CAPA,cACE,wBAAyB,CACzB,UAAY,CAIZ,eACF,CAEA,gBAKE,wBAAyB,CADzB,4BAA6B,CAH7B,YAAa,CACb,sBAAuB,CACvB,cAGF,CA0BA,yBACE,sBACE,aACF,CAEA,gBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,QACF,CAEA,aACE,qBAAsB,CACtB,SACF,CAUA,2CACE,yBACF,CAEA,YAEE,sBAAuB,CADvB,qBAAsB,CAEtB,SACF,CACF,CAEA,yBAWE,6FAEE,YACF,CACF,CCpVA,cAKE,wBAAyB,CAJzB,YAAa,CACb,qBAAsB,CAEtB,WAAY,CADZ,8BAA+B,CAG/B,gBACF,CAEA,qBAGE,kBAAmB,CAEnB,qBAAuB,CACvB,+BAAgC,CAChC,8BAAqC,CANrC,YAAa,CACb,6BAA8B,CAE9B,iBAIF,CAEA,wBAEE,aAAc,CADd,QAEF,CAOA,sBACE,YAAa,CACb,QAAO,CAEP,YAAa,CADb,gBAEF,CAEA,cAEE,qBAAuB,CACvB,8BAA+B,CAE/B,YAAa,CACb,qBAAsB,CAEtB,aAAc,CADd,8BAA+B,CAH/B,eAAgB,CAHhB,WAQF,CAEA,gBAGE,qBAAuB,CADvB,+BAAgC,CADhC,cAGF,CAEA,mBAEE,aAAc,CACd,gBAAiB,CAFjB,eAGF,CAEA,YACE,kBACF,CAEA,kBAIE,aAAc,CAHd,aAAc,CAEd,eAAgB,CADhB,mBAGF,CAEA,gBAGE,aAAc,CADd,iBAAmB,CAEnB,eAAgB,CAHhB,cAIF,CAGA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,UACE,eAAgB,CAChB,WAAY,CACZ,aAAc,CACd,cAAe,CACf,iBAAmB,CACnB,yBACF,CAEA,gBACE,aACF,CAEA,kBACE,gBAAiB,CACjB,eACF,CAEA,kBACE,aAAc,CACd,iBAAkB,CAGlB,QAAS,CADT,YAAa,CADb,iBAGF,CAEA,iBAKE,wBAAyB,CAJzB,wBAAyB,CACzB,iBAAkB,CAElB,oBAAsB,CADtB,YAGF,CAEA,kBAGE,sBAAuB,CAFvB,YAAa,CACb,6BAA8B,CAE9B,mBACF,CAEA,qBAGE,aAAc,CADd,cAAe,CADf,QAGF,CAEA,iBACE,wBAAyB,CAGzB,kBAAmB,CAFnB,UAAY,CAGZ,gBAAkB,CAElB,eAAgB,CAJhB,oBAAuB,CAGvB,wBAEF,CAEA,kBACE,cAIF,CAEA,WAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAGb,gBAAkB,CADlB,SAAW,CAGX,oBACF,CAOA,WAIE,gBAAiB,CADjB,iBAAkB,CAElB,cAAe,CAHf,gBAAkB,CAIlB,eAAgB,CALhB,sBAMF,CAEA,uBACE,wBAA6B,CAE7B,oBAAqB,CADrB,aAEF,CAEA,6BACE,wBAAyB,CACzB,UACF,CAEA,sBACE,wBAAyB,CAEzB,oBAAqB,CADrB,UAEF,CAEA,4BACE,wBAAyB,CACzB,oBACF,CAEA,uCAIE,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,cAAgB,CADhB,UAKF,CAEA,mDAGE,oBAAqB,CACrB,gCAAgD,CAFhD,YAGF,CAEA,mDAEE,oBACF,CAEA,iBACE,gBAAiB,CACjB,eACF,CAEA,aAKE,yBAA0B,CAC1B,iBAAkB,CAHlB,iBAAkB,CAClB,YAGF,CAEA,gBAGE,kBAAmB,CAGnB,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAPlB,YAAa,CACb,6BAA8B,CAG9B,mBAAqB,CADrB,cAKF,CAEA,YACE,QACF,CAEA,aACE,aAAc,CACd,eAEF,CAEA,YAEE,aAAc,CADd,iBAAmB,CAEnB,kBACF,CAWA,mCANE,uBAaF,CAPA,mBACE,wBAAyB,CAGzB,iBAAkB,CAFlB,aAAc,CAGd,gBAAkB,CAClB,eACF,CAEA,eACE,YAAa,CACb,UACF,CAEA,UACE,eAAgB,CAIhB,iBAAkB,CAClB,iBAAmB,CAHnB,cAIF,CAMA,mBAEE,kBAAmB,CADnB,UAEF,CAEA,uBACE,wBAAyB,CACzB,aACF,CAEA,cAEE,qBAAuB,CACvB,YAAa,CAFb,QAAO,CAGP,qBAAsB,CAGtB,8BAA+B,CAC/B,WAAY,CAFZ,eAAgB,CADhB,YAIF,CAEA,eAGE,kBAAmB,CAEnB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,cAEF,CAEA,kBAEE,aAAc,CADd,QAEF,CAEA,qBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAElB,aAAc,CADd,cAEF,CAEA,wBAEE,aAAc,CACd,gBAAiB,CAFjB,eAGF,CAEA,qBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,iCACE,eACF,CAEA,2BAIE,aAAc,CAHd,aAAc,CAEd,eAAgB,CADhB,mBAGF,CAEA,4BAME,qBAAuB,CAHvB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,cAAgB,CAKhB,oEAAwE,CANxE,UAOF,CAEA,kCAEE,oBAAqB,CACrB,gCAAgD,CAFhD,YAGF,CAEA,qCACE,wBAAyB,CAEzB,kBAAmB,CADnB,UAEF,CAEA,kCACE,oBACF,CAEA,wCACE,oBAAqB,CACrB,gCACF,CAIA,cAME,oEAAwE,CAFxE,UAGF,CAEA,oBAEE,oBAEF,CAEA,cAEE,+BAAgC,CADhC,YAAa,CAGb,aAAc,CADd,eAAgB,CAEhB,oBACF,CAEA,aACE,eAAgB,CAIhB,WAAoC,CAApC,6BAAoC,CAEpC,aAAc,CAHd,cAAe,CAKf,aAAc,CADd,eAAgB,CAEhB,qBAAsB,CAPtB,mBAAqB,CAGrB,kBAKF,CAEA,mBACE,wBAAyB,CACzB,aACF,CAEA,oBAGE,wBAAyB,CADzB,2BAA4B,CAD5B,aAGF,CAGA,yBAEE,wBAAyB,CACzB,+BAAgC,CAChC,YAAa,CACb,qBAAsB,CACtB,UAAY,CALZ,YAMF,CAEA,uBAEE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CADf,SAEF,CAEA,qBAEE,aAAc,CAEd,iBAAmB,CAHnB,eAAgB,CAEhB,kBAEF,CAEA,QAIE,oBAAsB,CAFtB,iBAAmB,CACnB,eAAgB,CAFhB,sBAIF,CAEA,aAEE,wBAEF,CAOA,wBACE,oBAAqB,CACrB,aACF,CAEA,8BACE,wBAAyB,CACzB,UACF,CAEA,sBACE,UAEF,CAEA,4BACE,wBAA6B,CAC7B,aACF,CAEA,mBAKE,qBAAuB,CAEvB,wBAAyB,CADzB,oBAAsB,CAJtB,aAAc,CADd,iBAAmB,CAEnB,eAAgB,CAChB,oBAIF,CAEA,aACE,QAAO,CAIP,8BAA+B,CAD/B,gBAAiB,CAFjB,eAAgB,CAChB,YAGF,CAEA,YAEE,kBAAmB,CAGnB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CANf,YAAa,CAGb,mBAAqB,CADrB,cAAgB,CAKhB,uBACF,CAEA,kBACE,wBAAyB,CACzB,oBACF,CAEA,qBACE,wBAAyB,CACzB,oBACF,CAEA,gBACE,mBACF,CAEA,qCAEE,aAAc,CADd,YAEF,CAEA,eACE,QACF,CAEA,YAGE,oBACF,CAEA,YAEE,kBAAmB,CADnB,YAAa,CAEb,SACF,CA2CA,wBAEE,kBAAmB,CADnB,WAEF,CAcA,OAEE,kBAAmB,CADnB,sBAIF,CAeA,yBACE,sBACE,qBACF,CAEA,cAEE,eAAgB,CADhB,UAEF,CAEA,cACE,WACF,CAEA,gBACE,qBAAsB,CACtB,UACF,CAEA,cACE,cACF,CAEA,uBAEE,mBAAoB,CADpB,qBAAsB,CAEtB,SACF,CAEA,+BAEE,iBAAkB,CADlB,UAEF,CAEA,qBACE,oBACF,CACF,CAGA,0BACE,cACE,WACF,CACF,CAGA,iCACE,UACF,CAEA,uCACE,kBAAmB,CACnB,iBACF,CAEA,uCACE,kBAAmB,CACnB,iBACF,CAEA,6CACE,kBACF,CAEA,gCACE,SACF,CAEA,sCACE,kBAAmB,CACnB,iBACF,CAEA,sCACE,kBAAmB,CACnB,iBACF,CAEA,4CACE,kBACF,CCltBA,qBAIE,qBAAuB,CACvB,iBAAkB,CAClB,8BAAwC,CAJxC,aAAc,CADd,gBAAiB,CAEjB,YAIF,CAEA,aAGE,+BAAgC,CAFhC,kBAAmB,CACnB,mBAEF,CAEA,gBAEE,aAAc,CACd,iBAAkB,CAFlB,gBAGF,CAEA,WACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,WAEE,aAAc,CACd,cACF,CAEA,kBAEE,iBAAmB,CACnB,iBACF,CAEA,aACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,cACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cACF,CAEA,iBAKE,+BAAgC,CAHhC,aAAc,CAId,oBAAqB,CAHrB,iBAAkB,CAFlB,iBAAoB,CAGpB,oBAGF,CAEA,aAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,wDAEF,CAGA,oIAGE,gBACF,CAEA,cAKE,4BAA6B,CAF7B,QAAS,CADT,wBAAyB,CAIzB,eAAgB,CAFhB,gBAGF,CA2BA,0CArBE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,iBAAkB,CAClB,iBA0BF,CARA,oBAOE,aACF,CAEA,sBAEE,gBAAiB,CADjB,kBAEF,CAGA,OAKE,iBAAmB,CAHnB,oBAIF,CAQA,eAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CAEA,eAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CAEA,YAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CAGA,KAUE,eACF,CA0BA,kCACE,wBAAyB,CACzB,UACF,CAEA,sBAEE,kBAAmB,CADnB,WAEF,CAGA,eAME,UAAY,CAHZ,gBAIF,CAEA,sBACE,WAAY,CACZ,cACF,CAGA,yBACE,qBAEE,WAAY,CADZ,YAEF,CAEA,gBACE,gBACF,CAEA,cACE,YACF,CAEA,aAEE,QAAS,CADT,yBAEF,CAEA,cAEE,mBAAoB,CADpB,qBAEF,CAEA,KAEE,gBAAiB,CADjB,YAEF,CACF,CAEA,yBACE,qBAEE,YAAc,CADd,aAEF,CAEA,cACE,cACF,CAEA,iBACE,gBACF,CACF,CAGA,aACE,qBAEE,qBAAsB,CADtB,eAEF,CAEA,cACE,YACF,CAEA,cACE,kBAAmB,CACnB,uBACF,CACF,CAGA,+BACE,cACE,gBACF,CAEA,iBACE,uBACF,CAEA,KACE,gBACF,CACF,CAGA,uCACE,sBAGE,cAAe,CADf,eAEF,CACF,CAGA,aAME,qBAAuB,CAHvB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,cAAgB,CAKhB,oEAAwE,CANxE,UAOF,CAEA,mBACE,oBAAqB,CAErB,gCAAgD,CADhD,SAEF,CAEA,sBACE,wBAAyB,CAEzB,kBAAmB,CADnB,SAEF,CAEA,mBACE,oBACF,CAEA,yBACE,oBAAqB,CACrB,gCACF,CAGA,iBAEE,wBAAyB,CACzB,wBAAyB,CAEzB,aAAc,CACd,iBAEF,CAEA,iCANE,iBAAkB,CAGlB,eAAgB,CANhB,YAcF,CALA,gBAIE,eACF,CAEA,wBACE,wBAAyB,CACzB,wBAAyB,CACzB,aACF,CAEA,sBACE,wBAAyB,CACzB,wBAAyB,CACzB,aACF,CAEA,qBACE,wBAAyB,CACzB,wBAAyB,CACzB,aACF,CAGA,iBAGE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,aAAc,CAJd,YAAa,CADb,iBAMF,CAEA,oBAEE,aAAc,CADd,eAEF,CAEA,mBAEE,eAAgB,CADhB,cAEF,CCxYA,uBAOE,wBAAyB,CAFzB,wBAAyB,CACzB,iBAAkB,CALlB,YAAa,CACb,qBAAsB,CACtB,QAAS,CACT,YAAa,CAIb,iBAAkB,CAClB,UACF,CAEA,gBACE,YAAa,CACb,qBAAsB,CACtB,SACF,CAEA,gBAEE,aAAc,CACd,iBAAmB,CAFnB,eAGF,CAEA,0BACE,aAAc,CACd,kBACF,CAEA,gBAKE,qBAAuB,CAHvB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,cAAgB,CAKhB,oEAAwE,CACxE,UACF,CAEA,sBAEE,oBAAqB,CACrB,gCAAgD,CAFhD,YAGF,CAEA,yBACE,wBAAyB,CAEzB,kBAAmB,CADnB,SAEF,CAEA,sBACE,oBACF,CAEA,4BACE,oBAAqB,CACrB,gCACF,CAEA,eACE,aAAc,CACd,iBAAmB,CACnB,iBACF,CAEA,mBAGE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAJlB,eAAgB,CAChB,YAIF,CAEA,sBAEE,aAAc,CACd,iBAAmB,CAFnB,gBAIF,CAEA,gBAEE,kBAAmB,CADnB,YAAa,CAEb,cAAe,CACf,SACF,CAEA,gBACE,wBAAyB,CAGzB,iBAAkB,CAFlB,UAAY,CAGZ,iBAAmB,CACnB,eAAgB,CAHhB,oBAIF,CAEA,WACE,aAAc,CACd,eACF,CAGA,yBACE,uBAEE,eAAgB,CADhB,kBAEF,CAEA,gBACE,QAAO,CACP,eACF,CAEA,mBACE,eAAgB,CAChB,eACF,CACF,CAGA,yBAKE,oCAAqC,CAJrC,kKAC2I,CAE3I,iCAAmC,CADnC,yBAGF,CAEA,mBACE,GACE,iCACF,CACA,GACE,uCACF,CACF,CCxIA,aACE,qBAAuB,CACvB,iBAAkB,CAClB,8BAAwC,CACxC,eACF,CAEA,aAGE,wBAAyB,CADzB,+BAAgC,CADhC,cAGF,CAEA,cAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,oBACF,CAEA,iBAEE,aAAc,CADd,QAEF,CAEA,gBAEE,SACF,CAEA,gBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,gBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAOA,cAEE,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,cAIF,CAEA,oBAEE,oBAAqB,CACrB,gCAAgD,CAFhD,YAGF,CAEA,kBACE,aACF,CAEA,gBACE,YAAa,CACb,SAAW,CACX,wBACF,CAEA,iBAGE,kBAAmB,CAGnB,+BAAgC,CAEhC,aAAc,CAPd,YAAa,CAMb,iBAAmB,CALnB,6BAA8B,CAE9B,mBAKF,CAEA,oBAEE,kBAAmB,CADnB,YAAa,CAEb,SACF,CAEA,2BAEE,wBAAyB,CACzB,iBAAkB,CAFlB,oBAGF,CAEA,mBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,iBAAkB,CAClB,iBACF,CAiBA,wBACE,eACF,CAEA,cAEE,wBAAyB,CACzB,iBAAmB,CAFnB,UAGF,CAEA,iBACE,wBAAyB,CAKzB,+BAAgC,CADhC,aAAc,CADd,eAAgB,CAFhB,mBAAqB,CACrB,eAAgB,CAIhB,kBACF,CAEA,iBAEE,+BAAgC,CADhC,mBAAqB,CAErB,kBACF,CAEA,6BACE,wBACF,CAEA,oBACE,aAAc,CACd,eACF,CAEA,WAEE,aAAc,CADd,gBAAkB,CAElB,iBACF,CAEA,0BAEE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,WAGE,iBACF,CAEA,wBAJE,aAAc,CADd,gBASF,CAJA,aAGE,iBACF,CAEA,cAEE,iBAAkB,CAClB,gBAAkB,CAClB,eAAgB,CAHhB,oBAKF,CAEA,uBACE,wBAAyB,CACzB,aACF,CAEA,uBACE,wBAAyB,CACzB,aACF,CAEA,uBACE,wBAAyB,CACzB,aACF,CAEA,uBACE,wBAAyB,CACzB,aACF,CAEA,gBAEE,UACF,CAEA,YAEE,WAAY,CAGZ,iBAAkB,CAClB,cAAe,CAHf,aAAe,CAIf,oCACF,CAEA,kBACE,wBACF,CAEA,uBACE,wBACF,CAEA,uBACE,wBACF,CAEA,SAGE,aAAc,CACd,iBAAkB,CAFlB,YAAa,CADb,iBAIF,CAEA,UACE,eAAgB,CAChB,WAAY,CACZ,aAAc,CACd,cAAe,CACf,yBACF,CAEA,gBACE,aACF,CAEA,sBAIE,4BAA6B,CAH7B,YAAa,CACb,sBAAuB,CACvB,cAEF,CAEA,YAEE,kBAAmB,CADnB,YAAa,CAEb,UACF,CAEA,gBAGE,qBAAuB,CADvB,wBAAyB,CAIzB,iBAAkB,CAFlB,aAAc,CACd,cAAe,CAEf,iBAAmB,CANnB,oBAAuB,CAOvB,uBACF,CAEA,qCACE,wBAAyB,CACzB,oBACF,CAEA,yBAEE,kBAAmB,CADnB,UAEF,CAEA,uBACE,wBAAyB,CACzB,oBAAqB,CACrB,UACF,CAEA,qBAEE,aAAc,CADd,oBAEF,CAEA,OAOE,kBAAmB,CAJnB,sBAA6B,CAC7B,iBAAkB,CAClB,YAAa,CACb,6BAA8B,CAJ9B,aAAc,CADd,oBAOF,CAEA,aAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CAEA,WACE,wBAAyB,CAEzB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,iBAAmB,CAHnB,kBAIF,CAEA,iBACE,wBACF,CAEA,KAIE,iBAAmB,CAHnB,kBASF,CAaA,eACE,wBAAyB,CACzB,oBAAqB,CACrB,UACF,CAEA,qBACE,wBAAyB,CACzB,oBACF,CAcA,yBACE,aACE,YACF,CAEA,cAEE,sBAAuB,CADvB,qBAAsB,CAEtB,QACF,CAEA,gBAEE,uBAAwB,CADxB,UAEF,CAEA,qBACE,QACF,CAEA,gBACE,yBACF,CAEA,gBACE,uBACF,CAEA,qBACE,QACF,CAEA,iBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,SACF,CAEA,cACE,gBACF,CAEA,kCAEE,oBACF,CAEA,YACE,cAAe,CACf,sBACF,CACF,CAEA,yBACE,wBACE,eACF,CAEA,gEAEE,YACF,CAEA,gBACE,qBACF,CACF,CCtbA,6BAIE,eAAmB,CACnB,kBAAmB,CACnB,8BAAwC,CAJxC,aAAc,CADd,eAAgB,CAEhB,YAIF,CAEA,kBAEE,kBAAmB,CADnB,iBAEF,CAEA,qBACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,iBACF,CAEA,uBACE,UAAW,CACX,cAAe,CACf,eAAgB,CAEhB,aAAc,CADd,eAEF,CAEA,eAKE,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAClB,aAAc,CALd,OAAQ,CAMR,kBAAmB,CALnB,iBAMF,CAEA,YACE,cACF,CAEA,gBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,mBACE,kBAAmB,CAGnB,wBAAyB,CADzB,iBAAkB,CADlB,YAGF,CAEA,sBACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,kBACF,CAEA,iBAEE,kBAAmB,CAGnB,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAGlB,aAAc,CATd,YAAa,CAQb,cAAe,CANf,OAAQ,CAKR,kBAAmB,CAJnB,iBAOF,CAEA,eACE,aAAc,CAEd,cAAe,CADf,eAEF,CAEA,gBACE,kBAAmB,CAGnB,wBAAyB,CADzB,iBAAkB,CADlB,YAGF,CAEA,mBACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,kBACF,CAEA,mBACE,eAAgB,CAEhB,QAAS,CADT,SAEF,CAEA,mBAEE,aAAc,CAEd,sBAAkB,CADlB,iBAEF,CAEA,0BAEE,aAAc,CADd,WAAY,CAEZ,eAAiB,CAEjB,MAAO,CADP,iBAEF,CAEA,mBAGE,kBAAmB,CAEnB,4BAA6B,CAJ7B,YAAa,CACb,6BAA8B,CAE9B,gBAEF,CAEA,KAQE,mBAAoB,CALpB,cAAe,CAOf,OAAQ,CATR,iBAUF,CAOA,eACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,oCACE,kBAAmB,CACnB,oBACF,CAEA,aACE,kBAEF,CAEA,kCACE,kBACF,CAEA,sBACE,kBACF,CAEA,mBAGE,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAJlB,eAAgB,CAChB,YAIF,CAEA,sBACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,kBACF,CAEA,cAEE,aAAc,CADd,aAEF,CAEA,qBACE,eAAgB,CAChB,gBACF,CAGA,yBACE,6BAEE,WAAY,CADZ,YAEF,CAEA,qBACE,cACF,CAEA,uBACE,cACF,CAEA,mBACE,YACF,CAEA,mBACE,qBAAsB,CACtB,QACF,CAEA,KAEE,sBAAuB,CADvB,UAEF,CACF,CAGA,mBACE,8BACF,CCnOA,aACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,wCAGE,aAAc,CACd,iBAAkB,CAFlB,eAGF,CAEA,kBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAElB,oBAAqB,CADrB,YAEF,CAEA,oBAEE,aAAc,CADd,gBAEF,CAEA,WAOE,wBAAyB,CANzB,yBAA0B,CAC1B,iBAAkB,CAGlB,cAAe,CAFf,iBAAkB,CAClB,iBAAkB,CAElB,uBAEF,CAOA,mCAHE,wBAAyB,CADzB,oBAQF,CAJA,kBAGE,kBACF,CAEA,oBAEE,wBAAyB,CADzB,oBAEF,CAEA,cAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,QACF,CAEA,WAEE,aAAc,CADd,cAEF,CAEA,aAEE,aAAc,CADd,eAEF,CAEA,yBAEE,aAAc,CADd,gBAEF,CAEA,eAEE,kBAAmB,CAGnB,qBAAuB,CAEvB,wBAAyB,CADzB,iBAAkB,CALlB,YAAa,CAEb,QAAS,CACT,YAIF,CAEA,WAEE,aAAc,CADd,cAEF,CAEA,cACE,QAAO,CACP,eACF,CAEA,WAEE,aAAc,CADd,eAAgB,CAEhB,oBACF,CAEA,WAEE,aAAc,CADd,iBAEF,CAEA,gBACE,cACF,CAEA,aACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAFlB,aAAc,CADd,cAAe,CADf,iBAAkB,CAGlB,cAEF,CAEA,mBACE,wBACF,CAEA,kBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cACF,CAEA,eACE,oBACF,CAEA,qBAIE,aAAc,CAHd,aAAc,CAEd,eAAgB,CADhB,mBAGF,CAEA,sBAME,qBAAuB,CAHvB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,cAAgB,CADhB,UAMF,CAEA,oCAGE,aAAc,CAFd,kBAAoB,CACpB,YAEF,CAEA,cAGE,aAAc,CADd,iBAAmB,CAEnB,iBAAkB,CAHlB,gBAIF,CAEA,gBAGE,kBAAmB,CAGnB,4BAA6B,CAL7B,YAAa,CAGb,QAAS,CAFT,6BAA8B,CAG9B,gBAEF,CAEA,eAQE,iBAAkB,CAJlB,eAMF,CAEA,YACE,iBACF,CA4CA,yBACE,aACE,UACF,CAEA,WACE,iBACF,CAEA,WACE,cACF,CAEA,eACE,qBAAsB,CACtB,iBACF,CAEA,kBACE,YACF,CAEA,gBACE,uBACF,CAEA,KACE,UACF,CACF,CAEA,yBACE,WACE,oBACF,CAEA,aACE,iBACF,CAMA,oCACE,cACF,CACF,CCrRA,eACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,gBACE,iBACF,CAEA,mBAEE,aAAc,CACd,gBAAiB,CAFjB,gBAGF,CAEA,kBAEE,aAAc,CADd,iBAEF,CAEA,eACE,YAAa,CAEb,QAAS,CADT,sBAAuB,CAEvB,oBACF,CAEA,MAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,iBACF,CAEA,YACE,cAGF,CAEA,oBACE,aACF,CAEA,kBACE,aACF,CAOA,iBACE,YAAa,CAEb,QAAS,CADT,sBAEF,CAEA,wBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cACF,CAEA,2BAEE,aAAc,CADd,eAEF,CAEA,sBAGE,eAAY,CAFZ,YAAa,CAEb,UAAY,CADZ,wDAEF,CAEA,gBAGE,kBAAmB,CAGnB,wBAAyB,CADzB,iBAAkB,CAJlB,YAAa,CACb,6BAA8B,CAE9B,cAGF,CAEA,uBACE,wBAAyB,CACzB,oBACF,CAEA,yBACE,wBAAyB,CACzB,oBACF,CAEA,YAEE,aAAc,CADd,eAEF,CAEA,cACE,iBACF,CAEA,yBAEE,wBAAyB,CACzB,iBAAkB,CAFlB,eAGF,CAEA,eAEE,wBAAyB,CACzB,iBAAmB,CAFnB,UAGF,CAEA,kBACE,wBAAyB,CAKzB,+BAAgC,CADhC,aAAc,CADd,eAAgB,CAFhB,mBAAqB,CACrB,eAAgB,CAIhB,kBACF,CAEA,kBAEE,+BAAgC,CADhC,mBAAqB,CAErB,kBACF,CAEA,YACE,wBACF,CAEA,cACE,wBACF,CAEA,oBACE,aAAc,CACd,eACF,CAEA,aAEE,aAAc,CADd,iBAEF,CAEA,cAGE,wBAAyB,CACzB,iBAAkB,CAClB,iBAAmB,CAHnB,aAAe,CADf,UAKF,CAEA,qBAEE,wBAAyB,CADzB,oBAEF,CAEA,uBAEE,wBAAyB,CADzB,oBAEF,CAEA,YACE,wBAAyB,CAGzB,iBAAkB,CAFlB,aAAc,CAId,eAAgB,CAHhB,oBAIF,CAEA,YACE,aAAc,CACd,iBACF,CAEA,gBACE,wBAMF,CAEA,gCALE,iBAAkB,CAFlB,UAAY,CAGZ,gBAAkB,CAClB,eAAgB,CAHhB,oBAaF,CAPA,gBACE,wBAMF,CAEA,kBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cACF,CAEA,qBAEE,aAAc,CADd,gBAEF,CAEA,oBAEE,aAAc,CADd,eAEF,CAEA,eACE,YAAa,CACb,cAAe,CACf,SACF,CAEA,iBACE,wBAAyB,CAGzB,iBAAkB,CAFlB,aAAc,CAGd,iBAAmB,CACnB,eAAgB,CAHhB,oBAIF,CAEA,iBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cACF,CAEA,oBAEE,aAAc,CADd,eAEF,CAEA,oBAEE,aAAc,CACd,cAAe,CAFf,iBAGF,CAEA,oBAGE,cAAW,CAFX,YAAa,CAEb,SAAW,CADX,wDAEF,CAEA,mBAGE,kBAAmB,CAEnB,qBAAuB,CACvB,wBAAyB,CACzB,iBAAkB,CANlB,YAAa,CACb,6BAA8B,CAE9B,aAIF,CAEA,aAEE,aAAc,CADd,eAEF,CAEA,sBACE,wBAAyB,CACzB,UACF,CAEA,sBACE,wBAAyB,CACzB,UACF,CAEA,aAEE,iBAAkB,CAClB,gBAAkB,CAClB,eAAgB,CAHhB,uBAIF,CAEA,oBAGE,kBAAmB,CAEnB,4BAA6B,CAJ7B,YAAa,CACb,6BAA8B,CAE9B,gBAEF,CAEA,kBAEE,kBAAmB,CAInB,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,aAAc,CARd,YAAa,CASb,eAAgB,CAPhB,SAAW,CACX,eAAgB,CAChB,YAMF,CAoBA,kCACE,wBAAyB,CACzB,oBACF,CAEA,sBACE,wBAAyB,CACzB,oBAAqB,CACrB,kBAAmB,CACnB,WACF,CAcA,wBACE,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAElB,eAAgB,CADhB,cAEF,CAEA,2BACE,aAAc,CAEd,gBAAiB,CADjB,gBAEF,CAEA,qBACE,aAAc,CACd,eAAiB,CAEjB,eAAgB,CADhB,iBAEF,CAEA,qBAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,wDAEF,CAEA,qBACE,YAAa,CACb,qBAAsB,CACtB,SACF,CAEA,qBAEE,aAAc,CACd,eAAiB,CAFjB,eAGF,CAEA,oBACE,aAAc,CACd,eACF,CAEA,iDAGE,wBAAyB,CACzB,iBAAkB,CAClB,eAAiB,CAHjB,aAAe,CAIf,oEACF,CAEA,6DAGE,oBAAqB,CACrB,gCAAgD,CAFhD,YAGF,CAEA,YACE,aAAc,CACd,eAAiB,CACjB,iBACF,CAGA,8BACE,eAAiB,CACjB,eACF,CAGA,yBACE,qBACE,yBACF,CACF,CC9aA,iBACE,YAAa,CACb,qBAAsB,CACtB,QAAS,CACT,YACF,CAEA,kBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,iBAAkB,CAClB,iBACF,CAEA,iBAME,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAOA,iBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,oBAEE,aAAc,CADd,QAEF,CAEA,UAGE,oBAAqB,CAFrB,YAAa,CACb,qBAAsB,CAEtB,UACF,CAEA,QAEE,aAAc,CACd,qBAAsB,CAFtB,iBAGF,CAEA,YAEE,cAAe,CADf,eAEF,CAEA,kBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cACF,CAEA,wBACE,kBACF,CAEA,cAGE,wBAAyB,CACzB,kBAAmB,CAFnB,WAAY,CAGZ,eAAgB,CAChB,iBAAkB,CALlB,UAMF,CAEA,eAGE,kBAAmB,CAFnB,WAAY,CACZ,yBAEF,CAEA,eAME,UAAY,CAEZ,iBAAmB,CAHnB,eAAgB,CAFhB,QAAS,CAFT,iBAAkB,CAMlB,iCAAwC,CALxC,OAAQ,CAER,8BAKF,CAEA,kBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAsBA,mBAEE,kBAAmB,CAGnB,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CANlB,YAAa,CAEb,QAAS,CACT,YAIF,CAEA,gBAEE,iCAAkC,CADlC,gBAEF,CAEA,gBAEE,aAAc,CADd,eAEF,CAEA,gBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cACF,CAEA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,WAEE,kBAAmB,CAInB,gBAAiB,CADjB,iBAAkB,CAJlB,YAAa,CAEb,QAAS,CACT,YAGF,CAEA,mBACE,wBAAyB,CACzB,oBACF,CAEA,iBACE,wBAAyB,CACzB,oBACF,CAEA,mBACE,wBAAyB,CACzB,oBACF,CAEA,gBACE,wBAAyB,CACzB,oBACF,CAEA,WACE,gBACF,CAEA,cACE,YAAa,CACb,qBACF,CAEA,YAGE,oBACF,CAEA,YACE,iBAAmB,CAEnB,eACF,CAEA,eACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cACF,CAEA,kBAEE,aAAc,CADd,eAEF,CAEA,aACE,YAAa,CACb,qBAAsB,CACtB,SACF,CAEA,YAGE,aAAS,CAET,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CANlB,YAAa,CAOb,iBAAmB,CALnB,QAAS,CADT,mCAAoC,CAEpC,cAKF,CAOA,wBAEE,aACF,CAMA,aAGE,aAAc,CACd,iBAAkB,CAFlB,aAAe,CADf,iBAIF,CAEA,gBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cACF,CAEA,mBAEE,aAAc,CADd,eAEF,CAEA,eACE,YAAa,CACb,qBAAsB,CACtB,SAAW,CACX,gBAAiB,CACjB,eACF,CAEA,WAIE,qBAAuB,CAEvB,wBAAyB,CADzB,iBAAkB,CAJlB,YAAa,CAMb,iBAAmB,CALnB,QAAS,CACT,aAKF,CAEA,mBACE,wBAAyB,CACzB,oBACF,CAEA,UAEE,aAAc,CACd,qBAAsB,CAFtB,eAAgB,CAGhB,cACF,CAEA,aACE,aACF,CAEA,kBAKE,4BAA6B,CAJ7B,YAAa,CAEb,QAAS,CADT,sBAAuB,CAEvB,gBAEF,CAEA,oBAEE,kBAAmB,CAGnB,iBAAkB,CAJlB,YAAa,CAEb,QAAS,CACT,cAAe,CAEf,iBACF,CAEA,8BACE,wBAAyB,CACzB,wBACF,CAEA,2BACE,wBAAyB,CACzB,wBACF,CAEA,8BACE,wBAAyB,CACzB,wBACF,CAEA,iBACE,cACF,CAEA,iBACE,QACF,CAEA,oBAGE,aAAc,CADd,iBAAmB,CADnB,gBAGF,CAEA,gBAEE,kBAAmB,CAInB,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,aAAc,CARd,YAAa,CASb,eAAgB,CAPhB,SAAW,CACX,eAAgB,CAChB,YAMF,CA0BA,yBACE,iBACE,qBAAsB,CAEtB,SACF,CAEA,2BAJE,sBAMF,CAEA,kBACE,yBACF,CAEA,YACE,mCACF,CAEA,YAEE,SAAW,CADX,yBAEF,CAEA,oBACE,qBAAsB,CACtB,iBACF,CACF,CAEA,yBACE,YACE,yBACF,CAEA,kBACE,qBACF,CACF,CCxaA,gBACE,YAAa,CACb,qBAAsB,CACtB,QAAS,CACT,YACF,CAEA,iBAGE,kBAAmB,CAGnB,aAAc,CAFd,iBAAkB,CAClB,iBAEF,CAEA,iCARE,YAAa,CACb,sBAWF,CAJA,gBAGE,kBACF,CAEA,kBAEE,kBAAmB,CAInB,gBAAiB,CADjB,iBAAkB,CAJlB,YAAa,CAEb,QAAS,CACT,cAGF,CAEA,0BACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CAEA,wBACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CAEA,aACE,cACF,CAEA,mBAEE,gBAAiB,CADjB,gBAEF,CAEA,kBAEE,iBAAmB,CADnB,QAAS,CAET,UACF,CAEA,iBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cACF,CAEA,oBAEE,aAAc,CADd,eAEF,CAEA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,cAEE,kBAAmB,CAInB,gBAAiB,CADjB,iBAAkB,CAJlB,YAAa,CAEb,QAAS,CACT,YAGF,CAEA,oBACE,wBAAyB,CACzB,oBACF,CAEA,sBACE,wBAAyB,CACzB,oBACF,CAEA,oBACE,wBAAyB,CACzB,oBACF,CAEA,sBACE,wBAAyB,CACzB,oBACF,CAEA,mBACE,wBAAyB,CACzB,oBACF,CAEA,WACE,gBACF,CAEA,cACE,YAAa,CACb,qBACF,CAEA,YACE,gBAAiB,CACjB,eAAgB,CAChB,oBACF,CAEA,YAEE,aAAc,CADd,iBAAmB,CAEnB,eACF,CAEA,oBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cACF,CAEA,uBAEE,aAAc,CADd,eAEF,CAEA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,aAGE,kBAAmB,CAEnB,qBAAuB,CAEvB,wBAAyB,CADzB,iBAAkB,CALlB,YAAa,CACb,6BAA8B,CAE9B,cAIF,CAEA,cAEE,aAAc,CADd,eAEF,CAEA,cAEE,aAAc,CADd,eAEF,CAEA,kBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cACF,CAEA,qBAEE,aAAc,CADd,eAEF,CAEA,iBACE,YAAa,CACb,qBAAsB,CACtB,SACF,CAEA,aAGE,kBAAmB,CAEnB,qBAAuB,CAEvB,wBAAyB,CADzB,iBAAkB,CALlB,YAAa,CACb,6BAA8B,CAE9B,aAIF,CAEA,gBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cACF,CAEA,cAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,iBAEE,aAAc,CADd,QAEF,CAEA,eAGE,kBAAmB,CAFnB,YAAa,CACb,SAEF,CAEA,cAEE,wBAAyB,CACzB,iBAAkB,CAClB,iBAAmB,CAHnB,aAIF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,aACE,wBAAyB,CACzB,iBAAkB,CAClB,eACF,CAEA,cAGE,kBAAmB,CAEnB,wBAAyB,CACzB,+BAAgC,CALhC,YAAa,CACb,6BAA8B,CAE9B,YAGF,CAEA,YAEE,aAAc,CADd,eAEF,CAEA,aAGE,wBAAyB,CAGzB,iBAAkB,CAJlB,aAAc,CAEd,UAAY,CAHZ,iBAAmB,CAInB,oBAEF,CAEA,gBAEE,qBAAuB,CADvB,YAEF,CAEA,eAGE,aAAS,CAGT,wBAAyB,CACzB,iBAAkB,CANlB,YAAa,CAOb,iBAAmB,CALnB,QAAS,CADT,wCAAyC,CAGzC,mBAAqB,CADrB,aAKF,CAEA,WAEE,aAAc,CADd,eAEF,CAEA,aAEE,aAAc,CADd,eAEF,CAMA,aAEE,aAAc,CADd,qBAAsB,CAEtB,iBACF,CAEA,iBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cACF,CAEA,oBAEE,aAAc,CADd,eAEF,CAEA,oBAGE,aAAc,CAFd,QAAS,CACT,mBAEF,CAEA,oBACE,mBACF,CAEA,iBAKE,4BAA6B,CAJ7B,YAAa,CAEb,QAAS,CADT,sBAAuB,CAEvB,gBAEF,CAEA,KAEE,sBAA6B,CAC7B,iBAAkB,CAKlB,oBAAqB,CAJrB,cAMF,CAEA,aACE,wBAAyB,CACzB,oBAEF,CAEA,mBACE,wBAAyB,CACzB,oBACF,CAEA,aAEE,oBAAqB,CACrB,aACF,CAEA,mBACE,wBAEF,CAGA,yBACE,gBAEE,UAAW,CADX,aAEF,CAEA,kBACE,qBAAsB,CACtB,iBACF,CAEA,cACE,mCACF,CAEA,cACE,yBACF,CAEA,cAEE,sBAAuB,CADvB,qBAAsB,CAEtB,QACF,CAEA,eAEE,mBAAoB,CADpB,qBAEF,CAEA,eAEE,SAAW,CADX,yBAEF,CAEA,iBACE,qBACF,CACF,CAEA,yBACE,cACE,yBACF,CAEA,kBACE,YACF,CAEA,aACE,cACF,CACF,CChaA,aAGE,wBAA6B,CAD7B,eAAgB,CADhB,YAGF,CAEA,kBACE,qBAAuB,CACvB,iBAAkB,CAClB,8BAAwC,CAKxC,YAAa,CACb,qBAAsB,CAHtB,aAAc,CADd,gBAAiB,CAEjB,eAAgB,CAHhB,UAMF,CAGA,sBASE,kBAAmB,CAHnB,0BAAoC,CADpC,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAQP,YAAa,CAVb,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,sBACE,qBAAuB,CACvB,iBAAkB,CAClB,8BAAwC,CAKxC,YAAa,CACb,qBAAsB,CAHtB,eAAgB,CADhB,gBAAiB,CAEjB,eAAgB,CAHhB,UAMF,CAEA,eAGE,kBAAmB,CAGnB,wBAAyB,CADzB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,cAGF,CAEA,kBAEE,aAAc,CADd,QAEF,CAEA,iBAEE,aAAc,CACd,eAAiB,CAFjB,gBAGF,CAiBA,oBAEE,aACF,CAEA,gBAIE,wBAAyB,CACzB,+BAAgC,CAJhC,YAAa,CACb,sBAAuB,CACvB,cAGF,CAEA,MAGE,kBAAmB,CAGnB,iBAAkB,CALlB,YAAa,CACb,qBAAsB,CAGtB,aAAc,CAGd,eAAgB,CAJhB,YAAa,CAGb,uBAEF,CAEA,aACE,wBAAyB,CACzB,wBACF,CAEA,cACE,wBAAyB,CACzB,UACF,CAEA,WACE,cAAe,CACf,mBACF,CAEA,YACE,iBAAmB,CACnB,eAAgB,CAChB,iBACF,CAEA,aACE,QAAO,CACP,eAAgB,CAChB,YACF,CAEA,cACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAElB,WAAY,CADZ,YAEF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,YACE,gBACF,CAEA,eACE,QAAO,CAEP,eACF,CAEA,cACE,wBAAyB,CAEzB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,iBAAmB,CAHnB,kBAIF,CAEA,oBACE,wBACF,CAGA,yBACE,sBACE,aACF,CAEA,sBAEE,eAAgB,CADhB,cAEF,CAEA,eACE,YACF,CAEA,gBAEE,eAAgB,CADhB,YAEF,CAEA,MACE,cAAgB,CAChB,eACF,CAEA,WACE,gBACF,CAEA,YACE,gBACF,CAEA,aACE,YACF,CACF,CAEA,yBACE,MACE,kBAAmB,CACnB,cAAe,CACf,aACF,CAEA,WACE,iBAAkB,CAClB,eAAgB,CAChB,kBACF,CAEA,YACE,eACF,CACF,CC/NA,iBASE,kBAAmB,CAHnB,0BAAoC,CADpC,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAQP,YAAa,CAVb,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAGA,kBAGE,wBAA6B,CAD7B,eAAgB,CADhB,UAGF,CAEA,iCAGE,+BAAyC,CAOzC,QAAS,CAJT,eAAgB,CADhB,cAAe,CAEf,gBAIF,CAEA,gDAZE,qBAAuB,CACvB,kBAAmB,CAMnB,YAAa,CACb,qBAAsB,CALtB,UAmBF,CAVA,eAGE,gCAA2C,CAG3C,eAAgB,CADhB,gBAAiB,CAEjB,eAGF,CAEA,cAGE,kBAAmB,CAGnB,wBAAyB,CADzB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,cAGF,CAEA,iBAEE,aAAc,CACd,gBAAiB,CAFjB,QAGF,CAEA,cAUE,kBAAmB,CATnB,eAAgB,CAChB,WAAY,CAUZ,iBAAkB,CAPlB,aAAc,CADd,cAAe,CAKf,YAAa,CANb,gBAAiB,CAKjB,WAAY,CAGZ,sBAAuB,CALvB,SAAU,CAOV,+BAAiC,CANjC,UAOF,CAEA,oBACE,wBACF,CAEA,YACE,QAAO,CACP,eAAgB,CAChB,cACF,CAGA,eAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,YACF,CAEA,SAME,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAQA,eAKE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,aAAc,CALd,SAAW,CAMX,kBAAmB,CALnB,YAMF,CAEA,YACE,gBACF,CAGA,aAGE,aAAc,CADd,YAAa,CADb,iBAGF,CAEA,YACE,cAAe,CACf,kBACF,CAEA,gBAEE,aACF,CAEA,eAEE,eACF,CAGA,iBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAGA,kBACE,wBAAyB,CACzB,iBAAkB,CAClB,eACF,CAEA,iBAGE,kBAAmB,CAEnB,wBAAyB,CACzB,+BAAgC,CALhC,YAAa,CACb,6BAA8B,CAE9B,mBAGF,CAEA,oBAEE,aAAc,CACd,gBAAiB,CAFjB,QAGF,CAEA,aACE,wBAAyB,CAGzB,kBAAmB,CAFnB,UAAY,CAGZ,eAAiB,CACjB,eAAgB,CAHhB,qBAIF,CAGA,kBACE,+BACF,CAEA,6BACE,kBACF,CAEA,iBAGE,kBAAmB,CAEnB,qBAAyB,CACzB,+BAAgC,CALhC,YAAa,CACb,6BAA8B,CAE9B,qBAGF,CAEA,oBAEE,aAAc,CACd,cAAe,CACf,eAAgB,CAHhB,QAIF,CAEA,sBACE,wBAAyB,CAGzB,kBAAmB,CAFnB,UAAY,CAGZ,gBAAkB,CAFlB,mBAGF,CAGA,YAEE,wBAAyB,CADzB,mBAEF,CAGA,gBAQE,kBAAmB,CAPnB,qBAAuB,CACvB,wBAAyB,CACzB,iBAAkB,CAGlB,YAAa,CAIb,QAAS,CAHT,6BAA8B,CAH9B,oBAAsB,CAKtB,mBAAoB,CAJpB,0CAMF,CAEA,sBAEE,oBAAqB,CADrB,8BAEF,CAEA,2BACE,eACF,CAEA,WACE,QAAO,CACP,WACF,CAEA,mBAEE,kBAAmB,CADnB,YAAa,CAEb,UAAY,CACZ,mBACF,CAEA,WAEE,aAAc,CAGd,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAHhB,QAKF,CAEA,cACE,YAAa,CAEb,aAAc,CADd,SAEF,CAEA,YASE,kBAAmB,CARnB,eAAgB,CAChB,qBAAsB,CACtB,iBAAkB,CAElB,cAAe,CAGf,YAAa,CAFb,eAAiB,CAIjB,UAAY,CANZ,kBAAoB,CAGpB,uBAAyB,CAIzB,kBACF,CAEA,kBACE,wBAAyB,CACzB,oBACF,CAEA,uBACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CAEA,yBACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CAEA,gBACE,YACF,CAEA,kBAEE,aAAc,CACd,eAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,YACE,YAAa,CACb,QAAS,CACT,oBACF,CAEA,MAEE,aAAc,CADd,eAEF,CAEA,WACE,YAAa,CACb,wBACF,CAEA,YAEE,kBAAmB,CACnB,gBAAkB,CAClB,eAAgB,CAHhB,qBAIF,CAEA,qBACE,wBAAyB,CACzB,aACF,CAEA,wBACE,wBAAyB,CACzB,aACF,CAEA,oBACE,wBAAyB,CACzB,aACF,CAGA,sBASE,kBAAmB,CAHnB,0BAAoC,CADpC,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YACF,CAEA,wBACE,qBAAuB,CACvB,iBAAkB,CAClB,4BAA0C,CAG1C,WAAY,CADZ,eAAgB,CADhB,UAGF,CAEA,uBAGE,wBAAyB,CADzB,+BAAgC,CADhC,mBAGF,CAEA,0BAEE,aAAc,CADd,QAEF,CAEA,qBACE,cACF,CAEA,uBAEE,aAAc,CADd,eAEF,CAEA,cACE,aAAc,CACd,eAAiB,CACjB,iBACF,CAEA,wBAME,wBAAyB,CADzB,4BAA6B,CAJ7B,YAAa,CAEb,QAAS,CADT,wBAAyB,CAEzB,mBAGF,CAEA,YAGE,qBAAuB,CADvB,wBAAyB,CAGzB,iBAAkB,CADlB,aAAc,CAEd,cAAe,CALf,kBAAoB,CAMpB,kBACF,CAEA,kBACE,wBAAyB,CACzB,UACF,CAEA,YAGE,wBAAyB,CADzB,wBAAyB,CAGzB,iBAAkB,CADlB,UAAY,CAEZ,cAAe,CALf,kBAAoB,CAMpB,kBACF,CAEA,kBACE,wBAAyB,CACzB,oBACF,CAGA,yBACE,iBACE,aACF,CAEA,eACE,eACF,CAMA,8CAEE,YACF,CAEA,gBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,QAAS,CACT,YACF,CAEA,WACE,UACF,CAEA,mBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,SACF,CAEA,cACE,mBAAoB,CACpB,UACF,CACF,CCreA,mBAEE,wBAAyB,CACzB,YAAa,CACb,qBAAsB,CAHtB,gBAIF,CAEA,mBACE,qBAAuB,CACvB,+BAAgC,CAChC,8BAAqC,CACrC,eAAgB,CAChB,KAAM,CACN,WACF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,mBACF,CAEA,mBAEE,aAAc,CACd,iBAAkB,CAFlB,QAGF,CAEA,cACE,YAAa,CACb,UACF,CAEA,WAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,iBACF,CAEA,YAEE,aAAc,CADd,gBAAkB,CAElB,oBACF,CAEA,YAGE,aAAc,CAFd,gBAAiB,CACjB,eAEF,CAEA,YAGE,wBAAyB,CACzB,4BAA6B,CAH7B,YAAa,CACb,cAGF,CAEA,SACE,eAAgB,CAOhB,WAAoC,CAApC,6BAAoC,CADpC,aAAc,CAHd,cAAe,CACf,iBAAmB,CACnB,eAAgB,CAHhB,mBAAoB,CAMpB,uBACF,CAEA,eAEE,wBAAyB,CADzB,aAEF,CAEA,gBAGE,qBAAuB,CADvB,2BAA4B,CAD5B,aAGF,CAEA,cASE,kBAAmB,CAGnB,0BAA4B,CAP5B,iBAAkB,CAClB,8BAAwC,CAExC,YAAa,CAEb,QAAS,CACT,eAAgB,CAPhB,mBAAoB,CAHpB,cAAe,CAEf,UAAW,CADX,QAAS,CAKT,YAMF,CAEA,sBACE,wBAAyB,CAEzB,wBAAyB,CADzB,aAEF,CAEA,oBACE,wBAAyB,CAEzB,wBAAyB,CADzB,aAEF,CAEA,sBACE,wBAAyB,CAEzB,wBAAyB,CADzB,aAEF,CAEA,oBACE,eAAgB,CAChB,WAAY,CAGZ,aAAc,CADd,cAAe,CADf,gBAAiB,CAGjB,UACF,CAEA,0BACE,SACF,CAEA,mBACE,GAEE,SAAU,CADV,0BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAEA,oBACE,QAAO,CAEP,eAAgB,CADhB,YAEF,CAEA,mBACE,qBAAuB,CACvB,4BAA6B,CAE7B,eAAgB,CADhB,YAEF,CAEA,gBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAG3D,aAAc,CADd,gBAEF,CAEA,mBAEE,aAAc,CACd,cAAe,CAFf,eAGF,CAEA,eACE,YAAa,CACb,qBAAsB,CACtB,SACF,CAEA,kBACE,wBAAyB,CAEzB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,iBAAmB,CAHnB,mBAAqB,CAKrB,eAAgB,CADhB,oCAEF,CAEA,wBACE,wBACF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,SACF,CAEA,aAGE,kBAAmB,CAEnB,wBAAyB,CACzB,iBAAkB,CALlB,YAAa,CAMb,iBAAmB,CALnB,6BAA8B,CAE9B,aAIF,CAEA,8BACE,aACF,CAEA,6BAEE,aAAc,CADd,eAEF,CAEA,aAKE,aAAc,CAJd,YAAa,CACb,qBAAsB,CAEtB,iBAAmB,CADnB,UAGF,CAGA,yBACE,gBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,QAAS,CACT,YACF,CAEA,cACE,kBAAmB,CACnB,QAAS,CAET,4BAA6B,CAD7B,UAEF,CAEA,YAEE,eAAgB,CADhB,cAEF,CAEA,SAEE,mBAAqB,CADrB,kBAEF,CAMA,uCACE,YACF,CAEA,gBAEE,UAAW,CADX,yBAEF,CAEA,cAGE,SAAU,CAEV,cAAe,CAJf,cAAe,CAGf,UAAW,CAFX,QAIF,CACF,CAEA,yBACE,mBACE,gBACF,CAEA,cACE,qBAAsB,CACtB,SACF,CAEA,WAKE,wBAAyB,CACzB,iBAAkB,CALlB,kBAAmB,CACnB,6BAA8B,CAE9B,aAAe,CADf,UAIF,CAEA,YACE,iBACF,CAEA,SACE,gBAAkB,CAClB,oBACF,CACF,CAGA,aACE,oDAGE,YACF,CAEA,oBACE,SACF,CACF,CAGA,+BACE,mBACE,uBACF,CAEA,gBACE,uBACF,CAEA,cACE,gBACF,CACF,CAGA,uCACE,yCAGE,eACF,CAEA,cACE,cACF,CACF,CClVA,cAEE,wBAAyB,CACzB,gBAAiB,CAFjB,YAGF,CAEA,gBAGE,kBAAmB,CAEnB,eAAiB,CAEjB,kBAAmB,CACnB,8BAAwC,CAPxC,YAAa,CACb,6BAA8B,CAE9B,kBAAmB,CAEnB,YAGF,CAEA,gBAGE,eAAgB,CAFhB,YAAa,CACb,UAEF,CAEA,8BACE,YAAa,CACb,qBAAsB,CACtB,SACF,CAEA,oCAGE,aAAc,CAFd,eAAiB,CACjB,eAAgB,CAEhB,QACF,CAEA,qCAKE,qBAAuB,CAHvB,wBAAyB,CACzB,iBAAkB,CAGlB,aAAc,CAFd,eAAiB,CAGjB,eAAgB,CANhB,cAAgB,CAOhB,gCACF,CAEA,2CAEE,oBAAqB,CADrB,YAEF,CAEA,8CACE,wBAAyB,CACzB,aAAc,CACd,kBACF,CAEA,8BAGE,kBAAmB,CADnB,YAAa,CADb,iBAGF,CAEA,oCAKE,qBAAuB,CAHvB,wBAAyB,CACzB,iBAAkB,CAGlB,aAAc,CAFd,eAAiB,CAGjB,eAAgB,CANhB,mCAAuC,CAOvC,gCACF,CAEA,0CAEE,oBAAqB,CADrB,YAEF,CAEA,6BAGE,aAAc,CADd,WAAa,CADb,iBAAkB,CAGlB,SACF,CAEA,gBAEE,QAEF,CAEA,qBAHE,kBAAmB,CAFnB,YAiBF,CAZA,KAKE,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAFf,eAAiB,CACjB,eAAgB,CALhB,SAAW,CACX,qBAAuB,CAOvB,oBAAqB,CADrB,uBAEF,CAEA,aACE,wBAAyB,CACzB,UACF,CAEA,mBACE,wBAAyB,CACzB,0BACF,CAEA,aACE,wBAA6B,CAE7B,wBAAyB,CADzB,aAEF,CAEA,mBACE,wBAAyB,CACzB,UACF,CAEA,cAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAGA,eACE,eAAiB,CACjB,kBAAmB,CAGnB,8BAAwC,CADxC,kBAAmB,CADnB,YAGF,CAEA,cAKE,eAAgB,CAJhB,YAAa,CAGb,cAAe,CAFf,UAAW,CACX,kBAGF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,SACF,CAEA,4BAEE,QAAO,CADP,eAEF,CAEA,mBAIE,mBAAqB,CAHrB,WAAY,CAEZ,wBAA0B,CAD1B,kBAGF,CAEA,oBAEE,aAAc,CACd,eAAiB,CAFjB,eAGF,CAEA,yCAGE,wBAAyB,CACzB,iBAAkB,CAClB,eAAiB,CAHjB,cAAgB,CAIhB,gCACF,CAEA,qDAGE,oBAAqB,CADrB,YAEF,CAEA,cACE,iBACF,CAEA,aAKE,aAAc,CAHd,WAAa,CADb,iBAAkB,CAElB,OAAQ,CACR,0BAEF,CAEA,oBACE,mBACF,CAEA,iBAGE,kBAAmB,CAEnB,4BAA6B,CAJ7B,YAAa,CACb,6BAA8B,CAE9B,kBAEF,CAEA,cACE,aAAc,CACd,eACF,CAGA,iBACE,eAAiB,CACjB,kBAAmB,CACnB,8BAAwC,CACxC,eACF,CAEA,eAGE,kBAAmB,CAEnB,qBAAsB,CAEtB,0BAA2B,CAD3B,UAAW,CALX,YAAa,CACb,6BAA8B,CAE9B,cAIF,CAEA,4BAIE,kBAAmB,CAGnB,aAAc,CALd,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,iBAEF,CAEA,oCAGE,aAAc,CADd,kBAEF,CAEA,UACE,iCACF,CAEA,gBACE,GAAO,sBAAyB,CAChC,GAAK,uBAA2B,CAClC,CAEA,gBAEE,aAAc,CADd,gBAEF,CAEA,eACE,QAAS,CACT,iBACF,CAGA,gBAGE,kBAAmB,CAEnB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,cAEF,CAUA,2CANE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAGb,eAAiB,CADjB,SAWF,CAEA,0BAEE,wBAAyB,CACzB,iBAAkB,CAClB,eAAiB,CAHjB,oBAIF,CAGA,yBACE,eACF,CAEA,eAEE,wBAAyB,CADzB,UAEF,CAEA,oCAIE,+BAAgC,CAFhC,YAAa,CACb,eAEF,CAEA,kBACE,wBAAyB,CAEzB,aAAc,CACd,eAAiB,CAFjB,eAAgB,CAGhB,eAAgB,CAChB,KAAM,CACN,UACF,CAEA,2BACE,cAAe,CAEf,iBAAkB,CADlB,wBAAiB,CAAjB,gBAEF,CAEA,iCACE,wBACF,CAEA,gBAEE,eAAiB,CADjB,iBAEF,CAEA,wBACE,wBACF,CAEA,2BACE,wBACF,CAEA,oBAEE,aAAc,CADd,aAAc,CAEd,gBACF,CAEA,mBAEE,aAAc,CADd,aAAc,CAEd,eAAiB,CACjB,iBACF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,cAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAGb,gBAAkB,CADlB,SAGF,CAEA,gBACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,oBAEE,aAAc,CACd,eAAiB,CAFjB,eAGF,CAEA,sBACE,aAAc,CACd,eACF,CAEA,eACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,eAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAGb,gBAAkB,CADlB,SAGF,CAEA,cAGE,kBAAmB,CAFnB,oBAAqB,CAGrB,eAAiB,CACjB,eAAgB,CAHhB,qBAAwB,CAIxB,wBACF,CAEA,uBACE,wBAAyB,CACzB,aACF,CAEA,uBACE,wBAAyB,CACzB,aACF,CAEA,uBACE,wBAAyB,CACzB,aACF,CAEA,uBACE,wBAAyB,CACzB,aACF,CAEA,aACE,YAAa,CACb,WACF,CAEA,MACE,aAAc,CACd,eACF,CAEA,aACE,aACF,CAEA,WACE,aAAc,CACd,eAAiB,CACjB,iBACF,CAEA,gBACE,YAAa,CACb,SACF,CAEA,UAEE,kBAAmB,CAMnB,wBAAyB,CAFzB,WAAY,CACZ,iBAAkB,CAElB,aAAc,CACd,cAAe,CATf,YAAa,CAIb,WAAY,CAFZ,sBAAuB,CAQvB,uBAAyB,CAPzB,UAQF,CAEA,gBACE,wBAAyB,CACzB,aACF,CAEA,uBACE,wBAAyB,CACzB,UACF,CAGA,yBACE,cACE,YACF,CAEA,gBAGE,mBAAoB,CAFpB,qBAAsB,CACtB,UAEF,CAEA,gBACE,qBAAsB,CACtB,QACF,CAOA,yEACE,cAAe,CACf,UACF,CAEA,gBAEE,cAAe,CADf,0BAEF,CAEA,cAEE,mBAAoB,CADpB,qBAEF,CAQA,iCAHE,mBAAoB,CAFpB,qBAAsB,CACtB,QAQF,CAEA,eACE,eACF,CAEA,oCAEE,aACF,CACF", "sources": ["index.css", "../node_modules/react-toastify/scss/_variables.scss", "../node_modules/react-toastify/dist/ReactToastify.css", "../node_modules/react-toastify/scss/_toastContainer.scss", "../node_modules/react-toastify/scss/_toast.scss", "../node_modules/react-toastify/scss/_theme.scss", "../node_modules/react-toastify/scss/_closeButton.scss", "../node_modules/react-toastify/scss/_progressBar.scss", "../node_modules/react-toastify/scss/_icons.scss", "../node_modules/react-toastify/scss/animations/_bounce.scss", "../node_modules/react-toastify/scss/animations/_zoom.scss", "../node_modules/react-toastify/scss/animations/_flip.scss", "../node_modules/react-toastify/scss/animations/_slide.scss", "../node_modules/react-toastify/scss/animations/_spin.scss", "components/Pagination.css", "components/forms/FieldConfigModal.css", "components/forms/FormField.css", "components/forms/FormPreview.css", "components/forms/FormBuilder.css", "components/forms/DynamicPersonForm.css", "components/forms/HierarchicalSelector.css", "components/PersonList.css", "components/import/DivisionCategorySelection.css", "components/import/FileUpload.css", "components/import/FieldMapping.css", "components/import/ImportProgress.css", "components/import/ImportResults.css", "components/import/ImportPersons.css", "components/forms/AllFormsModal.css", "components/PersonManagement.css", "components/PersonsView.css"], "sourcesContent": ["* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\n:root {\r\n  --primary-color: #667eea;\r\n  --primary-dark: #5a67d8;\r\n  --secondary-color: #764ba2;\r\n  --success-color: #48bb78;\r\n  --danger-color: #f56565;\r\n  --warning-color: #ed8936;\r\n  --info-color: #4299e1;\r\n  --dark-color: #2d3748;\r\n  --light-color: #f7fafc;\r\n  --gray-100: #f7fafc;\r\n  --gray-200: #edf2f7;\r\n  --gray-300: #e2e8f0;\r\n  --gray-400: #cbd5e0;\r\n  --gray-500: #a0aec0;\r\n  --gray-600: #718096;\r\n  --gray-700: #4a5568;\r\n  --gray-800: #2d3748;\r\n  --gray-900: #1a202c;\r\n  --white: #ffffff;\r\n  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\r\n  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\r\n  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\r\n  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\r\n}\r\n\r\nbody {\r\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\r\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  background: var(--gray-100);\r\n  min-height: 100vh;\r\n}\r\n\r\n/* Login Styles */\r\n.login-container {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.login-background {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  z-index: -2;\r\n}\r\n\r\n.login-background::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"50\" cy=\"10\" r=\"0.5\" fill=\"rgba(255,255,255,0.05)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\r\n  animation: float 20s ease-in-out infinite;\r\n}\r\n\r\n.login-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  z-index: -1;\r\n}\r\n\r\n.login-card {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 20px;\r\n  padding: 40px;\r\n  width: 100%;\r\n  max-width: 400px;\r\n  box-shadow: var(--shadow-xl);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.login-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.login-logo {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.logo-icon {\r\n  width: 80px;\r\n  height: 80px;\r\n  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto;\r\n  color: white;\r\n  box-shadow: var(--shadow-lg);\r\n}\r\n\r\n.login-header h1 {\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  color: var(--gray-800);\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.login-header p {\r\n  color: var(--gray-600);\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.login-form {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.input-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.input-icon {\r\n  position: absolute;\r\n  left: 15px;\r\n  color: var(--gray-500);\r\n  z-index: 1;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  padding: 15px 15px 15px 45px;\r\n  border: 2px solid var(--gray-200);\r\n  border-radius: 12px;\r\n  font-size: 16px;\r\n  transition: all 0.3s ease;\r\n  background: var(--white);\r\n}\r\n\r\n.form-input:focus {\r\n  outline: none;\r\n  border-color: var(--primary-color);\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.password-toggle {\r\n  position: absolute;\r\n  right: 15px;\r\n  background: none;\r\n  border: none;\r\n  color: var(--gray-500);\r\n  cursor: pointer;\r\n  padding: 5px;\r\n  border-radius: 4px;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.password-toggle:hover {\r\n  color: var(--primary-color);\r\n}\r\n\r\n.login-btn {\r\n  width: 100%;\r\n  padding: 15px;\r\n  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\r\n  color: white;\r\n  border: none;\r\n  border-radius: 12px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 50px;\r\n}\r\n\r\n.login-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-lg);\r\n}\r\n\r\n.login-btn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.login-footer {\r\n  text-align: center;\r\n  color: var(--gray-600);\r\n  font-size: 0.8rem;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 20px;\r\n  height: 20px;\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 50%;\r\n  border-top-color: white;\r\n  animation: spin 1s ease-in-out infinite;\r\n}\r\n\r\n/* Admin Sidebar Styles */\r\n.admin-sidebar {\r\n  position: fixed;\r\n  left: 0;\r\n  top: 0;\r\n  width: 280px;\r\n  height: 100vh;\r\n  background: linear-gradient(180deg, #a7f3d0 0%, #86efac 100%);\r\n  color: var(--gray-800);\r\n  z-index: 1000;\r\n  box-shadow: var(--shadow-xl);\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-header {\r\n  padding: 30px 20px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.admin-profile {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.profile-avatar {\r\n  width: 50px;\r\n  height: 50px;\r\n  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.profile-info h3 {\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.profile-info p {\r\n  font-size: 0.85rem;\r\n  color: var(--gray-600);\r\n}\r\n\r\n.sidebar-nav {\r\n  padding: 20px 0;\r\n}\r\n\r\n.sidebar-link {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  color: var(--gray-700);\r\n  text-decoration: none;\r\n  padding: 15px 20px;\r\n  margin: 0 10px;\r\n  border-radius: 12px;\r\n  transition: all 0.3s ease;\r\n  font-weight: 500;\r\n}\r\n\r\n.sidebar-link:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  color: var(--gray-800);\r\n  text-decoration: none;\r\n  transform: translateX(5px);\r\n}\r\n\r\n.sidebar-link.active {\r\n  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\r\n  color: white;\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.sidebar-icon {\r\n  font-size: 18px;\r\n}\r\n\r\n.sidebar-footer {\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 0 20px;\r\n}\r\n\r\n.logout-btn {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  background: rgba(245, 101, 101, 0.1);\r\n  color: #f56565;\r\n  border: 1px solid rgba(245, 101, 101, 0.2);\r\n  padding: 12px 20px;\r\n  border-radius: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-weight: 500;\r\n}\r\n\r\n.logout-btn:hover {\r\n  background: rgba(245, 101, 101, 0.2);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* Main content area */\r\n.main-content {\r\n  margin-left: 280px;\r\n  padding: 30px;\r\n  min-height: 100vh;\r\n  background: var(--gray-100);\r\n}\r\n\r\n/* Card Styles */\r\n.card {\r\n  background: white;\r\n  border-radius: 16px;\r\n  box-shadow: var(--shadow-sm);\r\n  padding: 30px;\r\n  margin-bottom: 30px;\r\n  border: 1px solid var(--gray-200);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.card:hover {\r\n  box-shadow: var(--shadow-md);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.card h1, .card h2, .card h3 {\r\n  color: var(--gray-800);\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card h1 {\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n}\r\n\r\n.card h3 {\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n}\r\n\r\n/* Form Styles */\r\n.form-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-weight: 600;\r\n  color: var(--gray-700);\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.form-control {\r\n  width: 100%;\r\n  padding: 12px 16px;\r\n  border: 2px solid var(--gray-200);\r\n  border-radius: 12px;\r\n  font-size: 14px;\r\n  transition: all 0.3s ease;\r\n  background: white;\r\n}\r\n\r\n.form-control:focus {\r\n  outline: none;\r\n  border-color: var(--primary-color);\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n/* Button Styles */\r\n.btn {\r\n  padding: 12px 24px;\r\n  border: none;\r\n  border-radius: 12px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  text-decoration: none;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  transition: all 0.3s ease;\r\n  min-height: 44px;\r\n}\r\n\r\n.btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.btn-primary {\r\n  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\r\n  color: white;\r\n}\r\n\r\n.btn-success {\r\n  background: var(--success-color);\r\n  color: white;\r\n}\r\n\r\n.btn-danger {\r\n  background: var(--danger-color);\r\n  color: white;\r\n}\r\n\r\n.btn-sm {\r\n  padding: 8px 16px;\r\n  font-size: 12px;\r\n  min-height: 36px;\r\n}\r\n\r\n/* Table Styles */\r\n.table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  margin-top: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.table th,\r\n.table td {\r\n  padding: 16px;\r\n  text-align: left;\r\n  border-bottom: 1px solid var(--gray-200);\r\n}\r\n\r\n.table th {\r\n  background: var(--gray-50);\r\n  font-weight: 600;\r\n  color: var(--gray-700);\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.table tbody tr:hover {\r\n  background: var(--gray-50);\r\n}\r\n\r\n/* Star Rating */\r\n.star-rating {\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n.star {\r\n  cursor: pointer;\r\n  font-size: 20px;\r\n  color: var(--gray-300);\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.star.active {\r\n  color: #fbbf24;\r\n}\r\n\r\n.star:hover {\r\n  color: #fbbf24;\r\n  transform: scale(1.1);\r\n}\r\n\r\n/* Multi Input */\r\n.multi-input {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.multi-input input {\r\n  flex: 1;\r\n}\r\n\r\n.add-btn, .remove-btn {\r\n  width: 36px;\r\n  height: 36px;\r\n  border: none;\r\n  border-radius: 50%;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.add-btn {\r\n  background: var(--success-color);\r\n  color: white;\r\n}\r\n\r\n.remove-btn {\r\n  background: var(--danger-color);\r\n  color: white;\r\n}\r\n\r\n.add-btn:hover, .remove-btn:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n/* Layout */\r\n.row {\r\n  display: flex;\r\n  gap: 30px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.col {\r\n  flex: 1;\r\n  min-width: 300px;\r\n}\r\n\r\n/* Loading */\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 100vh;\r\n  background: var(--gray-100);\r\n}\r\n\r\n.loading-spinner-large {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 4px solid var(--gray-300);\r\n  border-radius: 50%;\r\n  border-top-color: var(--primary-color);\r\n  animation: spin 1s ease-in-out infinite;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* Animations */\r\n@keyframes spin {\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-20px);\r\n  }\r\n}\r\n\r\n/* Responsive */\r\n@media (max-width: 768px) {\r\n  .admin-sidebar {\r\n    width: 100%;\r\n    height: auto;\r\n    position: relative;\r\n  }\r\n  \r\n  .main-content {\r\n    margin-left: 0;\r\n    padding: 20px;\r\n  }\r\n  \r\n  .row {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n  }\r\n  \r\n  .col {\r\n    min-width: auto;\r\n  }\r\n}/* Da\r\nshboard Styles */\r\n.dashboard {\r\n  padding: 0;\r\n  background: var(--gray-100);\r\n  min-height: 100vh;\r\n}\r\n\r\n.dashboard-loading {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 60vh;\r\n  color: var(--gray-600);\r\n}\r\n\r\n.dashboard-header {\r\n  background: white;\r\n  padding: 30px;\r\n  margin-bottom: 30px;\r\n  border-radius: 16px;\r\n  box-shadow: var(--shadow-sm);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n}\r\n\r\n.dashboard-title h1 {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: var(--gray-800);\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.dashboard-title p {\r\n  color: var(--gray-600);\r\n  font-size: 1.1rem;\r\n}\r\n\r\n.dashboard-controls {\r\n  display: flex;\r\n  gap: 15px;\r\n  align-items: center;\r\n}\r\n\r\n.dashboard-select {\r\n  min-width: 150px;\r\n}\r\n\r\n.dashboard-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.spinning {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n/* Metrics Grid */\r\n.metrics-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 25px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.metric-card {\r\n  background: white;\r\n  padding: 25px;\r\n  border-radius: 16px;\r\n  box-shadow: var(--shadow-sm);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid var(--gray-200);\r\n}\r\n\r\n.metric-card:hover {\r\n  box-shadow: var(--shadow-md);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.metric-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.metric-content h3 {\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  color: var(--gray-800);\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.metric-content p {\r\n  color: var(--gray-600);\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n}\r\n\r\n/* Charts Grid */\r\n.charts-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\r\n  gap: 25px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.chart-card {\r\n  background: white;\r\n  border-radius: 16px;\r\n  box-shadow: var(--shadow-sm);\r\n  border: 1px solid var(--gray-200);\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.chart-card:hover {\r\n  box-shadow: var(--shadow-md);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.chart-header {\r\n  padding: 20px 25px;\r\n  border-bottom: 1px solid var(--gray-200);\r\n  background: var(--gray-50);\r\n}\r\n\r\n.chart-header h3 {\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n  color: var(--gray-800);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.chart-container {\r\n  padding: 25px;\r\n  height: 300px;\r\n  position: relative;\r\n}\r\n\r\n/* Tables Grid */\r\n.tables-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));\r\n  gap: 25px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.table-card {\r\n  background: white;\r\n  border-radius: 16px;\r\n  box-shadow: var(--shadow-sm);\r\n  border: 1px solid var(--gray-200);\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.table-card:hover {\r\n  box-shadow: var(--shadow-md);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.table-header {\r\n  padding: 20px 25px;\r\n  border-bottom: 1px solid var(--gray-200);\r\n  background: var(--gray-50);\r\n}\r\n\r\n.table-header h3 {\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n  color: var(--gray-800);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.table-container {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.dashboard-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n}\r\n\r\n.dashboard-table th {\r\n  background: var(--gray-50);\r\n  padding: 15px 20px;\r\n  text-align: left;\r\n  font-weight: 600;\r\n  color: var(--gray-700);\r\n  font-size: 0.9rem;\r\n  border-bottom: 1px solid var(--gray-200);\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 10;\r\n}\r\n\r\n.dashboard-table td {\r\n  padding: 15px 20px;\r\n  border-bottom: 1px solid var(--gray-100);\r\n  color: var(--gray-700);\r\n}\r\n\r\n.dashboard-table tbody tr:hover {\r\n  background: var(--gray-50);\r\n}\r\n\r\n.rating-display {\r\n  color: #fbbf24;\r\n  font-size: 1.1rem;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.percentage-bar {\r\n  position: relative;\r\n  background: var(--gray-200);\r\n  height: 20px;\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  min-width: 100px;\r\n}\r\n\r\n.percentage-fill {\r\n  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));\r\n  height: 100%;\r\n  border-radius: 10px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.percentage-bar span {\r\n  position: absolute;\r\n  right: 8px;\r\n  font-size: 0.8rem;\r\n  font-weight: 600;\r\n  color: var(--gray-700);\r\n  z-index: 1;\r\n}\r\n\r\n/* Top Performers Section */\r\n.performers-section {\r\n  background: white;\r\n  border-radius: 16px;\r\n  padding: 30px;\r\n  box-shadow: var(--shadow-sm);\r\n  border: 1px solid var(--gray-200);\r\n}\r\n\r\n.performers-section h3 {\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  color: var(--gray-800);\r\n  margin-bottom: 25px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.performers-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 20px;\r\n}\r\n\r\n.performer-card {\r\n  background: var(--gray-50);\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  transition: all 0.3s ease;\r\n  border: 2px solid transparent;\r\n}\r\n\r\n.performer-card:hover {\r\n  background: white;\r\n  border-color: var(--primary-color);\r\n  box-shadow: var(--shadow-sm);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.performer-rank {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\r\n  color: white;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: 700;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n.performer-info h4 {\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  color: var(--gray-800);\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.performer-info p {\r\n  color: var(--gray-600);\r\n  font-size: 0.9rem;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.performer-rating {\r\n  color: #fbbf24;\r\n  font-size: 1.2rem;\r\n  letter-spacing: 2px;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1200px) {\r\n  .charts-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .tables-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .dashboard-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .dashboard-controls {\r\n    width: 100%;\r\n    justify-content: flex-start;\r\n    flex-wrap: wrap;\r\n  }\r\n  \r\n  .metrics-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .charts-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .chart-container {\r\n    height: 250px;\r\n  }\r\n  \r\n  .tables-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .performers-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .dashboard-title h1 {\r\n    font-size: 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .dashboard {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .dashboard-header {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .metric-card {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .chart-container {\r\n    padding: 15px;\r\n    height: 200px;\r\n  }\r\n  \r\n  .dashboard-table th,\r\n  .dashboard-table td {\r\n    padding: 10px 15px;\r\n  }\r\n}\r\n\r\n/* Animation for loading states */\r\n.chart-loading {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 200px;\r\n  color: var(--gray-500);\r\n}\r\n\r\n/* Custom scrollbar for table containers */\r\n.table-container::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-track {\r\n  background: var(--gray-100);\r\n  border-radius: 3px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb {\r\n  background: var(--gray-400);\r\n  border-radius: 3px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb:hover {\r\n  background: var(--gray-500);\r\n}/* Birth\r\nday Section Styles */\r\n.birthday-section {\r\n  background: white;\r\n  border-radius: 16px;\r\n  padding: 30px;\r\n  margin-bottom: 30px;\r\n  box-shadow: var(--shadow-sm);\r\n  border: 1px solid var(--gray-200);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.birthday-section:hover {\r\n  box-shadow: var(--shadow-md);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.birthday-header {\r\n  margin-bottom: 25px;\r\n}\r\n\r\n.birthday-header h3 {\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  color: var(--gray-800);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.birthday-content {\r\n  min-height: 120px;\r\n}\r\n\r\n.birthday-list {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n  gap: 20px;\r\n}\r\n\r\n.birthday-card {\r\n  background: linear-gradient(135deg, #fef3c7, #fde68a);\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  transition: all 0.3s ease;\r\n  border: 2px solid transparent;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.birthday-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%);\r\n  background-size: 20px 20px;\r\n  pointer-events: none;\r\n}\r\n\r\n.birthday-card:hover {\r\n  transform: translateY(-3px);\r\n  border-color: #f59e0b;\r\n  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.2);\r\n}\r\n\r\n.birthday-icon {\r\n  font-size: 2.5rem;\r\n  animation: bounce 2s infinite;\r\n}\r\n\r\n@keyframes bounce {\r\n  0%, 20%, 50%, 80%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  40% {\r\n    transform: translateY(-10px);\r\n  }\r\n  60% {\r\n    transform: translateY(-5px);\r\n  }\r\n}\r\n\r\n.birthday-info {\r\n  flex: 1;\r\n}\r\n\r\n.birthday-info h4 {\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n  color: var(--gray-800);\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.birthday-info p {\r\n  color: var(--gray-600);\r\n  font-size: 0.9rem;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.birthday-age {\r\n  background: rgba(245, 158, 11, 0.2);\r\n  color: #92400e;\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n  font-size: 0.8rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.birthday-rating {\r\n  color: #f59e0b;\r\n  font-size: 1.2rem;\r\n  letter-spacing: 2px;\r\n}\r\n\r\n.no-birthdays {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px 20px;\r\n  text-align: center;\r\n  color: var(--gray-500);\r\n}\r\n\r\n.no-birthdays-icon {\r\n  font-size: 3rem;\r\n  margin-bottom: 15px;\r\n  opacity: 0.7;\r\n}\r\n\r\n.no-birthdays p {\r\n  font-size: 1.1rem;\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n  color: var(--gray-600);\r\n}\r\n\r\n.no-birthdays span {\r\n  font-size: 0.9rem;\r\n  color: var(--gray-500);\r\n}\r\n\r\n/* Responsive Design for Birthday Section */\r\n@media (max-width: 768px) {\r\n  .birthday-list {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .birthday-card {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .birthday-icon {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .birthday-info h4 {\r\n    font-size: 1.1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .birthday-section {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .birthday-header h3 {\r\n    font-size: 1.3rem;\r\n  }\r\n  \r\n  .birthday-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .no-birthdays {\r\n    padding: 30px 15px;\r\n  }\r\n}/*\r\n Birthday Pagination Styles */\r\n.birthday-pagination {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 10px;\r\n  margin-top: 25px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid var(--gray-200);\r\n}\r\n\r\n.pagination-btn {\r\n  padding: 8px 16px;\r\n  background: white;\r\n  border: 2px solid var(--gray-300);\r\n  border-radius: 8px;\r\n  color: var(--gray-600);\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  min-width: 80px;\r\n}\r\n\r\n.pagination-btn:hover:not(:disabled) {\r\n  background: var(--primary-color);\r\n  color: white;\r\n  border-color: var(--primary-color);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.pagination-btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n  background: var(--gray-100);\r\n  color: var(--gray-400);\r\n}\r\n\r\n.pagination-numbers {\r\n  display: flex;\r\n  gap: 5px;\r\n}\r\n\r\n.pagination-number {\r\n  width: 36px;\r\n  height: 36px;\r\n  background: white;\r\n  border: 2px solid var(--gray-300);\r\n  border-radius: 8px;\r\n  color: var(--gray-600);\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.pagination-number:hover {\r\n  background: var(--primary-color);\r\n  color: white;\r\n  border-color: var(--primary-color);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.pagination-number.active {\r\n  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\r\n  color: white;\r\n  border-color: var(--primary-color);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n/* Responsive pagination */\r\n@media (max-width: 768px) {\r\n  .birthday-pagination {\r\n    flex-wrap: wrap;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .pagination-btn {\r\n    padding: 6px 12px;\r\n    font-size: 13px;\r\n    min-width: 70px;\r\n  }\r\n  \r\n  .pagination-number {\r\n    width: 32px;\r\n    height: 32px;\r\n    font-size: 13px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .birthday-pagination {\r\n    gap: 5px;\r\n  }\r\n  \r\n  .pagination-numbers {\r\n    gap: 3px;\r\n  }\r\n  \r\n  .pagination-btn {\r\n    padding: 5px 10px;\r\n    font-size: 12px;\r\n    min-width: 60px;\r\n  }\r\n  \r\n  .pagination-number {\r\n    width: 28px;\r\n    height: 28px;\r\n    font-size: 12px;\r\n  }\r\n}", "$rt-namespace: 'Toastify';\n$rt-mobile: 'only screen and (max-width : 480px)' !default;\n\n:root {\n  --toastify-color-light: #fff;\n  --toastify-color-dark: #121212;\n  --toastify-color-info: #3498db;\n  --toastify-color-success: #07bc0c;\n  --toastify-color-warning: #f1c40f;\n  --toastify-color-error: #e74c3c;\n  --toastify-color-transparent: rgba(255, 255, 255, 0.7);\n\n  --toastify-icon-color-info: var(--toastify-color-info);\n  --toastify-icon-color-success: var(--toastify-color-success);\n  --toastify-icon-color-warning: var(--toastify-color-warning);\n  --toastify-icon-color-error: var(--toastify-color-error);\n\n  --toastify-toast-width: 320px;\n  --toastify-toast-background: #fff;\n  --toastify-toast-min-height: 64px;\n  --toastify-toast-max-height: 800px;\n  --toastify-font-family: sans-serif;\n  --toastify-z-index: 9999;\n\n  --toastify-text-color-light: #757575;\n  --toastify-text-color-dark: #fff;\n\n  //Used only for colored theme\n  --toastify-text-color-info: #fff;\n  --toastify-text-color-success: #fff;\n  --toastify-text-color-warning: #fff;\n  --toastify-text-color-error: #fff;\n\n  --toastify-spinner-color: #616161;\n  --toastify-spinner-color-empty-area: #e0e0e0;\n\n  // Used when no type is provided\n  --toastify-color-progress-light: linear-gradient(\n    to right,\n    #4cd964,\n    #5ac8fa,\n    #007aff,\n    #34aadc,\n    #5856d6,\n    #ff2d55\n  );\n  // Used when no type is provided\n  --toastify-color-progress-dark: #bb86fc;\n  --toastify-color-progress-info: var(--toastify-color-info);\n  --toastify-color-progress-success: var(--toastify-color-success);\n  --toastify-color-progress-warning: var(--toastify-color-warning);\n  --toastify-color-progress-error: var(--toastify-color-error);\n}\n", ":root {\n  --toastify-color-light: #fff;\n  --toastify-color-dark: #121212;\n  --toastify-color-info: #3498db;\n  --toastify-color-success: #07bc0c;\n  --toastify-color-warning: #f1c40f;\n  --toastify-color-error: #e74c3c;\n  --toastify-color-transparent: rgba(255, 255, 255, 0.7);\n  --toastify-icon-color-info: var(--toastify-color-info);\n  --toastify-icon-color-success: var(--toastify-color-success);\n  --toastify-icon-color-warning: var(--toastify-color-warning);\n  --toastify-icon-color-error: var(--toastify-color-error);\n  --toastify-toast-width: 320px;\n  --toastify-toast-background: #fff;\n  --toastify-toast-min-height: 64px;\n  --toastify-toast-max-height: 800px;\n  --toastify-font-family: sans-serif;\n  --toastify-z-index: 9999;\n  --toastify-text-color-light: #757575;\n  --toastify-text-color-dark: #fff;\n  --toastify-text-color-info: #fff;\n  --toastify-text-color-success: #fff;\n  --toastify-text-color-warning: #fff;\n  --toastify-text-color-error: #fff;\n  --toastify-spinner-color: #616161;\n  --toastify-spinner-color-empty-area: #e0e0e0;\n  --toastify-color-progress-light: linear-gradient(\n    to right,\n    #4cd964,\n    #5ac8fa,\n    #007aff,\n    #34aadc,\n    #5856d6,\n    #ff2d55\n  );\n  --toastify-color-progress-dark: #bb86fc;\n  --toastify-color-progress-info: var(--toastify-color-info);\n  --toastify-color-progress-success: var(--toastify-color-success);\n  --toastify-color-progress-warning: var(--toastify-color-warning);\n  --toastify-color-progress-error: var(--toastify-color-error);\n}\n\n.Toastify__toast-container {\n  z-index: var(--toastify-z-index);\n  -webkit-transform: translate3d(0, 0, var(--toastify-z-index));\n  position: fixed;\n  padding: 4px;\n  width: var(--toastify-toast-width);\n  box-sizing: border-box;\n  color: #fff;\n}\n.Toastify__toast-container--top-left {\n  top: 1em;\n  left: 1em;\n}\n.Toastify__toast-container--top-center {\n  top: 1em;\n  left: 50%;\n  transform: translateX(-50%);\n}\n.Toastify__toast-container--top-right {\n  top: 1em;\n  right: 1em;\n}\n.Toastify__toast-container--bottom-left {\n  bottom: 1em;\n  left: 1em;\n}\n.Toastify__toast-container--bottom-center {\n  bottom: 1em;\n  left: 50%;\n  transform: translateX(-50%);\n}\n.Toastify__toast-container--bottom-right {\n  bottom: 1em;\n  right: 1em;\n}\n\n@media only screen and (max-width : 480px) {\n  .Toastify__toast-container {\n    width: 100vw;\n    padding: 0;\n    left: 0;\n    margin: 0;\n  }\n  .Toastify__toast-container--top-left, .Toastify__toast-container--top-center, .Toastify__toast-container--top-right {\n    top: 0;\n    transform: translateX(0);\n  }\n  .Toastify__toast-container--bottom-left, .Toastify__toast-container--bottom-center, .Toastify__toast-container--bottom-right {\n    bottom: 0;\n    transform: translateX(0);\n  }\n  .Toastify__toast-container--rtl {\n    right: 0;\n    left: initial;\n  }\n}\n.Toastify__toast {\n  position: relative;\n  min-height: var(--toastify-toast-min-height);\n  box-sizing: border-box;\n  margin-bottom: 1rem;\n  padding: 8px;\n  border-radius: 4px;\n  box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1), 0 2px 15px 0 rgba(0, 0, 0, 0.05);\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-pack: justify;\n      justify-content: space-between;\n  max-height: var(--toastify-toast-max-height);\n  overflow: hidden;\n  font-family: var(--toastify-font-family);\n  cursor: default;\n  direction: ltr;\n  /* webkit only issue #791 */\n  z-index: 0;\n}\n.Toastify__toast--rtl {\n  direction: rtl;\n}\n.Toastify__toast--close-on-click {\n  cursor: pointer;\n}\n.Toastify__toast-body {\n  margin: auto 0;\n  -ms-flex: 1 1 auto;\n      flex: 1 1 auto;\n  padding: 6px;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-align: center;\n      align-items: center;\n}\n.Toastify__toast-body > div:last-child {\n  word-break: break-word;\n  -ms-flex: 1;\n      flex: 1;\n}\n.Toastify__toast-icon {\n  -webkit-margin-end: 10px;\n          margin-inline-end: 10px;\n  width: 20px;\n  -ms-flex-negative: 0;\n      flex-shrink: 0;\n  display: -ms-flexbox;\n  display: flex;\n}\n\n.Toastify--animate {\n  animation-fill-mode: both;\n  animation-duration: 0.7s;\n}\n\n.Toastify--animate-icon {\n  animation-fill-mode: both;\n  animation-duration: 0.3s;\n}\n\n@media only screen and (max-width : 480px) {\n  .Toastify__toast {\n    margin-bottom: 0;\n    border-radius: 0;\n  }\n}\n.Toastify__toast-theme--dark {\n  background: var(--toastify-color-dark);\n  color: var(--toastify-text-color-dark);\n}\n.Toastify__toast-theme--light {\n  background: var(--toastify-color-light);\n  color: var(--toastify-text-color-light);\n}\n.Toastify__toast-theme--colored.Toastify__toast--default {\n  background: var(--toastify-color-light);\n  color: var(--toastify-text-color-light);\n}\n.Toastify__toast-theme--colored.Toastify__toast--info {\n  color: var(--toastify-text-color-info);\n  background: var(--toastify-color-info);\n}\n.Toastify__toast-theme--colored.Toastify__toast--success {\n  color: var(--toastify-text-color-success);\n  background: var(--toastify-color-success);\n}\n.Toastify__toast-theme--colored.Toastify__toast--warning {\n  color: var(--toastify-text-color-warning);\n  background: var(--toastify-color-warning);\n}\n.Toastify__toast-theme--colored.Toastify__toast--error {\n  color: var(--toastify-text-color-error);\n  background: var(--toastify-color-error);\n}\n\n.Toastify__progress-bar-theme--light {\n  background: var(--toastify-color-progress-light);\n}\n.Toastify__progress-bar-theme--dark {\n  background: var(--toastify-color-progress-dark);\n}\n.Toastify__progress-bar--info {\n  background: var(--toastify-color-progress-info);\n}\n.Toastify__progress-bar--success {\n  background: var(--toastify-color-progress-success);\n}\n.Toastify__progress-bar--warning {\n  background: var(--toastify-color-progress-warning);\n}\n.Toastify__progress-bar--error {\n  background: var(--toastify-color-progress-error);\n}\n.Toastify__progress-bar-theme--colored.Toastify__progress-bar--info, .Toastify__progress-bar-theme--colored.Toastify__progress-bar--success, .Toastify__progress-bar-theme--colored.Toastify__progress-bar--warning, .Toastify__progress-bar-theme--colored.Toastify__progress-bar--error {\n  background: var(--toastify-color-transparent);\n}\n\n.Toastify__close-button {\n  color: #fff;\n  background: transparent;\n  outline: none;\n  border: none;\n  padding: 0;\n  cursor: pointer;\n  opacity: 0.7;\n  transition: 0.3s ease;\n  -ms-flex-item-align: start;\n      align-self: flex-start;\n}\n.Toastify__close-button--light {\n  color: #000;\n  opacity: 0.3;\n}\n.Toastify__close-button > svg {\n  fill: currentColor;\n  height: 16px;\n  width: 14px;\n}\n.Toastify__close-button:hover, .Toastify__close-button:focus {\n  opacity: 1;\n}\n\n@keyframes Toastify__trackProgress {\n  0% {\n    transform: scaleX(1);\n  }\n  100% {\n    transform: scaleX(0);\n  }\n}\n.Toastify__progress-bar {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 5px;\n  z-index: var(--toastify-z-index);\n  opacity: 0.7;\n  transform-origin: left;\n}\n.Toastify__progress-bar--animated {\n  animation: Toastify__trackProgress linear 1 forwards;\n}\n.Toastify__progress-bar--controlled {\n  transition: transform 0.2s;\n}\n.Toastify__progress-bar--rtl {\n  right: 0;\n  left: initial;\n  transform-origin: right;\n}\n\n.Toastify__spinner {\n  width: 20px;\n  height: 20px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: var(--toastify-spinner-color-empty-area);\n  border-right-color: var(--toastify-spinner-color);\n  animation: Toastify__spin 0.65s linear infinite;\n}\n\n@keyframes Toastify__bounceInRight {\n  from, 60%, 75%, 90%, to {\n    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  }\n  from {\n    opacity: 0;\n    transform: translate3d(3000px, 0, 0);\n  }\n  60% {\n    opacity: 1;\n    transform: translate3d(-25px, 0, 0);\n  }\n  75% {\n    transform: translate3d(10px, 0, 0);\n  }\n  90% {\n    transform: translate3d(-5px, 0, 0);\n  }\n  to {\n    transform: none;\n  }\n}\n@keyframes Toastify__bounceOutRight {\n  20% {\n    opacity: 1;\n    transform: translate3d(-20px, 0, 0);\n  }\n  to {\n    opacity: 0;\n    transform: translate3d(2000px, 0, 0);\n  }\n}\n@keyframes Toastify__bounceInLeft {\n  from, 60%, 75%, 90%, to {\n    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  }\n  0% {\n    opacity: 0;\n    transform: translate3d(-3000px, 0, 0);\n  }\n  60% {\n    opacity: 1;\n    transform: translate3d(25px, 0, 0);\n  }\n  75% {\n    transform: translate3d(-10px, 0, 0);\n  }\n  90% {\n    transform: translate3d(5px, 0, 0);\n  }\n  to {\n    transform: none;\n  }\n}\n@keyframes Toastify__bounceOutLeft {\n  20% {\n    opacity: 1;\n    transform: translate3d(20px, 0, 0);\n  }\n  to {\n    opacity: 0;\n    transform: translate3d(-2000px, 0, 0);\n  }\n}\n@keyframes Toastify__bounceInUp {\n  from, 60%, 75%, 90%, to {\n    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  }\n  from {\n    opacity: 0;\n    transform: translate3d(0, 3000px, 0);\n  }\n  60% {\n    opacity: 1;\n    transform: translate3d(0, -20px, 0);\n  }\n  75% {\n    transform: translate3d(0, 10px, 0);\n  }\n  90% {\n    transform: translate3d(0, -5px, 0);\n  }\n  to {\n    transform: translate3d(0, 0, 0);\n  }\n}\n@keyframes Toastify__bounceOutUp {\n  20% {\n    transform: translate3d(0, -10px, 0);\n  }\n  40%, 45% {\n    opacity: 1;\n    transform: translate3d(0, 20px, 0);\n  }\n  to {\n    opacity: 0;\n    transform: translate3d(0, -2000px, 0);\n  }\n}\n@keyframes Toastify__bounceInDown {\n  from, 60%, 75%, 90%, to {\n    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  }\n  0% {\n    opacity: 0;\n    transform: translate3d(0, -3000px, 0);\n  }\n  60% {\n    opacity: 1;\n    transform: translate3d(0, 25px, 0);\n  }\n  75% {\n    transform: translate3d(0, -10px, 0);\n  }\n  90% {\n    transform: translate3d(0, 5px, 0);\n  }\n  to {\n    transform: none;\n  }\n}\n@keyframes Toastify__bounceOutDown {\n  20% {\n    transform: translate3d(0, 10px, 0);\n  }\n  40%, 45% {\n    opacity: 1;\n    transform: translate3d(0, -20px, 0);\n  }\n  to {\n    opacity: 0;\n    transform: translate3d(0, 2000px, 0);\n  }\n}\n.Toastify__bounce-enter--top-left, .Toastify__bounce-enter--bottom-left {\n  animation-name: Toastify__bounceInLeft;\n}\n.Toastify__bounce-enter--top-right, .Toastify__bounce-enter--bottom-right {\n  animation-name: Toastify__bounceInRight;\n}\n.Toastify__bounce-enter--top-center {\n  animation-name: Toastify__bounceInDown;\n}\n.Toastify__bounce-enter--bottom-center {\n  animation-name: Toastify__bounceInUp;\n}\n\n.Toastify__bounce-exit--top-left, .Toastify__bounce-exit--bottom-left {\n  animation-name: Toastify__bounceOutLeft;\n}\n.Toastify__bounce-exit--top-right, .Toastify__bounce-exit--bottom-right {\n  animation-name: Toastify__bounceOutRight;\n}\n.Toastify__bounce-exit--top-center {\n  animation-name: Toastify__bounceOutUp;\n}\n.Toastify__bounce-exit--bottom-center {\n  animation-name: Toastify__bounceOutDown;\n}\n\n@keyframes Toastify__zoomIn {\n  from {\n    opacity: 0;\n    transform: scale3d(0.3, 0.3, 0.3);\n  }\n  50% {\n    opacity: 1;\n  }\n}\n@keyframes Toastify__zoomOut {\n  from {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0;\n    transform: scale3d(0.3, 0.3, 0.3);\n  }\n  to {\n    opacity: 0;\n  }\n}\n.Toastify__zoom-enter {\n  animation-name: Toastify__zoomIn;\n}\n\n.Toastify__zoom-exit {\n  animation-name: Toastify__zoomOut;\n}\n\n@keyframes Toastify__flipIn {\n  from {\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    animation-timing-function: ease-in;\n    opacity: 0;\n  }\n  40% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    animation-timing-function: ease-in;\n  }\n  60% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n  80% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n  to {\n    transform: perspective(400px);\n  }\n}\n@keyframes Toastify__flipOut {\n  from {\n    transform: perspective(400px);\n  }\n  30% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    opacity: 1;\n  }\n  to {\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    opacity: 0;\n  }\n}\n.Toastify__flip-enter {\n  animation-name: Toastify__flipIn;\n}\n\n.Toastify__flip-exit {\n  animation-name: Toastify__flipOut;\n}\n\n@keyframes Toastify__slideInRight {\n  from {\n    transform: translate3d(110%, 0, 0);\n    visibility: visible;\n  }\n  to {\n    transform: translate3d(0, 0, 0);\n  }\n}\n@keyframes Toastify__slideInLeft {\n  from {\n    transform: translate3d(-110%, 0, 0);\n    visibility: visible;\n  }\n  to {\n    transform: translate3d(0, 0, 0);\n  }\n}\n@keyframes Toastify__slideInUp {\n  from {\n    transform: translate3d(0, 110%, 0);\n    visibility: visible;\n  }\n  to {\n    transform: translate3d(0, 0, 0);\n  }\n}\n@keyframes Toastify__slideInDown {\n  from {\n    transform: translate3d(0, -110%, 0);\n    visibility: visible;\n  }\n  to {\n    transform: translate3d(0, 0, 0);\n  }\n}\n@keyframes Toastify__slideOutRight {\n  from {\n    transform: translate3d(0, 0, 0);\n  }\n  to {\n    visibility: hidden;\n    transform: translate3d(110%, 0, 0);\n  }\n}\n@keyframes Toastify__slideOutLeft {\n  from {\n    transform: translate3d(0, 0, 0);\n  }\n  to {\n    visibility: hidden;\n    transform: translate3d(-110%, 0, 0);\n  }\n}\n@keyframes Toastify__slideOutDown {\n  from {\n    transform: translate3d(0, 0, 0);\n  }\n  to {\n    visibility: hidden;\n    transform: translate3d(0, 500px, 0);\n  }\n}\n@keyframes Toastify__slideOutUp {\n  from {\n    transform: translate3d(0, 0, 0);\n  }\n  to {\n    visibility: hidden;\n    transform: translate3d(0, -500px, 0);\n  }\n}\n.Toastify__slide-enter--top-left, .Toastify__slide-enter--bottom-left {\n  animation-name: Toastify__slideInLeft;\n}\n.Toastify__slide-enter--top-right, .Toastify__slide-enter--bottom-right {\n  animation-name: Toastify__slideInRight;\n}\n.Toastify__slide-enter--top-center {\n  animation-name: Toastify__slideInDown;\n}\n.Toastify__slide-enter--bottom-center {\n  animation-name: Toastify__slideInUp;\n}\n\n.Toastify__slide-exit--top-left, .Toastify__slide-exit--bottom-left {\n  animation-name: Toastify__slideOutLeft;\n}\n.Toastify__slide-exit--top-right, .Toastify__slide-exit--bottom-right {\n  animation-name: Toastify__slideOutRight;\n}\n.Toastify__slide-exit--top-center {\n  animation-name: Toastify__slideOutUp;\n}\n.Toastify__slide-exit--bottom-center {\n  animation-name: Toastify__slideOutDown;\n}\n\n@keyframes Toastify__spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/*# sourceMappingURL=ReactToastify.css.map */", ".#{$rt-namespace}__toast-container {\n  z-index: var(--toastify-z-index);\n  -webkit-transform: translate3d(0, 0, var(--toastify-z-index));\n  position: fixed;\n  padding: 4px;\n  width: var(--toastify-toast-width);\n  box-sizing: border-box;\n  color: #fff;\n  &--top-left {\n    top: 1em;\n    left: 1em;\n  }\n  &--top-center {\n    top: 1em;\n    left: 50%;\n    transform: translateX(-50%);\n  }\n  &--top-right {\n    top: 1em;\n    right: 1em;\n  }\n  &--bottom-left {\n    bottom: 1em;\n    left: 1em;\n  }\n  &--bottom-center {\n    bottom: 1em;\n    left: 50%;\n    transform: translateX(-50%);\n  }\n  &--bottom-right {\n    bottom: 1em;\n    right: 1em;\n  }\n}\n\n@media #{$rt-mobile} {\n  .#{$rt-namespace}__toast-container {\n    width: 100vw;\n    padding: 0;\n    left: 0;\n    margin: 0;\n    &--top-left,\n    &--top-center,\n    &--top-right {\n      top: 0;\n      transform: translateX(0);\n    }\n    &--bottom-left,\n    &--bottom-center,\n    &--bottom-right {\n      bottom: 0;\n      transform: translateX(0);\n    }\n    &--rtl {\n      right: 0;\n      left: initial;\n    }\n  }\n}\n", ".#{$rt-namespace}__toast {\n  position: relative;\n  min-height: var(--toastify-toast-min-height);\n  box-sizing: border-box;\n  margin-bottom: 1rem;\n  padding: 8px;\n  border-radius: 4px;\n  box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1), 0 2px 15px 0 rgba(0, 0, 0, 0.05);\n  display: flex;\n  justify-content: space-between;\n  max-height: var(--toastify-toast-max-height);\n  overflow: hidden;\n  font-family: var(--toastify-font-family);\n  cursor: default;\n  direction: ltr;\n  /* webkit only issue #791 */\n  z-index: 0;\n  &--rtl {\n    direction: rtl;\n  }\n  &--close-on-click {\n    cursor: pointer;\n  }\n  &-body {\n    margin: auto 0;\n    flex: 1 1 auto;\n    padding: 6px;\n    display: flex;\n    align-items: center;\n    & > div:last-child {\n      word-break: break-word;\n      flex: 1;\n    }\n  }\n  &-icon {\n    margin-inline-end: 10px;\n    width: 20px;\n    flex-shrink: 0;\n    display: flex;\n  }\n}\n\n.#{$rt-namespace}--animate {\n  animation-fill-mode: both;\n  animation-duration: 0.7s;\n}\n\n.#{$rt-namespace}--animate-icon {\n  animation-fill-mode: both;\n  animation-duration: 0.3s;\n}\n\n@media #{$rt-mobile} {\n  .#{$rt-namespace}__toast {\n    margin-bottom: 0;\n    border-radius: 0;\n  }\n}\n", ".#{$rt-namespace}__toast {\n  &-theme--dark {\n    background: var(--toastify-color-dark);\n    color: var(--toastify-text-color-dark);\n  }\n  &-theme--light {\n    background: var(--toastify-color-light);\n    color: var(--toastify-text-color-light);\n  }\n  &-theme--colored#{&}--default {\n    background: var(--toastify-color-light);\n    color: var(--toastify-text-color-light);\n  }\n  &-theme--colored#{&}--info {\n    color: var(--toastify-text-color-info);\n    background: var(--toastify-color-info);\n  }\n  &-theme--colored#{&}--success {\n    color: var(--toastify-text-color-success);\n    background: var(--toastify-color-success);\n  }\n  &-theme--colored#{&}--warning {\n    color: var(--toastify-text-color-warning);\n    background: var(--toastify-color-warning);\n  }\n  &-theme--colored#{&}--error {\n    color: var(--toastify-text-color-error);\n    background: var(--toastify-color-error);\n  }\n}\n\n.#{$rt-namespace}__progress-bar {\n  &-theme--light {\n    background: var(--toastify-color-progress-light);\n  }\n  &-theme--dark {\n    background: var(--toastify-color-progress-dark);\n  }\n  &--info {\n    background: var(--toastify-color-progress-info);\n  }\n  &--success {\n    background: var(--toastify-color-progress-success);\n  }\n  &--warning {\n    background: var(--toastify-color-progress-warning);\n  }\n  &--error {\n    background: var(--toastify-color-progress-error);\n  }\n  &-theme--colored#{&}--info,\n  &-theme--colored#{&}--success,\n  &-theme--colored#{&}--warning,\n  &-theme--colored#{&}--error {\n    background: var(--toastify-color-transparent);\n  }\n}\n", ".#{$rt-namespace}__close-button {\n  color: #fff;\n  background: transparent;\n  outline: none;\n  border: none;\n  padding: 0;\n  cursor: pointer;\n  opacity: 0.7;\n  transition: 0.3s ease;\n  align-self: flex-start;\n\n  &--light {\n    color: #000;\n    opacity: 0.3;\n  }\n\n  & > svg {\n    fill: currentColor;\n    height: 16px;\n    width: 14px;\n  }\n\n  &:hover,\n  &:focus {\n    opacity: 1;\n  }\n}\n", "@keyframes #{$rt-namespace}__trackProgress {\n  0% {\n    transform: scaleX(1);\n  }\n  100% {\n    transform: scaleX(0);\n  }\n}\n\n.#{$rt-namespace}__progress-bar {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 5px;\n  z-index: var(--toastify-z-index);\n  opacity: 0.7;\n  transform-origin: left;\n\n  &--animated {\n    animation: #{$rt-namespace}__trackProgress linear 1 forwards;\n  }\n\n  &--controlled {\n    transition: transform 0.2s;\n  }\n\n  &--rtl {\n    right: 0;\n    left: initial;\n    transform-origin: right;\n  }\n}\n", ".#{$rt-namespace}__spinner {\n  width: 20px;\n  height: 20px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: var(--toastify-spinner-color-empty-area);\n  border-right-color: var(--toastify-spinner-color);\n  animation: #{$rt-namespace}__spin 0.65s linear infinite;\n}\n", "@mixin timing-function {\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n}\n\n@keyframes #{$rt-namespace}__bounceInRight {\n  from,\n  60%,\n  75%,\n  90%,\n  to {\n    @include timing-function;\n  }\n  from {\n    opacity: 0;\n    transform: translate3d(3000px, 0, 0);\n  }\n  60% {\n    opacity: 1;\n    transform: translate3d(-25px, 0, 0);\n  }\n  75% {\n    transform: translate3d(10px, 0, 0);\n  }\n  90% {\n    transform: translate3d(-5px, 0, 0);\n  }\n  to {\n    transform: none;\n  }\n}\n\n@keyframes #{$rt-namespace}__bounceOutRight {\n  20% {\n    opacity: 1;\n    transform: translate3d(-20px, 0, 0);\n  }\n  to {\n    opacity: 0;\n    transform: translate3d(2000px, 0, 0);\n  }\n}\n\n@keyframes #{$rt-namespace}__bounceInLeft {\n  from,\n  60%,\n  75%,\n  90%,\n  to {\n    @include timing-function;\n  }\n  0% {\n    opacity: 0;\n    transform: translate3d(-3000px, 0, 0);\n  }\n  60% {\n    opacity: 1;\n    transform: translate3d(25px, 0, 0);\n  }\n  75% {\n    transform: translate3d(-10px, 0, 0);\n  }\n  90% {\n    transform: translate3d(5px, 0, 0);\n  }\n  to {\n    transform: none;\n  }\n}\n\n@keyframes #{$rt-namespace}__bounceOutLeft {\n  20% {\n    opacity: 1;\n    transform: translate3d(20px, 0, 0);\n  }\n  to {\n    opacity: 0;\n    transform: translate3d(-2000px, 0, 0);\n  }\n}\n\n@keyframes #{$rt-namespace}__bounceInUp {\n  from,\n  60%,\n  75%,\n  90%,\n  to {\n    @include timing-function;\n  }\n  from {\n    opacity: 0;\n    transform: translate3d(0, 3000px, 0);\n  }\n  60% {\n    opacity: 1;\n    transform: translate3d(0, -20px, 0);\n  }\n  75% {\n    transform: translate3d(0, 10px, 0);\n  }\n  90% {\n    transform: translate3d(0, -5px, 0);\n  }\n  to {\n    transform: translate3d(0, 0, 0);\n  }\n}\n\n@keyframes #{$rt-namespace}__bounceOutUp {\n  20% {\n    transform: translate3d(0, -10px, 0);\n  }\n  40%,\n  45% {\n    opacity: 1;\n    transform: translate3d(0, 20px, 0);\n  }\n  to {\n    opacity: 0;\n    transform: translate3d(0, -2000px, 0);\n  }\n}\n\n@keyframes #{$rt-namespace}__bounceInDown {\n  from,\n  60%,\n  75%,\n  90%,\n  to {\n    @include timing-function;\n  }\n  0% {\n    opacity: 0;\n    transform: translate3d(0, -3000px, 0);\n  }\n  60% {\n    opacity: 1;\n    transform: translate3d(0, 25px, 0);\n  }\n  75% {\n    transform: translate3d(0, -10px, 0);\n  }\n  90% {\n    transform: translate3d(0, 5px, 0);\n  }\n  to {\n    transform: none;\n  }\n}\n\n@keyframes #{$rt-namespace}__bounceOutDown {\n  20% {\n    transform: translate3d(0, 10px, 0);\n  }\n  40%,\n  45% {\n    opacity: 1;\n    transform: translate3d(0, -20px, 0);\n  }\n  to {\n    opacity: 0;\n    transform: translate3d(0, 2000px, 0);\n  }\n}\n\n.#{$rt-namespace}__bounce-enter {\n  &--top-left,\n  &--bottom-left {\n    animation-name: #{$rt-namespace}__bounceInLeft;\n  }\n  &--top-right,\n  &--bottom-right {\n    animation-name: #{$rt-namespace}__bounceInRight;\n  }\n  &--top-center {\n    animation-name: #{$rt-namespace}__bounceInDown;\n  }\n  &--bottom-center {\n    animation-name: #{$rt-namespace}__bounceInUp;\n  }\n}\n\n.#{$rt-namespace}__bounce-exit {\n  &--top-left,\n  &--bottom-left {\n    animation-name: #{$rt-namespace}__bounceOutLeft;\n  }\n  &--top-right,\n  &--bottom-right {\n    animation-name: #{$rt-namespace}__bounceOutRight;\n  }\n  &--top-center {\n    animation-name: #{$rt-namespace}__bounceOutUp;\n  }\n  &--bottom-center {\n    animation-name: #{$rt-namespace}__bounceOutDown;\n  }\n}\n", "@keyframes #{$rt-namespace}__zoomIn {\n  from {\n    opacity: 0;\n    transform: scale3d(0.3, 0.3, 0.3);\n  }\n  50% {\n    opacity: 1;\n  }\n}\n\n@keyframes #{$rt-namespace}__zoomOut {\n  from {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0;\n    transform: scale3d(0.3, 0.3, 0.3);\n  }\n  to {\n    opacity: 0;\n  }\n}\n\n.#{$rt-namespace}__zoom-enter {\n  animation-name: #{$rt-namespace}__zoomIn;\n}\n\n.#{$rt-namespace}__zoom-exit {\n  animation-name: #{$rt-namespace}__zoomOut;\n}\n", "@keyframes #{$rt-namespace}__flipIn {\n  from {\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    animation-timing-function: ease-in;\n    opacity: 0;\n  }\n  40% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    animation-timing-function: ease-in;\n  }\n  60% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n  80% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n  to {\n    transform: perspective(400px);\n  }\n}\n\n@keyframes #{$rt-namespace}__flipOut {\n  from {\n    transform: perspective(400px);\n  }\n  30% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    opacity: 1;\n  }\n  to {\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    opacity: 0;\n  }\n}\n\n.#{$rt-namespace}__flip-enter {\n  animation-name: #{$rt-namespace}__flipIn;\n}\n\n.#{$rt-namespace}__flip-exit {\n  animation-name: #{$rt-namespace}__flipOut;\n}\n", "@mixin transform {\n  transform: translate3d(0, 0, 0);\n}\n\n@keyframes #{$rt-namespace}__slideInRight {\n  from {\n    transform: translate3d(110%, 0, 0);\n    visibility: visible;\n  }\n  to {\n    @include transform;\n  }\n}\n\n@keyframes #{$rt-namespace}__slideInLeft {\n  from {\n    transform: translate3d(-110%, 0, 0);\n    visibility: visible;\n  }\n  to {\n    @include transform;\n  }\n}\n\n@keyframes #{$rt-namespace}__slideInUp {\n  from {\n    transform: translate3d(0, 110%, 0);\n    visibility: visible;\n  }\n  to {\n    @include transform;\n  }\n}\n\n@keyframes #{$rt-namespace}__slideInDown {\n  from {\n    transform: translate3d(0, -110%, 0);\n    visibility: visible;\n  }\n  to {\n    @include transform;\n  }\n}\n\n@keyframes #{$rt-namespace}__slideOutRight {\n  from {\n    @include transform;\n  }\n  to {\n    visibility: hidden;\n    transform: translate3d(110%, 0, 0);\n  }\n}\n\n@keyframes #{$rt-namespace}__slideOutLeft {\n  from {\n    @include transform;\n  }\n  to {\n    visibility: hidden;\n    transform: translate3d(-110%, 0, 0);\n  }\n}\n\n@keyframes #{$rt-namespace}__slideOutDown {\n  from {\n    @include transform;\n  }\n  to {\n    visibility: hidden;\n    transform: translate3d(0, 500px, 0);\n  }\n}\n\n@keyframes #{$rt-namespace}__slideOutUp {\n  from {\n    @include transform;\n  }\n  to {\n    visibility: hidden;\n    transform: translate3d(0, -500px, 0);\n  }\n}\n\n.#{$rt-namespace}__slide-enter {\n  &--top-left,\n  &--bottom-left {\n    animation-name: #{$rt-namespace}__slideInLeft;\n  }\n  &--top-right,\n  &--bottom-right {\n    animation-name: #{$rt-namespace}__slideInRight;\n  }\n  &--top-center {\n    animation-name: #{$rt-namespace}__slideInDown;\n  }\n  &--bottom-center {\n    animation-name: #{$rt-namespace}__slideInUp;\n  }\n}\n\n.#{$rt-namespace}__slide-exit {\n  &--top-left,\n  &--bottom-left {\n    animation-name: #{$rt-namespace}__slideOutLeft;\n  }\n  &--top-right,\n  &--bottom-right {\n    animation-name: #{$rt-namespace}__slideOutRight;\n  }\n  &--top-center {\n    animation-name: #{$rt-namespace}__slideOutUp;\n  }\n  &--bottom-center {\n    animation-name: #{$rt-namespace}__slideOutDown;\n  }\n}\n", "@keyframes #{$rt-namespace}__spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n", "/* Single Row Pagination Component Styles */\r\n.pagination-wrapper-single {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 1rem 0;\r\n  margin: 1rem 0;\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  background: #ffffff;\r\n  padding: 0.75rem 1.5rem;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.pagination-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.entries-text {\r\n  font-size: 0.875rem;\r\n  color: #6c757d;\r\n  font-weight: 500;\r\n  white-space: nowrap;\r\n}\r\n\r\n.pagination-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.25rem;\r\n}\r\n\r\n.pagination-pages {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.25rem;\r\n  margin: 0 0.5rem;\r\n}\r\n\r\n.pagination-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-width: 36px;\r\n  height: 36px;\r\n  padding: 0.5rem;\r\n  border: 1px solid #dee2e6;\r\n  background: #ffffff;\r\n  color: #495057;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease-in-out;\r\n  user-select: none;\r\n  text-decoration: none;\r\n}\r\n\r\n.pagination-btn:hover:not(.disabled) {\r\n  background: #f8f9fa;\r\n  border-color: #adb5bd;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.pagination-btn:focus {\r\n  outline: none;\r\n  box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);\r\n}\r\n\r\n.pagination-btn.active {\r\n  background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);\r\n  border-color: #0a58ca;\r\n  color: #ffffff;\r\n  font-weight: 600;\r\n  box-shadow: 0 2px 6px rgba(13, 110, 253, 0.3);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.pagination-btn.disabled {\r\n  background: #f8f9fa;\r\n  border-color: #e9ecef;\r\n  color: #adb5bd;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n.pagination-btn.dots {\r\n  border: none;\r\n  background: transparent;\r\n  cursor: default;\r\n  color: #adb5bd;\r\n}\r\n\r\n.pagination-btn.dots:hover {\r\n  background: transparent;\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n.pagination-prev,\r\n.pagination-next {\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  min-width: 40px;\r\n}\r\n\r\n.pagination-prev {\r\n  margin-right: 0.25rem;\r\n}\r\n\r\n.pagination-next {\r\n  margin-left: 0.25rem;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .pagination-container {\r\n    flex-direction: column;\r\n    gap: 0.75rem;\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .pagination-info {\r\n    order: 2;\r\n  }\r\n  \r\n  .pagination-controls {\r\n    order: 1;\r\n  }\r\n  \r\n  .entries-text {\r\n    font-size: 0.75rem;\r\n  }\r\n  \r\n  .pagination-btn {\r\n    min-width: 32px;\r\n    height: 32px;\r\n    font-size: 0.75rem;\r\n  }\r\n  \r\n  .pagination-prev,\r\n  .pagination-next {\r\n    font-size: 1rem;\r\n    min-width: 36px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .pagination-wrapper-single {\r\n    padding: 0.5rem 0;\r\n    margin: 0.5rem 0;\r\n  }\r\n  \r\n  .pagination-container {\r\n    padding: 0.75rem;\r\n    gap: 0.5rem;\r\n  }\r\n  \r\n  .pagination-controls {\r\n    gap: 0.125rem;\r\n  }\r\n  \r\n  .pagination-pages {\r\n    gap: 0.125rem;\r\n    margin: 0 0.25rem;\r\n  }\r\n  \r\n  .pagination-btn {\r\n    min-width: 28px;\r\n    height: 28px;\r\n    font-size: 0.75rem;\r\n  }\r\n}\r\n\r\n/* Animation */\r\n.pagination-btn.active {\r\n  animation: activePagePulse 0.3s ease-out;\r\n}\r\n\r\n@keyframes activePagePulse {\r\n  0% {\r\n    transform: translateY(-1px) scale(1);\r\n  }\r\n  50% {\r\n    transform: translateY(-2px) scale(1.05);\r\n  }\r\n  100% {\r\n    transform: translateY(-1px) scale(1);\r\n  }\r\n}\r\n\r\n/* Dark mode support */\r\n@media (prefers-color-scheme: dark) {\r\n  .pagination-container {\r\n    background: #2d3748;\r\n    border-color: #4a5568;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n  }\r\n  \r\n  .entries-text {\r\n    color: #a0aec0;\r\n  }\r\n  \r\n  .pagination-btn {\r\n    background: #4a5568;\r\n    border-color: #718096;\r\n    color: #e2e8f0;\r\n  }\r\n  \r\n  .pagination-btn:hover:not(.disabled) {\r\n    background: #718096;\r\n    border-color: #a0aec0;\r\n  }\r\n  \r\n  .pagination-btn.disabled {\r\n    background: #2d3748;\r\n    border-color: #4a5568;\r\n    color: #718096;\r\n  }\r\n}", ".modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  width: 90%;\n  max-width: 600px;\n  max-height: 90vh;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e1e5e9;\n  background-color: #f8f9fa;\n}\n\n.modal-header h3 {\n  margin: 0;\n  color: #495057;\n}\n\n.close-button {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  color: #6c757d;\n  padding: 0;\n  width: 2rem;\n  height: 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 4px;\n}\n\n.close-button:hover {\n  background-color: #e9ecef;\n  color: #495057;\n}\n\n.modal-body {\n  flex: 1;\n  overflow-y: auto;\n  padding: 1.5rem;\n}\n\n.modal-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n  padding: 1.5rem;\n  border-top: 1px solid #e1e5e9;\n  background-color: #f8f9fa;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n  color: #495057;\n}\n\n.form-group input,\n.form-group textarea,\n.form-group select {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 1rem;\n}\n\n.form-group input:focus,\n.form-group textarea:focus,\n.form-group select:focus {\n  outline: none;\n  border-color: #80bdff;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.form-group input[type=\"checkbox\"] {\n  width: auto;\n  margin-right: 0.5rem;\n}\n\n.config-section {\n  margin-top: 1.5rem;\n  padding-top: 1.5rem;\n  border-top: 1px solid #e1e5e9;\n}\n\n.config-section h4 {\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1rem;\n}\n\n.options-list {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.option-item {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n\n.option-item input {\n  flex: 1;\n}\n\n.btn-remove {\n  background-color: #dc3545;\n  color: white;\n  border: none;\n  padding: 0.5rem 0.75rem;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 0.875rem;\n}\n\n.btn-remove:hover {\n  background-color: #c82333;\n}\n\n.btn-add {\n  background-color: #28a745;\n  color: white;\n  border: none;\n  padding: 0.5rem 1rem;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 0.875rem;\n  margin-top: 0.5rem;\n}\n\n.btn-add:hover {\n  background-color: #218838;\n}\n\n.btn {\n  padding: 0.5rem 1rem;\n  border: 1px solid transparent;\n  border-radius: 4px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  text-decoration: none;\n  display: inline-block;\n  transition: all 0.2s ease;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: white;\n}\n\n.btn-primary:hover {\n  background-color: #0056b3;\n  border-color: #0056b3;\n}\n\n.btn-outline {\n  background-color: transparent;\n  border-color: #6c757d;\n  color: #6c757d;\n}\n\n.btn-outline:hover {\n  background-color: #6c757d;\n  color: white;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .modal-content {\n    width: 95%;\n    margin: 1rem;\n  }\n  \n  .modal-header,\n  .modal-body,\n  .modal-footer {\n    padding: 1rem;\n  }\n  \n  .option-item {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .modal-footer {\n    flex-direction: column;\n  }\n}\n", ".form-field {\n  margin-bottom: 1.5rem;\n}\n\n.form-field.checkbox-field {\n  margin-bottom: 1rem;\n}\n\n.form-label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n  color: #495057;\n  font-size: 0.875rem;\n}\n\n.required-indicator {\n  color: #dc3545;\n  margin-left: 0.25rem;\n}\n\n.form-input {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 1rem;\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n  background-color: white;\n}\n\n.form-input:focus {\n  outline: none;\n  border-color: #80bdff;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.form-input:disabled {\n  background-color: #e9ecef;\n  opacity: 1;\n}\n\n.form-input.error {\n  border-color: #dc3545;\n}\n\n.form-input.error:focus {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n/* Textarea specific styles */\ntextarea.form-input {\n  resize: vertical;\n  min-height: 80px;\n}\n\n/* Select specific styles */\nselect.form-input {\n  cursor: pointer;\n}\n\nselect.form-input[multiple] {\n  height: auto;\n  min-height: 120px;\n}\n\nselect.form-input[multiple] option {\n  padding: 0.5rem;\n}\n\nselect.form-input[multiple] option:checked {\n  background-color: #007bff;\n  color: white;\n}\n\n/* Checkbox specific styles */\n.checkbox-wrapper {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.form-checkbox {\n  width: 1.2rem !important;\n  height: 1.2rem;\n  cursor: pointer;\n  accent-color: #007bff;\n}\n\n.checkbox-label {\n  font-weight: 600;\n  color: #495057;\n  cursor: pointer;\n  margin: 0;\n  font-size: 0.875rem;\n}\n\n.checkbox-label:hover {\n  color: #007bff;\n}\n\n/* Help text */\n.help-text {\n  margin-top: 0.25rem;\n  font-size: 0.875rem;\n  color: #6c757d;\n  font-style: italic;\n}\n\n/* Error message */\n.error-message {\n  margin-top: 0.25rem;\n  font-size: 0.875rem;\n  color: #dc3545;\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n.error-message::before {\n  content: \"⚠\";\n  font-size: 1rem;\n}\n\n/* Number input specific styles */\ninput[type=\"number\"].form-input {\n  -moz-appearance: textfield;\n}\n\ninput[type=\"number\"].form-input::-webkit-outer-spin-button,\ninput[type=\"number\"].form-input::-webkit-inner-spin-button {\n  -webkit-appearance: none;\n  margin: 0;\n}\n\n/* Date input specific styles */\ninput[type=\"date\"].form-input {\n  cursor: pointer;\n}\n\ninput[type=\"date\"].form-input::-webkit-calendar-picker-indicator {\n  cursor: pointer;\n  padding: 0.25rem;\n  border-radius: 3px;\n}\n\ninput[type=\"date\"].form-input::-webkit-calendar-picker-indicator:hover {\n  background-color: #f8f9fa;\n}\n\n/* URL and Email input styles */\ninput[type=\"url\"].form-input,\ninput[type=\"email\"].form-input {\n  font-family: monospace;\n}\n\n/* Tel input styles */\ninput[type=\"tel\"].form-input {\n  letter-spacing: 0.5px;\n}\n\n/* Array input styles */\n.form-field input[placeholder*=\"comma\"] {\n  font-family: monospace;\n  font-size: 0.9rem;\n}\n\n/* Focus states for better accessibility */\n.form-input:focus-visible {\n  outline: 2px solid #007bff;\n  outline-offset: 2px;\n}\n\n.form-checkbox:focus-visible {\n  outline: 2px solid #007bff;\n  outline-offset: 2px;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .form-input {\n    padding: 0.875rem;\n    font-size: 1.1rem;\n  }\n  \n  .form-checkbox {\n    width: 1.4rem !important;\n    height: 1.4rem;\n  }\n  \n  .checkbox-label {\n    font-size: 1rem;\n  }\n  \n  select.form-input[multiple] {\n    min-height: 150px;\n  }\n}\n\n/* High contrast mode support */\n@media (prefers-contrast: high) {\n  .form-input {\n    border-width: 2px;\n  }\n  \n  .form-input:focus {\n    border-width: 3px;\n  }\n  \n  .error-message {\n    font-weight: bold;\n  }\n}\n\n/* Reduced motion support */\n@media (prefers-reduced-motion: reduce) {\n  .form-input {\n    transition: none;\n  }\n}\n", ".form-preview-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  padding: 1rem;\n}\n\n.form-preview-modal {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  width: 100%;\n  max-width: 1200px;\n  max-height: 90vh;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e1e5e9;\n  background-color: #f8f9fa;\n}\n\n.preview-title h2 {\n  margin: 0 0 0.5rem 0;\n  color: #495057;\n}\n\n.preview-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.form-name {\n  font-weight: 600;\n  color: #007bff;\n  font-size: 1rem;\n}\n\n.field-stats {\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n\n.stat {\n  font-size: 0.875rem;\n  color: #6c757d;\n  background-color: #e9ecef;\n  padding: 0.25rem 0.5rem;\n  border-radius: 3px;\n}\n\n.close-button {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  color: #6c757d;\n  padding: 0;\n  width: 2rem;\n  height: 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 4px;\n}\n\n.close-button:hover {\n  background-color: #e9ecef;\n  color: #495057;\n}\n\n.preview-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 1.5rem;\n}\n\n.preview-notice {\n  background-color: #e7f3ff;\n  border: 1px solid #b3d7ff;\n  border-radius: 4px;\n  padding: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.preview-notice p {\n  margin: 0;\n  color: #0056b3;\n  font-size: 0.875rem;\n}\n\n.preview-form {\n  margin-bottom: 2rem;\n}\n\n.preview-section {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\n.preview-section h3 {\n  margin: 0 0 1.5rem 0;\n  color: #495057;\n  font-size: 1.25rem;\n  padding-bottom: 0.5rem;\n  border-bottom: 2px solid #007bff;\n  display: inline-block;\n}\n\n.preview-fields {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n}\n\n.field-summary {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\n.field-summary h4 {\n  margin: 0 0 1rem 0;\n  color: #495057;\n}\n\n.summary-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n\n.summary-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem;\n  background-color: white;\n  border-radius: 4px;\n  border: 1px solid #e1e5e9;\n}\n\n.summary-label {\n  font-weight: 600;\n  color: #495057;\n  font-size: 0.875rem;\n}\n\n.summary-value {\n  font-weight: 700;\n  color: #007bff;\n  font-size: 1rem;\n}\n\n.field-list {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.field-list h4 {\n  margin: 0 0 1rem 0;\n  color: #495057;\n}\n\n.field-items {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 0.75rem;\n}\n\n.field-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem;\n  background-color: white;\n  border: 1px solid #e1e5e9;\n  border-radius: 4px;\n  transition: all 0.2s ease;\n}\n\n.field-item.visible {\n  border-left: 4px solid #28a745;\n}\n\n.field-item.hidden {\n  border-left: 4px solid #6c757d;\n  opacity: 0.7;\n}\n\n.field-name {\n  font-weight: 600;\n  color: #495057;\n}\n\n.field-meta {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n\n.field-type {\n  font-size: 0.75rem;\n  color: #6c757d;\n  background-color: #e9ecef;\n  padding: 0.125rem 0.375rem;\n  border-radius: 3px;\n}\n\n.required-badge {\n  background-color: #dc3545;\n  color: white;\n  padding: 0.125rem 0.375rem;\n  border-radius: 3px;\n  font-size: 0.75rem;\n  font-weight: 600;\n}\n\n.conditional-badge {\n  background-color: #ffc107;\n  color: #212529;\n  padding: 0.125rem 0.375rem;\n  border-radius: 3px;\n  font-size: 0.75rem;\n  font-weight: 600;\n}\n\n.hidden-badge {\n  background-color: #6c757d;\n  color: white;\n  padding: 0.125rem 0.375rem;\n  border-radius: 3px;\n  font-size: 0.75rem;\n  font-weight: 600;\n}\n\n.preview-footer {\n  display: flex;\n  justify-content: center;\n  padding: 1.5rem;\n  border-top: 1px solid #e1e5e9;\n  background-color: #f8f9fa;\n}\n\n.btn {\n  padding: 0.75rem 1.5rem;\n  border: 1px solid transparent;\n  border-radius: 4px;\n  font-size: 1rem;\n  font-weight: 500;\n  cursor: pointer;\n  text-decoration: none;\n  display: inline-block;\n  transition: all 0.2s ease;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: white;\n}\n\n.btn-primary:hover {\n  background-color: #0056b3;\n  border-color: #0056b3;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .form-preview-overlay {\n    padding: 0.5rem;\n  }\n  \n  .preview-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n  \n  .field-stats {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n  \n  .preview-fields {\n    grid-template-columns: 1fr;\n  }\n  \n  .summary-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .field-items {\n    grid-template-columns: 1fr;\n  }\n  \n  .field-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .preview-header,\n  .preview-content,\n  .preview-footer {\n    padding: 1rem;\n  }\n  \n  .preview-section {\n    padding: 1rem;\n  }\n  \n  .field-summary,\n  .field-list {\n    padding: 1rem;\n  }\n}\n", ".form-builder {\n  display: flex;\n  flex-direction: column;\n  min-height: calc(100vh - 120px);\n  height: auto;\n  background-color: #f8f9fa;\n  overflow: visible;\n}\n\n.form-builder-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 2rem;\n  background-color: white;\n  border-bottom: 1px solid #e1e5e9;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.form-builder-header h2 {\n  margin: 0;\n  color: #495057;\n}\n\n.header-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.form-builder-content {\n  display: flex;\n  flex: 1;\n  overflow: visible;\n  min-height: 0;\n}\n\n.config-panel {\n  width: 300px;\n  background-color: white;\n  border-right: 1px solid #e1e5e9;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  max-height: calc(100vh - 200px);\n  flex-shrink: 0;\n}\n\n.config-section {\n  padding: 1.5rem;\n  border-bottom: 1px solid #e1e5e9;\n  background-color: white;\n}\n\n.config-section h3 {\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1.1rem;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n  color: #495057;\n}\n\n.form-help-text {\n  margin: 0.5rem 0;\n  font-size: 0.875rem;\n  color: #6c757d;\n  line-height: 1.4;\n}\n\n/* Saved Forms Section */\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.btn-link {\n  background: none;\n  border: none;\n  color: #007bff;\n  cursor: pointer;\n  font-size: 0.875rem;\n  text-decoration: underline;\n}\n\n.btn-link:hover {\n  color: #0056b3;\n}\n\n.saved-forms-list {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.no-forms-message {\n  color: #6c757d;\n  font-style: italic;\n  text-align: center;\n  padding: 1rem;\n  margin: 0;\n}\n\n.saved-form-item {\n  border: 1px solid #e1e5e9;\n  border-radius: 6px;\n  padding: 1rem;\n  margin-bottom: 0.75rem;\n  background-color: #f8f9fa;\n}\n\n.form-item-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 0.5rem;\n}\n\n.form-item-header h4 {\n  margin: 0;\n  font-size: 1rem;\n  color: #495057;\n}\n\n.form-type-badge {\n  background-color: #007bff;\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  text-transform: uppercase;\n  font-weight: 500;\n}\n\n.form-description {\n  margin: 0.5rem 0;\n  font-size: 0.875rem;\n  color: #6c757d;\n  line-height: 1.4;\n}\n\n.form-meta {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-bottom: 0.75rem;\n}\n\n.form-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.btn-small {\n  padding: 0.375rem 0.75rem;\n  font-size: 0.75rem;\n  border-radius: 4px;\n  border: 1px solid;\n  cursor: pointer;\n  font-weight: 500;\n}\n\n.btn-small.btn-outline {\n  background-color: transparent;\n  color: #007bff;\n  border-color: #007bff;\n}\n\n.btn-small.btn-outline:hover {\n  background-color: #007bff;\n  color: white;\n}\n\n.btn-small.btn-danger {\n  background-color: #dc3545;\n  color: white;\n  border-color: #dc3545;\n}\n\n.btn-small.btn-danger:hover {\n  background-color: #c82333;\n  border-color: #bd2130;\n}\n\n.form-group input,\n.form-group textarea {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 1rem;\n}\n\n.form-group input:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #80bdff;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.form-group input.error,\n.form-group textarea.error {\n  border-color: #dc3545;\n}\n\n.selected-fields {\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.empty-state {\n  text-align: center;\n  color: #6c757d;\n  font-style: italic;\n  padding: 2rem;\n  border: 2px dashed #dee2e6;\n  border-radius: 4px;\n}\n\n.selected-field {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem;\n  margin-bottom: 0.5rem;\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 4px;\n}\n\n.field-info {\n  flex: 1;\n}\n\n.field-label {\n  display: block;\n  font-weight: 600;\n  color: #495057;\n}\n\n.field-type {\n  font-size: 0.875rem;\n  color: #6c757d;\n  margin-right: 0.5rem;\n}\n\n.required-badge {\n  background-color: #dc3545;\n  color: white;\n  padding: 0.125rem 0.375rem;\n  border-radius: 3px;\n  font-size: 0.75rem;\n  font-weight: 600;\n}\n\n.conditional-badge {\n  background-color: #ffc107;\n  color: #212529;\n  padding: 0.125rem 0.375rem;\n  border-radius: 3px;\n  font-size: 0.75rem;\n  font-weight: 600;\n}\n\n.field-actions {\n  display: flex;\n  gap: 0.25rem;\n}\n\n.btn-icon {\n  background: none;\n  border: none;\n  padding: 0.25rem;\n  cursor: pointer;\n  border-radius: 3px;\n  font-size: 0.875rem;\n}\n\n.btn-icon:hover {\n  background-color: #e9ecef;\n}\n\n.btn-icon:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.btn-icon.remove:hover {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n\n.fields-panel {\n  flex: 1;\n  background-color: white;\n  display: flex;\n  flex-direction: column;\n  padding: 1rem;\n  overflow-y: auto;\n  max-height: calc(100vh - 200px);\n  min-width: 0;\n}\n\n.fields-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e1e5e9;\n}\n\n.fields-header h3 {\n  margin: 0;\n  color: #495057;\n}\n\n.hierarchy-selection {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n  margin: 1rem 0;\n}\n\n.hierarchy-selection h4 {\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1.1rem;\n}\n\n.hierarchy-dropdowns {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n\n.hierarchy-dropdowns .form-group {\n  margin-bottom: 0;\n}\n\n.hierarchy-dropdowns label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: #495057;\n}\n\n.hierarchy-dropdowns select {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 1rem;\n  background-color: white;\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n\n.hierarchy-dropdowns select:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.hierarchy-dropdowns select:disabled {\n  background-color: #e9ecef;\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.hierarchy-dropdowns select.error {\n  border-color: #dc3545;\n}\n\n.hierarchy-dropdowns select.error:focus {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n\n\n.search-input {\n  padding: 0.75rem;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  width: 100%;\n  font-size: 1rem;\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n\n.search-input:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.section-tabs {\n  display: flex;\n  border-bottom: 1px solid #e1e5e9;\n  overflow-x: auto;\n  flex-shrink: 0;\n  scrollbar-width: thin;\n}\n\n.section-tab {\n  background: none;\n  border: none;\n  padding: 0.75rem 1rem;\n  cursor: pointer;\n  border-bottom: 3px solid transparent;\n  white-space: nowrap;\n  color: #6c757d;\n  font-weight: 500;\n  flex-shrink: 0;\n  min-width: fit-content;\n}\n\n.section-tab:hover {\n  background-color: #f8f9fa;\n  color: #495057;\n}\n\n.section-tab.active {\n  color: #007bff;\n  border-bottom-color: #007bff;\n  background-color: #f8f9fa;\n}\n\n/* Bulk Selection Controls */\n.bulk-selection-controls {\n  padding: 1rem;\n  background-color: #f8f9fa;\n  border-bottom: 1px solid #e1e5e9;\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.bulk-controls-section {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n}\n\n.bulk-controls-label {\n  font-weight: 500;\n  color: #495057;\n  margin-right: 0.5rem;\n  font-size: 0.875rem;\n}\n\n.btn-sm {\n  padding: 0.375rem 0.75rem;\n  font-size: 0.875rem;\n  line-height: 1.4;\n  border-radius: 0.25rem;\n}\n\n.btn-outline {\n  background-color: transparent;\n  border: 1px solid #6c757d;\n  color: #6c757d;\n}\n\n.btn-outline:hover {\n  background-color: #6c757d;\n  color: white;\n}\n\n.btn-outline.btn-danger {\n  border-color: #dc3545;\n  color: #dc3545;\n}\n\n.btn-outline.btn-danger:hover {\n  background-color: #dc3545;\n  color: white;\n}\n\n.btn-outline:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.btn-outline:disabled:hover {\n  background-color: transparent;\n  color: #6c757d;\n}\n\n.selection-summary {\n  font-size: 0.875rem;\n  color: #6c757d;\n  font-weight: 500;\n  padding: 0.25rem 0.5rem;\n  background-color: white;\n  border-radius: 0.25rem;\n  border: 1px solid #dee2e6;\n}\n\n.fields-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 1rem;\n  min-height: 400px;\n  max-height: calc(100vh - 400px);\n}\n\n.field-item {\n  display: flex;\n  align-items: center;\n  padding: 0.75rem;\n  margin-bottom: 0.5rem;\n  border: 1px solid #e1e5e9;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.field-item:hover {\n  background-color: #f8f9fa;\n  border-color: #007bff;\n}\n\n.field-item.selected {\n  background-color: #e7f3ff;\n  border-color: #007bff;\n}\n\n.field-checkbox {\n  margin-right: 0.75rem;\n}\n\n.field-checkbox input[type=\"checkbox\"] {\n  width: 1.2rem;\n  height: 1.2rem;\n}\n\n.field-details {\n  flex: 1;\n}\n\n.field-name {\n  font-weight: 600;\n  color: #495057;\n  margin-bottom: 0.25rem;\n}\n\n.field-meta {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n/* Buttons */\n.btn {\n  padding: 0.5rem 1rem;\n  border: 1px solid transparent;\n  border-radius: 4px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  text-decoration: none;\n  display: inline-block;\n  transition: all 0.2s ease;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: white;\n}\n\n.btn-primary:hover {\n  background-color: #0056b3;\n  border-color: #0056b3;\n}\n\n.btn-primary:disabled {\n  background-color: #6c757d;\n  border-color: #6c757d;\n  cursor: not-allowed;\n}\n\n.btn-secondary {\n  background-color: #6c757d;\n  border-color: #6c757d;\n  color: white;\n}\n\n.btn-secondary:hover {\n  background-color: #545b62;\n  border-color: #545b62;\n}\n\n.btn-secondary:disabled {\n  opacity: 0.65;\n  cursor: not-allowed;\n}\n\n.btn-outline {\n  background-color: transparent;\n  border-color: #6c757d;\n  color: #6c757d;\n}\n\n.btn-outline:hover {\n  background-color: #6c757d;\n  color: white;\n}\n\n/* Alerts */\n.alert {\n  padding: 0.75rem 1.25rem;\n  margin-bottom: 1rem;\n  border: 1px solid transparent;\n  border-radius: 4px;\n}\n\n.alert-error {\n  color: #721c24;\n  background-color: #f8d7da;\n  border-color: #f5c6cb;\n}\n\n.error-message {\n  color: #dc3545;\n  font-size: 0.875rem;\n  margin-top: 0.25rem;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .form-builder-content {\n    flex-direction: column;\n  }\n  \n  .config-panel {\n    width: 100%;\n    max-height: 50vh;\n  }\n  \n  .fields-panel {\n    height: 50vh;\n  }\n  \n  .header-actions {\n    flex-direction: column;\n    gap: 0.25rem;\n  }\n  \n  .section-tabs {\n    flex-wrap: wrap;\n  }\n\n  .bulk-controls-section {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 0.5rem;\n  }\n\n  .bulk-controls-section .btn-sm {\n    width: 100%;\n    text-align: center;\n  }\n\n  .bulk-controls-label {\n    margin-bottom: 0.25rem;\n  }\n}\n\n/* Additional responsive improvements */\n@media (max-width: 1200px) {\n  .config-panel {\n    width: 250px;\n  }\n}\n\n/* Ensure scrollbars are visible and styled */\n.section-tabs::-webkit-scrollbar {\n  height: 6px;\n}\n\n.section-tabs::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.section-tabs::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.section-tabs::-webkit-scrollbar-thumb:hover {\n  background: #a1a1a1;\n}\n\n.fields-list::-webkit-scrollbar {\n  width: 8px;\n}\n\n.fields-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 4px;\n}\n\n.fields-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 4px;\n}\n\n.fields-list::-webkit-scrollbar-thumb:hover {\n  background: #a1a1a1;\n}\n", ".dynamic-person-form {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 2rem;\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.form-header {\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e1e5e9;\n}\n\n.form-header h2 {\n  margin: 0 0 0.5rem 0;\n  color: #495057;\n  font-size: 1.75rem;\n}\n\n.form-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.form-name {\n  font-weight: 600;\n  color: #007bff;\n  font-size: 1rem;\n}\n\n.form-description {\n  color: #6c757d;\n  font-size: 0.875rem;\n  font-style: italic;\n}\n\n.person-form {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n}\n\n.form-section {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.form-section h3 {\n  margin: 0 0 1.5rem 0;\n  color: #495057;\n  font-size: 1.25rem;\n  padding-bottom: 0.5rem;\n  border-bottom: 2px solid #007bff;\n  display: inline-block;\n}\n\n.form-fields {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n}\n\n/* Single column for certain field types */\n.form-fields .form-field:has(textarea),\n.form-fields .form-field:has(select[multiple]),\n.form-fields .form-field:has(input[type=\"url\"]) {\n  grid-column: 1 / -1;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n  padding-top: 2rem;\n  border-top: 1px solid #e1e5e9;\n  margin-top: 2rem;\n}\n\n/* Loading and error states */\n.dynamic-form-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n.loading-spinner {\n  width: 3rem;\n  height: 3rem;\n  border: 3px solid #f3f3f3;\n  border-top: 3px solid #007bff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 1rem;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.dynamic-form-error {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n  color: #dc3545;\n}\n\n.dynamic-form-error p {\n  margin-bottom: 1rem;\n  font-size: 1.1rem;\n}\n\n/* Alert styles */\n.alert {\n  padding: 1rem 1.25rem;\n  margin-bottom: 1.5rem;\n  border: 1px solid transparent;\n  border-radius: 4px;\n  font-size: 0.875rem;\n}\n\n.alert-error {\n  color: #721c24;\n  background-color: #f8d7da;\n  border-color: #f5c6cb;\n}\n\n.alert-success {\n  color: #155724;\n  background-color: #d4edda;\n  border-color: #c3e6cb;\n}\n\n.alert-warning {\n  color: #856404;\n  background-color: #fff3cd;\n  border-color: #ffeaa7;\n}\n\n.alert-info {\n  color: #0c5460;\n  background-color: #d1ecf1;\n  border-color: #bee5eb;\n}\n\n/* Button styles */\n.btn {\n  padding: 0.75rem 1.5rem;\n  border: 1px solid transparent;\n  border-radius: 4px;\n  font-size: 1rem;\n  font-weight: 500;\n  cursor: pointer;\n  text-decoration: none;\n  display: inline-block;\n  transition: all 0.2s ease;\n  min-width: 120px;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background-color: #0056b3;\n  border-color: #0056b3;\n}\n\n.btn-primary:disabled {\n  background-color: #6c757d;\n  border-color: #6c757d;\n  cursor: not-allowed;\n  opacity: 0.65;\n}\n\n.btn-outline {\n  background-color: transparent;\n  border-color: #6c757d;\n  color: #6c757d;\n}\n\n.btn-outline:hover:not(:disabled) {\n  background-color: #6c757d;\n  color: white;\n}\n\n.btn-outline:disabled {\n  opacity: 0.65;\n  cursor: not-allowed;\n}\n\n/* Error message styles */\n.error-message {\n  color: #dc3545;\n  font-size: 0.875rem;\n  margin-top: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n.error-message::before {\n  content: \"⚠\";\n  font-size: 1rem;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .dynamic-person-form {\n    padding: 1rem;\n    margin: 1rem;\n  }\n  \n  .form-header h2 {\n    font-size: 1.5rem;\n  }\n  \n  .form-section {\n    padding: 1rem;\n  }\n  \n  .form-fields {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n  \n  .form-actions {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .btn {\n    padding: 1rem;\n    font-size: 1.1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .dynamic-person-form {\n    padding: 0.5rem;\n    margin: 0.5rem;\n  }\n  \n  .form-section {\n    padding: 0.75rem;\n  }\n  \n  .form-section h3 {\n    font-size: 1.1rem;\n  }\n}\n\n/* Print styles */\n@media print {\n  .dynamic-person-form {\n    box-shadow: none;\n    border: 1px solid #000;\n  }\n  \n  .form-actions {\n    display: none;\n  }\n  \n  .form-section {\n    break-inside: avoid;\n    page-break-inside: avoid;\n  }\n}\n\n/* High contrast mode */\n@media (prefers-contrast: high) {\n  .form-section {\n    border-width: 2px;\n  }\n  \n  .form-section h3 {\n    border-bottom-width: 3px;\n  }\n  \n  .btn {\n    border-width: 2px;\n  }\n}\n\n/* Reduced motion */\n@media (prefers-reduced-motion: reduce) {\n  .btn,\n  .loading-spinner {\n    transition: none;\n    animation: none;\n  }\n}\n\n/* Hierarchical Dropdown Styles */\n.form-select {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 1rem;\n  background-color: white;\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n\n.form-select:focus {\n  border-color: #007bff;\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.form-select:disabled {\n  background-color: #e9ecef;\n  opacity: 1;\n  cursor: not-allowed;\n}\n\n.form-select.error {\n  border-color: #dc3545;\n}\n\n.form-select.error:focus {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n/* Status Messages */\n.loading-message {\n  padding: 1rem;\n  background-color: #e3f2fd;\n  border: 1px solid #bbdefb;\n  border-radius: 4px;\n  color: #1976d2;\n  text-align: center;\n  margin-top: 1rem;\n}\n\n.status-message {\n  padding: 1rem;\n  border-radius: 4px;\n  margin-top: 1rem;\n  font-weight: 500;\n}\n\n.status-message.success {\n  background-color: #d4edda;\n  border: 1px solid #c3e6cb;\n  color: #155724;\n}\n\n.status-message.error {\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n  color: #721c24;\n}\n\n.status-message.info {\n  background-color: #d1ecf1;\n  border: 1px solid #bee5eb;\n  color: #0c5460;\n}\n\n/* No Form Available Message */\n.no-form-message {\n  text-align: center;\n  padding: 2rem;\n  background-color: #fff3cd;\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  color: #856404;\n}\n\n.no-form-message h3 {\n  margin: 0 0 1rem 0;\n  color: #856404;\n}\n\n.no-form-message p {\n  margin: 0.5rem 0;\n  line-height: 1.5;\n}\n", ".hierarchical-selector {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  padding: 1rem;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  background-color: #f8f9fa;\n  position: relative;\n  width: 100%;\n}\n\n.selector-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.selector-label {\n  font-weight: 600;\n  color: #495057;\n  font-size: 0.875rem;\n}\n\n.selector-label .required {\n  color: #dc3545;\n  margin-left: 0.25rem;\n}\n\n.selector-input {\n  padding: 0.75rem;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 1rem;\n  background-color: white;\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n  width: 100%;\n}\n\n.selector-input:focus {\n  outline: none;\n  border-color: #80bdff;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.selector-input:disabled {\n  background-color: #e9ecef;\n  opacity: 1;\n  cursor: not-allowed;\n}\n\n.selector-input.error {\n  border-color: #dc3545;\n}\n\n.selector-input.error:focus {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.error-message {\n  color: #dc3545;\n  font-size: 0.875rem;\n  margin-top: 0.25rem;\n}\n\n.selection-summary {\n  margin-top: 1rem;\n  padding: 1rem;\n  background-color: #e7f3ff;\n  border: 1px solid #b3d7ff;\n  border-radius: 4px;\n}\n\n.selection-summary h4 {\n  margin: 0 0 0.5rem 0;\n  color: #0056b3;\n  font-size: 0.875rem;\n  font-weight: 600;\n}\n\n.selection-path {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n}\n\n.selection-item {\n  background-color: #007bff;\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n\n.separator {\n  color: #6c757d;\n  font-weight: bold;\n}\n\n/* Responsive design */\n@media (min-width: 768px) {\n  .hierarchical-selector {\n    flex-direction: row;\n    align-items: end;\n  }\n  \n  .selector-group {\n    flex: 1;\n    min-width: 200px;\n  }\n  \n  .selection-summary {\n    flex-basis: 100%;\n    margin-top: 1rem;\n  }\n}\n\n/* Loading state */\n.selector-input:disabled {\n  background-image: linear-gradient(45deg, transparent 25%, rgba(255,255,255,.5) 25%, rgba(255,255,255,.5) 75%, transparent 75%, transparent),\n                    linear-gradient(45deg, transparent 25%, rgba(255,255,255,.5) 25%, rgba(255,255,255,.5) 75%, transparent 75%, transparent);\n  background-size: 20px 20px;\n  background-position: 0 0, 10px 10px;\n  animation: loading 1s linear infinite;\n}\n\n@keyframes loading {\n  0% {\n    background-position: 0 0, 10px 10px;\n  }\n  100% {\n    background-position: 20px 20px, 30px 30px;\n  }\n}\n", ".person-list {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.list-header {\n  padding: 1.5rem;\n  border-bottom: 1px solid #e1e5e9;\n  background-color: #f8f9fa;\n}\n\n.header-title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.header-title h2 {\n  margin: 0;\n  color: #495057;\n}\n\n.header-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.search-section {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.search-filters {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n\n.filter-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.search-input {\n  padding: 0.75rem;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 1rem;\n}\n\n.search-input:focus {\n  outline: none;\n  border-color: #80bdff;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.hierarchy-filter {\n  margin: 1rem 0;\n}\n\n.search-actions {\n  display: flex;\n  gap: 0.5rem;\n  justify-content: flex-end;\n}\n\n.results-summary {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 1.5rem;\n  background-color: #f8f9fa;\n  border-bottom: 1px solid #e1e5e9;\n  font-size: 0.875rem;\n  color: #6c757d;\n}\n\n.page-size-selector {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.page-size-selector select {\n  padding: 0.25rem 0.5rem;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n.loading-spinner {\n  width: 3rem;\n  height: 3rem;\n  border: 3px solid #f3f3f3;\n  border-top: 3px solid #007bff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 1rem;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.person-table-container {\n  overflow-x: auto;\n}\n\n.person-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: 0.875rem;\n}\n\n.person-table th {\n  background-color: #f8f9fa;\n  padding: 1rem 0.75rem;\n  text-align: left;\n  font-weight: 600;\n  color: #495057;\n  border-bottom: 2px solid #e1e5e9;\n  white-space: nowrap;\n}\n\n.person-table td {\n  padding: 1rem 0.75rem;\n  border-bottom: 1px solid #e1e5e9;\n  vertical-align: top;\n}\n\n.person-table tbody tr:hover {\n  background-color: #f8f9fa;\n}\n\n.person-name strong {\n  color: #495057;\n  font-weight: 600;\n}\n\n.firm-name {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 0.25rem;\n}\n\n.contact-info,\n.email-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.alternate {\n  font-size: 0.75rem;\n  color: #6c757d;\n  font-style: italic;\n}\n\n.subcategory {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 0.25rem;\n}\n\n.nature-badge {\n  padding: 0.25rem 0.5rem;\n  border-radius: 3px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  text-transform: uppercase;\n}\n\n.nature-badge.nature-1 { /* Business */\n  background-color: #e7f3ff;\n  color: #0056b3;\n}\n\n.nature-badge.nature-2 { /* Corporate */\n  background-color: #f8d7da;\n  color: #721c24;\n}\n\n.nature-badge.nature-3 { /* Agriculture */\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.nature-badge.nature-4 { /* Individual */\n  background-color: #fff3cd;\n  color: #856404;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 0.25rem;\n}\n\n.btn-action {\n  background: none;\n  border: none;\n  padding: 0.5rem;\n  cursor: pointer;\n  border-radius: 4px;\n  font-size: 1rem;\n  transition: background-color 0.2s ease;\n}\n\n.btn-action:hover {\n  background-color: #e9ecef;\n}\n\n.btn-action.edit:hover {\n  background-color: #fff3cd;\n}\n\n.btn-action.view:hover {\n  background-color: #e7f3ff;\n}\n\n.no-data {\n  text-align: center;\n  padding: 3rem;\n  color: #6c757d;\n  font-style: italic;\n}\n\n.link-btn {\n  background: none;\n  border: none;\n  color: #007bff;\n  cursor: pointer;\n  text-decoration: underline;\n}\n\n.link-btn:hover {\n  color: #0056b3;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  padding: 1.5rem;\n  border-top: 1px solid #e1e5e9;\n}\n\n.pagination {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n.pagination-btn {\n  padding: 0.5rem 0.75rem;\n  border: 1px solid #ced4da;\n  background-color: white;\n  color: #495057;\n  cursor: pointer;\n  border-radius: 4px;\n  font-size: 0.875rem;\n  transition: all 0.2s ease;\n}\n\n.pagination-btn:hover:not(:disabled) {\n  background-color: #e9ecef;\n  border-color: #adb5bd;\n}\n\n.pagination-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.pagination-btn.active {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: white;\n}\n\n.pagination-ellipsis {\n  padding: 0.5rem 0.25rem;\n  color: #6c757d;\n}\n\n.alert {\n  padding: 1rem 1.25rem;\n  margin: 1.5rem;\n  border: 1px solid transparent;\n  border-radius: 4px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.alert-error {\n  color: #721c24;\n  background-color: #f8d7da;\n  border-color: #f5c6cb;\n}\n\n.retry-btn {\n  background-color: #dc3545;\n  color: white;\n  border: none;\n  padding: 0.5rem 1rem;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 0.875rem;\n}\n\n.retry-btn:hover {\n  background-color: #c82333;\n}\n\n.btn {\n  padding: 0.5rem 1rem;\n  border: 1px solid transparent;\n  border-radius: 4px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  text-decoration: none;\n  display: inline-block;\n  transition: all 0.2s ease;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: white;\n}\n\n.btn-primary:hover {\n  background-color: #0056b3;\n  border-color: #0056b3;\n}\n\n.btn-secondary {\n  background-color: #6c757d;\n  border-color: #6c757d;\n  color: white;\n}\n\n.btn-secondary:hover {\n  background-color: #545b62;\n  border-color: #545b62;\n}\n\n.btn-outline {\n  background-color: transparent;\n  border-color: #6c757d;\n  color: #6c757d;\n}\n\n.btn-outline:hover {\n  background-color: #6c757d;\n  color: white;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .list-header {\n    padding: 1rem;\n  }\n  \n  .header-title {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n  \n  .header-actions {\n    width: 100%;\n    justify-content: stretch;\n  }\n  \n  .header-actions .btn {\n    flex: 1;\n  }\n  \n  .search-filters {\n    grid-template-columns: 1fr;\n  }\n  \n  .search-actions {\n    justify-content: stretch;\n  }\n  \n  .search-actions .btn {\n    flex: 1;\n  }\n  \n  .results-summary {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n  \n  .person-table {\n    font-size: 0.75rem;\n  }\n  \n  .person-table th,\n  .person-table td {\n    padding: 0.5rem 0.25rem;\n  }\n  \n  .pagination {\n    flex-wrap: wrap;\n    justify-content: center;\n  }\n}\n\n@media (max-width: 480px) {\n  .person-table-container {\n    font-size: 0.7rem;\n  }\n  \n  .person-table th:nth-child(n+6),\n  .person-table td:nth-child(n+6) {\n    display: none;\n  }\n  \n  .action-buttons {\n    flex-direction: column;\n  }\n}\n", ".division-category-selection {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 24px;\n  background: #ffffff;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.selection-header {\n  text-align: center;\n  margin-bottom: 32px;\n}\n\n.selection-header h3 {\n  color: #1a1a1a;\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 8px;\n}\n\n.selection-description {\n  color: #666;\n  font-size: 16px;\n  line-height: 1.5;\n  max-width: 600px;\n  margin: 0 auto;\n}\n\n.error-message {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px 16px;\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 8px;\n  color: #dc2626;\n  margin-bottom: 24px;\n}\n\n.error-icon {\n  font-size: 18px;\n}\n\n.selection-form {\n  display: flex;\n  flex-direction: column;\n  gap: 32px;\n}\n\n.selection-section {\n  background: #f8fafc;\n  padding: 24px;\n  border-radius: 8px;\n  border: 1px solid #e2e8f0;\n}\n\n.selection-section h4 {\n  color: #1a1a1a;\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 16px;\n}\n\n.required-notice {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px 16px;\n  background: #fef3c7;\n  border: 1px solid #fbbf24;\n  border-radius: 6px;\n  margin-bottom: 24px;\n  font-size: 14px;\n  color: #92400e;\n}\n\n.required-icon {\n  color: #dc2626;\n  font-weight: bold;\n  font-size: 16px;\n}\n\n.selection-info {\n  background: #f0f9ff;\n  padding: 20px;\n  border-radius: 8px;\n  border: 1px solid #bae6fd;\n}\n\n.selection-info h4 {\n  color: #0369a1;\n  font-size: 16px;\n  font-weight: 600;\n  margin-bottom: 12px;\n}\n\n.selection-info ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.selection-info li {\n  padding: 6px 0;\n  color: #0369a1;\n  position: relative;\n  padding-left: 20px;\n}\n\n.selection-info li::before {\n  content: \"•\";\n  color: #0369a1;\n  font-weight: bold;\n  position: absolute;\n  left: 0;\n}\n\n.selection-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 24px;\n  border-top: 1px solid #e2e8f0;\n}\n\n.btn {\n  padding: 12px 24px;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border: none;\n  display: inline-flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.btn-secondary {\n  background: #f1f5f9;\n  color: #475569;\n  border: 1px solid #cbd5e1;\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background: #e2e8f0;\n  border-color: #94a3b8;\n}\n\n.btn-primary {\n  background: #3b82f6;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background: #2563eb;\n}\n\n.btn-primary:disabled {\n  background: #94a3b8;\n}\n\n.selection-summary {\n  margin-top: 24px;\n  padding: 20px;\n  background: #f0fdf4;\n  border: 1px solid #bbf7d0;\n  border-radius: 8px;\n}\n\n.selection-summary h4 {\n  color: #166534;\n  font-size: 16px;\n  font-weight: 600;\n  margin-bottom: 12px;\n}\n\n.summary-item {\n  padding: 4px 0;\n  color: #166534;\n}\n\n.summary-item strong {\n  font-weight: 600;\n  margin-right: 8px;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .division-category-selection {\n    padding: 16px;\n    margin: 16px;\n  }\n  \n  .selection-header h3 {\n    font-size: 20px;\n  }\n  \n  .selection-description {\n    font-size: 14px;\n  }\n  \n  .selection-section {\n    padding: 16px;\n  }\n  \n  .selection-actions {\n    flex-direction: column;\n    gap: 12px;\n  }\n  \n  .btn {\n    width: 100%;\n    justify-content: center;\n  }\n}\n\n/* Animation for smooth transitions */\n.selection-summary {\n  animation: slideIn 0.3s ease-out;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n", ".file-upload {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n}\n\n.upload-section h3,\n.settings-section h3 {\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1.25rem;\n}\n\n.template-section {\n  background-color: #e7f3ff;\n  border: 1px solid #b3d7ff;\n  border-radius: 4px;\n  padding: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.template-section p {\n  margin: 0 0 0.5rem 0;\n  color: #0056b3;\n}\n\n.drop-zone {\n  border: 2px dashed #ced4da;\n  border-radius: 8px;\n  padding: 3rem 2rem;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background-color: #f8f9fa;\n}\n\n.drop-zone:hover {\n  border-color: #007bff;\n  background-color: #e7f3ff;\n}\n\n.drop-zone.active {\n  border-color: #007bff;\n  background-color: #e7f3ff;\n  border-style: solid;\n}\n\n.drop-zone.has-file {\n  border-color: #28a745;\n  background-color: #d4edda;\n}\n\n.drop-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n}\n\n.drop-icon {\n  font-size: 3rem;\n  color: #6c757d;\n}\n\n.drop-text p {\n  margin: 0.25rem 0;\n  color: #6c757d;\n}\n\n.drop-text p:first-child {\n  font-size: 1.1rem;\n  color: #495057;\n}\n\n.file-selected {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem;\n  background-color: white;\n  border-radius: 4px;\n  border: 1px solid #28a745;\n}\n\n.file-icon {\n  font-size: 2rem;\n  color: #28a745;\n}\n\n.file-details {\n  flex: 1;\n  text-align: left;\n}\n\n.file-name {\n  font-weight: 600;\n  color: #495057;\n  margin-bottom: 0.25rem;\n}\n\n.file-meta {\n  font-size: 0.875rem;\n  color: #6c757d;\n}\n\n.file-meta span {\n  margin: 0 0.5rem;\n}\n\n.remove-file {\n  background: none;\n  border: none;\n  font-size: 1.25rem;\n  cursor: pointer;\n  color: #dc3545;\n  padding: 0.25rem;\n  border-radius: 4px;\n}\n\n.remove-file:hover {\n  background-color: #f8d7da;\n}\n\n.settings-section {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.setting-group {\n  margin-bottom: 1.5rem;\n}\n\n.setting-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n  color: #495057;\n}\n\n.setting-group select {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 1rem;\n  background-color: white;\n}\n\n.setting-group input[type=\"checkbox\"] {\n  margin-right: 0.5rem;\n  width: 1.2rem;\n  height: 1.2rem;\n}\n\n.setting-help {\n  margin-top: 0.5rem;\n  font-size: 0.875rem;\n  color: #6c757d;\n  font-style: italic;\n}\n\n.upload-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #e1e5e9;\n}\n\n.error-message {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-top: 1rem;\n  padding: 1rem;\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n  border-radius: 4px;\n  color: #721c24;\n}\n\n.error-icon {\n  font-size: 1.25rem;\n}\n\n.btn {\n  padding: 0.75rem 1.5rem;\n  border: 1px solid transparent;\n  border-radius: 4px;\n  font-size: 1rem;\n  font-weight: 500;\n  cursor: pointer;\n  text-decoration: none;\n  display: inline-block;\n  transition: all 0.2s ease;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background-color: #0056b3;\n  border-color: #0056b3;\n}\n\n.btn-primary:disabled {\n  background-color: #6c757d;\n  border-color: #6c757d;\n  cursor: not-allowed;\n  opacity: 0.65;\n}\n\n.btn-outline {\n  background-color: transparent;\n  border-color: #6c757d;\n  color: #6c757d;\n}\n\n.btn-outline:hover {\n  background-color: #6c757d;\n  color: white;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .file-upload {\n    gap: 1.5rem;\n  }\n  \n  .drop-zone {\n    padding: 2rem 1rem;\n  }\n  \n  .drop-icon {\n    font-size: 2rem;\n  }\n  \n  .file-selected {\n    flex-direction: column;\n    text-align: center;\n  }\n  \n  .settings-section {\n    padding: 1rem;\n  }\n  \n  .upload-actions {\n    justify-content: stretch;\n  }\n  \n  .btn {\n    width: 100%;\n  }\n}\n\n@media (max-width: 480px) {\n  .drop-zone {\n    padding: 1.5rem 0.5rem;\n  }\n  \n  .drop-text p {\n    font-size: 0.875rem;\n  }\n  \n  .template-section {\n    padding: 0.75rem;\n  }\n  \n  .settings-section {\n    padding: 0.75rem;\n  }\n}\n", ".field-mapping {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n}\n\n.mapping-header {\n  text-align: center;\n}\n\n.mapping-header h3 {\n  margin: 0 0 0.5rem 0;\n  color: #495057;\n  font-size: 1.5rem;\n}\n\n.mapping-header p {\n  margin: 0 0 1.5rem 0;\n  color: #6c757d;\n}\n\n.mapping-stats {\n  display: flex;\n  justify-content: center;\n  gap: 2rem;\n  margin-bottom: 1.5rem;\n}\n\n.stat {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n}\n\n.stat-value {\n  font-size: 2rem;\n  font-weight: 700;\n  margin-bottom: 0.25rem;\n}\n\n.stat-value.success {\n  color: #28a745;\n}\n\n.stat-value.error {\n  color: #dc3545;\n}\n\n.stat-label {\n  font-size: 0.875rem;\n  color: #6c757d;\n}\n\n.mapping-actions {\n  display: flex;\n  justify-content: center;\n  gap: 1rem;\n}\n\n.required-fields-status {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.required-fields-status h4 {\n  margin: 0 0 1rem 0;\n  color: #495057;\n}\n\n.required-fields-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 0.75rem;\n}\n\n.required-field {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem;\n  border-radius: 4px;\n  border: 1px solid #e1e5e9;\n}\n\n.required-field.mapped {\n  background-color: #d4edda;\n  border-color: #c3e6cb;\n}\n\n.required-field.unmapped {\n  background-color: #f8d7da;\n  border-color: #f5c6cb;\n}\n\n.field-name {\n  font-weight: 600;\n  color: #495057;\n}\n\n.field-status {\n  font-size: 1.25rem;\n}\n\n.mapping-table-container {\n  overflow-x: auto;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n}\n\n.mapping-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: 0.875rem;\n}\n\n.mapping-table th {\n  background-color: #f8f9fa;\n  padding: 1rem 0.75rem;\n  text-align: left;\n  font-weight: 600;\n  color: #495057;\n  border-bottom: 2px solid #e1e5e9;\n  white-space: nowrap;\n}\n\n.mapping-table td {\n  padding: 1rem 0.75rem;\n  border-bottom: 1px solid #e1e5e9;\n  vertical-align: top;\n}\n\n.mapped-row {\n  background-color: #f8fff8;\n}\n\n.unmapped-row {\n  background-color: #fff8f8;\n}\n\n.file-header strong {\n  color: #495057;\n  font-weight: 600;\n}\n\n.sample-data {\n  font-style: italic;\n  color: #6c757d;\n}\n\n.field-select {\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 0.875rem;\n}\n\n.field-select.mapped {\n  border-color: #28a745;\n  background-color: #f8fff8;\n}\n\n.field-select.unmapped {\n  border-color: #dc3545;\n  background-color: #fff8f8;\n}\n\n.type-badge {\n  background-color: #e9ecef;\n  color: #495057;\n  padding: 0.25rem 0.5rem;\n  border-radius: 3px;\n  font-size: 0.75rem;\n  font-weight: 600;\n}\n\n.no-mapping {\n  color: #6c757d;\n  font-style: italic;\n}\n\n.required-badge {\n  background-color: #dc3545;\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 3px;\n  font-size: 0.75rem;\n  font-weight: 600;\n}\n\n.optional-badge {\n  background-color: #6c757d;\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 3px;\n  font-size: 0.75rem;\n  font-weight: 600;\n}\n\n.unmapped-warning {\n  background-color: #fff3cd;\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.unmapped-warning h4 {\n  margin: 0 0 0.5rem 0;\n  color: #856404;\n}\n\n.unmapped-warning p {\n  margin: 0 0 1rem 0;\n  color: #856404;\n}\n\n.unmapped-list {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n}\n\n.unmapped-header {\n  background-color: #ffc107;\n  color: #212529;\n  padding: 0.25rem 0.5rem;\n  border-radius: 3px;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n\n.mapping-summary {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.mapping-summary h4 {\n  margin: 0 0 1rem 0;\n  color: #495057;\n}\n\n.summary-section h5 {\n  margin: 0 0 0.75rem 0;\n  color: #495057;\n  font-size: 1rem;\n}\n\n.mapped-fields-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 0.5rem;\n}\n\n.mapped-field-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem;\n  background-color: white;\n  border: 1px solid #e1e5e9;\n  border-radius: 4px;\n}\n\n.field-label {\n  font-weight: 500;\n  color: #495057;\n}\n\n.field-badge.required {\n  background-color: #dc3545;\n  color: white;\n}\n\n.field-badge.optional {\n  background-color: #6c757d;\n  color: white;\n}\n\n.field-badge {\n  padding: 0.125rem 0.375rem;\n  border-radius: 3px;\n  font-size: 0.75rem;\n  font-weight: 600;\n}\n\n.mapping-navigation {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 1rem;\n  border-top: 1px solid #e1e5e9;\n}\n\n.validation-error {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-top: 1rem;\n  padding: 1rem;\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n  border-radius: 4px;\n  color: #721c24;\n  font-weight: 500;\n}\n\n.btn {\n  padding: 0.75rem 1.5rem;\n  border: 1px solid transparent;\n  border-radius: 4px;\n  font-size: 1rem;\n  font-weight: 500;\n  cursor: pointer;\n  text-decoration: none;\n  display: inline-block;\n  transition: all 0.2s ease;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background-color: #0056b3;\n  border-color: #0056b3;\n}\n\n.btn-primary:disabled {\n  background-color: #6c757d;\n  border-color: #6c757d;\n  cursor: not-allowed;\n  opacity: 0.65;\n}\n\n.btn-outline {\n  background-color: transparent;\n  border-color: #6c757d;\n  color: #6c757d;\n}\n\n.btn-outline:hover {\n  background-color: #6c757d;\n  color: white;\n}\n\n/* Default Values Section */\n.default-values-section {\n  background: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  padding: 1.5rem;\n  margin-top: 1rem;\n}\n\n.default-values-section h4 {\n  color: #495057;\n  margin: 0 0 0.5rem 0;\n  font-size: 1.1rem;\n}\n\n.section-description {\n  color: #6c757d;\n  font-size: 0.9rem;\n  margin: 0 0 1.5rem 0;\n  line-height: 1.4;\n}\n\n.default-values-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n}\n\n.default-value-input {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.default-value-label {\n  font-weight: 500;\n  color: #495057;\n  font-size: 0.9rem;\n}\n\n.required-indicator {\n  color: #dc3545;\n  font-weight: bold;\n}\n\n.default-value-input-field,\n.default-value-select {\n  padding: 0.5rem;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 0.9rem;\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n\n.default-value-input-field:focus,\n.default-value-select:focus {\n  outline: none;\n  border-color: #80bdff;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.field-hint {\n  color: #6c757d;\n  font-size: 0.8rem;\n  font-style: italic;\n}\n\n/* Enhanced Required Field Status */\n.required-field .field-status {\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .default-values-grid {\n    grid-template-columns: 1fr;\n  }\n}\n", ".import-progress {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n  padding: 1rem;\n}\n\n.progress-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n.loading-spinner {\n  width: 3rem;\n  height: 3rem;\n  border: 3px solid #f3f3f3;\n  border-top: 3px solid #007bff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 1rem;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.progress-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.progress-header h3 {\n  margin: 0;\n  color: #495057;\n}\n\n.job-info {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 0.25rem;\n}\n\n.job-id {\n  font-size: 0.875rem;\n  color: #6c757d;\n  font-family: monospace;\n}\n\n.job-status {\n  font-weight: 600;\n  font-size: 1rem;\n}\n\n.progress-section {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.progress-bar-container {\n  margin-bottom: 1rem;\n}\n\n.progress-bar {\n  width: 100%;\n  height: 2rem;\n  background-color: #e9ecef;\n  border-radius: 1rem;\n  overflow: hidden;\n  position: relative;\n}\n\n.progress-fill {\n  height: 100%;\n  transition: width 0.3s ease;\n  border-radius: 1rem;\n}\n\n.progress-text {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  font-weight: 600;\n  color: white;\n  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);\n  font-size: 0.875rem;\n}\n\n.progress-details {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n\n.detail-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem;\n  background-color: white;\n  border-radius: 4px;\n  border: 1px solid #e1e5e9;\n}\n\n.detail-label {\n  font-weight: 600;\n  color: #495057;\n}\n\n.detail-value {\n  font-weight: 700;\n  color: #007bff;\n}\n\n.current-operation {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem;\n  background-color: #e7f3ff;\n  border: 1px solid #b3d7ff;\n  border-radius: 8px;\n}\n\n.operation-icon {\n  font-size: 1.5rem;\n  animation: spin 2s linear infinite;\n}\n\n.operation-text {\n  font-weight: 500;\n  color: #0056b3;\n}\n\n.progress-stats {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 1rem;\n}\n\n.stat-card {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem;\n  border-radius: 8px;\n  border: 1px solid;\n}\n\n.stat-card.success {\n  background-color: #d4edda;\n  border-color: #c3e6cb;\n}\n\n.stat-card.error {\n  background-color: #f8d7da;\n  border-color: #f5c6cb;\n}\n\n.stat-card.warning {\n  background-color: #fff3cd;\n  border-color: #ffeaa7;\n}\n\n.stat-card.info {\n  background-color: #d1ecf1;\n  border-color: #bee5eb;\n}\n\n.stat-icon {\n  font-size: 1.5rem;\n}\n\n.stat-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.stat-value {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 0.25rem;\n}\n\n.stat-label {\n  font-size: 0.875rem;\n  color: #6c757d;\n  font-weight: 500;\n}\n\n.recent-errors {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.recent-errors h4 {\n  margin: 0 0 1rem 0;\n  color: #495057;\n}\n\n.errors-list {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.error-item {\n  display: grid;\n  grid-template-columns: auto auto 1fr;\n  gap: 1rem;\n  padding: 0.75rem;\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n  border-radius: 4px;\n  font-size: 0.875rem;\n}\n\n.error-row {\n  font-weight: 600;\n  color: #721c24;\n}\n\n.error-field {\n  font-weight: 500;\n  color: #721c24;\n}\n\n.error-message {\n  color: #721c24;\n}\n\n.more-errors {\n  text-align: center;\n  padding: 0.5rem;\n  color: #6c757d;\n  font-style: italic;\n}\n\n.processing-log {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.processing-log h4 {\n  margin: 0 0 1rem 0;\n  color: #495057;\n}\n\n.log-container {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n  max-height: 200px;\n  overflow-y: auto;\n}\n\n.log-entry {\n  display: flex;\n  gap: 1rem;\n  padding: 0.5rem;\n  background-color: white;\n  border-radius: 4px;\n  border: 1px solid #e1e5e9;\n  font-size: 0.875rem;\n}\n\n.log-entry.current {\n  background-color: #e7f3ff;\n  border-color: #b3d7ff;\n}\n\n.log-time {\n  font-weight: 600;\n  color: #6c757d;\n  font-family: monospace;\n  min-width: 80px;\n}\n\n.log-message {\n  color: #495057;\n}\n\n.progress-actions {\n  display: flex;\n  justify-content: center;\n  gap: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #e1e5e9;\n}\n\n.completion-message {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1.5rem;\n  border-radius: 8px;\n  text-align: center;\n}\n\n.completion-message.completed {\n  background-color: #d4edda;\n  border: 1px solid #c3e6cb;\n}\n\n.completion-message.failed {\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n}\n\n.completion-message.cancelled {\n  background-color: #e2e3e5;\n  border: 1px solid #d6d8db;\n}\n\n.completion-icon {\n  font-size: 2rem;\n}\n\n.completion-text {\n  flex: 1;\n}\n\n.completion-details {\n  margin-top: 0.5rem;\n  font-size: 0.875rem;\n  color: #6c757d;\n}\n\n.progress-error {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-top: 1rem;\n  padding: 1rem;\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n  border-radius: 4px;\n  color: #721c24;\n  font-weight: 500;\n}\n\n.btn {\n  padding: 0.75rem 1.5rem;\n  border: 1px solid transparent;\n  border-radius: 4px;\n  font-size: 1rem;\n  font-weight: 500;\n  cursor: pointer;\n  text-decoration: none;\n  display: inline-block;\n  transition: all 0.2s ease;\n}\n\n.btn-outline {\n  background-color: transparent;\n  border-color: #6c757d;\n  color: #6c757d;\n}\n\n.btn-outline:hover {\n  background-color: #6c757d;\n  color: white;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .progress-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n  \n  .job-info {\n    align-items: flex-start;\n  }\n  \n  .progress-details {\n    grid-template-columns: 1fr;\n  }\n  \n  .stats-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  \n  .error-item {\n    grid-template-columns: 1fr;\n    gap: 0.5rem;\n  }\n  \n  .completion-message {\n    flex-direction: column;\n    text-align: center;\n  }\n}\n\n@media (max-width: 480px) {\n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .progress-actions {\n    flex-direction: column;\n  }\n}\n", ".import-results {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n  padding: 1rem;\n}\n\n.results-loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 4rem 2rem;\n  text-align: center;\n  color: #6c757d;\n}\n\n.results-header {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 1rem;\n}\n\n.status-indicator {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1.5rem;\n  border-radius: 8px;\n  border: 2px solid;\n}\n\n.status-indicator.success {\n  background-color: #d4edda;\n  border-color: #28a745;\n  color: #155724;\n}\n\n.status-indicator.error {\n  background-color: #f8d7da;\n  border-color: #dc3545;\n  color: #721c24;\n}\n\n.status-icon {\n  font-size: 3rem;\n}\n\n.status-content h3 {\n  margin: 0 0 0.5rem 0;\n  font-size: 1.5rem;\n}\n\n.status-content p {\n  margin: 0;\n  font-size: 0.875rem;\n  opacity: 0.8;\n}\n\n.results-summary {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.results-summary h4 {\n  margin: 0 0 1rem 0;\n  color: #495057;\n}\n\n.summary-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 1rem;\n}\n\n.summary-card {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem;\n  border-radius: 8px;\n  border: 1px solid;\n}\n\n.summary-card.total {\n  background-color: #e7f3ff;\n  border-color: #b3d7ff;\n}\n\n.summary-card.success {\n  background-color: #d4edda;\n  border-color: #c3e6cb;\n}\n\n.summary-card.error {\n  background-color: #f8d7da;\n  border-color: #f5c6cb;\n}\n\n.summary-card.warning {\n  background-color: #fff3cd;\n  border-color: #ffeaa7;\n}\n\n.summary-card.info {\n  background-color: #d1ecf1;\n  border-color: #bee5eb;\n}\n\n.card-icon {\n  font-size: 1.5rem;\n}\n\n.card-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.card-value {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 0.25rem;\n}\n\n.card-label {\n  font-size: 0.875rem;\n  color: #6c757d;\n  font-weight: 500;\n}\n\n.processing-details {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.processing-details h4 {\n  margin: 0 0 1rem 0;\n  color: #495057;\n}\n\n.details-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n\n.detail-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem;\n  background-color: white;\n  border-radius: 4px;\n  border: 1px solid #e1e5e9;\n}\n\n.detail-label {\n  font-weight: 600;\n  color: #495057;\n}\n\n.detail-value {\n  font-weight: 700;\n  color: #007bff;\n}\n\n.detailed-summary {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.detailed-summary h4 {\n  margin: 0 0 1rem 0;\n  color: #495057;\n}\n\n.summary-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.summary-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem;\n  background-color: white;\n  border-radius: 4px;\n  border: 1px solid #e1e5e9;\n}\n\n.error-analysis {\n  background-color: #f8f9fa;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.error-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.error-header h4 {\n  margin: 0;\n  color: #495057;\n}\n\n.error-actions {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n\n.error-filter {\n  padding: 0.5rem;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 0.875rem;\n}\n\n.error-groups {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.error-group {\n  border: 1px solid #e1e5e9;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.group-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem;\n  background-color: #f8d7da;\n  border-bottom: 1px solid #f5c6cb;\n}\n\n.group-type {\n  font-weight: 600;\n  color: #721c24;\n}\n\n.group-count {\n  font-size: 0.875rem;\n  color: #721c24;\n  background-color: #dc3545;\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 3px;\n}\n\n.group-examples {\n  padding: 1rem;\n  background-color: white;\n}\n\n.error-example {\n  display: grid;\n  grid-template-columns: auto auto 1fr auto;\n  gap: 1rem;\n  padding: 0.5rem;\n  margin-bottom: 0.5rem;\n  background-color: #f8f9fa;\n  border-radius: 4px;\n  font-size: 0.875rem;\n}\n\n.error-row {\n  font-weight: 600;\n  color: #dc3545;\n}\n\n.error-field {\n  font-weight: 500;\n  color: #495057;\n}\n\n.error-message {\n  color: #721c24;\n}\n\n.error-value {\n  font-family: monospace;\n  color: #6c757d;\n  font-style: italic;\n}\n\n.recommendations {\n  background-color: #e7f3ff;\n  border: 1px solid #b3d7ff;\n  border-radius: 8px;\n  padding: 1.5rem;\n}\n\n.recommendations h4 {\n  margin: 0 0 1rem 0;\n  color: #0056b3;\n}\n\n.recommendations ul {\n  margin: 0;\n  padding-left: 1.5rem;\n  color: #0056b3;\n}\n\n.recommendations li {\n  margin-bottom: 0.5rem;\n}\n\n.results-actions {\n  display: flex;\n  justify-content: center;\n  gap: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #e1e5e9;\n}\n\n.btn {\n  padding: 0.75rem 1.5rem;\n  border: 1px solid transparent;\n  border-radius: 4px;\n  font-size: 1rem;\n  font-weight: 500;\n  cursor: pointer;\n  text-decoration: none;\n  display: inline-block;\n  transition: all 0.2s ease;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: white;\n}\n\n.btn-primary:hover {\n  background-color: #0056b3;\n  border-color: #0056b3;\n}\n\n.btn-outline {\n  background-color: transparent;\n  border-color: #6c757d;\n  color: #6c757d;\n}\n\n.btn-outline:hover {\n  background-color: #6c757d;\n  color: white;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .import-results {\n    padding: 0.5rem;\n    gap: 1.5rem;\n  }\n  \n  .status-indicator {\n    flex-direction: column;\n    text-align: center;\n  }\n  \n  .summary-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  \n  .details-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .error-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n  \n  .error-actions {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .error-example {\n    grid-template-columns: 1fr;\n    gap: 0.5rem;\n  }\n  \n  .results-actions {\n    flex-direction: column;\n  }\n}\n\n@media (max-width: 480px) {\n  .summary-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .status-indicator {\n    padding: 1rem;\n  }\n  \n  .status-icon {\n    font-size: 2rem;\n  }\n}\n", "/* Standalone page styles */\n.import-page {\n  padding: 1rem;\n  min-height: auto;\n  background-color: transparent;\n}\n\n.import-container {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  width: 100%;\n  max-width: 1200px;\n  margin: 0 auto;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n/* Legacy modal styles for backward compatibility */\n.import-persons-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  padding: 1rem;\n}\n\n.import-modal-content {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  width: 100%;\n  max-width: 1200px;\n  max-height: 90vh;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n.import-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e1e5e9;\n  background-color: #f8f9fa;\n}\n\n.import-header h2 {\n  margin: 0;\n  color: #495057;\n}\n\n.import-subtitle {\n  margin: 0.5rem 0 0 0;\n  color: #6c757d;\n  font-size: 0.9rem;\n}\n\n.close-button {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  color: #6c757d;\n  padding: 0;\n  width: 2rem;\n  height: 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 4px;\n}\n\n.close-button:hover {\n  background-color: #e9ecef;\n  color: #495057;\n}\n\n.step-indicator {\n  display: flex;\n  justify-content: center;\n  padding: 1.5rem;\n  background-color: #f8f9fa;\n  border-bottom: 1px solid #e1e5e9;\n}\n\n.step {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 1rem;\n  margin: 0 1rem;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  min-width: 120px;\n}\n\n.step.active {\n  background-color: #e7f3ff;\n  border: 2px solid #007bff;\n}\n\n.step.current {\n  background-color: #007bff;\n  color: white;\n}\n\n.step-icon {\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n}\n\n.step-title {\n  font-size: 0.875rem;\n  font-weight: 600;\n  text-align: center;\n}\n\n.import-body {\n  flex: 1;\n  overflow-y: auto;\n  padding: 2rem;\n}\n\n.import-error {\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n  border-radius: 4px;\n  padding: 1rem;\n  margin: 1rem;\n}\n\n.error-content {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.error-icon {\n  font-size: 1.5rem;\n}\n\n.error-message {\n  flex: 1;\n  color: #721c24;\n  font-weight: 500;\n}\n\n.retry-button {\n  background-color: #dc3545;\n  color: white;\n  border: none;\n  padding: 0.5rem 1rem;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 0.875rem;\n}\n\n.retry-button:hover {\n  background-color: #c82333;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .import-persons-modal {\n    padding: 0.5rem;\n  }\n  \n  .import-modal-content {\n    max-width: 100%;\n    max-height: 95vh;\n  }\n  \n  .import-header {\n    padding: 1rem;\n  }\n  \n  .step-indicator {\n    padding: 1rem;\n    overflow-x: auto;\n  }\n  \n  .step {\n    margin: 0 0.5rem;\n    min-width: 100px;\n  }\n  \n  .step-icon {\n    font-size: 1.5rem;\n  }\n  \n  .step-title {\n    font-size: 0.75rem;\n  }\n  \n  .import-body {\n    padding: 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .step {\n    flex-direction: row;\n    min-width: auto;\n    padding: 0.5rem;\n  }\n  \n  .step-icon {\n    font-size: 1.25rem;\n    margin-bottom: 0;\n    margin-right: 0.5rem;\n  }\n  \n  .step-title {\n    font-size: 0.7rem;\n  }\n}\n", "/* All Forms Modal Styles */\n.all-forms-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  padding: 1rem;\n}\n\n/* All Forms Inline Styles */\n.all-forms-inline {\n  width: 100%;\n  min-height: auto;\n  background-color: transparent;\n}\n\n.all-forms-inline .modal-content {\n  background-color: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  width: 100%;\n  max-width: none;\n  max-height: none;\n  overflow: visible;\n  display: flex;\n  flex-direction: column;\n  margin: 0;\n}\n\n.modal-content {\n  background-color: white;\n  border-radius: 12px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n  width: 100%;\n  max-width: 1200px;\n  max-height: 90vh;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e1e5e9;\n  background-color: #f8f9fa;\n}\n\n.modal-header h2 {\n  margin: 0;\n  color: #495057;\n  font-size: 1.5rem;\n}\n\n.close-button {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  color: #6c757d;\n  padding: 0;\n  width: 2rem;\n  height: 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 4px;\n  transition: background-color 0.2s;\n}\n\n.close-button:hover {\n  background-color: #e9ecef;\n}\n\n.modal-body {\n  flex: 1;\n  overflow-y: auto;\n  padding: 1.5rem;\n}\n\n/* Loading State */\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 3rem;\n}\n\n.spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #f3f3f3;\n  border-top: 4px solid #007bff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 1rem;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Error State */\n.error-message {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem;\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n  border-radius: 6px;\n  color: #721c24;\n  margin-bottom: 1rem;\n}\n\n.error-icon {\n  font-size: 1.2rem;\n}\n\n/* Empty State */\n.empty-state {\n  text-align: center;\n  padding: 3rem;\n  color: #6c757d;\n}\n\n.empty-icon {\n  font-size: 4rem;\n  margin-bottom: 1rem;\n}\n\n.empty-state h3 {\n  margin: 0 0 0.5rem 0;\n  color: #495057;\n}\n\n.empty-state p {\n  margin: 0;\n  font-size: 0.9rem;\n}\n\n/* Forms Container */\n.forms-container {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n}\n\n/* Division Section */\n.division-section {\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.division-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 1.5rem;\n  background-color: #f8f9fa;\n  border-bottom: 1px solid #e1e5e9;\n}\n\n.division-header h3 {\n  margin: 0;\n  color: #495057;\n  font-size: 1.2rem;\n}\n\n.forms-count {\n  background-color: #007bff;\n  color: white;\n  padding: 0.25rem 0.75rem;\n  border-radius: 12px;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n/* Category Section */\n.category-section {\n  border-bottom: 1px solid #e1e5e9;\n}\n\n.category-section:last-child {\n  border-bottom: none;\n}\n\n.category-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem 1.5rem;\n  background-color: #ffffff;\n  border-bottom: 1px solid #f1f3f4;\n}\n\n.category-header h4 {\n  margin: 0;\n  color: #6c757d;\n  font-size: 1rem;\n  font-weight: 500;\n}\n\n.category-forms-count {\n  background-color: #6c757d;\n  color: white;\n  padding: 0.2rem 0.6rem;\n  border-radius: 10px;\n  font-size: 0.75rem;\n}\n\n/* Forms List */\n.forms-list {\n  padding: 1rem 1.5rem;\n  background-color: #fafbfc;\n}\n\n/* Form List Item */\n.form-list-item {\n  background-color: white;\n  border: 1px solid #e1e5e9;\n  border-radius: 8px;\n  margin-bottom: 0.75rem;\n  transition: box-shadow 0.2s, border-color 0.2s;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 1.5rem;\n  gap: 1rem;\n}\n\n.form-list-item:hover {\n  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);\n  border-color: #007bff;\n}\n\n.form-list-item:last-child {\n  margin-bottom: 0;\n}\n\n.form-info {\n  flex: 1;\n  min-width: 0; /* Allow text truncation */\n}\n\n.form-name-section {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 0.5rem;\n}\n\n.form-name {\n  margin: 0;\n  color: #495057;\n  font-size: 1.1rem;\n  font-weight: 600;\n  flex-shrink: 0;\n}\n\n.form-actions {\n  display: flex;\n  gap: 0.5rem;\n  flex-shrink: 0;\n}\n\n.btn-action {\n  background: none;\n  border: 1px solid #ddd;\n  border-radius: 6px;\n  padding: 0.5rem 1rem;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  white-space: nowrap;\n}\n\n.btn-action:hover {\n  background-color: #f8f9fa;\n  border-color: #007bff;\n}\n\n.btn-action.edit:hover {\n  background-color: #e3f2fd;\n  border-color: #2196f3;\n  color: #1976d2;\n}\n\n.btn-action.delete:hover {\n  background-color: #ffebee;\n  border-color: #f44336;\n  color: #d32f2f;\n}\n\n.form-card-body {\n  padding: 1rem;\n}\n\n.form-description {\n  margin: 0 0 1rem 0;\n  color: #6c757d;\n  font-size: 0.9rem;\n  line-height: 1.4;\n}\n\n.form-stats {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 0.75rem;\n}\n\n.stat {\n  font-size: 0.8rem;\n  color: #6c757d;\n}\n\n.form-type {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.type-badge {\n  padding: 0.25rem 0.75rem;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n.type-badge.category {\n  background-color: #e3f2fd;\n  color: #1976d2;\n}\n\n.type-badge.subcategory {\n  background-color: #f3e5f5;\n  color: #7b1fa2;\n}\n\n.type-badge.default {\n  background-color: #e8f5e8;\n  color: #388e3c;\n}\n\n/* Delete Confirmation Modal */\n.delete-confirm-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1100;\n}\n\n.delete-confirm-content {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\n  width: 100%;\n  max-width: 400px;\n  margin: 1rem;\n}\n\n.delete-confirm-header {\n  padding: 1rem 1.5rem;\n  border-bottom: 1px solid #e1e5e9;\n  background-color: #f8f9fa;\n}\n\n.delete-confirm-header h3 {\n  margin: 0;\n  color: #495057;\n}\n\n.delete-confirm-body {\n  padding: 1.5rem;\n}\n\n.delete-confirm-body p {\n  margin: 0 0 1rem 0;\n  color: #495057;\n}\n\n.warning-text {\n  color: #dc3545;\n  font-size: 0.9rem;\n  font-style: italic;\n}\n\n.delete-confirm-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n  padding: 1rem 1.5rem;\n  border-top: 1px solid #e1e5e9;\n  background-color: #f8f9fa;\n}\n\n.btn-cancel {\n  padding: 0.5rem 1rem;\n  border: 1px solid #6c757d;\n  background-color: white;\n  color: #6c757d;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.btn-cancel:hover {\n  background-color: #6c757d;\n  color: white;\n}\n\n.btn-delete {\n  padding: 0.5rem 1rem;\n  border: 1px solid #dc3545;\n  background-color: #dc3545;\n  color: white;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.btn-delete:hover {\n  background-color: #c82333;\n  border-color: #c82333;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .all-forms-modal {\n    padding: 0.5rem;\n  }\n  \n  .modal-content {\n    max-height: 95vh;\n  }\n  \n  .forms-list {\n    padding: 1rem;\n  }\n\n  .division-header,\n  .category-header {\n    padding: 1rem;\n  }\n\n  .form-list-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n    padding: 1rem;\n  }\n\n  .form-info {\n    width: 100%;\n  }\n\n  .form-name-section {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n\n  .form-actions {\n    align-self: flex-end;\n    width: auto;\n  }\n}\n", ".person-management {\n  min-height: 100vh;\n  background-color: #f8f9fa;\n  display: flex;\n  flex-direction: column;\n}\n\n.management-header {\n  background-color: white;\n  border-bottom: 1px solid #e1e5e9;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  position: sticky;\n  top: 0;\n  z-index: 100;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem 2rem;\n}\n\n.header-content h1 {\n  margin: 0;\n  color: #495057;\n  font-size: 1.75rem;\n}\n\n.header-stats {\n  display: flex;\n  gap: 1.5rem;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n}\n\n.stat-label {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-bottom: 0.25rem;\n}\n\n.stat-value {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #007bff;\n}\n\n.header-nav {\n  display: flex;\n  padding: 0 2rem;\n  background-color: #f8f9fa;\n  border-top: 1px solid #e1e5e9;\n}\n\n.nav-btn {\n  background: none;\n  border: none;\n  padding: 1rem 1.5rem;\n  cursor: pointer;\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: #6c757d;\n  border-bottom: 3px solid transparent;\n  transition: all 0.2s ease;\n}\n\n.nav-btn:hover {\n  color: #495057;\n  background-color: #e9ecef;\n}\n\n.nav-btn.active {\n  color: #007bff;\n  border-bottom-color: #007bff;\n  background-color: white;\n}\n\n.notification {\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  padding: 1rem 1.5rem;\n  border-radius: 4px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  min-width: 300px;\n  animation: slideIn 0.3s ease;\n}\n\n.notification.success {\n  background-color: #d4edda;\n  color: #155724;\n  border: 1px solid #c3e6cb;\n}\n\n.notification.error {\n  background-color: #f8d7da;\n  color: #721c24;\n  border: 1px solid #f5c6cb;\n}\n\n.notification.warning {\n  background-color: #fff3cd;\n  color: #856404;\n  border: 1px solid #ffeaa7;\n}\n\n.notification-close {\n  background: none;\n  border: none;\n  font-size: 1.2rem;\n  cursor: pointer;\n  color: inherit;\n  opacity: 0.7;\n}\n\n.notification-close:hover {\n  opacity: 1;\n}\n\n@keyframes slideIn {\n  from {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n.management-content {\n  flex: 1;\n  padding: 2rem;\n  overflow-y: auto;\n}\n\n.management-footer {\n  background-color: white;\n  border-top: 1px solid #e1e5e9;\n  padding: 2rem;\n  margin-top: auto;\n}\n\n.footer-content {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.footer-section h4 {\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1rem;\n}\n\n.quick-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.quick-action-btn {\n  background-color: #007bff;\n  color: white;\n  border: none;\n  padding: 0.75rem 1rem;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 0.875rem;\n  transition: background-color 0.2s ease;\n  text-align: left;\n}\n\n.quick-action-btn:hover {\n  background-color: #0056b3;\n}\n\n.footer-stats {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.footer-stat {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem;\n  background-color: #f8f9fa;\n  border-radius: 4px;\n  font-size: 0.875rem;\n}\n\n.footer-stat span:first-child {\n  color: #6c757d;\n}\n\n.footer-stat span:last-child {\n  font-weight: 600;\n  color: #495057;\n}\n\n.footer-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n  font-size: 0.875rem;\n  color: #6c757d;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .header-content {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n    padding: 1rem;\n  }\n  \n  .header-stats {\n    flex-direction: row;\n    gap: 1rem;\n    width: 100%;\n    justify-content: space-around;\n  }\n  \n  .header-nav {\n    padding: 0 1rem;\n    overflow-x: auto;\n  }\n  \n  .nav-btn {\n    white-space: nowrap;\n    padding: 0.75rem 1rem;\n  }\n  \n  .management-content {\n    padding: 1rem;\n  }\n  \n  .management-footer {\n    padding: 1rem;\n  }\n  \n  .footer-content {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n  \n  .notification {\n    position: fixed;\n    top: 10px;\n    left: 10px;\n    right: 10px;\n    min-width: auto;\n  }\n}\n\n@media (max-width: 480px) {\n  .header-content h1 {\n    font-size: 1.5rem;\n  }\n  \n  .header-stats {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n  \n  .stat-item {\n    flex-direction: row;\n    justify-content: space-between;\n    width: 100%;\n    padding: 0.5rem;\n    background-color: #f8f9fa;\n    border-radius: 4px;\n  }\n  \n  .stat-value {\n    font-size: 1.25rem;\n  }\n  \n  .nav-btn {\n    font-size: 0.75rem;\n    padding: 0.5rem 0.75rem;\n  }\n}\n\n/* Print styles */\n@media print {\n  .management-header,\n  .management-footer,\n  .notification {\n    display: none;\n  }\n  \n  .management-content {\n    padding: 0;\n  }\n}\n\n/* High contrast mode */\n@media (prefers-contrast: high) {\n  .management-header {\n    border-bottom-width: 2px;\n  }\n  \n  .nav-btn.active {\n    border-bottom-width: 4px;\n  }\n  \n  .notification {\n    border-width: 2px;\n  }\n}\n\n/* Reduced motion */\n@media (prefers-reduced-motion: reduce) {\n  .nav-btn,\n  .quick-action-btn,\n  .notification {\n    transition: none;\n  }\n  \n  .notification {\n    animation: none;\n  }\n}\n", ".persons-view {\n  padding: 2rem;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n.persons-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  background: white;\n  padding: 2rem;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.header-filters {\n  display: flex;\n  gap: 1.5rem;\n  align-items: end;\n}\n\n.header-filters .filter-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.header-filters .filter-group label {\n  font-size: 0.9rem;\n  font-weight: 500;\n  color: #2c3e50;\n  margin: 0;\n}\n\n.header-filters .filter-group select {\n  padding: 0.75rem;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  background-color: white;\n  color: #2c3e50;\n  min-width: 150px;\n  transition: border-color 0.2s ease;\n}\n\n.header-filters .filter-group select:focus {\n  outline: none;\n  border-color: #3498db;\n}\n\n.header-filters .filter-group select:disabled {\n  background-color: #f8f9fa;\n  color: #6c757d;\n  cursor: not-allowed;\n}\n\n.header-filters .search-input {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.header-filters .search-input input {\n  padding: 0.75rem 0.75rem 0.75rem 2.5rem;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  background-color: white;\n  color: #2c3e50;\n  min-width: 200px;\n  transition: border-color 0.2s ease;\n}\n\n.header-filters .search-input input:focus {\n  outline: none;\n  border-color: #3498db;\n}\n\n.header-filters .search-icon {\n  position: absolute;\n  left: 0.75rem;\n  color: #6c757d;\n  z-index: 1;\n}\n\n.header-actions {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n.btn {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  text-decoration: none;\n}\n\n.btn-primary {\n  background-color: #3498db;\n  color: white;\n}\n\n.btn-primary:hover {\n  background-color: #2980b9;\n  transform: translateY(-1px);\n}\n\n.btn-outline {\n  background-color: transparent;\n  color: #3498db;\n  border: 2px solid #3498db;\n}\n\n.btn-outline:hover {\n  background-color: #3498db;\n  color: white;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n\n/* Filters Panel */\n.filters-panel {\n  background: white;\n  border-radius: 12px;\n  padding: 2rem;\n  margin-bottom: 2rem;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.filters-grid {\n  display: flex;\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n  flex-wrap: wrap;\n  align-items: end;\n}\n\n.filter-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.filters-grid .filter-group {\n  min-width: 150px;\n  flex: 1;\n}\n\n.clear-filters-btn {\n  height: 42px;\n  white-space: nowrap;\n  min-width: auto !important;\n  flex: none !important;\n}\n\n.filter-group label {\n  font-weight: 500;\n  color: #2c3e50;\n  font-size: 0.9rem;\n}\n\n.filter-group input,\n.filter-group select {\n  padding: 0.75rem;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  transition: border-color 0.2s ease;\n}\n\n.filter-group input:focus,\n.filter-group select:focus {\n  outline: none;\n  border-color: #3498db;\n}\n\n.search-input {\n  position: relative;\n}\n\n.search-icon {\n  position: absolute;\n  left: 0.75rem;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #6c757d;\n}\n\n.search-input input {\n  padding-left: 2.5rem;\n}\n\n.filters-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 1.5rem;\n  border-top: 1px solid #e9ecef;\n}\n\n.results-info {\n  color: #6c757d;\n  font-size: 0.9rem;\n}\n\n/* Content Area */\n.persons-content {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.error-message {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  background-color: #fee;\n  color: #c33;\n  border-left: 4px solid #c33;\n}\n\n.loading-state,\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  color: #6c757d;\n}\n\n.loading-state svg,\n.empty-state svg {\n  margin-bottom: 1rem;\n  color: #3498db;\n}\n\n.spinning {\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n.empty-state h3 {\n  margin: 0 0 0.5rem 0;\n  color: #2c3e50;\n}\n\n.empty-state p {\n  margin: 0;\n  text-align: center;\n}\n\n/* Table Controls */\n.table-controls {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.table-info label {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.9rem;\n  color: #6c757d;\n}\n\n.page-size-control label {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.9rem;\n  color: #6c757d;\n}\n\n.page-size-control select {\n  padding: 0.25rem 0.5rem;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  font-size: 0.9rem;\n}\n\n/* Table */\n.persons-table-container {\n  overflow-x: auto;\n}\n\n.persons-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.persons-table th,\n.persons-table td {\n  padding: 1rem;\n  text-align: left;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.persons-table th {\n  background-color: #f8f9fa;\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 0.9rem;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n\n.persons-table th.sortable {\n  cursor: pointer;\n  user-select: none;\n  position: relative;\n}\n\n.persons-table th.sortable:hover {\n  background-color: #e9ecef;\n}\n\n.sort-indicator {\n  margin-left: 0.5rem;\n  font-size: 0.8rem;\n}\n\n.persons-table tr:hover {\n  background-color: #f8f9fa;\n}\n\n.persons-table tr.selected {\n  background-color: #e3f2fd;\n}\n\n.person-name strong {\n  display: block;\n  color: #2c3e50;\n  font-size: 0.95rem;\n}\n\n.person-name small {\n  display: block;\n  color: #6c757d;\n  font-size: 0.8rem;\n  margin-top: 0.25rem;\n}\n\n.contact-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.contact-item {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.85rem;\n  color: #6c757d;\n}\n\n.hierarchy-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.hierarchy-info div {\n  font-weight: 500;\n  color: #2c3e50;\n  font-size: 0.9rem;\n}\n\n.hierarchy-info small {\n  color: #6c757d;\n  font-size: 0.8rem;\n}\n\n.location-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.location-item {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.85rem;\n  color: #6c757d;\n}\n\n.nature-badge {\n  display: inline-block;\n  padding: 0.25rem 0.75rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 500;\n  text-transform: uppercase;\n}\n\n.nature-badge.nature-1 {\n  background-color: #e8f5e8;\n  color: #2e7d32;\n}\n\n.nature-badge.nature-2 {\n  background-color: #e3f2fd;\n  color: #1976d2;\n}\n\n.nature-badge.nature-3 {\n  background-color: #fff3e0;\n  color: #f57c00;\n}\n\n.nature-badge.nature-4 {\n  background-color: #fce4ec;\n  color: #c2185b;\n}\n\n.star-rating {\n  display: flex;\n  gap: 0.125rem;\n}\n\n.star {\n  color: #e0e0e0;\n  font-size: 0.9rem;\n}\n\n.star.filled {\n  color: #ffc107;\n}\n\n.no-rating {\n  color: #6c757d;\n  font-size: 0.8rem;\n  font-style: italic;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.btn-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 2rem;\n  height: 2rem;\n  border: none;\n  border-radius: 6px;\n  background-color: #f8f9fa;\n  color: #6c757d;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.btn-icon:hover {\n  background-color: #e9ecef;\n  color: #495057;\n}\n\n.btn-icon.danger:hover {\n  background-color: #dc3545;\n  color: white;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .persons-view {\n    padding: 1rem;\n  }\n\n  .persons-header {\n    flex-direction: column;\n    gap: 1.5rem;\n    align-items: stretch;\n  }\n\n  .header-filters {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .header-filters .filter-group select {\n    min-width: auto;\n    width: 100%;\n  }\n\n  .header-filters .search-input input {\n    min-width: auto;\n    width: 100%;\n  }\n\n  .header-actions {\n    justify-content: flex-start;\n    flex-wrap: wrap;\n  }\n\n  .filters-grid {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .filters-actions {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n\n  .table-controls {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n\n  .persons-table {\n    font-size: 0.8rem;\n  }\n\n  .persons-table th,\n  .persons-table td {\n    padding: 0.5rem;\n  }\n}\n"], "names": [], "sourceRoot": ""}