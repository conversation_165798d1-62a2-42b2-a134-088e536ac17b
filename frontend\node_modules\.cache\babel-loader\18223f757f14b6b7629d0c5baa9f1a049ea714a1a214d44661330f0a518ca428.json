{"ast": null, "code": "import { useState, useEffect } from 'react';\nimport { inView } from '../render/dom/viewport/index.mjs';\nfunction useInView(ref) {\n  let {\n    root,\n    margin,\n    amount,\n    once = false\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const [isInView, setInView] = useState(false);\n  useEffect(() => {\n    if (!ref.current || once && isInView) return;\n    const onEnter = () => {\n      setInView(true);\n      return once ? undefined : () => setInView(false);\n    };\n    const options = {\n      root: root && root.current || undefined,\n      margin,\n      amount\n    };\n    return inView(ref.current, onEnter, options);\n  }, [root, ref, margin, once, amount]);\n  return isInView;\n}\nexport { useInView };", "map": {"version": 3, "names": ["useState", "useEffect", "inView", "useInView", "ref", "root", "margin", "amount", "once", "arguments", "length", "undefined", "isInView", "setInView", "current", "onEnter", "options"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/utils/use-in-view.mjs"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { inView } from '../render/dom/viewport/index.mjs';\n\nfunction useInView(ref, { root, margin, amount, once = false } = {}) {\n    const [isInView, setInView] = useState(false);\n    useEffect(() => {\n        if (!ref.current || (once && isInView))\n            return;\n        const onEnter = () => {\n            setInView(true);\n            return once ? undefined : () => setInView(false);\n        };\n        const options = {\n            root: (root && root.current) || undefined,\n            margin,\n            amount,\n        };\n        return inView(ref.current, onEnter, options);\n    }, [root, ref, margin, once, amount]);\n    return isInView;\n}\n\nexport { useInView };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,MAAM,QAAQ,kCAAkC;AAEzD,SAASC,SAASA,CAACC,GAAG,EAA+C;EAAA,IAA7C;IAAEC,IAAI;IAAEC,MAAM;IAAEC,MAAM;IAAEC,IAAI,GAAG;EAAM,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC/D,MAAM,CAACG,QAAQ,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7CC,SAAS,CAAC,MAAM;IACZ,IAAI,CAACG,GAAG,CAACU,OAAO,IAAKN,IAAI,IAAII,QAAS,EAClC;IACJ,MAAMG,OAAO,GAAGA,CAAA,KAAM;MAClBF,SAAS,CAAC,IAAI,CAAC;MACf,OAAOL,IAAI,GAAGG,SAAS,GAAG,MAAME,SAAS,CAAC,KAAK,CAAC;IACpD,CAAC;IACD,MAAMG,OAAO,GAAG;MACZX,IAAI,EAAGA,IAAI,IAAIA,IAAI,CAACS,OAAO,IAAKH,SAAS;MACzCL,MAAM;MACNC;IACJ,CAAC;IACD,OAAOL,MAAM,CAACE,GAAG,CAACU,OAAO,EAAEC,OAAO,EAAEC,OAAO,CAAC;EAChD,CAAC,EAAE,CAACX,IAAI,EAAED,GAAG,EAAEE,MAAM,EAAEE,IAAI,EAAED,MAAM,CAAC,CAAC;EACrC,OAAOK,QAAQ;AACnB;AAEA,SAAST,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}