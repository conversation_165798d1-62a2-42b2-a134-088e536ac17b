using CrmApi.Models.Person;

namespace CrmApi.Repositories.Person
{
    public interface IPersonRepository
    {
        // Basic CRUD Operations
        Task<IEnumerable<Models.Person.Person>> GetAllAsync(bool includeDeleted = false);
        Task<Models.Person.Person?> GetByIdAsync(int id, bool includeDeleted = false);
        Task<Models.Person.Person?> GetByIdWithRelationsAsync(int id, bool includeDeleted = false);
        Task<Models.Person.Person> CreateAsync(Models.Person.Person person);
        Task<Models.Person.Person> UpdateAsync(Models.Person.Person person);
        Task<bool> DeleteAsync(int id);
        Task<bool> SoftDeleteAsync(int id);
        Task<bool> RestoreAsync(int id);
        Task<bool> ExistsAsync(int id, bool includeDeleted = false);

        // Search and Filter Operations
        Task<(IEnumerable<Models.Person.Person> persons, int totalCount)> SearchAsync(PersonSearchRequest request);
        Task<IEnumerable<Models.Person.Person>> GetByDivisionAsync(int divisionId, bool includeDeleted = false);
        Task<IEnumerable<Models.Person.Person>> GetByCategoryAsync(int categoryId, bool includeDeleted = false);
        Task<IEnumerable<Models.Person.Person>> GetByFirmNatureAsync(int firmNatureId, bool includeDeleted = false);
        Task<IEnumerable<Models.Person.Person>> GetByMobileNumberAsync(string mobileNumber, bool includeDeleted = false);
        Task<Models.Person.Person?> GetByMobileNumberExactAsync(string mobileNumber, bool includeDeleted = false);

        // Validation Operations
        Task<bool> MobileNumberExistsAsync(string mobileNumber, int? excludeId = null, bool includeDeleted = false);
        Task<bool> EmailExistsAsync(string email, int? excludeId = null, bool includeDeleted = false);
        Task<bool> MobileNumberExistsInDivisionCategoryAsync(string mobileNumber, int divisionId, int categoryId, int? excludeId = null, bool includeDeleted = false);

        // Statistics Operations
        Task<int> GetCountByDivisionAsync(int divisionId, bool includeDeleted = false);
        Task<int> GetCountByCategoryAsync(int categoryId, bool includeDeleted = false);
        Task<int> GetCountByNatureAsync(PersonNature nature, bool includeDeleted = false);
        Task<decimal> GetAverageStarRatingAsync(bool includeDeleted = false);
        Task<decimal> GetTotalTransactionValueAsync(bool includeDeleted = false);

        // Bulk Operations
        Task<IEnumerable<Models.Person.Person>> CreateBulkAsync(IEnumerable<Models.Person.Person> persons);
        Task<bool> SoftDeleteBulkAsync(IEnumerable<int> ids);
        Task<bool> RestoreBulkAsync(IEnumerable<int> ids);
    }
}
