{"ast": null, "code": "import React,{useState,useEffect}from'react';import{PersonNatureLabels,GenderLabels,WorkingProfileLabels,getDefaultPersonData}from'../../constants/personConstants';import formConfigService from'../../services/formConfigService';import apiService from'../../services/apiService';import Form<PERSON>ield from'./FormField';import'./DynamicPersonForm.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DynamicPersonForm=_ref=>{let{onSubmit,onCancel,initialData=null,mode='create'}=_ref;// Hierarchy state\nconst[divisions,setDivisions]=useState([]);const[categories,setCategories]=useState([]);const[firmNatures,setFirmNatures]=useState([]);// Selection state\nconst[selectedDivision,setSelectedDivision]=useState('');const[selectedCategory,setSelectedCategory]=useState('');const[selectedFirmNature,setSelectedFirmNature]=useState('');// Form state\nconst[formConfig,setFormConfig]=useState(null);const[formData,setFormData]=useState(getDefaultPersonData());const[errors,setErrors]=useState({});// Loading states\nconst[loading,setLoading]=useState({divisions:false,categories:false,firmNatures:false,form:false});const[submitting,setSubmitting]=useState(false);// Form availability state\nconst[formAvailability,setFormAvailability]=useState({categoryHasForm:false,firmNatureHasForm:false,showFirmNatureDropdown:false,message:''});// Load divisions on component mount\nuseEffect(()=>{loadDivisions();// Test API connectivity\ntestApiConnectivity();// Load initial data if provided\nif(initialData){var _initialData$division,_initialData$category,_initialData$firmNatu;setFormData(initialData);setSelectedDivision(((_initialData$division=initialData.divisionId)===null||_initialData$division===void 0?void 0:_initialData$division.toString())||'');setSelectedCategory(((_initialData$category=initialData.categoryId)===null||_initialData$category===void 0?void 0:_initialData$category.toString())||'');setSelectedFirmNature(((_initialData$firmNatu=initialData.firmNatureId)===null||_initialData$firmNatu===void 0?void 0:_initialData$firmNatu.toString())||'');}else{// Load default form for new person creation\nconst defaultForm=formConfigService.getDefaultFormConfig();// Ensure no duplicate fields in default form\nif(defaultForm.fields){defaultForm.fields=deduplicateFields(defaultForm.fields);}setFormConfig(defaultForm);}},[initialData]);// Test API connectivity\nconst testApiConnectivity=async()=>{try{await apiService.getDivisions();}catch(error){console.error('API connectivity test failed:',error);}};// Deduplicate fields based on field key\nconst deduplicateFields=fields=>{const seen=new Set();const deduplicated=[];fields.forEach(field=>{if(!seen.has(field.key)){seen.add(field.key);deduplicated.push(field);}else{console.warn(`DynamicPersonForm: Removing duplicate field: ${field.key}`);}});if(fields.length!==deduplicated.length){console.log(`DynamicPersonForm: Deduplicated ${fields.length} fields to ${deduplicated.length} unique fields`);}return deduplicated;};// Load categories when division changes\nuseEffect(()=>{if(selectedDivision){loadCategories(selectedDivision);// Reset category and firm nature when division changes\nsetSelectedCategory('');setSelectedFirmNature('');setFirmNatures([]);setFormConfig(null);setFormAvailability({categoryHasForm:false,firmNatureHasForm:false,showFirmNatureDropdown:false,message:''});}else{setCategories([]);setFirmNatures([]);setSelectedCategory('');setSelectedFirmNature('');setFormConfig(null);}},[selectedDivision]);// Handle category selection and form loading\nuseEffect(()=>{if(selectedCategory){handleCategorySelection(selectedCategory);// Reset firm nature when category changes\nsetSelectedFirmNature('');}else{setFirmNatures([]);setSelectedFirmNature('');setFormConfig(null);setFormAvailability({categoryHasForm:false,firmNatureHasForm:false,showFirmNatureDropdown:false,message:''});}},[selectedCategory]);// Handle firm nature selection and form loading\nuseEffect(()=>{if(selectedFirmNature){handleFirmNatureSelection(selectedFirmNature);}},[selectedFirmNature]);// Load divisions from API\nconst loadDivisions=async()=>{setLoading(prev=>({...prev,divisions:true}));try{const response=await apiService.getDivisions();setDivisions(response.data||[]);}catch(error){console.error('Error loading divisions:',error);setErrors(prev=>({...prev,divisions:'Failed to load divisions'}));}finally{setLoading(prev=>({...prev,divisions:false}));}};// Load categories by division\nconst loadCategories=async divisionId=>{setLoading(prev=>({...prev,categories:true}));try{const response=await apiService.getCategoriesByDivision(divisionId);setCategories(response.data||[]);}catch(error){console.error('Error loading categories:',error);setErrors(prev=>({...prev,categories:'Failed to load categories'}));setCategories([]);}finally{setLoading(prev=>({...prev,categories:false}));}};// Load firm natures by category\nconst loadFirmNatures=async categoryId=>{setLoading(prev=>({...prev,firmNatures:true}));try{const response=await apiService.getFirmNaturesByCategory(categoryId);setFirmNatures(response.data||[]);}catch(error){console.error('Error loading firm natures:',error);setErrors(prev=>({...prev,firmNatures:'Failed to load firm natures'}));setFirmNatures([]);}finally{setLoading(prev=>({...prev,firmNatures:false}));}};// Handle category selection and form loading logic\nconst handleCategorySelection=async categoryId=>{setLoading(prev=>({...prev,form:true}));try{// Check if category has a form\nconst categoryHasForm=formConfigService.hasFormForCategory(parseInt(categoryId));if(categoryHasForm){// Load category form and hide firm nature dropdown\nconst categoryForm=formConfigService.loadFormConfig('category',parseInt(categoryId));// Deduplicate fields in loaded form\nif(categoryForm&&categoryForm.fields){categoryForm.fields=deduplicateFields(categoryForm.fields);}setFormConfig(categoryForm);setFormAvailability({categoryHasForm:true,firmNatureHasForm:false,showFirmNatureDropdown:false,message:'Using category-specific form'});}else{// No category form, show firm nature dropdown and load firm natures\nawait loadFirmNatures(categoryId);// Load default form as fallback\nconst defaultForm=formConfigService.getDefaultFormConfig();// Deduplicate fields in default form\nif(defaultForm.fields){defaultForm.fields=deduplicateFields(defaultForm.fields);}setFormConfig(defaultForm);setFormAvailability({categoryHasForm:false,firmNatureHasForm:false,showFirmNatureDropdown:true,message:'Using default form. You can select a firm nature if available.'});}}catch(error){console.error('Error handling category selection:',error);setErrors(prev=>({...prev,form:'Error loading form configuration'}));}finally{setLoading(prev=>({...prev,form:false}));}};// Handle firm nature selection and form loading logic\nconst handleFirmNatureSelection=async firmNatureId=>{setLoading(prev=>({...prev,form:true}));try{// Check if firm nature has a form\nconst firmNatureHasForm=formConfigService.hasFormForSubCategory(parseInt(firmNatureId));if(firmNatureHasForm){// Load firm nature form\nconst firmNatureForm=formConfigService.loadFormConfig('subcategory',parseInt(firmNatureId));// Deduplicate fields in loaded form\nif(firmNatureForm&&firmNatureForm.fields){firmNatureForm.fields=deduplicateFields(firmNatureForm.fields);}setFormConfig(firmNatureForm);setFormAvailability({categoryHasForm:false,firmNatureHasForm:true,showFirmNatureDropdown:true,message:'Using firm nature-specific form'});}else{// No firm nature form available, use default form\nconst defaultForm=formConfigService.getDefaultFormConfig();// Deduplicate fields in default form\nif(defaultForm.fields){defaultForm.fields=deduplicateFields(defaultForm.fields);}setFormConfig(defaultForm);setFormAvailability({categoryHasForm:false,firmNatureHasForm:false,showFirmNatureDropdown:true,message:'No specific form found for this firm nature. Using default form.'});}}catch(error){console.error('Error handling firm nature selection:',error);setErrors(prev=>({...prev,form:'Error loading form configuration'}));}finally{setLoading(prev=>({...prev,form:false}));}};// Handle dropdown changes\nconst handleDivisionChange=e=>{const value=e.target.value;setSelectedDivision(value);// Update form data\nsetFormData(prev=>({...prev,divisionId:value?parseInt(value):null,categoryId:null,firmNatureId:null}));};const handleCategoryChange=e=>{const value=e.target.value;setSelectedCategory(value);// Update form data\nsetFormData(prev=>({...prev,categoryId:value?parseInt(value):null,firmNatureId:null}));};const handleFirmNatureChange=e=>{const value=e.target.value;setSelectedFirmNature(value);// Update form data\nsetFormData(prev=>({...prev,firmNatureId:value?parseInt(value):null}));};const handleFieldChange=(fieldKey,value)=>{setFormData(prev=>({...prev,[fieldKey]:value}));// Clear field error when user starts typing\nif(errors[fieldKey]){setErrors(prev=>({...prev,[fieldKey]:null}));}};const validateForm=()=>{const newErrors={};// Validate hierarchy selection (required by backend)\nif(!selectedDivision){newErrors.division='Division is required';}else if(parseInt(selectedDivision)<1){newErrors.division='Division ID must be a positive number';}if(!selectedCategory){newErrors.category='Category is required';}else if(parseInt(selectedCategory)<1){newErrors.category='Category ID must be a positive number';}if(selectedFirmNature&&parseInt(selectedFirmNature)<1){newErrors.firmNature='Firm Nature ID must be a positive number';}// Validate required fields that match backend requirements\nif(!formData.name||formData.name.trim()===''){newErrors.name='Name is required';}else if(formData.name.length>255){newErrors.name='Name cannot exceed 255 characters';}if(!formData.mobileNumber||formData.mobileNumber.trim()===''){newErrors.mobileNumber='Mobile number is required';}else{// Validate mobile number format (matches backend regex)\nconst mobileRegex=/^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;if(!mobileRegex.test(formData.mobileNumber)){newErrors.mobileNumber='Invalid mobile number format';}}if(!formData.nature||formData.nature===''||formData.nature==='0'){newErrors.nature='Nature is required';}else if(![1,2,3,4].includes(parseInt(formData.nature))){newErrors.nature='Invalid nature value';}// Check if form is available (should always have default form as fallback)\nif(!formConfig){newErrors.form='Form configuration not loaded. Please try again.';return{isValid:false,errors:newErrors};}// Validate form fields\nformConfig.fields.forEach(field=>{const value=formData[field.key];// Required field validation\nif(field.required&&(!value||typeof value==='string'&&value.trim()==='')){newErrors[field.key]=`${field.label} is required`;return;}// Conditional field validation\nif(field.conditional&&shouldShowField(field)){if(field.required&&(!value||typeof value==='string'&&value.trim()==='')){newErrors[field.key]=`${field.label} is required`;return;}}// Type-specific validation\nif(value&&typeof value==='string'&&value.trim()!==''){var _field$validation,_field$validation2,_field$validation3;switch(field.type){case'email':const emailRegex=/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;if(!emailRegex.test(value)){newErrors[field.key]='Please enter a valid email address';}break;case'tel':const phoneRegex=/^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;if(!phoneRegex.test(value)){newErrors[field.key]='Please enter a valid mobile number';}break;case'url':try{new URL(value);}catch{newErrors[field.key]='Please enter a valid URL';}break;case'number':if(field.validation){const numValue=parseFloat(value);if(field.validation.min!==undefined&&numValue<field.validation.min){newErrors[field.key]=`Value must be at least ${field.validation.min}`;}if(field.validation.max!==undefined&&numValue>field.validation.max){newErrors[field.key]=`Value must be at most ${field.validation.max}`;}}break;}// Pattern validation\nif((_field$validation=field.validation)!==null&&_field$validation!==void 0&&_field$validation.pattern){const regex=new RegExp(field.validation.pattern);if(!regex.test(value)){newErrors[field.key]=`${field.label} format is invalid`;}}// Length validation\nif((_field$validation2=field.validation)!==null&&_field$validation2!==void 0&&_field$validation2.minLength&&value.length<field.validation.minLength){newErrors[field.key]=`${field.label} must be at least ${field.validation.minLength} characters`;}if((_field$validation3=field.validation)!==null&&_field$validation3!==void 0&&_field$validation3.maxLength&&value.length>field.validation.maxLength){newErrors[field.key]=`${field.label} must be at most ${field.validation.maxLength} characters`;}// Backend-specific field validations\nif(field.key==='primaryEmailId'&&value.length>255){newErrors[field.key]='Email cannot exceed 255 characters';}if(field.key==='workingState'&&value.length>100){newErrors[field.key]='Working state cannot exceed 100 characters';}if(field.key==='domesticState'&&value.length>100){newErrors[field.key]='Domestic state cannot exceed 100 characters';}if(field.key==='district'&&value.length>100){newErrors[field.key]='District cannot exceed 100 characters';}if(field.key==='address'&&value.length>500){newErrors[field.key]='Address cannot exceed 500 characters';}if(field.key==='workingArea'&&value.length>200){newErrors[field.key]='Working area cannot exceed 200 characters';}if(field.key==='associateName'&&value.length>255){newErrors[field.key]='Associate name cannot exceed 255 characters';}if(field.key==='associateRelation'&&value.length>100){newErrors[field.key]='Associate relation cannot exceed 100 characters';}if(field.key==='reraRegistrationNumber'&&value.length>50){newErrors[field.key]='RERA registration number cannot exceed 50 characters';}if(field.key==='source'&&value.length>200){newErrors[field.key]='Source cannot exceed 200 characters';}if(field.key==='remarks'&&value.length>1000){newErrors[field.key]='Remarks cannot exceed 1000 characters';}if(field.key==='firmName'&&value.length>255){newErrors[field.key]='Firm name cannot exceed 255 characters';}if(field.key==='authorizedPersonName'&&value.length>255){newErrors[field.key]='Authorized person name cannot exceed 255 characters';}if(field.key==='authorizedPersonEmail'&&value.length>255){newErrors[field.key]='Authorized person email cannot exceed 255 characters';}if(field.key==='designation'&&value.length>100){newErrors[field.key]='Designation cannot exceed 100 characters';}if(field.key==='marketingContact'&&value.length>255){newErrors[field.key]='Marketing contact cannot exceed 255 characters';}if(field.key==='marketingDesignation'&&value.length>100){newErrors[field.key]='Marketing designation cannot exceed 100 characters';}if(field.key==='placeOfPosting'&&value.length>200){newErrors[field.key]='Place of posting cannot exceed 200 characters';}if(field.key==='department'&&value.length>100){newErrors[field.key]='Department cannot exceed 100 characters';}}// Validate numeric fields\nif(field.type==='number'&&value){const numValue=parseFloat(value);if(field.key==='starRating'&&(numValue<1||numValue>5)){newErrors[field.key]='Star rating must be between 1 and 5';}if(['numberOfOffices','numberOfBranches','totalEmployeeStrength'].includes(field.key)&&numValue<0){newErrors[field.key]=`${field.label} must be non-negative`;}if(field.key==='transactionValue'&&numValue<0){newErrors[field.key]='Transaction value must be non-negative';}}// Validate associate mobile number format\nif(field.key==='associateMobile'&&value&&value.trim()!==''){const mobileRegex=/^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;if(!mobileRegex.test(value)){newErrors[field.key]='Invalid associate mobile number format';}}});// Business logic validation\nif(formData.isMarried&&formData.dateOfMarriage&&formData.dateOfBirth){const birthDate=new Date(formData.dateOfBirth);const marriageDate=new Date(formData.dateOfMarriage);if(marriageDate<=birthDate){newErrors.dateOfMarriage='Marriage date must be after birth date';}}return{isValid:Object.keys(newErrors).length===0,errors:newErrors};};const handleSubmit=async e=>{e.preventDefault();// Debug: Basic form data validation\nconsole.log('Form submission - validation-sensitive fields:',{primaryEmailId:formData.primaryEmailId,website:formData.website,websiteLink:formData.websiteLink,crmAppLink:formData.crmAppLink,authorizedPersonEmail:formData.authorizedPersonEmail,associateMobile:formData.associateMobile});const validation=validateForm();if(!validation.isValid){setErrors(validation.errors);return;}setSubmitting(true);try{// Prepare data for API - match backend model exactly (PascalCase field names)\nconst submitData={// Required hierarchy fields\nDivisionId:parseInt(selectedDivision),CategoryId:parseInt(selectedCategory),FirmNatureId:selectedFirmNature?parseInt(selectedFirmNature):null,// Required fields\nName:formData.name||'',MobileNumber:formData.mobileNumber||'',Nature:formData.nature?parseInt(formData.nature):1,// Default to Business (1) if not provided\n// Optional enum fields\nGender:formData.gender?parseInt(formData.gender):null,// Contact Information\nAlternateNumbers:Array.isArray(formData.alternateNumbers)?formData.alternateNumbers.filter(num=>num&&num.trim()!==''):formData.alternateNumbers?formData.alternateNumbers.split(',').map(s=>s.trim()).filter(s=>s!==''):[],AlternateEmailIds:Array.isArray(formData.alternateEmailIds)?formData.alternateEmailIds.filter(email=>email&&email.trim()!==''):formData.alternateEmailIds?formData.alternateEmailIds.split(',').map(s=>s.trim()).filter(s=>s!==''):[],// Note: PrimaryEmailId is added conditionally below only if it has a valid value\n// Personal Information\nDateOfBirth:formData.dateOfBirth||null,IsMarried:Boolean(formData.isMarried),DateOfMarriage:formData.dateOfMarriage||null,// Location Information\nWorkingState:formData.workingState||'',DomesticState:formData.domesticState||'',District:formData.district||'',Address:formData.address||'',WorkingArea:formData.workingArea||'',// Associate Information\nHasAssociate:Boolean(formData.hasAssociate),AssociateName:formData.associateName||'',AssociateRelation:formData.associateRelation||'',// Note: AssociateMobile is added conditionally below only if it has a valid value\n// Digital Presence\nUsingWebsite:Boolean(formData.usingWebsite),UsingCRMApp:Boolean(formData.usingCRMApp),// Note: Website, WebsiteLink, CRMAppLink are added conditionally below only if they have valid values\n// Business Information\nTransactionValue:formData.transactionValue?parseFloat(formData.transactionValue):null,RERARegistrationNumber:formData.reraRegistrationNumber||'',WorkingProfiles:Array.isArray(formData.workingProfiles)?formData.workingProfiles.map(wp=>parseInt(wp)).filter(wp=>!isNaN(wp)):[],StarRating:formData.starRating?parseInt(formData.starRating):null,Source:formData.source||'',Remarks:formData.remarks||'',// Company Information\nFirmName:formData.firmName||'',NumberOfOffices:formData.numberOfOffices&&!isNaN(parseInt(formData.numberOfOffices))?parseInt(formData.numberOfOffices):null,NumberOfBranches:formData.numberOfBranches&&!isNaN(parseInt(formData.numberOfBranches))?parseInt(formData.numberOfBranches):null,TotalEmployeeStrength:formData.totalEmployeeStrength&&!isNaN(parseInt(formData.totalEmployeeStrength))?parseInt(formData.totalEmployeeStrength):null,// Authorized Person\nAuthorizedPersonName:formData.authorizedPersonName||'',Designation:formData.designation||'',// Note: AuthorizedPersonEmail is added conditionally below only if it has a valid value\n// Marketing Information\nMarketingContact:formData.marketingContact||'',MarketingDesignation:formData.marketingDesignation||'',PlaceOfPosting:formData.placeOfPosting||'',Department:formData.department||''};// Only add optional URL and email fields if they have valid values (not empty strings or null)\n// This prevents backend validation errors for empty strings on URL/Email fields\n// Helper function to check if a field has a valid value\n// This function is CRITICAL - it must return false for any value that would cause backend validation errors\nconst hasValidValue=value=>{return value!==null&&value!==undefined&&value!==''&&(typeof value==='string'?value.trim()!=='':true);};// Debug: Check what values we have for validation-sensitive fields\nconsole.log('=== SUBMITDATA CONSTRUCTION DEBUG ===');console.log('Validation-sensitive field values:',{primaryEmailId:formData.primaryEmailId,authorizedPersonEmail:formData.authorizedPersonEmail,website:formData.website,websiteLink:formData.websiteLink,crmAppLink:formData.crmAppLink,associateMobile:formData.associateMobile});// Test our hasValidValue function on each field\nconsole.log('hasValidValue test results:');console.log('- primaryEmailId:',hasValidValue(formData.primaryEmailId));console.log('- authorizedPersonEmail:',hasValidValue(formData.authorizedPersonEmail));console.log('- website:',hasValidValue(formData.website));console.log('- websiteLink:',hasValidValue(formData.websiteLink));console.log('- crmAppLink:',hasValidValue(formData.crmAppLink));console.log('- associateMobile:',hasValidValue(formData.associateMobile));// Email fields - only add if not empty and valid\nif(hasValidValue(formData.primaryEmailId)){console.log('Adding PrimaryEmailId:',formData.primaryEmailId);submitData.PrimaryEmailId=formData.primaryEmailId.trim();}if(hasValidValue(formData.authorizedPersonEmail)){console.log('Adding AuthorizedPersonEmail:',formData.authorizedPersonEmail);submitData.AuthorizedPersonEmail=formData.authorizedPersonEmail.trim();}// URL fields - only add if not empty and valid\nif(hasValidValue(formData.website)){console.log('Adding Website:',formData.website);submitData.Website=formData.website.trim();}if(hasValidValue(formData.websiteLink)){console.log('Adding WebsiteLink:',formData.websiteLink);submitData.WebsiteLink=formData.websiteLink.trim();}if(hasValidValue(formData.crmAppLink)){console.log('Adding CRMAppLink:',formData.crmAppLink);submitData.CRMAppLink=formData.crmAppLink.trim();}// Associate mobile - only add if not empty and valid (has regex validation)\nif(hasValidValue(formData.associateMobile)){console.log('Adding AssociateMobile:',formData.associateMobile);submitData.AssociateMobile=formData.associateMobile.trim();}// Validate required fields before submission\nconst requiredFieldsCheck={DivisionId:submitData.DivisionId,CategoryId:submitData.CategoryId,Name:submitData.Name,MobileNumber:submitData.MobileNumber,Nature:submitData.Nature};// Check for missing required fields\nconst missingFields=Object.entries(requiredFieldsCheck).filter(_ref2=>{let[,value]=_ref2;return!value||value===''||value===null;}).map(_ref3=>{let[key]=_ref3;return key;});if(missingFields.length>0){setErrors({general:'Missing required fields: '+missingFields.join(', ')});return;}// FINAL SAFETY CHECK: Remove any validation-sensitive fields that have empty values\n// This is a bulletproof approach to ensure no empty strings reach the backend\nconst validationSensitiveFields=['PrimaryEmailId','AuthorizedPersonEmail','Website','WebsiteLink','CRMAppLink','AssociateMobile'];console.log('=== FINAL SAFETY CHECK ===');console.log('Before cleanup:',Object.keys(submitData));validationSensitiveFields.forEach(fieldName=>{if(submitData.hasOwnProperty(fieldName)){const value=submitData[fieldName];const isEmpty=value===null||value===undefined||value===''||typeof value==='string'&&value.trim()==='';console.log(`Checking ${fieldName}:`,{value,isEmpty,action:isEmpty?'REMOVING':'KEEPING'});if(isEmpty){delete submitData[fieldName];console.log(`🗑️ REMOVED ${fieldName} (was: \"${value}\")`);}}});console.log('After cleanup:',Object.keys(submitData));console.log('Final submitData:',JSON.stringify(submitData,null,2));let result;if(mode==='create'){result=await apiService.createPerson(submitData);}else{result=await apiService.updatePerson(initialData.id,submitData);}onSubmit(result);}catch(error){var _error$data,_error$response,_error$response$data,_error$data3,_error$data4,_error$response3,_error$response3$data,_error$response4,_error$response4$data;console.error('Error submitting form:',error);console.error('Error details:',{status:error.status,data:error.data,response:error.response,message:error.message});// Enhanced error handling for backend validation errors\nif((_error$data=error.data)!==null&&_error$data!==void 0&&_error$data.errors||(_error$response=error.response)!==null&&_error$response!==void 0&&(_error$response$data=_error$response.data)!==null&&_error$response$data!==void 0&&_error$response$data.errors){var _error$data2,_error$response2,_error$response2$data;// Handle ASP.NET Core ModelState validation errors\nconst validationErrors=((_error$data2=error.data)===null||_error$data2===void 0?void 0:_error$data2.errors)||((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.errors);const backendErrors={};console.log('Validation errors received:',validationErrors);Object.keys(validationErrors).forEach(key=>{const errorMessages=validationErrors[key];// Map backend field names to frontend field names for display\nconst frontendFieldName=mapBackendFieldToFrontend(key);backendErrors[frontendFieldName]=Array.isArray(errorMessages)?errorMessages.join(', '):errorMessages;});setErrors(backendErrors);}else if((_error$data3=error.data)!==null&&_error$data3!==void 0&&_error$data3.title||(_error$data4=error.data)!==null&&_error$data4!==void 0&&_error$data4.detail||(_error$response3=error.response)!==null&&_error$response3!==void 0&&(_error$response3$data=_error$response3.data)!==null&&_error$response3$data!==void 0&&_error$response3$data.title||(_error$response4=error.response)!==null&&_error$response4!==void 0&&(_error$response4$data=_error$response4.data)!==null&&_error$response4$data!==void 0&&_error$response4$data.detail){var _error$data5,_error$data6,_error$response5,_error$response5$data,_error$response6,_error$response6$data;// Handle ProblemDetails format errors\nconst errorMessage=((_error$data5=error.data)===null||_error$data5===void 0?void 0:_error$data5.detail)||((_error$data6=error.data)===null||_error$data6===void 0?void 0:_error$data6.title)||((_error$response5=error.response)===null||_error$response5===void 0?void 0:(_error$response5$data=_error$response5.data)===null||_error$response5$data===void 0?void 0:_error$response5$data.detail)||((_error$response6=error.response)===null||_error$response6===void 0?void 0:(_error$response6$data=_error$response6.data)===null||_error$response6$data===void 0?void 0:_error$response6$data.title);setErrors({general:errorMessage});}else if(error.isValidationError&&error.isValidationError()){const validationErrors=error.getValidationErrors();console.log('ApiError validation errors:',validationErrors);setErrors(validationErrors);}else{var _error$data7,_error$response7,_error$response7$data,_error$data8;// Handle other error formats\nconst errorMessage=((_error$data7=error.data)===null||_error$data7===void 0?void 0:_error$data7.message)||((_error$response7=error.response)===null||_error$response7===void 0?void 0:(_error$response7$data=_error$response7.data)===null||_error$response7$data===void 0?void 0:_error$response7$data.message)||((_error$data8=error.data)===null||_error$data8===void 0?void 0:_error$data8.title)||error.message||'An error occurred while saving the person. Please check your input and try again.';setErrors({general:errorMessage});}}finally{setSubmitting(false);}};// Helper function to map backend field names to frontend field names for error display\nconst mapBackendFieldToFrontend=backendFieldName=>{const fieldMapping={'DivisionId':'division','CategoryId':'category','FirmNatureId':'firmNature','Name':'name','MobileNumber':'mobileNumber','Nature':'nature','Gender':'gender','PrimaryEmailId':'primaryEmailId','AlternateNumbers':'alternateNumbers','AlternateEmailIds':'alternateEmailIds','Website':'website','DateOfBirth':'dateOfBirth','IsMarried':'isMarried','DateOfMarriage':'dateOfMarriage','WorkingState':'workingState','DomesticState':'domesticState','District':'district','Address':'address','WorkingArea':'workingArea','HasAssociate':'hasAssociate','AssociateName':'associateName','AssociateRelation':'associateRelation','AssociateMobile':'associateMobile','UsingWebsite':'usingWebsite','WebsiteLink':'websiteLink','UsingCRMApp':'usingCRMApp','CRMAppLink':'crmAppLink','TransactionValue':'transactionValue','RERARegistrationNumber':'reraRegistrationNumber','WorkingProfiles':'workingProfiles','StarRating':'starRating','Source':'source','Remarks':'remarks','FirmName':'firmName','NumberOfOffices':'numberOfOffices','NumberOfBranches':'numberOfBranches','TotalEmployeeStrength':'totalEmployeeStrength','AuthorizedPersonName':'authorizedPersonName','AuthorizedPersonEmail':'authorizedPersonEmail','Designation':'designation','MarketingContact':'marketingContact','MarketingDesignation':'marketingDesignation','PlaceOfPosting':'placeOfPosting','Department':'department'};return fieldMapping[backendFieldName]||backendFieldName.toLowerCase();};const shouldShowField=field=>{if(!field.conditional)return true;const conditionValue=formData[field.conditional.field];const expectedValue=field.conditional.value;// Handle boolean conditions\nif(typeof expectedValue==='boolean'||expectedValue==='true'||expectedValue==='false'){return conditionValue===(expectedValue===true||expectedValue==='true');}return conditionValue===expectedValue;};const groupFieldsBySections=()=>{if(!formConfig||!formConfig.fields)return{};const sections={};formConfig.fields.forEach(field=>{const sectionKey=field.section||'general';if(!sections[sectionKey]){sections[sectionKey]={title:getSectionTitle(sectionKey),fields:[]};}sections[sectionKey].fields.push(field);});return sections;};const getSectionTitle=sectionKey=>{const titles={personalInfo:'Personal Information',contactInfo:'Contact Information',locationInfo:'Location Information',businessInfo:'Business Information',associateInfo:'Associate Information',digitalPresence:'Digital Presence',companyInfo:'Company Information',authorizedPerson:'Authorized Person',marketingInfo:'Marketing Information',general:'General Information'};return titles[sectionKey]||sectionKey;};const sections=formConfig?groupFieldsBySections():{};return/*#__PURE__*/_jsxs(\"div\",{className:\"dynamic-person-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:mode==='create'?'Create New Person':'Edit Person'}),formConfig&&/*#__PURE__*/_jsxs(\"div\",{className:\"form-info\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"form-name\",children:formConfig.name}),formConfig.description&&/*#__PURE__*/_jsx(\"span\",{className:\"form-description\",children:formConfig.description})]})]}),errors.general&&/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-error\",children:errors.general}),Object.keys(errors).length>0&&!errors.general&&/*#__PURE__*/_jsxs(\"div\",{className:\"alert alert-error\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Please fix the following errors:\"}),/*#__PURE__*/_jsx(\"ul\",{style:{margin:'8px 0 0 20px'},children:Object.entries(errors).filter(_ref4=>{let[key]=_ref4;return key!=='general';}).map(_ref5=>{let[key,message]=_ref5;return/*#__PURE__*/_jsx(\"li\",{children:message},key);})})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"person-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Division & Category Selection\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"form-label\",children:[\"Division \",/*#__PURE__*/_jsx(\"span\",{className:\"required\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"select\",{value:selectedDivision,onChange:handleDivisionChange,disabled:loading.divisions,className:`form-select ${errors.division?'error':''}`,required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:loading.divisions?'Loading divisions...':'Select Division'}),divisions.map(division=>/*#__PURE__*/_jsx(\"option\",{value:division.id,children:division.name},division.id))]}),errors.division&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:errors.division}),errors.divisions&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:errors.divisions})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"form-label\",children:[\"Category \",/*#__PURE__*/_jsx(\"span\",{className:\"required\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"select\",{value:selectedCategory,onChange:handleCategoryChange,disabled:!selectedDivision||loading.categories,className:`form-select ${errors.category?'error':''}`,required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:!selectedDivision?'Select Division first':loading.categories?'Loading categories...':'Select Category'}),categories.map(category=>/*#__PURE__*/_jsx(\"option\",{value:category.id,children:category.name},category.id))]}),errors.category&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:errors.category}),errors.categories&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:errors.categories})]}),formAvailability.showFirmNatureDropdown&&/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"form-label\",children:\"Firm Nature (Required)\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedFirmNature,onChange:handleFirmNatureChange,disabled:!selectedCategory||loading.firmNatures,className:\"form-select\",required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:!selectedCategory?'Select Category first':loading.firmNatures?'Loading firm natures...':'Select Firm Nature (Required)'}),firmNatures.map(firmNature=>/*#__PURE__*/_jsx(\"option\",{value:firmNature.id,children:firmNature.name},firmNature.id))]}),errors.firmNatures&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:errors.firmNatures})]}),loading.form&&/*#__PURE__*/_jsx(\"div\",{className:\"loading-message\",children:/*#__PURE__*/_jsx(\"span\",{children:\"Loading form configuration...\"})}),formAvailability.message&&!loading.form&&/*#__PURE__*/_jsx(\"div\",{className:`status-message ${formAvailability.categoryHasForm||formAvailability.firmNatureHasForm?'success':formAvailability.message.includes('No form')?'error':'info'}`,children:formAvailability.message}),errors.form&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:errors.form})]}),formConfig&&Object.entries(sections).map(_ref6=>{let[sectionKey,section]=_ref6;return/*#__PURE__*/_jsxs(\"div\",{className:\"form-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:section.title}),/*#__PURE__*/_jsx(\"div\",{className:\"form-fields\",children:section.fields.filter(field=>shouldShowField(field)).map((field,fieldIndex)=>/*#__PURE__*/_jsx(FormField,{field:field,value:formData[field.key],onChange:value=>handleFieldChange(field.key,value),error:errors[field.key]},`${sectionKey}-${field.key}-${fieldIndex}`))})]},sectionKey);}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:onCancel,className:\"btn btn-outline\",disabled:submitting,children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"btn btn-primary\",disabled:submitting||!selectedDivision||!selectedCategory,children:submitting?mode==='create'?'Creating...':'Updating...':mode==='create'?'Create Person':'Update Person'})]})]})]});};export default DynamicPersonForm;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PersonNatureLabels", "<PERSON><PERSON><PERSON><PERSON>", "WorkingProfileLabels", "getDefaultPersonData", "formConfigService", "apiService", "FormField", "jsx", "_jsx", "jsxs", "_jsxs", "DynamicPersonForm", "_ref", "onSubmit", "onCancel", "initialData", "mode", "divisions", "setDivisions", "categories", "setCategories", "firmNatures", "setFirmNatures", "selectedDivision", "setSelectedDivision", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedFirmNature", "setSelectedFirmNature", "formConfig", "setFormConfig", "formData", "setFormData", "errors", "setErrors", "loading", "setLoading", "form", "submitting", "setSubmitting", "formAvailability", "setFormAvailability", "categoryHasForm", "firmNatureHasForm", "showFirmNatureDropdown", "message", "loadDivisions", "testApiConnectivity", "_initialData$division", "_initialData$category", "_initialData$firmNatu", "divisionId", "toString", "categoryId", "firmNatureId", "defaultForm", "getDefaultFormConfig", "fields", "deduplicateFields", "getDivisions", "error", "console", "seen", "Set", "deduplicated", "for<PERSON>ach", "field", "has", "key", "add", "push", "warn", "length", "log", "loadCategories", "handleCategorySelection", "handleFirmNatureSelection", "prev", "response", "data", "getCategoriesByDivision", "loadFirmNatures", "getFirmNaturesByCategory", "hasFormForCategory", "parseInt", "categoryForm", "loadFormConfig", "hasFormForSubCategory", "firmNatureForm", "handleDivisionChange", "e", "value", "target", "handleCategoryChange", "handleFirmNatureChange", "handleFieldChange", "<PERSON><PERSON><PERSON>", "validateForm", "newErrors", "division", "category", "firmNature", "name", "trim", "mobileNumber", "mobileRegex", "test", "nature", "includes", "<PERSON><PERSON><PERSON><PERSON>", "required", "label", "conditional", "shouldShowField", "_field$validation", "_field$validation2", "_field$validation3", "type", "emailRegex", "phoneRegex", "URL", "validation", "numValue", "parseFloat", "min", "undefined", "max", "pattern", "regex", "RegExp", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dateOfMarriage", "dateOfBirth", "birthDate", "Date", "marriageDate", "Object", "keys", "handleSubmit", "preventDefault", "primaryEmailId", "website", "websiteLink", "crmAppLink", "authorizedPersonEmail", "associate<PERSON><PERSON><PERSON>", "submitData", "DivisionId", "CategoryId", "FirmNatureId", "Name", "MobileNumber", "Nature", "Gender", "gender", "AlternateNumbers", "Array", "isArray", "alternateNumbers", "filter", "num", "split", "map", "s", "AlternateEmailIds", "alternateEmailIds", "email", "DateOfBirth", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "DateOfMarriage", "WorkingState", "workingState", "DomesticState", "domesticState", "District", "district", "Address", "address", "WorkingArea", "workingArea", "HasAssociate", "hasAssociate", "<PERSON><PERSON><PERSON>", "associate<PERSON><PERSON>", "AssociateRelation", "associateRelation", "UsingWebsite", "usingWebsite", "UsingCRMApp", "usingCRMApp", "TransactionValue", "transactionValue", "RERARegistrationNumber", "reraRegistrationNumber", "WorkingProfiles", "workingProfiles", "wp", "isNaN", "StarRating", "starRating", "Source", "source", "Remarks", "remarks", "FirmName", "firmName", "NumberOfOffices", "numberOfOffices", "NumberOfBranches", "numberOfBranches", "TotalEmployeeStrength", "totalEmployeeStrength", "AuthorizedPersonName", "authorizedPersonName", "Designation", "designation", "MarketingContact", "marketingContact", "MarketingDesignation", "marketingDesignation", "PlaceOfPosting", "placeOfPosting", "Department", "department", "hasValidValue", "PrimaryEmailId", "AuthorizedPersonEmail", "Website", "WebsiteLink", "CRMAppLink", "AssociateMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingFields", "entries", "_ref2", "_ref3", "general", "join", "validationSensitiveFields", "fieldName", "hasOwnProperty", "isEmpty", "action", "JSON", "stringify", "result", "create<PERSON>erson", "update<PERSON><PERSON>", "id", "_error$data", "_error$response", "_error$response$data", "_error$data3", "_error$data4", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data", "status", "_error$data2", "_error$response2", "_error$response2$data", "validationErrors", "backendErrors", "errorMessages", "frontendFieldName", "mapBackendFieldToFrontend", "title", "detail", "_error$data5", "_error$data6", "_error$response5", "_error$response5$data", "_error$response6", "_error$response6$data", "errorMessage", "isValidationError", "getValidationErrors", "_error$data7", "_error$response7", "_error$response7$data", "_error$data8", "backendFieldName", "fieldMapping", "toLowerCase", "conditionValue", "expectedValue", "groupFieldsBySections", "sections", "sectionKey", "section", "getSectionTitle", "titles", "personalInfo", "contactInfo", "locationInfo", "businessInfo", "associateInfo", "digitalPresence", "companyInfo", "<PERSON><PERSON><PERSON>", "marketingInfo", "className", "children", "description", "style", "margin", "_ref4", "_ref5", "onChange", "disabled", "_ref6", "fieldIndex", "onClick"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/DynamicPersonForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\n\nconst DynamicPersonForm = ({ onSubmit, onCancel, initialData = null, mode = 'create' }) => {\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [firmNatures, setFirmNatures] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedFirmNature, setSelectedFirmNature] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    firmNatures: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    firmNatureHasForm: false,\n    showFirmNatureDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Test API connectivity\n    testApiConnectivity();\n\n    // Load initial data if provided\n    if (initialData) {\n      setFormData(initialData);\n      setSelectedDivision(initialData.divisionId?.toString() || '');\n      setSelectedCategory(initialData.categoryId?.toString() || '');\n      setSelectedFirmNature(initialData.firmNatureId?.toString() || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      // Ensure no duplicate fields in default form\n      if (defaultForm.fields) {\n        defaultForm.fields = deduplicateFields(defaultForm.fields);\n      }\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Test API connectivity\n  const testApiConnectivity = async () => {\n    try {\n      await apiService.getDivisions();\n    } catch (error) {\n      console.error('API connectivity test failed:', error);\n    }\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = (fields) => {\n    const seen = new Set();\n    const deduplicated = [];\n\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      } else {\n        console.warn(`DynamicPersonForm: Removing duplicate field: ${field.key}`);\n      }\n    });\n\n    if (fields.length !== deduplicated.length) {\n      console.log(`DynamicPersonForm: Deduplicated ${fields.length} fields to ${deduplicated.length} unique fields`);\n    }\n\n    return deduplicated;\n  };\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and firm nature when division changes\n      setSelectedCategory('');\n      setSelectedFirmNature('');\n      setFirmNatures([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        firmNatureHasForm: false,\n        showFirmNatureDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setFirmNatures([]);\n      setSelectedCategory('');\n      setSelectedFirmNature('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset firm nature when category changes\n      setSelectedFirmNature('');\n    } else {\n      setFirmNatures([]);\n      setSelectedFirmNature('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        firmNatureHasForm: false,\n        showFirmNatureDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle firm nature selection and form loading\n  useEffect(() => {\n    if (selectedFirmNature) {\n      handleFirmNatureSelection(selectedFirmNature);\n    }\n  }, [selectedFirmNature]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({ ...prev, divisions: 'Failed to load divisions' }));\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async (divisionId) => {\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({ ...prev, categories: 'Failed to load categories' }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  // Load firm natures by category\n  const loadFirmNatures = async (categoryId) => {\n    setLoading(prev => ({ ...prev, firmNatures: true }));\n    try {\n      const response = await apiService.getFirmNaturesByCategory(categoryId);\n      setFirmNatures(response.data || []);\n    } catch (error) {\n      console.error('Error loading firm natures:', error);\n      setErrors(prev => ({ ...prev, firmNatures: 'Failed to load firm natures' }));\n      setFirmNatures([]);\n    } finally {\n      setLoading(prev => ({ ...prev, firmNatures: false }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async (categoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n\n      if (categoryHasForm) {\n        // Load category form and hide firm nature dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        // Deduplicate fields in loaded form\n        if (categoryForm && categoryForm.fields) {\n          categoryForm.fields = deduplicateFields(categoryForm.fields);\n        }\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          firmNatureHasForm: false,\n          showFirmNatureDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show firm nature dropdown and load firm natures\n        await loadFirmNatures(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          firmNatureHasForm: false,\n          showFirmNatureDropdown: true,\n          message: 'Using default form. You can select a firm nature if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle firm nature selection and form loading logic\n  const handleFirmNatureSelection = async (firmNatureId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if firm nature has a form\n      const firmNatureHasForm = formConfigService.hasFormForSubCategory(parseInt(firmNatureId));\n\n      if (firmNatureHasForm) {\n        // Load firm nature form\n        const firmNatureForm = formConfigService.loadFormConfig('subcategory', parseInt(firmNatureId));\n        // Deduplicate fields in loaded form\n        if (firmNatureForm && firmNatureForm.fields) {\n          firmNatureForm.fields = deduplicateFields(firmNatureForm.fields);\n        }\n        setFormConfig(firmNatureForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          firmNatureHasForm: true,\n          showFirmNatureDropdown: true,\n          message: 'Using firm nature-specific form'\n        });\n      } else {\n        // No firm nature form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          firmNatureHasForm: false,\n          showFirmNatureDropdown: true,\n          message: 'No specific form found for this firm nature. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling firm nature selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = (e) => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      firmNatureId: null\n    }));\n  };\n\n  const handleCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      firmNatureId: null\n    }));\n  };\n\n  const handleFirmNatureChange = (e) => {\n    const value = e.target.value;\n    setSelectedFirmNature(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      firmNatureId: value ? parseInt(value) : null\n    }));\n  };\n\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection (required by backend)\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    } else if (parseInt(selectedDivision) < 1) {\n      newErrors.division = 'Division ID must be a positive number';\n    }\n\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    } else if (parseInt(selectedCategory) < 1) {\n      newErrors.category = 'Category ID must be a positive number';\n    }\n\n    if (selectedFirmNature && parseInt(selectedFirmNature) < 1) {\n      newErrors.firmNature = 'Firm Nature ID must be a positive number';\n    }\n\n    // Validate required fields that match backend requirements\n    if (!formData.name || formData.name.trim() === '') {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.length > 255) {\n      newErrors.name = 'Name cannot exceed 255 characters';\n    }\n\n    if (!formData.mobileNumber || formData.mobileNumber.trim() === '') {\n      newErrors.mobileNumber = 'Mobile number is required';\n    } else {\n      // Validate mobile number format (matches backend regex)\n      const mobileRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n      if (!mobileRegex.test(formData.mobileNumber)) {\n        newErrors.mobileNumber = 'Invalid mobile number format';\n      }\n    }\n\n    if (!formData.nature || formData.nature === '' || formData.nature === '0') {\n      newErrors.nature = 'Nature is required';\n    } else if (![1, 2, 3, 4].includes(parseInt(formData.nature))) {\n      newErrors.nature = 'Invalid nature value';\n    }\n\n    // Check if form is available (should always have default form as fallback)\n    if (!formConfig) {\n      newErrors.form = 'Form configuration not loaded. Please try again.';\n      return { isValid: false, errors: newErrors };\n    }\n\n\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n      \n      // Required field validation\n      if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if (field.validation?.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if (field.validation?.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if (field.validation?.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n\n        // Backend-specific field validations\n        if (field.key === 'primaryEmailId' && value.length > 255) {\n          newErrors[field.key] = 'Email cannot exceed 255 characters';\n        }\n        if (field.key === 'workingState' && value.length > 100) {\n          newErrors[field.key] = 'Working state cannot exceed 100 characters';\n        }\n        if (field.key === 'domesticState' && value.length > 100) {\n          newErrors[field.key] = 'Domestic state cannot exceed 100 characters';\n        }\n        if (field.key === 'district' && value.length > 100) {\n          newErrors[field.key] = 'District cannot exceed 100 characters';\n        }\n        if (field.key === 'address' && value.length > 500) {\n          newErrors[field.key] = 'Address cannot exceed 500 characters';\n        }\n        if (field.key === 'workingArea' && value.length > 200) {\n          newErrors[field.key] = 'Working area cannot exceed 200 characters';\n        }\n        if (field.key === 'associateName' && value.length > 255) {\n          newErrors[field.key] = 'Associate name cannot exceed 255 characters';\n        }\n        if (field.key === 'associateRelation' && value.length > 100) {\n          newErrors[field.key] = 'Associate relation cannot exceed 100 characters';\n        }\n        if (field.key === 'reraRegistrationNumber' && value.length > 50) {\n          newErrors[field.key] = 'RERA registration number cannot exceed 50 characters';\n        }\n        if (field.key === 'source' && value.length > 200) {\n          newErrors[field.key] = 'Source cannot exceed 200 characters';\n        }\n        if (field.key === 'remarks' && value.length > 1000) {\n          newErrors[field.key] = 'Remarks cannot exceed 1000 characters';\n        }\n        if (field.key === 'firmName' && value.length > 255) {\n          newErrors[field.key] = 'Firm name cannot exceed 255 characters';\n        }\n        if (field.key === 'authorizedPersonName' && value.length > 255) {\n          newErrors[field.key] = 'Authorized person name cannot exceed 255 characters';\n        }\n        if (field.key === 'authorizedPersonEmail' && value.length > 255) {\n          newErrors[field.key] = 'Authorized person email cannot exceed 255 characters';\n        }\n        if (field.key === 'designation' && value.length > 100) {\n          newErrors[field.key] = 'Designation cannot exceed 100 characters';\n        }\n        if (field.key === 'marketingContact' && value.length > 255) {\n          newErrors[field.key] = 'Marketing contact cannot exceed 255 characters';\n        }\n        if (field.key === 'marketingDesignation' && value.length > 100) {\n          newErrors[field.key] = 'Marketing designation cannot exceed 100 characters';\n        }\n        if (field.key === 'placeOfPosting' && value.length > 200) {\n          newErrors[field.key] = 'Place of posting cannot exceed 200 characters';\n        }\n        if (field.key === 'department' && value.length > 100) {\n          newErrors[field.key] = 'Department cannot exceed 100 characters';\n        }\n      }\n\n      // Validate numeric fields\n      if (field.type === 'number' && value) {\n        const numValue = parseFloat(value);\n        if (field.key === 'starRating' && (numValue < 1 || numValue > 5)) {\n          newErrors[field.key] = 'Star rating must be between 1 and 5';\n        }\n        if (['numberOfOffices', 'numberOfBranches', 'totalEmployeeStrength'].includes(field.key) && numValue < 0) {\n          newErrors[field.key] = `${field.label} must be non-negative`;\n        }\n        if (field.key === 'transactionValue' && numValue < 0) {\n          newErrors[field.key] = 'Transaction value must be non-negative';\n        }\n      }\n\n      // Validate associate mobile number format\n      if (field.key === 'associateMobile' && value && value.trim() !== '') {\n        const mobileRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n        if (!mobileRegex.test(value)) {\n          newErrors[field.key] = 'Invalid associate mobile number format';\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    // Debug: Basic form data validation\n    console.log('Form submission - validation-sensitive fields:', {\n      primaryEmailId: formData.primaryEmailId,\n      website: formData.website,\n      websiteLink: formData.websiteLink,\n      crmAppLink: formData.crmAppLink,\n      authorizedPersonEmail: formData.authorizedPersonEmail,\n      associateMobile: formData.associateMobile\n    });\n\n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSubmitting(true);\n    try {\n      // Prepare data for API - match backend model exactly (PascalCase field names)\n      const submitData = {\n        // Required hierarchy fields\n        DivisionId: parseInt(selectedDivision),\n        CategoryId: parseInt(selectedCategory),\n        FirmNatureId: selectedFirmNature ? parseInt(selectedFirmNature) : null,\n\n        // Required fields\n        Name: formData.name || '',\n        MobileNumber: formData.mobileNumber || '',\n        Nature: formData.nature ? parseInt(formData.nature) : 1, // Default to Business (1) if not provided\n\n        // Optional enum fields\n        Gender: formData.gender ? parseInt(formData.gender) : null,\n\n        // Contact Information\n        AlternateNumbers: Array.isArray(formData.alternateNumbers)\n          ? formData.alternateNumbers.filter(num => num && num.trim() !== '')\n          : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()).filter(s => s !== '') : [],\n        AlternateEmailIds: Array.isArray(formData.alternateEmailIds)\n          ? formData.alternateEmailIds.filter(email => email && email.trim() !== '')\n          : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()).filter(s => s !== '') : [],\n        // Note: PrimaryEmailId is added conditionally below only if it has a valid value\n\n        // Personal Information\n        DateOfBirth: formData.dateOfBirth || null,\n        IsMarried: Boolean(formData.isMarried),\n        DateOfMarriage: formData.dateOfMarriage || null,\n\n        // Location Information\n        WorkingState: formData.workingState || '',\n        DomesticState: formData.domesticState || '',\n        District: formData.district || '',\n        Address: formData.address || '',\n        WorkingArea: formData.workingArea || '',\n\n        // Associate Information\n        HasAssociate: Boolean(formData.hasAssociate),\n        AssociateName: formData.associateName || '',\n        AssociateRelation: formData.associateRelation || '',\n        // Note: AssociateMobile is added conditionally below only if it has a valid value\n\n        // Digital Presence\n        UsingWebsite: Boolean(formData.usingWebsite),\n        UsingCRMApp: Boolean(formData.usingCRMApp),\n        // Note: Website, WebsiteLink, CRMAppLink are added conditionally below only if they have valid values\n\n        // Business Information\n        TransactionValue: formData.transactionValue ? parseFloat(formData.transactionValue) : null,\n        RERARegistrationNumber: formData.reraRegistrationNumber || '',\n        WorkingProfiles: Array.isArray(formData.workingProfiles)\n          ? formData.workingProfiles.map(wp => parseInt(wp)).filter(wp => !isNaN(wp))\n          : [],\n        StarRating: formData.starRating ? parseInt(formData.starRating) : null,\n        Source: formData.source || '',\n        Remarks: formData.remarks || '',\n\n        // Company Information\n        FirmName: formData.firmName || '',\n        NumberOfOffices: formData.numberOfOffices && !isNaN(parseInt(formData.numberOfOffices)) ? parseInt(formData.numberOfOffices) : null,\n        NumberOfBranches: formData.numberOfBranches && !isNaN(parseInt(formData.numberOfBranches)) ? parseInt(formData.numberOfBranches) : null,\n        TotalEmployeeStrength: formData.totalEmployeeStrength && !isNaN(parseInt(formData.totalEmployeeStrength)) ? parseInt(formData.totalEmployeeStrength) : null,\n\n        // Authorized Person\n        AuthorizedPersonName: formData.authorizedPersonName || '',\n        Designation: formData.designation || '',\n        // Note: AuthorizedPersonEmail is added conditionally below only if it has a valid value\n\n        // Marketing Information\n        MarketingContact: formData.marketingContact || '',\n        MarketingDesignation: formData.marketingDesignation || '',\n        PlaceOfPosting: formData.placeOfPosting || '',\n        Department: formData.department || ''\n      };\n\n      // Only add optional URL and email fields if they have valid values (not empty strings or null)\n      // This prevents backend validation errors for empty strings on URL/Email fields\n\n      // Helper function to check if a field has a valid value\n      // This function is CRITICAL - it must return false for any value that would cause backend validation errors\n      const hasValidValue = (value) => {\n        return value !== null && value !== undefined && value !== '' &&\n               (typeof value === 'string' ? value.trim() !== '' : true);\n      };\n\n      // Debug: Check what values we have for validation-sensitive fields\n      console.log('=== SUBMITDATA CONSTRUCTION DEBUG ===');\n      console.log('Validation-sensitive field values:', {\n        primaryEmailId: formData.primaryEmailId,\n        authorizedPersonEmail: formData.authorizedPersonEmail,\n        website: formData.website,\n        websiteLink: formData.websiteLink,\n        crmAppLink: formData.crmAppLink,\n        associateMobile: formData.associateMobile\n      });\n\n      // Test our hasValidValue function on each field\n      console.log('hasValidValue test results:');\n      console.log('- primaryEmailId:', hasValidValue(formData.primaryEmailId));\n      console.log('- authorizedPersonEmail:', hasValidValue(formData.authorizedPersonEmail));\n      console.log('- website:', hasValidValue(formData.website));\n      console.log('- websiteLink:', hasValidValue(formData.websiteLink));\n      console.log('- crmAppLink:', hasValidValue(formData.crmAppLink));\n      console.log('- associateMobile:', hasValidValue(formData.associateMobile));\n\n      // Email fields - only add if not empty and valid\n      if (hasValidValue(formData.primaryEmailId)) {\n        console.log('Adding PrimaryEmailId:', formData.primaryEmailId);\n        submitData.PrimaryEmailId = formData.primaryEmailId.trim();\n      }\n\n      if (hasValidValue(formData.authorizedPersonEmail)) {\n        console.log('Adding AuthorizedPersonEmail:', formData.authorizedPersonEmail);\n        submitData.AuthorizedPersonEmail = formData.authorizedPersonEmail.trim();\n      }\n\n      // URL fields - only add if not empty and valid\n      if (hasValidValue(formData.website)) {\n        console.log('Adding Website:', formData.website);\n        submitData.Website = formData.website.trim();\n      }\n\n      if (hasValidValue(formData.websiteLink)) {\n        console.log('Adding WebsiteLink:', formData.websiteLink);\n        submitData.WebsiteLink = formData.websiteLink.trim();\n      }\n\n      if (hasValidValue(formData.crmAppLink)) {\n        console.log('Adding CRMAppLink:', formData.crmAppLink);\n        submitData.CRMAppLink = formData.crmAppLink.trim();\n      }\n\n      // Associate mobile - only add if not empty and valid (has regex validation)\n      if (hasValidValue(formData.associateMobile)) {\n        console.log('Adding AssociateMobile:', formData.associateMobile);\n        submitData.AssociateMobile = formData.associateMobile.trim();\n      }\n\n\n\n      // Validate required fields before submission\n      const requiredFieldsCheck = {\n        DivisionId: submitData.DivisionId,\n        CategoryId: submitData.CategoryId,\n        Name: submitData.Name,\n        MobileNumber: submitData.MobileNumber,\n        Nature: submitData.Nature\n      };\n\n      // Check for missing required fields\n      const missingFields = Object.entries(requiredFieldsCheck)\n        .filter(([, value]) => !value || value === '' || value === null)\n        .map(([key]) => key);\n\n      if (missingFields.length > 0) {\n        setErrors({ general: 'Missing required fields: ' + missingFields.join(', ') });\n        return;\n      }\n\n      // FINAL SAFETY CHECK: Remove any validation-sensitive fields that have empty values\n      // This is a bulletproof approach to ensure no empty strings reach the backend\n      const validationSensitiveFields = [\n        'PrimaryEmailId', 'AuthorizedPersonEmail', 'Website', 'WebsiteLink',\n        'CRMAppLink', 'AssociateMobile'\n      ];\n\n      console.log('=== FINAL SAFETY CHECK ===');\n      console.log('Before cleanup:', Object.keys(submitData));\n\n      validationSensitiveFields.forEach(fieldName => {\n        if (submitData.hasOwnProperty(fieldName)) {\n          const value = submitData[fieldName];\n          const isEmpty = value === null || value === undefined || value === '' ||\n                         (typeof value === 'string' && value.trim() === '');\n\n          console.log(`Checking ${fieldName}:`, {\n            value,\n            isEmpty,\n            action: isEmpty ? 'REMOVING' : 'KEEPING'\n          });\n\n          if (isEmpty) {\n            delete submitData[fieldName];\n            console.log(`🗑️ REMOVED ${fieldName} (was: \"${value}\")`);\n          }\n        }\n      });\n\n      console.log('After cleanup:', Object.keys(submitData));\n      console.log('Final submitData:', JSON.stringify(submitData, null, 2));\n\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n\n      onSubmit(result);\n    } catch (error) {\n      console.error('Error submitting form:', error);\n      console.error('Error details:', {\n        status: error.status,\n        data: error.data,\n        response: error.response,\n        message: error.message\n      });\n\n      // Enhanced error handling for backend validation errors\n      if (error.data?.errors || error.response?.data?.errors) {\n        // Handle ASP.NET Core ModelState validation errors\n        const validationErrors = error.data?.errors || error.response?.data?.errors;\n        const backendErrors = {};\n\n        console.log('Validation errors received:', validationErrors);\n\n        Object.keys(validationErrors).forEach(key => {\n          const errorMessages = validationErrors[key];\n          // Map backend field names to frontend field names for display\n          const frontendFieldName = mapBackendFieldToFrontend(key);\n          backendErrors[frontendFieldName] = Array.isArray(errorMessages)\n            ? errorMessages.join(', ')\n            : errorMessages;\n        });\n        setErrors(backendErrors);\n      } else if (error.data?.title || error.data?.detail || error.response?.data?.title || error.response?.data?.detail) {\n        // Handle ProblemDetails format errors\n        const errorMessage = error.data?.detail || error.data?.title || error.response?.data?.detail || error.response?.data?.title;\n        setErrors({ general: errorMessage });\n      } else if (error.isValidationError && error.isValidationError()) {\n        const validationErrors = error.getValidationErrors();\n        console.log('ApiError validation errors:', validationErrors);\n        setErrors(validationErrors);\n      } else {\n        // Handle other error formats\n        const errorMessage = error.data?.message ||\n                           error.response?.data?.message ||\n                           error.data?.title ||\n                           error.message ||\n                           'An error occurred while saving the person. Please check your input and try again.';\n        setErrors({ general: errorMessage });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  // Helper function to map backend field names to frontend field names for error display\n  const mapBackendFieldToFrontend = (backendFieldName) => {\n    const fieldMapping = {\n      'DivisionId': 'division',\n      'CategoryId': 'category',\n      'FirmNatureId': 'firmNature',\n      'Name': 'name',\n      'MobileNumber': 'mobileNumber',\n      'Nature': 'nature',\n      'Gender': 'gender',\n      'PrimaryEmailId': 'primaryEmailId',\n      'AlternateNumbers': 'alternateNumbers',\n      'AlternateEmailIds': 'alternateEmailIds',\n      'Website': 'website',\n      'DateOfBirth': 'dateOfBirth',\n      'IsMarried': 'isMarried',\n      'DateOfMarriage': 'dateOfMarriage',\n      'WorkingState': 'workingState',\n      'DomesticState': 'domesticState',\n      'District': 'district',\n      'Address': 'address',\n      'WorkingArea': 'workingArea',\n      'HasAssociate': 'hasAssociate',\n      'AssociateName': 'associateName',\n      'AssociateRelation': 'associateRelation',\n      'AssociateMobile': 'associateMobile',\n      'UsingWebsite': 'usingWebsite',\n      'WebsiteLink': 'websiteLink',\n      'UsingCRMApp': 'usingCRMApp',\n      'CRMAppLink': 'crmAppLink',\n      'TransactionValue': 'transactionValue',\n      'RERARegistrationNumber': 'reraRegistrationNumber',\n      'WorkingProfiles': 'workingProfiles',\n      'StarRating': 'starRating',\n      'Source': 'source',\n      'Remarks': 'remarks',\n      'FirmName': 'firmName',\n      'NumberOfOffices': 'numberOfOffices',\n      'NumberOfBranches': 'numberOfBranches',\n      'TotalEmployeeStrength': 'totalEmployeeStrength',\n      'AuthorizedPersonName': 'authorizedPersonName',\n      'AuthorizedPersonEmail': 'authorizedPersonEmail',\n      'Designation': 'designation',\n      'MarketingContact': 'marketingContact',\n      'MarketingDesignation': 'marketingDesignation',\n      'PlaceOfPosting': 'placeOfPosting',\n      'Department': 'department'\n    };\n\n    return fieldMapping[backendFieldName] || backendFieldName.toLowerCase();\n  };\n\n  const shouldShowField = (field) => {\n    if (!field.conditional) return true;\n\n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n\n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n\n    return conditionValue === expectedValue;\n  };\n\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n\n    const sections = {};\n    formConfig.fields.forEach((field) => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n\n    return sections;\n  };\n\n  const getSectionTitle = (sectionKey) => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n\n  const sections = formConfig ? groupFieldsBySections() : {};\n\n\n\n  return (\n    <div className=\"dynamic-person-form\">\n      <div className=\"form-header\">\n        <h2>{mode === 'create' ? 'Create New Person' : 'Edit Person'}</h2>\n        {formConfig && (\n          <div className=\"form-info\">\n            <span className=\"form-name\">{formConfig.name}</span>\n            {formConfig.description && (\n              <span className=\"form-description\">{formConfig.description}</span>\n            )}\n          </div>\n        )}\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      {/* Display validation error summary */}\n      {Object.keys(errors).length > 0 && !errors.general && (\n        <div className=\"alert alert-error\">\n          <strong>Please fix the following errors:</strong>\n          <ul style={{ margin: '8px 0 0 20px' }}>\n            {Object.entries(errors)\n              .filter(([key]) => key !== 'general')\n              .map(([key, message]) => (\n                <li key={key}>{message}</li>\n              ))}\n          </ul>\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit} className=\"person-form\">\n        {/* Hierarchical Dropdowns */}\n        <div className=\"form-section\">\n          <h3>Division & Category Selection</h3>\n\n          {/* Division Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Division <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedDivision}\n              onChange={handleDivisionChange}\n              disabled={loading.divisions}\n              className={`form-select ${errors.division ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {loading.divisions ? 'Loading divisions...' : 'Select Division'}\n              </option>\n              {divisions.map(division => (\n                <option key={division.id} value={division.id}>\n                  {division.name}\n                </option>\n              ))}\n            </select>\n            {errors.division && (\n              <div className=\"error-message\">{errors.division}</div>\n            )}\n            {errors.divisions && (\n              <div className=\"error-message\">{errors.divisions}</div>\n            )}\n          </div>\n\n          {/* Category Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Category <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedCategory}\n              onChange={handleCategoryChange}\n              disabled={!selectedDivision || loading.categories}\n              className={`form-select ${errors.category ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {!selectedDivision\n                  ? 'Select Division first'\n                  : loading.categories\n                    ? 'Loading categories...'\n                    : 'Select Category'\n                }\n              </option>\n              {categories.map(category => (\n                <option key={category.id} value={category.id}>\n                  {category.name}\n                </option>\n              ))}\n            </select>\n            {errors.category && (\n              <div className=\"error-message\">{errors.category}</div>\n            )}\n            {errors.categories && (\n              <div className=\"error-message\">{errors.categories}</div>\n            )}\n          </div>\n\n          {/* Firm Nature Dropdown - Only show if category doesn't have a form */}\n          {formAvailability.showFirmNatureDropdown && (\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                Firm Nature (Required)\n              </label>\n              <select\n                value={selectedFirmNature}\n                onChange={handleFirmNatureChange}\n                disabled={!selectedCategory || loading.firmNatures}\n                className=\"form-select\"\n                required\n              >\n                <option value=\"\">\n                  {!selectedCategory\n                    ? 'Select Category first'\n                    : loading.firmNatures\n                      ? 'Loading firm natures...'\n                      : 'Select Firm Nature (Required)'\n                  }\n                </option>\n                {firmNatures.map(firmNature => (\n                  <option key={firmNature.id} value={firmNature.id}>\n                    {firmNature.name}\n                  </option>\n                ))}\n              </select>\n              {errors.firmNatures && (\n                <div className=\"error-message\">{errors.firmNatures}</div>\n              )}\n            </div>\n          )}\n\n          {/* Form Status Messages */}\n          {loading.form && (\n            <div className=\"loading-message\">\n              <span>Loading form configuration...</span>\n            </div>\n          )}\n\n          {formAvailability.message && !loading.form && (\n            <div className={`status-message ${\n              formAvailability.categoryHasForm || formAvailability.firmNatureHasForm\n                ? 'success'\n                : formAvailability.message.includes('No form')\n                  ? 'error'\n                  : 'info'\n            }`}>\n              {formAvailability.message}\n            </div>\n          )}\n\n          {errors.form && (\n            <div className=\"error-message\">{errors.form}</div>\n          )}\n        </div>\n\n        {/* Dynamic Form Fields - Only show when form is available */}\n        {formConfig && Object.entries(sections).map(([sectionKey, section]) => (\n          <div key={sectionKey} className=\"form-section\">\n            <h3>{section.title}</h3>\n            <div className=\"form-fields\">\n              {section.fields\n                .filter(field => shouldShowField(field))\n                .map((field, fieldIndex) => (\n                  <FormField\n                    key={`${sectionKey}-${field.key}-${fieldIndex}`}\n                    field={field}\n                    value={formData[field.key]}\n                    onChange={(value) => handleFieldChange(field.key, value)}\n                    error={errors[field.key]}\n                  />\n                ))}\n            </div>\n          </div>\n        ))}\n\n\n\n        {/* Form Actions */}\n        <div className=\"form-actions\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"btn btn-outline\"\n            disabled={submitting}\n          >\n            Cancel\n          </button>\n          <button\n            type=\"submit\"\n            className=\"btn btn-primary\"\n            disabled={submitting || !selectedDivision || !selectedCategory}\n          >\n            {submitting\n              ? (mode === 'create' ? 'Creating...' : 'Updating...')\n              : (mode === 'create' ? 'Create Person' : 'Update Person')\n            }\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default DynamicPersonForm;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,kBAAkB,CAAEC,YAAY,CAAEC,oBAAoB,CAAEC,oBAAoB,KAAQ,iCAAiC,CAC9H,MAAO,CAAAC,iBAAiB,KAAM,kCAAkC,CAChE,MAAO,CAAAC,UAAU,KAAM,2BAA2B,CAClD,MAAO,CAAAC,SAAS,KAAM,aAAa,CACnC,MAAO,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjC,KAAM,CAAAC,iBAAiB,CAAGC,IAAA,EAAiE,IAAhE,CAAEC,QAAQ,CAAEC,QAAQ,CAAEC,WAAW,CAAG,IAAI,CAAEC,IAAI,CAAG,QAAS,CAAC,CAAAJ,IAAA,CACpF;AACA,KAAM,CAACK,SAAS,CAAEC,YAAY,CAAC,CAAGpB,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACqB,UAAU,CAAEC,aAAa,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACuB,WAAW,CAAEC,cAAc,CAAC,CAAGxB,QAAQ,CAAC,EAAE,CAAC,CAElD;AACA,KAAM,CAACyB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAAC2B,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAAC6B,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA,KAAM,CAAC+B,UAAU,CAAEC,aAAa,CAAC,CAAGhC,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACiC,QAAQ,CAAEC,WAAW,CAAC,CAAGlC,QAAQ,CAACK,oBAAoB,CAAC,CAAC,CAAC,CAChE,KAAM,CAAC8B,MAAM,CAAEC,SAAS,CAAC,CAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAExC;AACA,KAAM,CAACqC,OAAO,CAAEC,UAAU,CAAC,CAAGtC,QAAQ,CAAC,CACrCmB,SAAS,CAAE,KAAK,CAChBE,UAAU,CAAE,KAAK,CACjBE,WAAW,CAAE,KAAK,CAClBgB,IAAI,CAAE,KACR,CAAC,CAAC,CACF,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CAEnD;AACA,KAAM,CAAC0C,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3C,QAAQ,CAAC,CACvD4C,eAAe,CAAE,KAAK,CACtBC,iBAAiB,CAAE,KAAK,CACxBC,sBAAsB,CAAE,KAAK,CAC7BC,OAAO,CAAE,EACX,CAAC,CAAC,CAEF;AACA9C,SAAS,CAAC,IAAM,CACd+C,aAAa,CAAC,CAAC,CAEf;AACAC,mBAAmB,CAAC,CAAC,CAErB;AACA,GAAIhC,WAAW,CAAE,KAAAiC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CACflB,WAAW,CAACjB,WAAW,CAAC,CACxBS,mBAAmB,CAAC,EAAAwB,qBAAA,CAAAjC,WAAW,CAACoC,UAAU,UAAAH,qBAAA,iBAAtBA,qBAAA,CAAwBI,QAAQ,CAAC,CAAC,GAAI,EAAE,CAAC,CAC7D1B,mBAAmB,CAAC,EAAAuB,qBAAA,CAAAlC,WAAW,CAACsC,UAAU,UAAAJ,qBAAA,iBAAtBA,qBAAA,CAAwBG,QAAQ,CAAC,CAAC,GAAI,EAAE,CAAC,CAC7DxB,qBAAqB,CAAC,EAAAsB,qBAAA,CAAAnC,WAAW,CAACuC,YAAY,UAAAJ,qBAAA,iBAAxBA,qBAAA,CAA0BE,QAAQ,CAAC,CAAC,GAAI,EAAE,CAAC,CACnE,CAAC,IAAM,CACL;AACA,KAAM,CAAAG,WAAW,CAAGnD,iBAAiB,CAACoD,oBAAoB,CAAC,CAAC,CAC5D;AACA,GAAID,WAAW,CAACE,MAAM,CAAE,CACtBF,WAAW,CAACE,MAAM,CAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC,CAC5D,CACA3B,aAAa,CAACyB,WAAW,CAAC,CAC5B,CACF,CAAC,CAAE,CAACxC,WAAW,CAAC,CAAC,CAEjB;AACA,KAAM,CAAAgC,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CACF,KAAM,CAAA1C,UAAU,CAACsD,YAAY,CAAC,CAAC,CACjC,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CACF,CAAC,CAED;AACA,KAAM,CAAAF,iBAAiB,CAAID,MAAM,EAAK,CACpC,KAAM,CAAAK,IAAI,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CACtB,KAAM,CAAAC,YAAY,CAAG,EAAE,CAEvBP,MAAM,CAACQ,OAAO,CAACC,KAAK,EAAI,CACtB,GAAI,CAACJ,IAAI,CAACK,GAAG,CAACD,KAAK,CAACE,GAAG,CAAC,CAAE,CACxBN,IAAI,CAACO,GAAG,CAACH,KAAK,CAACE,GAAG,CAAC,CACnBJ,YAAY,CAACM,IAAI,CAACJ,KAAK,CAAC,CAC1B,CAAC,IAAM,CACLL,OAAO,CAACU,IAAI,CAAC,gDAAgDL,KAAK,CAACE,GAAG,EAAE,CAAC,CAC3E,CACF,CAAC,CAAC,CAEF,GAAIX,MAAM,CAACe,MAAM,GAAKR,YAAY,CAACQ,MAAM,CAAE,CACzCX,OAAO,CAACY,GAAG,CAAC,mCAAmChB,MAAM,CAACe,MAAM,cAAcR,YAAY,CAACQ,MAAM,gBAAgB,CAAC,CAChH,CAEA,MAAO,CAAAR,YAAY,CACrB,CAAC,CAED;AACAjE,SAAS,CAAC,IAAM,CACd,GAAIwB,gBAAgB,CAAE,CACpBmD,cAAc,CAACnD,gBAAgB,CAAC,CAChC;AACAG,mBAAmB,CAAC,EAAE,CAAC,CACvBE,qBAAqB,CAAC,EAAE,CAAC,CACzBN,cAAc,CAAC,EAAE,CAAC,CAClBQ,aAAa,CAAC,IAAI,CAAC,CACnBW,mBAAmB,CAAC,CAClBC,eAAe,CAAE,KAAK,CACtBC,iBAAiB,CAAE,KAAK,CACxBC,sBAAsB,CAAE,KAAK,CAC7BC,OAAO,CAAE,EACX,CAAC,CAAC,CACJ,CAAC,IAAM,CACLzB,aAAa,CAAC,EAAE,CAAC,CACjBE,cAAc,CAAC,EAAE,CAAC,CAClBI,mBAAmB,CAAC,EAAE,CAAC,CACvBE,qBAAqB,CAAC,EAAE,CAAC,CACzBE,aAAa,CAAC,IAAI,CAAC,CACrB,CACF,CAAC,CAAE,CAACP,gBAAgB,CAAC,CAAC,CAEtB;AACAxB,SAAS,CAAC,IAAM,CACd,GAAI0B,gBAAgB,CAAE,CACpBkD,uBAAuB,CAAClD,gBAAgB,CAAC,CACzC;AACAG,qBAAqB,CAAC,EAAE,CAAC,CAC3B,CAAC,IAAM,CACLN,cAAc,CAAC,EAAE,CAAC,CAClBM,qBAAqB,CAAC,EAAE,CAAC,CACzBE,aAAa,CAAC,IAAI,CAAC,CACnBW,mBAAmB,CAAC,CAClBC,eAAe,CAAE,KAAK,CACtBC,iBAAiB,CAAE,KAAK,CACxBC,sBAAsB,CAAE,KAAK,CAC7BC,OAAO,CAAE,EACX,CAAC,CAAC,CACJ,CACF,CAAC,CAAE,CAACpB,gBAAgB,CAAC,CAAC,CAEtB;AACA1B,SAAS,CAAC,IAAM,CACd,GAAI4B,kBAAkB,CAAE,CACtBiD,yBAAyB,CAACjD,kBAAkB,CAAC,CAC/C,CACF,CAAC,CAAE,CAACA,kBAAkB,CAAC,CAAC,CAExB;AACA,KAAM,CAAAmB,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChCV,UAAU,CAACyC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE5D,SAAS,CAAE,IAAK,CAAC,CAAC,CAAC,CAClD,GAAI,CACF,KAAM,CAAA6D,QAAQ,CAAG,KAAM,CAAAzE,UAAU,CAACsD,YAAY,CAAC,CAAC,CAChDzC,YAAY,CAAC4D,QAAQ,CAACC,IAAI,EAAI,EAAE,CAAC,CACnC,CAAE,MAAOnB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD1B,SAAS,CAAC2C,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE5D,SAAS,CAAE,0BAA2B,CAAC,CAAC,CAAC,CACzE,CAAC,OAAS,CACRmB,UAAU,CAACyC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE5D,SAAS,CAAE,KAAM,CAAC,CAAC,CAAC,CACrD,CACF,CAAC,CAED;AACA,KAAM,CAAAyD,cAAc,CAAG,KAAO,CAAAvB,UAAU,EAAK,CAC3Cf,UAAU,CAACyC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE1D,UAAU,CAAE,IAAK,CAAC,CAAC,CAAC,CACnD,GAAI,CACF,KAAM,CAAA2D,QAAQ,CAAG,KAAM,CAAAzE,UAAU,CAAC2E,uBAAuB,CAAC7B,UAAU,CAAC,CACrE/B,aAAa,CAAC0D,QAAQ,CAACC,IAAI,EAAI,EAAE,CAAC,CACpC,CAAE,MAAOnB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD1B,SAAS,CAAC2C,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE1D,UAAU,CAAE,2BAA4B,CAAC,CAAC,CAAC,CACzEC,aAAa,CAAC,EAAE,CAAC,CACnB,CAAC,OAAS,CACRgB,UAAU,CAACyC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE1D,UAAU,CAAE,KAAM,CAAC,CAAC,CAAC,CACtD,CACF,CAAC,CAED;AACA,KAAM,CAAA8D,eAAe,CAAG,KAAO,CAAA5B,UAAU,EAAK,CAC5CjB,UAAU,CAACyC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAExD,WAAW,CAAE,IAAK,CAAC,CAAC,CAAC,CACpD,GAAI,CACF,KAAM,CAAAyD,QAAQ,CAAG,KAAM,CAAAzE,UAAU,CAAC6E,wBAAwB,CAAC7B,UAAU,CAAC,CACtE/B,cAAc,CAACwD,QAAQ,CAACC,IAAI,EAAI,EAAE,CAAC,CACrC,CAAE,MAAOnB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD1B,SAAS,CAAC2C,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAExD,WAAW,CAAE,6BAA8B,CAAC,CAAC,CAAC,CAC5EC,cAAc,CAAC,EAAE,CAAC,CACpB,CAAC,OAAS,CACRc,UAAU,CAACyC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAExD,WAAW,CAAE,KAAM,CAAC,CAAC,CAAC,CACvD,CACF,CAAC,CAED;AACA,KAAM,CAAAsD,uBAAuB,CAAG,KAAO,CAAAtB,UAAU,EAAK,CACpDjB,UAAU,CAACyC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAExC,IAAI,CAAE,IAAK,CAAC,CAAC,CAAC,CAE7C,GAAI,CACF;AACA,KAAM,CAAAK,eAAe,CAAGtC,iBAAiB,CAAC+E,kBAAkB,CAACC,QAAQ,CAAC/B,UAAU,CAAC,CAAC,CAElF,GAAIX,eAAe,CAAE,CACnB;AACA,KAAM,CAAA2C,YAAY,CAAGjF,iBAAiB,CAACkF,cAAc,CAAC,UAAU,CAAEF,QAAQ,CAAC/B,UAAU,CAAC,CAAC,CACvF;AACA,GAAIgC,YAAY,EAAIA,YAAY,CAAC5B,MAAM,CAAE,CACvC4B,YAAY,CAAC5B,MAAM,CAAGC,iBAAiB,CAAC2B,YAAY,CAAC5B,MAAM,CAAC,CAC9D,CACA3B,aAAa,CAACuD,YAAY,CAAC,CAC3B5C,mBAAmB,CAAC,CAClBC,eAAe,CAAE,IAAI,CACrBC,iBAAiB,CAAE,KAAK,CACxBC,sBAAsB,CAAE,KAAK,CAC7BC,OAAO,CAAE,8BACX,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACA,KAAM,CAAAoC,eAAe,CAAC5B,UAAU,CAAC,CACjC;AACA,KAAM,CAAAE,WAAW,CAAGnD,iBAAiB,CAACoD,oBAAoB,CAAC,CAAC,CAC5D;AACA,GAAID,WAAW,CAACE,MAAM,CAAE,CACtBF,WAAW,CAACE,MAAM,CAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC,CAC5D,CACA3B,aAAa,CAACyB,WAAW,CAAC,CAC1Bd,mBAAmB,CAAC,CAClBC,eAAe,CAAE,KAAK,CACtBC,iBAAiB,CAAE,KAAK,CACxBC,sBAAsB,CAAE,IAAI,CAC5BC,OAAO,CAAE,gEACX,CAAC,CAAC,CACJ,CACF,CAAE,MAAOe,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1D1B,SAAS,CAAC2C,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAExC,IAAI,CAAE,kCAAmC,CAAC,CAAC,CAAC,CAC5E,CAAC,OAAS,CACRD,UAAU,CAACyC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAExC,IAAI,CAAE,KAAM,CAAC,CAAC,CAAC,CAChD,CACF,CAAC,CAED;AACA,KAAM,CAAAuC,yBAAyB,CAAG,KAAO,CAAAtB,YAAY,EAAK,CACxDlB,UAAU,CAACyC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAExC,IAAI,CAAE,IAAK,CAAC,CAAC,CAAC,CAE7C,GAAI,CACF;AACA,KAAM,CAAAM,iBAAiB,CAAGvC,iBAAiB,CAACmF,qBAAqB,CAACH,QAAQ,CAAC9B,YAAY,CAAC,CAAC,CAEzF,GAAIX,iBAAiB,CAAE,CACrB;AACA,KAAM,CAAA6C,cAAc,CAAGpF,iBAAiB,CAACkF,cAAc,CAAC,aAAa,CAAEF,QAAQ,CAAC9B,YAAY,CAAC,CAAC,CAC9F;AACA,GAAIkC,cAAc,EAAIA,cAAc,CAAC/B,MAAM,CAAE,CAC3C+B,cAAc,CAAC/B,MAAM,CAAGC,iBAAiB,CAAC8B,cAAc,CAAC/B,MAAM,CAAC,CAClE,CACA3B,aAAa,CAAC0D,cAAc,CAAC,CAC7B/C,mBAAmB,CAAC,CAClBC,eAAe,CAAE,KAAK,CACtBC,iBAAiB,CAAE,IAAI,CACvBC,sBAAsB,CAAE,IAAI,CAC5BC,OAAO,CAAE,iCACX,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACA,KAAM,CAAAU,WAAW,CAAGnD,iBAAiB,CAACoD,oBAAoB,CAAC,CAAC,CAC5D;AACA,GAAID,WAAW,CAACE,MAAM,CAAE,CACtBF,WAAW,CAACE,MAAM,CAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC,CAC5D,CACA3B,aAAa,CAACyB,WAAW,CAAC,CAC1Bd,mBAAmB,CAAC,CAClBC,eAAe,CAAE,KAAK,CACtBC,iBAAiB,CAAE,KAAK,CACxBC,sBAAsB,CAAE,IAAI,CAC5BC,OAAO,CAAE,kEACX,CAAC,CAAC,CACJ,CACF,CAAE,MAAOe,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,CAAEA,KAAK,CAAC,CAC7D1B,SAAS,CAAC2C,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAExC,IAAI,CAAE,kCAAmC,CAAC,CAAC,CAAC,CAC5E,CAAC,OAAS,CACRD,UAAU,CAACyC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAExC,IAAI,CAAE,KAAM,CAAC,CAAC,CAAC,CAChD,CACF,CAAC,CAED;AACA,KAAM,CAAAoD,oBAAoB,CAAIC,CAAC,EAAK,CAClC,KAAM,CAAAC,KAAK,CAAGD,CAAC,CAACE,MAAM,CAACD,KAAK,CAC5BnE,mBAAmB,CAACmE,KAAK,CAAC,CAE1B;AACA3D,WAAW,CAAC6C,IAAI,GAAK,CACnB,GAAGA,IAAI,CACP1B,UAAU,CAAEwC,KAAK,CAAGP,QAAQ,CAACO,KAAK,CAAC,CAAG,IAAI,CAC1CtC,UAAU,CAAE,IAAI,CAChBC,YAAY,CAAE,IAChB,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAuC,oBAAoB,CAAIH,CAAC,EAAK,CAClC,KAAM,CAAAC,KAAK,CAAGD,CAAC,CAACE,MAAM,CAACD,KAAK,CAC5BjE,mBAAmB,CAACiE,KAAK,CAAC,CAE1B;AACA3D,WAAW,CAAC6C,IAAI,GAAK,CACnB,GAAGA,IAAI,CACPxB,UAAU,CAAEsC,KAAK,CAAGP,QAAQ,CAACO,KAAK,CAAC,CAAG,IAAI,CAC1CrC,YAAY,CAAE,IAChB,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAwC,sBAAsB,CAAIJ,CAAC,EAAK,CACpC,KAAM,CAAAC,KAAK,CAAGD,CAAC,CAACE,MAAM,CAACD,KAAK,CAC5B/D,qBAAqB,CAAC+D,KAAK,CAAC,CAE5B;AACA3D,WAAW,CAAC6C,IAAI,GAAK,CACnB,GAAGA,IAAI,CACPvB,YAAY,CAAEqC,KAAK,CAAGP,QAAQ,CAACO,KAAK,CAAC,CAAG,IAC1C,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAI,iBAAiB,CAAGA,CAACC,QAAQ,CAAEL,KAAK,GAAK,CAC7C3D,WAAW,CAAC6C,IAAI,GAAK,CACnB,GAAGA,IAAI,CACP,CAACmB,QAAQ,EAAGL,KACd,CAAC,CAAC,CAAC,CAEH;AACA,GAAI1D,MAAM,CAAC+D,QAAQ,CAAC,CAAE,CACpB9D,SAAS,CAAC2C,IAAI,GAAK,CACjB,GAAGA,IAAI,CACP,CAACmB,QAAQ,EAAG,IACd,CAAC,CAAC,CAAC,CACL,CACF,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,SAAS,CAAG,CAAC,CAAC,CAEpB;AACA,GAAI,CAAC3E,gBAAgB,CAAE,CACrB2E,SAAS,CAACC,QAAQ,CAAG,sBAAsB,CAC7C,CAAC,IAAM,IAAIf,QAAQ,CAAC7D,gBAAgB,CAAC,CAAG,CAAC,CAAE,CACzC2E,SAAS,CAACC,QAAQ,CAAG,uCAAuC,CAC9D,CAEA,GAAI,CAAC1E,gBAAgB,CAAE,CACrByE,SAAS,CAACE,QAAQ,CAAG,sBAAsB,CAC7C,CAAC,IAAM,IAAIhB,QAAQ,CAAC3D,gBAAgB,CAAC,CAAG,CAAC,CAAE,CACzCyE,SAAS,CAACE,QAAQ,CAAG,uCAAuC,CAC9D,CAEA,GAAIzE,kBAAkB,EAAIyD,QAAQ,CAACzD,kBAAkB,CAAC,CAAG,CAAC,CAAE,CAC1DuE,SAAS,CAACG,UAAU,CAAG,0CAA0C,CACnE,CAEA;AACA,GAAI,CAACtE,QAAQ,CAACuE,IAAI,EAAIvE,QAAQ,CAACuE,IAAI,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACjDL,SAAS,CAACI,IAAI,CAAG,kBAAkB,CACrC,CAAC,IAAM,IAAIvE,QAAQ,CAACuE,IAAI,CAAC9B,MAAM,CAAG,GAAG,CAAE,CACrC0B,SAAS,CAACI,IAAI,CAAG,mCAAmC,CACtD,CAEA,GAAI,CAACvE,QAAQ,CAACyE,YAAY,EAAIzE,QAAQ,CAACyE,YAAY,CAACD,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACjEL,SAAS,CAACM,YAAY,CAAG,2BAA2B,CACtD,CAAC,IAAM,CACL;AACA,KAAM,CAAAC,WAAW,CAAG,qCAAqC,CACzD,GAAI,CAACA,WAAW,CAACC,IAAI,CAAC3E,QAAQ,CAACyE,YAAY,CAAC,CAAE,CAC5CN,SAAS,CAACM,YAAY,CAAG,8BAA8B,CACzD,CACF,CAEA,GAAI,CAACzE,QAAQ,CAAC4E,MAAM,EAAI5E,QAAQ,CAAC4E,MAAM,GAAK,EAAE,EAAI5E,QAAQ,CAAC4E,MAAM,GAAK,GAAG,CAAE,CACzET,SAAS,CAACS,MAAM,CAAG,oBAAoB,CACzC,CAAC,IAAM,IAAI,CAAC,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACC,QAAQ,CAACxB,QAAQ,CAACrD,QAAQ,CAAC4E,MAAM,CAAC,CAAC,CAAE,CAC5DT,SAAS,CAACS,MAAM,CAAG,sBAAsB,CAC3C,CAEA;AACA,GAAI,CAAC9E,UAAU,CAAE,CACfqE,SAAS,CAAC7D,IAAI,CAAG,kDAAkD,CACnE,MAAO,CAAEwE,OAAO,CAAE,KAAK,CAAE5E,MAAM,CAAEiE,SAAU,CAAC,CAC9C,CAIA;AACArE,UAAU,CAAC4B,MAAM,CAACQ,OAAO,CAACC,KAAK,EAAI,CACjC,KAAM,CAAAyB,KAAK,CAAG5D,QAAQ,CAACmC,KAAK,CAACE,GAAG,CAAC,CAEjC;AACA,GAAIF,KAAK,CAAC4C,QAAQ,GAAK,CAACnB,KAAK,EAAK,MAAO,CAAAA,KAAK,GAAK,QAAQ,EAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,GAAK,EAAG,CAAC,CAAE,CACpFL,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,GAAGF,KAAK,CAAC6C,KAAK,cAAc,CACnD,OACF,CAEA;AACA,GAAI7C,KAAK,CAAC8C,WAAW,EAAIC,eAAe,CAAC/C,KAAK,CAAC,CAAE,CAC/C,GAAIA,KAAK,CAAC4C,QAAQ,GAAK,CAACnB,KAAK,EAAK,MAAO,CAAAA,KAAK,GAAK,QAAQ,EAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,GAAK,EAAG,CAAC,CAAE,CACpFL,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,GAAGF,KAAK,CAAC6C,KAAK,cAAc,CACnD,OACF,CACF,CAEA;AACA,GAAIpB,KAAK,EAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,EAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,KAAAW,iBAAA,CAAAC,kBAAA,CAAAC,kBAAA,CAC7D,OAAQlD,KAAK,CAACmD,IAAI,EAChB,IAAK,OAAO,CACV,KAAM,CAAAC,UAAU,CAAG,4BAA4B,CAC/C,GAAI,CAACA,UAAU,CAACZ,IAAI,CAACf,KAAK,CAAC,CAAE,CAC3BO,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,oCAAoC,CAC7D,CACA,MACF,IAAK,KAAK,CACR,KAAM,CAAAmD,UAAU,CAAG,qCAAqC,CACxD,GAAI,CAACA,UAAU,CAACb,IAAI,CAACf,KAAK,CAAC,CAAE,CAC3BO,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,oCAAoC,CAC7D,CACA,MACF,IAAK,KAAK,CACR,GAAI,CACF,GAAI,CAAAoD,GAAG,CAAC7B,KAAK,CAAC,CAChB,CAAE,KAAM,CACNO,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,0BAA0B,CACnD,CACA,MACF,IAAK,QAAQ,CACX,GAAIF,KAAK,CAACuD,UAAU,CAAE,CACpB,KAAM,CAAAC,QAAQ,CAAGC,UAAU,CAAChC,KAAK,CAAC,CAClC,GAAIzB,KAAK,CAACuD,UAAU,CAACG,GAAG,GAAKC,SAAS,EAAIH,QAAQ,CAAGxD,KAAK,CAACuD,UAAU,CAACG,GAAG,CAAE,CACzE1B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,0BAA0BF,KAAK,CAACuD,UAAU,CAACG,GAAG,EAAE,CACzE,CACA,GAAI1D,KAAK,CAACuD,UAAU,CAACK,GAAG,GAAKD,SAAS,EAAIH,QAAQ,CAAGxD,KAAK,CAACuD,UAAU,CAACK,GAAG,CAAE,CACzE5B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,yBAAyBF,KAAK,CAACuD,UAAU,CAACK,GAAG,EAAE,CACxE,CACF,CACA,MACJ,CAEA;AACA,IAAAZ,iBAAA,CAAIhD,KAAK,CAACuD,UAAU,UAAAP,iBAAA,WAAhBA,iBAAA,CAAkBa,OAAO,CAAE,CAC7B,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAC,MAAM,CAAC/D,KAAK,CAACuD,UAAU,CAACM,OAAO,CAAC,CAClD,GAAI,CAACC,KAAK,CAACtB,IAAI,CAACf,KAAK,CAAC,CAAE,CACtBO,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,GAAGF,KAAK,CAAC6C,KAAK,oBAAoB,CAC3D,CACF,CAEA;AACA,GAAI,CAAAI,kBAAA,CAAAjD,KAAK,CAACuD,UAAU,UAAAN,kBAAA,WAAhBA,kBAAA,CAAkBe,SAAS,EAAIvC,KAAK,CAACnB,MAAM,CAAGN,KAAK,CAACuD,UAAU,CAACS,SAAS,CAAE,CAC5EhC,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,GAAGF,KAAK,CAAC6C,KAAK,qBAAqB7C,KAAK,CAACuD,UAAU,CAACS,SAAS,aAAa,CACnG,CACA,GAAI,CAAAd,kBAAA,CAAAlD,KAAK,CAACuD,UAAU,UAAAL,kBAAA,WAAhBA,kBAAA,CAAkBe,SAAS,EAAIxC,KAAK,CAACnB,MAAM,CAAGN,KAAK,CAACuD,UAAU,CAACU,SAAS,CAAE,CAC5EjC,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,GAAGF,KAAK,CAAC6C,KAAK,oBAAoB7C,KAAK,CAACuD,UAAU,CAACU,SAAS,aAAa,CAClG,CAEA;AACA,GAAIjE,KAAK,CAACE,GAAG,GAAK,gBAAgB,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CACxD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,oCAAoC,CAC7D,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,cAAc,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CACtD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,4CAA4C,CACrE,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,eAAe,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CACvD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,6CAA6C,CACtE,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,UAAU,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CAClD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,uCAAuC,CAChE,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,SAAS,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CACjD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,sCAAsC,CAC/D,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,aAAa,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CACrD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,2CAA2C,CACpE,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,eAAe,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CACvD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,6CAA6C,CACtE,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,mBAAmB,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CAC3D0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,iDAAiD,CAC1E,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,wBAAwB,EAAIuB,KAAK,CAACnB,MAAM,CAAG,EAAE,CAAE,CAC/D0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,sDAAsD,CAC/E,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,QAAQ,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CAChD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,qCAAqC,CAC9D,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,SAAS,EAAIuB,KAAK,CAACnB,MAAM,CAAG,IAAI,CAAE,CAClD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,uCAAuC,CAChE,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,UAAU,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CAClD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,wCAAwC,CACjE,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,sBAAsB,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CAC9D0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,qDAAqD,CAC9E,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,uBAAuB,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CAC/D0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,sDAAsD,CAC/E,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,aAAa,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CACrD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,0CAA0C,CACnE,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,kBAAkB,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CAC1D0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,gDAAgD,CACzE,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,sBAAsB,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CAC9D0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,oDAAoD,CAC7E,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,gBAAgB,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CACxD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,+CAA+C,CACxE,CACA,GAAIF,KAAK,CAACE,GAAG,GAAK,YAAY,EAAIuB,KAAK,CAACnB,MAAM,CAAG,GAAG,CAAE,CACpD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,yCAAyC,CAClE,CACF,CAEA;AACA,GAAIF,KAAK,CAACmD,IAAI,GAAK,QAAQ,EAAI1B,KAAK,CAAE,CACpC,KAAM,CAAA+B,QAAQ,CAAGC,UAAU,CAAChC,KAAK,CAAC,CAClC,GAAIzB,KAAK,CAACE,GAAG,GAAK,YAAY,GAAKsD,QAAQ,CAAG,CAAC,EAAIA,QAAQ,CAAG,CAAC,CAAC,CAAE,CAChExB,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,qCAAqC,CAC9D,CACA,GAAI,CAAC,iBAAiB,CAAE,kBAAkB,CAAE,uBAAuB,CAAC,CAACwC,QAAQ,CAAC1C,KAAK,CAACE,GAAG,CAAC,EAAIsD,QAAQ,CAAG,CAAC,CAAE,CACxGxB,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,GAAGF,KAAK,CAAC6C,KAAK,uBAAuB,CAC9D,CACA,GAAI7C,KAAK,CAACE,GAAG,GAAK,kBAAkB,EAAIsD,QAAQ,CAAG,CAAC,CAAE,CACpDxB,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,wCAAwC,CACjE,CACF,CAEA;AACA,GAAIF,KAAK,CAACE,GAAG,GAAK,iBAAiB,EAAIuB,KAAK,EAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACnE,KAAM,CAAAE,WAAW,CAAG,qCAAqC,CACzD,GAAI,CAACA,WAAW,CAACC,IAAI,CAACf,KAAK,CAAC,CAAE,CAC5BO,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,CAAG,wCAAwC,CACjE,CACF,CACF,CAAC,CAAC,CAEF;AACA,GAAIrC,QAAQ,CAACqG,SAAS,EAAIrG,QAAQ,CAACsG,cAAc,EAAItG,QAAQ,CAACuG,WAAW,CAAE,CACzE,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAC,IAAI,CAACzG,QAAQ,CAACuG,WAAW,CAAC,CAChD,KAAM,CAAAG,YAAY,CAAG,GAAI,CAAAD,IAAI,CAACzG,QAAQ,CAACsG,cAAc,CAAC,CACtD,GAAII,YAAY,EAAIF,SAAS,CAAE,CAC7BrC,SAAS,CAACmC,cAAc,CAAG,wCAAwC,CACrE,CACF,CAEA,MAAO,CACLxB,OAAO,CAAE6B,MAAM,CAACC,IAAI,CAACzC,SAAS,CAAC,CAAC1B,MAAM,GAAK,CAAC,CAC5CvC,MAAM,CAAEiE,SACV,CAAC,CACH,CAAC,CAED,KAAM,CAAA0C,YAAY,CAAG,KAAO,CAAAlD,CAAC,EAAK,CAChCA,CAAC,CAACmD,cAAc,CAAC,CAAC,CAElB;AACAhF,OAAO,CAACY,GAAG,CAAC,gDAAgD,CAAE,CAC5DqE,cAAc,CAAE/G,QAAQ,CAAC+G,cAAc,CACvCC,OAAO,CAAEhH,QAAQ,CAACgH,OAAO,CACzBC,WAAW,CAAEjH,QAAQ,CAACiH,WAAW,CACjCC,UAAU,CAAElH,QAAQ,CAACkH,UAAU,CAC/BC,qBAAqB,CAAEnH,QAAQ,CAACmH,qBAAqB,CACrDC,eAAe,CAAEpH,QAAQ,CAACoH,eAC5B,CAAC,CAAC,CAEF,KAAM,CAAA1B,UAAU,CAAGxB,YAAY,CAAC,CAAC,CACjC,GAAI,CAACwB,UAAU,CAACZ,OAAO,CAAE,CACvB3E,SAAS,CAACuF,UAAU,CAACxF,MAAM,CAAC,CAC5B,OACF,CAEAM,aAAa,CAAC,IAAI,CAAC,CACnB,GAAI,CACF;AACA,KAAM,CAAA6G,UAAU,CAAG,CACjB;AACAC,UAAU,CAAEjE,QAAQ,CAAC7D,gBAAgB,CAAC,CACtC+H,UAAU,CAAElE,QAAQ,CAAC3D,gBAAgB,CAAC,CACtC8H,YAAY,CAAE5H,kBAAkB,CAAGyD,QAAQ,CAACzD,kBAAkB,CAAC,CAAG,IAAI,CAEtE;AACA6H,IAAI,CAAEzH,QAAQ,CAACuE,IAAI,EAAI,EAAE,CACzBmD,YAAY,CAAE1H,QAAQ,CAACyE,YAAY,EAAI,EAAE,CACzCkD,MAAM,CAAE3H,QAAQ,CAAC4E,MAAM,CAAGvB,QAAQ,CAACrD,QAAQ,CAAC4E,MAAM,CAAC,CAAG,CAAC,CAAE;AAEzD;AACAgD,MAAM,CAAE5H,QAAQ,CAAC6H,MAAM,CAAGxE,QAAQ,CAACrD,QAAQ,CAAC6H,MAAM,CAAC,CAAG,IAAI,CAE1D;AACAC,gBAAgB,CAAEC,KAAK,CAACC,OAAO,CAAChI,QAAQ,CAACiI,gBAAgB,CAAC,CACtDjI,QAAQ,CAACiI,gBAAgB,CAACC,MAAM,CAACC,GAAG,EAAIA,GAAG,EAAIA,GAAG,CAAC3D,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CACjExE,QAAQ,CAACiI,gBAAgB,CAAGjI,QAAQ,CAACiI,gBAAgB,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,EAAIA,CAAC,CAAC9D,IAAI,CAAC,CAAC,CAAC,CAAC0D,MAAM,CAACI,CAAC,EAAIA,CAAC,GAAK,EAAE,CAAC,CAAG,EAAE,CAClHC,iBAAiB,CAAER,KAAK,CAACC,OAAO,CAAChI,QAAQ,CAACwI,iBAAiB,CAAC,CACxDxI,QAAQ,CAACwI,iBAAiB,CAACN,MAAM,CAACO,KAAK,EAAIA,KAAK,EAAIA,KAAK,CAACjE,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CACxExE,QAAQ,CAACwI,iBAAiB,CAAGxI,QAAQ,CAACwI,iBAAiB,CAACJ,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,EAAIA,CAAC,CAAC9D,IAAI,CAAC,CAAC,CAAC,CAAC0D,MAAM,CAACI,CAAC,EAAIA,CAAC,GAAK,EAAE,CAAC,CAAG,EAAE,CACpH;AAEA;AACAI,WAAW,CAAE1I,QAAQ,CAACuG,WAAW,EAAI,IAAI,CACzCoC,SAAS,CAAEC,OAAO,CAAC5I,QAAQ,CAACqG,SAAS,CAAC,CACtCwC,cAAc,CAAE7I,QAAQ,CAACsG,cAAc,EAAI,IAAI,CAE/C;AACAwC,YAAY,CAAE9I,QAAQ,CAAC+I,YAAY,EAAI,EAAE,CACzCC,aAAa,CAAEhJ,QAAQ,CAACiJ,aAAa,EAAI,EAAE,CAC3CC,QAAQ,CAAElJ,QAAQ,CAACmJ,QAAQ,EAAI,EAAE,CACjCC,OAAO,CAAEpJ,QAAQ,CAACqJ,OAAO,EAAI,EAAE,CAC/BC,WAAW,CAAEtJ,QAAQ,CAACuJ,WAAW,EAAI,EAAE,CAEvC;AACAC,YAAY,CAAEZ,OAAO,CAAC5I,QAAQ,CAACyJ,YAAY,CAAC,CAC5CC,aAAa,CAAE1J,QAAQ,CAAC2J,aAAa,EAAI,EAAE,CAC3CC,iBAAiB,CAAE5J,QAAQ,CAAC6J,iBAAiB,EAAI,EAAE,CACnD;AAEA;AACAC,YAAY,CAAElB,OAAO,CAAC5I,QAAQ,CAAC+J,YAAY,CAAC,CAC5CC,WAAW,CAAEpB,OAAO,CAAC5I,QAAQ,CAACiK,WAAW,CAAC,CAC1C;AAEA;AACAC,gBAAgB,CAAElK,QAAQ,CAACmK,gBAAgB,CAAGvE,UAAU,CAAC5F,QAAQ,CAACmK,gBAAgB,CAAC,CAAG,IAAI,CAC1FC,sBAAsB,CAAEpK,QAAQ,CAACqK,sBAAsB,EAAI,EAAE,CAC7DC,eAAe,CAAEvC,KAAK,CAACC,OAAO,CAAChI,QAAQ,CAACuK,eAAe,CAAC,CACpDvK,QAAQ,CAACuK,eAAe,CAAClC,GAAG,CAACmC,EAAE,EAAInH,QAAQ,CAACmH,EAAE,CAAC,CAAC,CAACtC,MAAM,CAACsC,EAAE,EAAI,CAACC,KAAK,CAACD,EAAE,CAAC,CAAC,CACzE,EAAE,CACNE,UAAU,CAAE1K,QAAQ,CAAC2K,UAAU,CAAGtH,QAAQ,CAACrD,QAAQ,CAAC2K,UAAU,CAAC,CAAG,IAAI,CACtEC,MAAM,CAAE5K,QAAQ,CAAC6K,MAAM,EAAI,EAAE,CAC7BC,OAAO,CAAE9K,QAAQ,CAAC+K,OAAO,EAAI,EAAE,CAE/B;AACAC,QAAQ,CAAEhL,QAAQ,CAACiL,QAAQ,EAAI,EAAE,CACjCC,eAAe,CAAElL,QAAQ,CAACmL,eAAe,EAAI,CAACV,KAAK,CAACpH,QAAQ,CAACrD,QAAQ,CAACmL,eAAe,CAAC,CAAC,CAAG9H,QAAQ,CAACrD,QAAQ,CAACmL,eAAe,CAAC,CAAG,IAAI,CACnIC,gBAAgB,CAAEpL,QAAQ,CAACqL,gBAAgB,EAAI,CAACZ,KAAK,CAACpH,QAAQ,CAACrD,QAAQ,CAACqL,gBAAgB,CAAC,CAAC,CAAGhI,QAAQ,CAACrD,QAAQ,CAACqL,gBAAgB,CAAC,CAAG,IAAI,CACvIC,qBAAqB,CAAEtL,QAAQ,CAACuL,qBAAqB,EAAI,CAACd,KAAK,CAACpH,QAAQ,CAACrD,QAAQ,CAACuL,qBAAqB,CAAC,CAAC,CAAGlI,QAAQ,CAACrD,QAAQ,CAACuL,qBAAqB,CAAC,CAAG,IAAI,CAE3J;AACAC,oBAAoB,CAAExL,QAAQ,CAACyL,oBAAoB,EAAI,EAAE,CACzDC,WAAW,CAAE1L,QAAQ,CAAC2L,WAAW,EAAI,EAAE,CACvC;AAEA;AACAC,gBAAgB,CAAE5L,QAAQ,CAAC6L,gBAAgB,EAAI,EAAE,CACjDC,oBAAoB,CAAE9L,QAAQ,CAAC+L,oBAAoB,EAAI,EAAE,CACzDC,cAAc,CAAEhM,QAAQ,CAACiM,cAAc,EAAI,EAAE,CAC7CC,UAAU,CAAElM,QAAQ,CAACmM,UAAU,EAAI,EACrC,CAAC,CAED;AACA;AAEA;AACA;AACA,KAAM,CAAAC,aAAa,CAAIxI,KAAK,EAAK,CAC/B,MAAO,CAAAA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKkC,SAAS,EAAIlC,KAAK,GAAK,EAAE,GACpD,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAGA,KAAK,CAACY,IAAI,CAAC,CAAC,GAAK,EAAE,CAAG,IAAI,CAAC,CACjE,CAAC,CAED;AACA1C,OAAO,CAACY,GAAG,CAAC,uCAAuC,CAAC,CACpDZ,OAAO,CAACY,GAAG,CAAC,oCAAoC,CAAE,CAChDqE,cAAc,CAAE/G,QAAQ,CAAC+G,cAAc,CACvCI,qBAAqB,CAAEnH,QAAQ,CAACmH,qBAAqB,CACrDH,OAAO,CAAEhH,QAAQ,CAACgH,OAAO,CACzBC,WAAW,CAAEjH,QAAQ,CAACiH,WAAW,CACjCC,UAAU,CAAElH,QAAQ,CAACkH,UAAU,CAC/BE,eAAe,CAAEpH,QAAQ,CAACoH,eAC5B,CAAC,CAAC,CAEF;AACAtF,OAAO,CAACY,GAAG,CAAC,6BAA6B,CAAC,CAC1CZ,OAAO,CAACY,GAAG,CAAC,mBAAmB,CAAE0J,aAAa,CAACpM,QAAQ,CAAC+G,cAAc,CAAC,CAAC,CACxEjF,OAAO,CAACY,GAAG,CAAC,0BAA0B,CAAE0J,aAAa,CAACpM,QAAQ,CAACmH,qBAAqB,CAAC,CAAC,CACtFrF,OAAO,CAACY,GAAG,CAAC,YAAY,CAAE0J,aAAa,CAACpM,QAAQ,CAACgH,OAAO,CAAC,CAAC,CAC1DlF,OAAO,CAACY,GAAG,CAAC,gBAAgB,CAAE0J,aAAa,CAACpM,QAAQ,CAACiH,WAAW,CAAC,CAAC,CAClEnF,OAAO,CAACY,GAAG,CAAC,eAAe,CAAE0J,aAAa,CAACpM,QAAQ,CAACkH,UAAU,CAAC,CAAC,CAChEpF,OAAO,CAACY,GAAG,CAAC,oBAAoB,CAAE0J,aAAa,CAACpM,QAAQ,CAACoH,eAAe,CAAC,CAAC,CAE1E;AACA,GAAIgF,aAAa,CAACpM,QAAQ,CAAC+G,cAAc,CAAC,CAAE,CAC1CjF,OAAO,CAACY,GAAG,CAAC,wBAAwB,CAAE1C,QAAQ,CAAC+G,cAAc,CAAC,CAC9DM,UAAU,CAACgF,cAAc,CAAGrM,QAAQ,CAAC+G,cAAc,CAACvC,IAAI,CAAC,CAAC,CAC5D,CAEA,GAAI4H,aAAa,CAACpM,QAAQ,CAACmH,qBAAqB,CAAC,CAAE,CACjDrF,OAAO,CAACY,GAAG,CAAC,+BAA+B,CAAE1C,QAAQ,CAACmH,qBAAqB,CAAC,CAC5EE,UAAU,CAACiF,qBAAqB,CAAGtM,QAAQ,CAACmH,qBAAqB,CAAC3C,IAAI,CAAC,CAAC,CAC1E,CAEA;AACA,GAAI4H,aAAa,CAACpM,QAAQ,CAACgH,OAAO,CAAC,CAAE,CACnClF,OAAO,CAACY,GAAG,CAAC,iBAAiB,CAAE1C,QAAQ,CAACgH,OAAO,CAAC,CAChDK,UAAU,CAACkF,OAAO,CAAGvM,QAAQ,CAACgH,OAAO,CAACxC,IAAI,CAAC,CAAC,CAC9C,CAEA,GAAI4H,aAAa,CAACpM,QAAQ,CAACiH,WAAW,CAAC,CAAE,CACvCnF,OAAO,CAACY,GAAG,CAAC,qBAAqB,CAAE1C,QAAQ,CAACiH,WAAW,CAAC,CACxDI,UAAU,CAACmF,WAAW,CAAGxM,QAAQ,CAACiH,WAAW,CAACzC,IAAI,CAAC,CAAC,CACtD,CAEA,GAAI4H,aAAa,CAACpM,QAAQ,CAACkH,UAAU,CAAC,CAAE,CACtCpF,OAAO,CAACY,GAAG,CAAC,oBAAoB,CAAE1C,QAAQ,CAACkH,UAAU,CAAC,CACtDG,UAAU,CAACoF,UAAU,CAAGzM,QAAQ,CAACkH,UAAU,CAAC1C,IAAI,CAAC,CAAC,CACpD,CAEA;AACA,GAAI4H,aAAa,CAACpM,QAAQ,CAACoH,eAAe,CAAC,CAAE,CAC3CtF,OAAO,CAACY,GAAG,CAAC,yBAAyB,CAAE1C,QAAQ,CAACoH,eAAe,CAAC,CAChEC,UAAU,CAACqF,eAAe,CAAG1M,QAAQ,CAACoH,eAAe,CAAC5C,IAAI,CAAC,CAAC,CAC9D,CAIA;AACA,KAAM,CAAAmI,mBAAmB,CAAG,CAC1BrF,UAAU,CAAED,UAAU,CAACC,UAAU,CACjCC,UAAU,CAAEF,UAAU,CAACE,UAAU,CACjCE,IAAI,CAAEJ,UAAU,CAACI,IAAI,CACrBC,YAAY,CAAEL,UAAU,CAACK,YAAY,CACrCC,MAAM,CAAEN,UAAU,CAACM,MACrB,CAAC,CAED;AACA,KAAM,CAAAiF,aAAa,CAAGjG,MAAM,CAACkG,OAAO,CAACF,mBAAmB,CAAC,CACtDzE,MAAM,CAAC4E,KAAA,MAAC,EAAGlJ,KAAK,CAAC,CAAAkJ,KAAA,OAAK,CAAClJ,KAAK,EAAIA,KAAK,GAAK,EAAE,EAAIA,KAAK,GAAK,IAAI,GAAC,CAC/DyE,GAAG,CAAC0E,KAAA,MAAC,CAAC1K,GAAG,CAAC,CAAA0K,KAAA,OAAK,CAAA1K,GAAG,GAAC,CAEtB,GAAIuK,aAAa,CAACnK,MAAM,CAAG,CAAC,CAAE,CAC5BtC,SAAS,CAAC,CAAE6M,OAAO,CAAE,2BAA2B,CAAGJ,aAAa,CAACK,IAAI,CAAC,IAAI,CAAE,CAAC,CAAC,CAC9E,OACF,CAEA;AACA;AACA,KAAM,CAAAC,yBAAyB,CAAG,CAChC,gBAAgB,CAAE,uBAAuB,CAAE,SAAS,CAAE,aAAa,CACnE,YAAY,CAAE,iBAAiB,CAChC,CAEDpL,OAAO,CAACY,GAAG,CAAC,4BAA4B,CAAC,CACzCZ,OAAO,CAACY,GAAG,CAAC,iBAAiB,CAAEiE,MAAM,CAACC,IAAI,CAACS,UAAU,CAAC,CAAC,CAEvD6F,yBAAyB,CAAChL,OAAO,CAACiL,SAAS,EAAI,CAC7C,GAAI9F,UAAU,CAAC+F,cAAc,CAACD,SAAS,CAAC,CAAE,CACxC,KAAM,CAAAvJ,KAAK,CAAGyD,UAAU,CAAC8F,SAAS,CAAC,CACnC,KAAM,CAAAE,OAAO,CAAGzJ,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKkC,SAAS,EAAIlC,KAAK,GAAK,EAAE,EACrD,MAAO,CAAAA,KAAK,GAAK,QAAQ,EAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,GAAK,EAAG,CAEjE1C,OAAO,CAACY,GAAG,CAAC,YAAYyK,SAAS,GAAG,CAAE,CACpCvJ,KAAK,CACLyJ,OAAO,CACPC,MAAM,CAAED,OAAO,CAAG,UAAU,CAAG,SACjC,CAAC,CAAC,CAEF,GAAIA,OAAO,CAAE,CACX,MAAO,CAAAhG,UAAU,CAAC8F,SAAS,CAAC,CAC5BrL,OAAO,CAACY,GAAG,CAAC,eAAeyK,SAAS,WAAWvJ,KAAK,IAAI,CAAC,CAC3D,CACF,CACF,CAAC,CAAC,CAEF9B,OAAO,CAACY,GAAG,CAAC,gBAAgB,CAAEiE,MAAM,CAACC,IAAI,CAACS,UAAU,CAAC,CAAC,CACtDvF,OAAO,CAACY,GAAG,CAAC,mBAAmB,CAAE6K,IAAI,CAACC,SAAS,CAACnG,UAAU,CAAE,IAAI,CAAE,CAAC,CAAC,CAAC,CAErE,GAAI,CAAAoG,MAAM,CACV,GAAIxO,IAAI,GAAK,QAAQ,CAAE,CACrBwO,MAAM,CAAG,KAAM,CAAAnP,UAAU,CAACoP,YAAY,CAACrG,UAAU,CAAC,CACpD,CAAC,IAAM,CACLoG,MAAM,CAAG,KAAM,CAAAnP,UAAU,CAACqP,YAAY,CAAC3O,WAAW,CAAC4O,EAAE,CAAEvG,UAAU,CAAC,CACpE,CAEAvI,QAAQ,CAAC2O,MAAM,CAAC,CAClB,CAAE,MAAO5L,KAAK,CAAE,KAAAgM,WAAA,CAAAC,eAAA,CAAAC,oBAAA,CAAAC,YAAA,CAAAC,YAAA,CAAAC,gBAAA,CAAAC,qBAAA,CAAAC,gBAAA,CAAAC,qBAAA,CACdvM,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CC,OAAO,CAACD,KAAK,CAAC,gBAAgB,CAAE,CAC9ByM,MAAM,CAAEzM,KAAK,CAACyM,MAAM,CACpBtL,IAAI,CAAEnB,KAAK,CAACmB,IAAI,CAChBD,QAAQ,CAAElB,KAAK,CAACkB,QAAQ,CACxBjC,OAAO,CAAEe,KAAK,CAACf,OACjB,CAAC,CAAC,CAEF;AACA,GAAI,CAAA+M,WAAA,CAAAhM,KAAK,CAACmB,IAAI,UAAA6K,WAAA,WAAVA,WAAA,CAAY3N,MAAM,GAAA4N,eAAA,CAAIjM,KAAK,CAACkB,QAAQ,UAAA+K,eAAA,YAAAC,oBAAA,CAAdD,eAAA,CAAgB9K,IAAI,UAAA+K,oBAAA,WAApBA,oBAAA,CAAsB7N,MAAM,CAAE,KAAAqO,YAAA,CAAAC,gBAAA,CAAAC,qBAAA,CACtD;AACA,KAAM,CAAAC,gBAAgB,CAAG,EAAAH,YAAA,CAAA1M,KAAK,CAACmB,IAAI,UAAAuL,YAAA,iBAAVA,YAAA,CAAYrO,MAAM,KAAAsO,gBAAA,CAAI3M,KAAK,CAACkB,QAAQ,UAAAyL,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBxL,IAAI,UAAAyL,qBAAA,iBAApBA,qBAAA,CAAsBvO,MAAM,EAC3E,KAAM,CAAAyO,aAAa,CAAG,CAAC,CAAC,CAExB7M,OAAO,CAACY,GAAG,CAAC,6BAA6B,CAAEgM,gBAAgB,CAAC,CAE5D/H,MAAM,CAACC,IAAI,CAAC8H,gBAAgB,CAAC,CAACxM,OAAO,CAACG,GAAG,EAAI,CAC3C,KAAM,CAAAuM,aAAa,CAAGF,gBAAgB,CAACrM,GAAG,CAAC,CAC3C;AACA,KAAM,CAAAwM,iBAAiB,CAAGC,yBAAyB,CAACzM,GAAG,CAAC,CACxDsM,aAAa,CAACE,iBAAiB,CAAC,CAAG9G,KAAK,CAACC,OAAO,CAAC4G,aAAa,CAAC,CAC3DA,aAAa,CAAC3B,IAAI,CAAC,IAAI,CAAC,CACxB2B,aAAa,CACnB,CAAC,CAAC,CACFzO,SAAS,CAACwO,aAAa,CAAC,CAC1B,CAAC,IAAM,IAAI,CAAAX,YAAA,CAAAnM,KAAK,CAACmB,IAAI,UAAAgL,YAAA,WAAVA,YAAA,CAAYe,KAAK,GAAAd,YAAA,CAAIpM,KAAK,CAACmB,IAAI,UAAAiL,YAAA,WAAVA,YAAA,CAAYe,MAAM,GAAAd,gBAAA,CAAIrM,KAAK,CAACkB,QAAQ,UAAAmL,gBAAA,YAAAC,qBAAA,CAAdD,gBAAA,CAAgBlL,IAAI,UAAAmL,qBAAA,WAApBA,qBAAA,CAAsBY,KAAK,GAAAX,gBAAA,CAAIvM,KAAK,CAACkB,QAAQ,UAAAqL,gBAAA,YAAAC,qBAAA,CAAdD,gBAAA,CAAgBpL,IAAI,UAAAqL,qBAAA,WAApBA,qBAAA,CAAsBW,MAAM,CAAE,KAAAC,YAAA,CAAAC,YAAA,CAAAC,gBAAA,CAAAC,qBAAA,CAAAC,gBAAA,CAAAC,qBAAA,CACjH;AACA,KAAM,CAAAC,YAAY,CAAG,EAAAN,YAAA,CAAApN,KAAK,CAACmB,IAAI,UAAAiM,YAAA,iBAAVA,YAAA,CAAYD,MAAM,KAAAE,YAAA,CAAIrN,KAAK,CAACmB,IAAI,UAAAkM,YAAA,iBAAVA,YAAA,CAAYH,KAAK,KAAAI,gBAAA,CAAItN,KAAK,CAACkB,QAAQ,UAAAoM,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBnM,IAAI,UAAAoM,qBAAA,iBAApBA,qBAAA,CAAsBJ,MAAM,KAAAK,gBAAA,CAAIxN,KAAK,CAACkB,QAAQ,UAAAsM,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBrM,IAAI,UAAAsM,qBAAA,iBAApBA,qBAAA,CAAsBP,KAAK,EAC3H5O,SAAS,CAAC,CAAE6M,OAAO,CAAEuC,YAAa,CAAC,CAAC,CACtC,CAAC,IAAM,IAAI1N,KAAK,CAAC2N,iBAAiB,EAAI3N,KAAK,CAAC2N,iBAAiB,CAAC,CAAC,CAAE,CAC/D,KAAM,CAAAd,gBAAgB,CAAG7M,KAAK,CAAC4N,mBAAmB,CAAC,CAAC,CACpD3N,OAAO,CAACY,GAAG,CAAC,6BAA6B,CAAEgM,gBAAgB,CAAC,CAC5DvO,SAAS,CAACuO,gBAAgB,CAAC,CAC7B,CAAC,IAAM,KAAAgB,YAAA,CAAAC,gBAAA,CAAAC,qBAAA,CAAAC,YAAA,CACL;AACA,KAAM,CAAAN,YAAY,CAAG,EAAAG,YAAA,CAAA7N,KAAK,CAACmB,IAAI,UAAA0M,YAAA,iBAAVA,YAAA,CAAY5O,OAAO,KAAA6O,gBAAA,CACrB9N,KAAK,CAACkB,QAAQ,UAAA4M,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB3M,IAAI,UAAA4M,qBAAA,iBAApBA,qBAAA,CAAsB9O,OAAO,KAAA+O,YAAA,CAC7BhO,KAAK,CAACmB,IAAI,UAAA6M,YAAA,iBAAVA,YAAA,CAAYd,KAAK,GACjBlN,KAAK,CAACf,OAAO,EACb,mFAAmF,CACtGX,SAAS,CAAC,CAAE6M,OAAO,CAAEuC,YAAa,CAAC,CAAC,CACtC,CACF,CAAC,OAAS,CACR/O,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED;AACA,KAAM,CAAAsO,yBAAyB,CAAIgB,gBAAgB,EAAK,CACtD,KAAM,CAAAC,YAAY,CAAG,CACnB,YAAY,CAAE,UAAU,CACxB,YAAY,CAAE,UAAU,CACxB,cAAc,CAAE,YAAY,CAC5B,MAAM,CAAE,MAAM,CACd,cAAc,CAAE,cAAc,CAC9B,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,QAAQ,CAClB,gBAAgB,CAAE,gBAAgB,CAClC,kBAAkB,CAAE,kBAAkB,CACtC,mBAAmB,CAAE,mBAAmB,CACxC,SAAS,CAAE,SAAS,CACpB,aAAa,CAAE,aAAa,CAC5B,WAAW,CAAE,WAAW,CACxB,gBAAgB,CAAE,gBAAgB,CAClC,cAAc,CAAE,cAAc,CAC9B,eAAe,CAAE,eAAe,CAChC,UAAU,CAAE,UAAU,CACtB,SAAS,CAAE,SAAS,CACpB,aAAa,CAAE,aAAa,CAC5B,cAAc,CAAE,cAAc,CAC9B,eAAe,CAAE,eAAe,CAChC,mBAAmB,CAAE,mBAAmB,CACxC,iBAAiB,CAAE,iBAAiB,CACpC,cAAc,CAAE,cAAc,CAC9B,aAAa,CAAE,aAAa,CAC5B,aAAa,CAAE,aAAa,CAC5B,YAAY,CAAE,YAAY,CAC1B,kBAAkB,CAAE,kBAAkB,CACtC,wBAAwB,CAAE,wBAAwB,CAClD,iBAAiB,CAAE,iBAAiB,CACpC,YAAY,CAAE,YAAY,CAC1B,QAAQ,CAAE,QAAQ,CAClB,SAAS,CAAE,SAAS,CACpB,UAAU,CAAE,UAAU,CACtB,iBAAiB,CAAE,iBAAiB,CACpC,kBAAkB,CAAE,kBAAkB,CACtC,uBAAuB,CAAE,uBAAuB,CAChD,sBAAsB,CAAE,sBAAsB,CAC9C,uBAAuB,CAAE,uBAAuB,CAChD,aAAa,CAAE,aAAa,CAC5B,kBAAkB,CAAE,kBAAkB,CACtC,sBAAsB,CAAE,sBAAsB,CAC9C,gBAAgB,CAAE,gBAAgB,CAClC,YAAY,CAAE,YAChB,CAAC,CAED,MAAO,CAAAA,YAAY,CAACD,gBAAgB,CAAC,EAAIA,gBAAgB,CAACE,WAAW,CAAC,CAAC,CACzE,CAAC,CAED,KAAM,CAAA9K,eAAe,CAAI/C,KAAK,EAAK,CACjC,GAAI,CAACA,KAAK,CAAC8C,WAAW,CAAE,MAAO,KAAI,CAEnC,KAAM,CAAAgL,cAAc,CAAGjQ,QAAQ,CAACmC,KAAK,CAAC8C,WAAW,CAAC9C,KAAK,CAAC,CACxD,KAAM,CAAA+N,aAAa,CAAG/N,KAAK,CAAC8C,WAAW,CAACrB,KAAK,CAE7C;AACA,GAAI,MAAO,CAAAsM,aAAa,GAAK,SAAS,EAAIA,aAAa,GAAK,MAAM,EAAIA,aAAa,GAAK,OAAO,CAAE,CAC/F,MAAO,CAAAD,cAAc,IAAMC,aAAa,GAAK,IAAI,EAAIA,aAAa,GAAK,MAAM,CAAC,CAChF,CAEA,MAAO,CAAAD,cAAc,GAAKC,aAAa,CACzC,CAAC,CAED,KAAM,CAAAC,qBAAqB,CAAGA,CAAA,GAAM,CAClC,GAAI,CAACrQ,UAAU,EAAI,CAACA,UAAU,CAAC4B,MAAM,CAAE,MAAO,CAAC,CAAC,CAEhD,KAAM,CAAA0O,QAAQ,CAAG,CAAC,CAAC,CACnBtQ,UAAU,CAAC4B,MAAM,CAACQ,OAAO,CAAEC,KAAK,EAAK,CACnC,KAAM,CAAAkO,UAAU,CAAGlO,KAAK,CAACmO,OAAO,EAAI,SAAS,CAC7C,GAAI,CAACF,QAAQ,CAACC,UAAU,CAAC,CAAE,CACzBD,QAAQ,CAACC,UAAU,CAAC,CAAG,CACrBtB,KAAK,CAAEwB,eAAe,CAACF,UAAU,CAAC,CAClC3O,MAAM,CAAE,EACV,CAAC,CACH,CACA0O,QAAQ,CAACC,UAAU,CAAC,CAAC3O,MAAM,CAACa,IAAI,CAACJ,KAAK,CAAC,CACzC,CAAC,CAAC,CAEF,MAAO,CAAAiO,QAAQ,CACjB,CAAC,CAED,KAAM,CAAAG,eAAe,CAAIF,UAAU,EAAK,CACtC,KAAM,CAAAG,MAAM,CAAG,CACbC,YAAY,CAAE,sBAAsB,CACpCC,WAAW,CAAE,qBAAqB,CAClCC,YAAY,CAAE,sBAAsB,CACpCC,YAAY,CAAE,sBAAsB,CACpCC,aAAa,CAAE,uBAAuB,CACtCC,eAAe,CAAE,kBAAkB,CACnCC,WAAW,CAAE,qBAAqB,CAClCC,gBAAgB,CAAE,mBAAmB,CACrCC,aAAa,CAAE,uBAAuB,CACtCjE,OAAO,CAAE,qBACX,CAAC,CACD,MAAO,CAAAwD,MAAM,CAACH,UAAU,CAAC,EAAIA,UAAU,CACzC,CAAC,CAED,KAAM,CAAAD,QAAQ,CAAGtQ,UAAU,CAAGqQ,qBAAqB,CAAC,CAAC,CAAG,CAAC,CAAC,CAI1D,mBACExR,KAAA,QAAKuS,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCxS,KAAA,QAAKuS,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B1S,IAAA,OAAA0S,QAAA,CAAKlS,IAAI,GAAK,QAAQ,CAAG,mBAAmB,CAAG,aAAa,CAAK,CAAC,CACjEa,UAAU,eACTnB,KAAA,QAAKuS,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB1S,IAAA,SAAMyS,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAErR,UAAU,CAACyE,IAAI,CAAO,CAAC,CACnDzE,UAAU,CAACsR,WAAW,eACrB3S,IAAA,SAAMyS,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAErR,UAAU,CAACsR,WAAW,CAAO,CAClE,EACE,CACN,EACE,CAAC,CAELlR,MAAM,CAAC8M,OAAO,eACbvO,IAAA,QAAKyS,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAEjR,MAAM,CAAC8M,OAAO,CAAM,CACzD,CAGArG,MAAM,CAACC,IAAI,CAAC1G,MAAM,CAAC,CAACuC,MAAM,CAAG,CAAC,EAAI,CAACvC,MAAM,CAAC8M,OAAO,eAChDrO,KAAA,QAAKuS,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC1S,IAAA,WAAA0S,QAAA,CAAQ,kCAAgC,CAAQ,CAAC,cACjD1S,IAAA,OAAI4S,KAAK,CAAE,CAAEC,MAAM,CAAE,cAAe,CAAE,CAAAH,QAAA,CACnCxK,MAAM,CAACkG,OAAO,CAAC3M,MAAM,CAAC,CACpBgI,MAAM,CAACqJ,KAAA,MAAC,CAAClP,GAAG,CAAC,CAAAkP,KAAA,OAAK,CAAAlP,GAAG,GAAK,SAAS,GAAC,CACpCgG,GAAG,CAACmJ,KAAA,MAAC,CAACnP,GAAG,CAAEvB,OAAO,CAAC,CAAA0Q,KAAA,oBAClB/S,IAAA,OAAA0S,QAAA,CAAerQ,OAAO,EAAbuB,GAAkB,CAAC,EAC7B,CAAC,CACF,CAAC,EACF,CACN,cAED1D,KAAA,SAAMG,QAAQ,CAAE+H,YAAa,CAACqK,SAAS,CAAC,aAAa,CAAAC,QAAA,eAEnDxS,KAAA,QAAKuS,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B1S,IAAA,OAAA0S,QAAA,CAAI,+BAA6B,CAAI,CAAC,cAGtCxS,KAAA,QAAKuS,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBxS,KAAA,UAAOuS,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,WACnB,cAAA1S,IAAA,SAAMyS,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,EACvC,CAAC,cACRxS,KAAA,WACEiF,KAAK,CAAEpE,gBAAiB,CACxBiS,QAAQ,CAAE/N,oBAAqB,CAC/BgO,QAAQ,CAAEtR,OAAO,CAAClB,SAAU,CAC5BgS,SAAS,CAAE,eAAehR,MAAM,CAACkE,QAAQ,CAAG,OAAO,CAAG,EAAE,EAAG,CAC3DW,QAAQ,MAAAoM,QAAA,eAER1S,IAAA,WAAQmF,KAAK,CAAC,EAAE,CAAAuN,QAAA,CACb/Q,OAAO,CAAClB,SAAS,CAAG,sBAAsB,CAAG,iBAAiB,CACzD,CAAC,CACRA,SAAS,CAACmJ,GAAG,CAACjE,QAAQ,eACrB3F,IAAA,WAA0BmF,KAAK,CAAEQ,QAAQ,CAACwJ,EAAG,CAAAuD,QAAA,CAC1C/M,QAAQ,CAACG,IAAI,EADHH,QAAQ,CAACwJ,EAEd,CACT,CAAC,EACI,CAAC,CACR1N,MAAM,CAACkE,QAAQ,eACd3F,IAAA,QAAKyS,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEjR,MAAM,CAACkE,QAAQ,CAAM,CACtD,CACAlE,MAAM,CAAChB,SAAS,eACfT,IAAA,QAAKyS,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEjR,MAAM,CAAChB,SAAS,CAAM,CACvD,EACE,CAAC,cAGNP,KAAA,QAAKuS,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBxS,KAAA,UAAOuS,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,WACnB,cAAA1S,IAAA,SAAMyS,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,EACvC,CAAC,cACRxS,KAAA,WACEiF,KAAK,CAAElE,gBAAiB,CACxB+R,QAAQ,CAAE3N,oBAAqB,CAC/B4N,QAAQ,CAAE,CAAClS,gBAAgB,EAAIY,OAAO,CAAChB,UAAW,CAClD8R,SAAS,CAAE,eAAehR,MAAM,CAACmE,QAAQ,CAAG,OAAO,CAAG,EAAE,EAAG,CAC3DU,QAAQ,MAAAoM,QAAA,eAER1S,IAAA,WAAQmF,KAAK,CAAC,EAAE,CAAAuN,QAAA,CACb,CAAC3R,gBAAgB,CACd,uBAAuB,CACvBY,OAAO,CAAChB,UAAU,CAChB,uBAAuB,CACvB,iBAAiB,CAEjB,CAAC,CACRA,UAAU,CAACiJ,GAAG,CAAChE,QAAQ,eACtB5F,IAAA,WAA0BmF,KAAK,CAAES,QAAQ,CAACuJ,EAAG,CAAAuD,QAAA,CAC1C9M,QAAQ,CAACE,IAAI,EADHF,QAAQ,CAACuJ,EAEd,CACT,CAAC,EACI,CAAC,CACR1N,MAAM,CAACmE,QAAQ,eACd5F,IAAA,QAAKyS,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEjR,MAAM,CAACmE,QAAQ,CAAM,CACtD,CACAnE,MAAM,CAACd,UAAU,eAChBX,IAAA,QAAKyS,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEjR,MAAM,CAACd,UAAU,CAAM,CACxD,EACE,CAAC,CAGLqB,gBAAgB,CAACI,sBAAsB,eACtClC,KAAA,QAAKuS,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB1S,IAAA,UAAOyS,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,wBAE9B,CAAO,CAAC,cACRxS,KAAA,WACEiF,KAAK,CAAEhE,kBAAmB,CAC1B6R,QAAQ,CAAE1N,sBAAuB,CACjC2N,QAAQ,CAAE,CAAChS,gBAAgB,EAAIU,OAAO,CAACd,WAAY,CACnD4R,SAAS,CAAC,aAAa,CACvBnM,QAAQ,MAAAoM,QAAA,eAER1S,IAAA,WAAQmF,KAAK,CAAC,EAAE,CAAAuN,QAAA,CACb,CAACzR,gBAAgB,CACd,uBAAuB,CACvBU,OAAO,CAACd,WAAW,CACjB,yBAAyB,CACzB,+BAA+B,CAE/B,CAAC,CACRA,WAAW,CAAC+I,GAAG,CAAC/D,UAAU,eACzB7F,IAAA,WAA4BmF,KAAK,CAAEU,UAAU,CAACsJ,EAAG,CAAAuD,QAAA,CAC9C7M,UAAU,CAACC,IAAI,EADLD,UAAU,CAACsJ,EAEhB,CACT,CAAC,EACI,CAAC,CACR1N,MAAM,CAACZ,WAAW,eACjBb,IAAA,QAAKyS,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEjR,MAAM,CAACZ,WAAW,CAAM,CACzD,EACE,CACN,CAGAc,OAAO,CAACE,IAAI,eACX7B,IAAA,QAAKyS,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B1S,IAAA,SAAA0S,QAAA,CAAM,+BAA6B,CAAM,CAAC,CACvC,CACN,CAEA1Q,gBAAgB,CAACK,OAAO,EAAI,CAACV,OAAO,CAACE,IAAI,eACxC7B,IAAA,QAAKyS,SAAS,CAAE,kBACdzQ,gBAAgB,CAACE,eAAe,EAAIF,gBAAgB,CAACG,iBAAiB,CAClE,SAAS,CACTH,gBAAgB,CAACK,OAAO,CAAC+D,QAAQ,CAAC,SAAS,CAAC,CAC1C,OAAO,CACP,MAAM,EACX,CAAAsM,QAAA,CACA1Q,gBAAgB,CAACK,OAAO,CACtB,CACN,CAEAZ,MAAM,CAACI,IAAI,eACV7B,IAAA,QAAKyS,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEjR,MAAM,CAACI,IAAI,CAAM,CAClD,EACE,CAAC,CAGLR,UAAU,EAAI6G,MAAM,CAACkG,OAAO,CAACuD,QAAQ,CAAC,CAAC/H,GAAG,CAACsJ,KAAA,MAAC,CAACtB,UAAU,CAAEC,OAAO,CAAC,CAAAqB,KAAA,oBAChEhT,KAAA,QAAsBuS,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC5C1S,IAAA,OAAA0S,QAAA,CAAKb,OAAO,CAACvB,KAAK,CAAK,CAAC,cACxBtQ,IAAA,QAAKyS,SAAS,CAAC,aAAa,CAAAC,QAAA,CACzBb,OAAO,CAAC5O,MAAM,CACZwG,MAAM,CAAC/F,KAAK,EAAI+C,eAAe,CAAC/C,KAAK,CAAC,CAAC,CACvCkG,GAAG,CAAC,CAAClG,KAAK,CAAEyP,UAAU,gBACrBnT,IAAA,CAACF,SAAS,EAER4D,KAAK,CAAEA,KAAM,CACbyB,KAAK,CAAE5D,QAAQ,CAACmC,KAAK,CAACE,GAAG,CAAE,CAC3BoP,QAAQ,CAAG7N,KAAK,EAAKI,iBAAiB,CAAC7B,KAAK,CAACE,GAAG,CAAEuB,KAAK,CAAE,CACzD/B,KAAK,CAAE3B,MAAM,CAACiC,KAAK,CAACE,GAAG,CAAE,EAJpB,GAAGgO,UAAU,IAAIlO,KAAK,CAACE,GAAG,IAAIuP,UAAU,EAK9C,CACF,CAAC,CACD,CAAC,GAdEvB,UAeL,CAAC,EACP,CAAC,cAKF1R,KAAA,QAAKuS,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B1S,IAAA,WACE6G,IAAI,CAAC,QAAQ,CACbuM,OAAO,CAAE9S,QAAS,CAClBmS,SAAS,CAAC,iBAAiB,CAC3BQ,QAAQ,CAAEnR,UAAW,CAAA4Q,QAAA,CACtB,QAED,CAAQ,CAAC,cACT1S,IAAA,WACE6G,IAAI,CAAC,QAAQ,CACb4L,SAAS,CAAC,iBAAiB,CAC3BQ,QAAQ,CAAEnR,UAAU,EAAI,CAACf,gBAAgB,EAAI,CAACE,gBAAiB,CAAAyR,QAAA,CAE9D5Q,UAAU,CACNtB,IAAI,GAAK,QAAQ,CAAG,aAAa,CAAG,aAAa,CACjDA,IAAI,GAAK,QAAQ,CAAG,eAAe,CAAG,eAAgB,CAErD,CAAC,EACN,CAAC,EACF,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAL,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}