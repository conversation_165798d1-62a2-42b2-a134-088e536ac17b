const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(response.status, errorData.message || 'An error occurred', errorData);
      }

      // Handle blob responses (for file downloads)
      if (options.responseType === 'blob') {
        return { data: await response.blob() };
      }

      const data = await response.json();
      return { data }; // Wrap in data property for consistency
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(0, 'Network error', { originalError: error.message });
    }
  }

  // Generic HTTP methods
  async get(endpoint, options = {}) {
    return this.request(endpoint, { method: 'GET', ...options });
  }

  async post(endpoint, data = null, options = {}) {
    const config = { method: 'POST', ...options };
    if (data) {
      config.body = JSON.stringify(data);
    }
    return this.request(endpoint, config);
  }

  async put(endpoint, data = null, options = {}) {
    const config = { method: 'PUT', ...options };
    if (data) {
      config.body = JSON.stringify(data);
    }
    return this.request(endpoint, config);
  }

  async delete(endpoint, options = {}) {
    return this.request(endpoint, { method: 'DELETE', ...options });
  }

  // Division endpoints
  async getDivisions() {
    return this.get('/divisions');
  }

  async getDivision(id) {
    return this.get(`/divisions/${id}`);
  }

  // Category endpoints
  async getCategories() {
    return this.get('/categories');
  }

  async getCategory(id) {
    return this.get(`/categories/${id}`);
  }

  async getCategoriesByDivision(divisionId) {
    return this.get(`/categories/division/${divisionId}`);
  }

  // FirmNature endpoints
  async getFirmNatures() {
    return this.get('/firmnatures');
  }

  async getFirmNature(id) {
    return this.get(`/firmnatures/${id}`);
  }

  async getFirmNaturesByCategory(categoryId) {
    return this.get(`/firmnatures/category/${categoryId}`);
  }

  // States endpoints
  async getStates() {
    return this.get('/states');
  }

  // Person endpoints
  async getPersons(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.get(`/persons${queryString ? `?${queryString}` : ''}`);
  }

  async getPerson(id) {
    return this.get(`/persons/${id}`);
  }

  async createPerson(personData) {
    return this.post('/persons', personData);
  }

  async updatePerson(id, personData) {
    return this.put(`/persons/${id}`, personData);
  }

  async deletePerson(id) {
    return this.delete(`/persons/${id}`);
  }

  async searchPersons(searchRequest) {
    return this.post('/persons/search', searchRequest);
  }

  async getPersonsByDivision(divisionId) {
    return this.get(`/persons/division/${divisionId}`);
  }

  async getPersonsByCategory(categoryId) {
    return this.get(`/persons/category/${categoryId}`);
  }

  async getPersonsByFirmNature(firmNatureId) {
    return this.get(`/persons/firmnature/${firmNatureId}`);
  }

  async getPersonStatistics() {
    return this.get('/persons/statistics');
  }

  async getPersonEnums() {
    return this.get('/persons/enums');
  }

  // Bulk operations
  async bulkCreatePersons(personsData) {
    return this.post('/persons/bulk', personsData);
  }

  async bulkSoftDeletePersons(personIds) {
    return this.post('/persons/bulk/soft-delete', personIds);
  }

  async bulkRestorePersons(personIds) {
    return this.post('/persons/bulk/restore', personIds);
  }
}

class ApiError extends Error {
  constructor(status, message, data = {}) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }

  isValidationError() {
    return this.status === 400 && this.data.errors;
  }

  isNotFoundError() {
    return this.status === 404;
  }

  isServerError() {
    return this.status >= 500;
  }

  getValidationErrors() {
    return this.data.errors || {};
  }
}

export { ApiService, ApiError };
export default new ApiService();
