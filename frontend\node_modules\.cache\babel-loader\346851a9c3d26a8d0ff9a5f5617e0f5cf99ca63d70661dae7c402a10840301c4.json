{"ast": null, "code": "import { isMotionComponent } from './is-motion-component.mjs';\nimport { motionComponentSymbol } from './symbol.mjs';\n\n/**\n * Unwraps a `motion` component and returns either a string for `motion.div` or\n * the React component for `motion(Component)`.\n *\n * If the component is not a `motion` component it returns undefined.\n */\nfunction unwrapMotionComponent(component) {\n  if (isMotionComponent(component)) {\n    return component[motionComponentSymbol];\n  }\n  return undefined;\n}\nexport { unwrapMotionComponent };", "map": {"version": 3, "names": ["isMotionComponent", "motionComponentSymbol", "unwrapMotionComponent", "component", "undefined"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/motion/utils/unwrap-motion-component.mjs"], "sourcesContent": ["import { isMotionComponent } from './is-motion-component.mjs';\nimport { motionComponentSymbol } from './symbol.mjs';\n\n/**\n * Unwraps a `motion` component and returns either a string for `motion.div` or\n * the React component for `motion(Component)`.\n *\n * If the component is not a `motion` component it returns undefined.\n */\nfunction unwrapMotionComponent(component) {\n    if (isMotionComponent(component)) {\n        return component[motionComponentSymbol];\n    }\n    return undefined;\n}\n\nexport { unwrapMotionComponent };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,2BAA2B;AAC7D,SAASC,qBAAqB,QAAQ,cAAc;;AAEpD;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAACC,SAAS,EAAE;EACtC,IAAIH,iBAAiB,CAACG,SAAS,CAAC,EAAE;IAC9B,OAAOA,SAAS,CAACF,qBAAqB,CAAC;EAC3C;EACA,OAAOG,SAAS;AACpB;AAEA,SAASF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}