using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrmApi.Migrations
{
    /// <inheritdoc />
    public partial class RenameSubCategoryToFirmNature : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Drop foreign key constraint first
            migrationBuilder.DropForeignKey(
                name: "FK_persons_subcategories",
                table: "persons");

            // Drop indexes on the old column
            migrationBuilder.DropIndex(
                name: "IX_persons_subcategory_id",
                table: "persons");

            // Rename the table from sub_categories to firm_natures
            migrationBuilder.RenameTable(
                name: "sub_categories",
                newName: "firm_natures");

            // Rename the column in persons table
            migrationBuilder.RenameColumn(
                name: "subcategory_id",
                table: "persons",
                newName: "firm_nature_id");

            // Make firm_nature_id NOT NULL (required field)
            migrationBuilder.AlterColumn<int>(
                name: "firm_nature_id",
                table: "persons",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            // Recreate the foreign key constraint with new names
            migrationBuilder.AddForeignKey(
                name: "FK_persons_firm_natures",
                table: "persons",
                column: "firm_nature_id",
                principalTable: "firm_natures",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            // Recreate indexes with new names
            migrationBuilder.CreateIndex(
                name: "IX_persons_firm_nature_id",
                table: "persons",
                column: "firm_nature_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop foreign key constraint
            migrationBuilder.DropForeignKey(
                name: "FK_persons_firm_natures",
                table: "persons");

            // Drop indexes
            migrationBuilder.DropIndex(
                name: "IX_persons_firm_nature_id",
                table: "persons");

            // Make firm_nature_id nullable again
            migrationBuilder.AlterColumn<int>(
                name: "firm_nature_id",
                table: "persons",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            // Rename the column back
            migrationBuilder.RenameColumn(
                name: "firm_nature_id",
                table: "persons",
                newName: "subcategory_id");

            // Rename the table back
            migrationBuilder.RenameTable(
                name: "firm_natures",
                newName: "sub_categories");

            // Recreate the original foreign key constraint
            migrationBuilder.AddForeignKey(
                name: "FK_persons_subcategories",
                table: "persons",
                column: "subcategory_id",
                principalTable: "sub_categories",
                principalColumn: "id");

            // Recreate original indexes
            migrationBuilder.CreateIndex(
                name: "IX_persons_subcategory_id",
                table: "persons",
                column: "subcategory_id");
        }
    }
}
