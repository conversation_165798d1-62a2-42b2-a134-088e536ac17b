{"ast": null, "code": "const instantAnimationState = {\n  current: false\n};\nexport { instantAnimationState };", "map": {"version": 3, "names": ["instantAnimationState", "current"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/utils/use-instant-transition-state.mjs"], "sourcesContent": ["const instantAnimationState = {\n    current: false,\n};\n\nexport { instantAnimationState };\n"], "mappings": "AAAA,MAAMA,qBAAqB,GAAG;EAC1BC,OAAO,EAAE;AACb,CAAC;AAED,SAASD,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}