{"ast": null, "code": "import { resize } from '../resize/index.mjs';\nimport { createScrollInfo } from './info.mjs';\nimport { createOnScrollHandler } from './on-scroll-handler.mjs';\nimport { frame, cancelFrame, frameData } from '../../../frameloop/frame.mjs';\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = element => element === document.documentElement ? window : element;\nfunction scrollInfo(onScroll) {\n  let {\n    container = document.documentElement,\n    ...options\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let containerHandlers = onScrollHandlers.get(container);\n  /**\n   * Get the onScroll handlers for this container.\n   * If one isn't found, create a new one.\n   */\n  if (!containerHandlers) {\n    containerHandlers = new Set();\n    onScrollHandlers.set(container, containerHandlers);\n  }\n  /**\n   * Create a new onScroll handler for the provided callback.\n   */\n  const info = createScrollInfo();\n  const containerHandler = createOnScrollHandler(container, onScroll, info, options);\n  containerHandlers.add(containerHandler);\n  /**\n   * Check if there's a scroll event listener for this container.\n   * If not, create one.\n   */\n  if (!scrollListeners.has(container)) {\n    const measureAll = () => {\n      for (const handler of containerHandlers) handler.measure();\n    };\n    const updateAll = () => {\n      for (const handler of containerHandlers) {\n        handler.update(frameData.timestamp);\n      }\n    };\n    const notifyAll = () => {\n      for (const handler of containerHandlers) handler.notify();\n    };\n    const listener = () => {\n      frame.read(measureAll, false, true);\n      frame.read(updateAll, false, true);\n      frame.update(notifyAll, false, true);\n    };\n    scrollListeners.set(container, listener);\n    const target = getEventTarget(container);\n    window.addEventListener(\"resize\", listener, {\n      passive: true\n    });\n    if (container !== document.documentElement) {\n      resizeListeners.set(container, resize(container, listener));\n    }\n    target.addEventListener(\"scroll\", listener, {\n      passive: true\n    });\n  }\n  const listener = scrollListeners.get(container);\n  frame.read(listener, false, true);\n  return () => {\n    var _a;\n    cancelFrame(listener);\n    /**\n     * Check if we even have any handlers for this container.\n     */\n    const currentHandlers = onScrollHandlers.get(container);\n    if (!currentHandlers) return;\n    currentHandlers.delete(containerHandler);\n    if (currentHandlers.size) return;\n    /**\n     * If no more handlers, remove the scroll listener too.\n     */\n    const scrollListener = scrollListeners.get(container);\n    scrollListeners.delete(container);\n    if (scrollListener) {\n      getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n      (_a = resizeListeners.get(container)) === null || _a === void 0 ? void 0 : _a();\n      window.removeEventListener(\"resize\", scrollListener);\n    }\n  };\n}\nexport { scrollInfo };", "map": {"version": 3, "names": ["resize", "createScrollInfo", "createOnScrollHandler", "frame", "cancelFrame", "frameData", "scrollListeners", "WeakMap", "resizeListeners", "onScrollHandlers", "getEventTarget", "element", "document", "documentElement", "window", "scrollInfo", "onScroll", "container", "options", "arguments", "length", "undefined", "containerHandlers", "get", "Set", "set", "info", "containerHandler", "add", "has", "measureAll", "handler", "measure", "updateAll", "update", "timestamp", "notifyAll", "notify", "listener", "read", "target", "addEventListener", "passive", "_a", "currentHandlers", "delete", "size", "scrollListener", "removeEventListener"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs"], "sourcesContent": ["import { resize } from '../resize/index.mjs';\nimport { createScrollInfo } from './info.mjs';\nimport { createOnScrollHandler } from './on-scroll-handler.mjs';\nimport { frame, cancelFrame, frameData } from '../../../frameloop/frame.mjs';\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.documentElement ? window : element;\nfunction scrollInfo(onScroll, { container = document.documentElement, ...options } = {}) {\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = createScrollInfo();\n    const containerHandler = createOnScrollHandler(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const measureAll = () => {\n            for (const handler of containerHandlers)\n                handler.measure();\n        };\n        const updateAll = () => {\n            for (const handler of containerHandlers) {\n                handler.update(frameData.timestamp);\n            }\n        };\n        const notifyAll = () => {\n            for (const handler of containerHandlers)\n                handler.notify();\n        };\n        const listener = () => {\n            frame.read(measureAll, false, true);\n            frame.read(updateAll, false, true);\n            frame.update(notifyAll, false, true);\n        };\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, resize(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n    }\n    const listener = scrollListeners.get(container);\n    frame.read(listener, false, true);\n    return () => {\n        var _a;\n        cancelFrame(listener);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const currentHandlers = onScrollHandlers.get(container);\n        if (!currentHandlers)\n            return;\n        currentHandlers.delete(containerHandler);\n        if (currentHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const scrollListener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (scrollListener) {\n            getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n            (_a = resizeListeners.get(container)) === null || _a === void 0 ? void 0 : _a();\n            window.removeEventListener(\"resize\", scrollListener);\n        }\n    };\n}\n\nexport { scrollInfo };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,KAAK,EAAEC,WAAW,EAAEC,SAAS,QAAQ,8BAA8B;AAE5E,MAAMC,eAAe,GAAG,IAAIC,OAAO,CAAC,CAAC;AACrC,MAAMC,eAAe,GAAG,IAAID,OAAO,CAAC,CAAC;AACrC,MAAME,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;AACtC,MAAMG,cAAc,GAAIC,OAAO,IAAKA,OAAO,KAAKC,QAAQ,CAACC,eAAe,GAAGC,MAAM,GAAGH,OAAO;AAC3F,SAASI,UAAUA,CAACC,QAAQ,EAA6D;EAAA,IAA3D;IAAEC,SAAS,GAAGL,QAAQ,CAACC,eAAe;IAAE,GAAGK;EAAQ,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACnF,IAAIG,iBAAiB,GAAGb,gBAAgB,CAACc,GAAG,CAACN,SAAS,CAAC;EACvD;AACJ;AACA;AACA;EACI,IAAI,CAACK,iBAAiB,EAAE;IACpBA,iBAAiB,GAAG,IAAIE,GAAG,CAAC,CAAC;IAC7Bf,gBAAgB,CAACgB,GAAG,CAACR,SAAS,EAAEK,iBAAiB,CAAC;EACtD;EACA;AACJ;AACA;EACI,MAAMI,IAAI,GAAGzB,gBAAgB,CAAC,CAAC;EAC/B,MAAM0B,gBAAgB,GAAGzB,qBAAqB,CAACe,SAAS,EAAED,QAAQ,EAAEU,IAAI,EAAER,OAAO,CAAC;EAClFI,iBAAiB,CAACM,GAAG,CAACD,gBAAgB,CAAC;EACvC;AACJ;AACA;AACA;EACI,IAAI,CAACrB,eAAe,CAACuB,GAAG,CAACZ,SAAS,CAAC,EAAE;IACjC,MAAMa,UAAU,GAAGA,CAAA,KAAM;MACrB,KAAK,MAAMC,OAAO,IAAIT,iBAAiB,EACnCS,OAAO,CAACC,OAAO,CAAC,CAAC;IACzB,CAAC;IACD,MAAMC,SAAS,GAAGA,CAAA,KAAM;MACpB,KAAK,MAAMF,OAAO,IAAIT,iBAAiB,EAAE;QACrCS,OAAO,CAACG,MAAM,CAAC7B,SAAS,CAAC8B,SAAS,CAAC;MACvC;IACJ,CAAC;IACD,MAAMC,SAAS,GAAGA,CAAA,KAAM;MACpB,KAAK,MAAML,OAAO,IAAIT,iBAAiB,EACnCS,OAAO,CAACM,MAAM,CAAC,CAAC;IACxB,CAAC;IACD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;MACnBnC,KAAK,CAACoC,IAAI,CAACT,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC;MACnC3B,KAAK,CAACoC,IAAI,CAACN,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC;MAClC9B,KAAK,CAAC+B,MAAM,CAACE,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC;IACxC,CAAC;IACD9B,eAAe,CAACmB,GAAG,CAACR,SAAS,EAAEqB,QAAQ,CAAC;IACxC,MAAME,MAAM,GAAG9B,cAAc,CAACO,SAAS,CAAC;IACxCH,MAAM,CAAC2B,gBAAgB,CAAC,QAAQ,EAAEH,QAAQ,EAAE;MAAEI,OAAO,EAAE;IAAK,CAAC,CAAC;IAC9D,IAAIzB,SAAS,KAAKL,QAAQ,CAACC,eAAe,EAAE;MACxCL,eAAe,CAACiB,GAAG,CAACR,SAAS,EAAEjB,MAAM,CAACiB,SAAS,EAAEqB,QAAQ,CAAC,CAAC;IAC/D;IACAE,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,QAAQ,EAAE;MAAEI,OAAO,EAAE;IAAK,CAAC,CAAC;EAClE;EACA,MAAMJ,QAAQ,GAAGhC,eAAe,CAACiB,GAAG,CAACN,SAAS,CAAC;EAC/Cd,KAAK,CAACoC,IAAI,CAACD,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC;EACjC,OAAO,MAAM;IACT,IAAIK,EAAE;IACNvC,WAAW,CAACkC,QAAQ,CAAC;IACrB;AACR;AACA;IACQ,MAAMM,eAAe,GAAGnC,gBAAgB,CAACc,GAAG,CAACN,SAAS,CAAC;IACvD,IAAI,CAAC2B,eAAe,EAChB;IACJA,eAAe,CAACC,MAAM,CAAClB,gBAAgB,CAAC;IACxC,IAAIiB,eAAe,CAACE,IAAI,EACpB;IACJ;AACR;AACA;IACQ,MAAMC,cAAc,GAAGzC,eAAe,CAACiB,GAAG,CAACN,SAAS,CAAC;IACrDX,eAAe,CAACuC,MAAM,CAAC5B,SAAS,CAAC;IACjC,IAAI8B,cAAc,EAAE;MAChBrC,cAAc,CAACO,SAAS,CAAC,CAAC+B,mBAAmB,CAAC,QAAQ,EAAED,cAAc,CAAC;MACvE,CAACJ,EAAE,GAAGnC,eAAe,CAACe,GAAG,CAACN,SAAS,CAAC,MAAM,IAAI,IAAI0B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC;MAC/E7B,MAAM,CAACkC,mBAAmB,CAAC,QAAQ,EAAED,cAAc,CAAC;IACxD;EACJ,CAAC;AACL;AAEA,SAAShC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}