{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion}from'framer-motion';import{FiPie<PERSON>hart,FiBarChart2,FiGrid,FiLayers,FiRefreshCw}from'react-icons/fi';import axios from'axios';import{Bar}from'react-chartjs-2';import{Chart as ChartJS,CategoryScale,LinearScale,BarElement,Title,Tooltip,Legend}from'chart.js';// Register Chart.js components\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";ChartJS.register(CategoryScale,LinearScale,BarElement,Title,Tooltip,Legend);const API_BASE_URL='http://localhost:5000/api';const Dashboard=()=>{const[dashboardData,setDashboardData]=useState({totalDivisions:0,totalCategories:0,totalSubCategories:0,totalStates:0,divisions:[],categories:[],subCategories:[],states:[]});const[loading,setLoading]=useState(true);const[refreshing,setRefreshing]=useState(false);useEffect(()=>{fetchDashboardData();},[]);const fetchDashboardData=async()=>{setLoading(true);try{const[divisionsRes,categoriesRes,subCategoriesRes,statesRes]=await Promise.all([axios.get(`${API_BASE_URL}/divisions`),axios.get(`${API_BASE_URL}/categories`),axios.get(`${API_BASE_URL}/subcategories`),axios.get(`${API_BASE_URL}/states`)]);const divisions=divisionsRes.data;const categories=categoriesRes.data;const subCategories=subCategoriesRes.data;const states=statesRes.data;setDashboardData({totalDivisions:divisions.length,totalCategories:categories.length,totalSubCategories:subCategories.length,totalStates:states.length,divisions,categories,subCategories,states});}catch(error){console.error('Error fetching dashboard data:',error);setDashboardData({totalDivisions:0,totalCategories:0,totalSubCategories:0,totalStates:0,divisions:[],categories:[],subCategories:[],states:[]});}finally{setLoading(false);}};const getRandomColor=()=>{const colors=['#667eea','#764ba2','#f093fb','#f5576c','#4facfe','#00f2fe','#43e97b','#38f9d7','#ffecd2','#fcb69f','#a8edea','#fed6e3'];return colors[Math.floor(Math.random()*colors.length)];};const handleRefresh=async()=>{setRefreshing(true);await fetchDashboardData();setRefreshing(false);};// Chart configurations\nconst divisionsChartData={labels:dashboardData.divisions.map(item=>item.name),datasets:[{label:'Divisions',data:dashboardData.divisions.map((_,index)=>index+1),backgroundColor:dashboardData.divisions.map(()=>getRandomColor()),borderRadius:8}]};const categoriesChartData={labels:dashboardData.categories.map(item=>item.name),datasets:[{label:'Categories',data:dashboardData.categories.map((_,index)=>index+1),backgroundColor:dashboardData.categories.map(()=>getRandomColor()),borderRadius:8}]};const chartOptions={responsive:true,maintainAspectRatio:false,plugins:{legend:{position:'bottom',labels:{padding:20,usePointStyle:true}}}};if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"dashboard-loading\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner-large\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Loading dashboard...\"})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"dashboard\",children:[/*#__PURE__*/_jsxs(motion.div,{className:\"dashboard-header\",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:0.5},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"dashboard-title\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"\\uD83D\\uDCCA System Overview\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Overview of your CRM system configuration\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"dashboard-controls\",children:/*#__PURE__*/_jsxs(\"button\",{className:\"btn btn-secondary dashboard-btn\",onClick:handleRefresh,disabled:refreshing,children:[/*#__PURE__*/_jsx(FiRefreshCw,{className:refreshing?'spinning':''}),\"Refresh\"]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"metrics-grid\",children:[{title:'Divisions',value:dashboardData.totalDivisions,icon:FiGrid,color:'#22c55e'},{title:'Categories',value:dashboardData.totalCategories,icon:FiLayers,color:'#f59e0b'},{title:'Sub Categories',value:dashboardData.totalSubCategories,icon:FiBarChart2,color:'#8b5cf6'},{title:'States',value:dashboardData.totalStates,icon:FiPieChart,color:'#ef4444'}].map((metric,index)=>/*#__PURE__*/_jsxs(motion.div,{className:\"metric-card\",initial:{opacity:0,scale:0.9},animate:{opacity:1,scale:1},transition:{duration:0.5,delay:index*0.1},whileHover:{scale:1.05},children:[/*#__PURE__*/_jsx(\"div\",{className:\"metric-icon\",style:{backgroundColor:metric.color},children:/*#__PURE__*/_jsx(metric.icon,{size:24})}),/*#__PURE__*/_jsxs(\"div\",{className:\"metric-content\",children:[/*#__PURE__*/_jsx(\"h3\",{children:metric.value.toLocaleString()}),/*#__PURE__*/_jsx(\"p\",{children:metric.title})]})]},metric.title))}),/*#__PURE__*/_jsxs(\"div\",{className:\"charts-grid\",children:[/*#__PURE__*/_jsxs(motion.div,{className:\"chart-card\",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:0.6},children:[/*#__PURE__*/_jsx(\"div\",{className:\"chart-header\",children:/*#__PURE__*/_jsxs(\"h3\",{children:[/*#__PURE__*/_jsx(FiGrid,{}),\" Divisions Overview\"]})}),/*#__PURE__*/_jsx(\"div\",{className:\"chart-container\",children:/*#__PURE__*/_jsx(Bar,{data:divisionsChartData,options:chartOptions})})]}),/*#__PURE__*/_jsxs(motion.div,{className:\"chart-card\",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:0.6,delay:0.1},children:[/*#__PURE__*/_jsx(\"div\",{className:\"chart-header\",children:/*#__PURE__*/_jsxs(\"h3\",{children:[/*#__PURE__*/_jsx(FiLayers,{}),\" Categories Overview\"]})}),/*#__PURE__*/_jsx(\"div\",{className:\"chart-container\",children:/*#__PURE__*/_jsx(Bar,{data:categoriesChartData,options:chartOptions})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"tables-grid\",children:[/*#__PURE__*/_jsxs(motion.div,{className:\"table-card\",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.6,delay:0.4},children:[/*#__PURE__*/_jsx(\"div\",{className:\"table-header\",children:/*#__PURE__*/_jsxs(\"h3\",{children:[/*#__PURE__*/_jsx(FiGrid,{}),\" Divisions\"]})}),/*#__PURE__*/_jsx(\"div\",{className:\"table-container\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"dashboard-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Name\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Created\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:dashboardData.divisions.slice(0,10).map(division=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:division.name}),/*#__PURE__*/_jsx(\"td\",{children:new Date(division.createdAt).toLocaleDateString()})]},division.id))})]})})]}),/*#__PURE__*/_jsxs(motion.div,{className:\"table-card\",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.6,delay:0.5},children:[/*#__PURE__*/_jsx(\"div\",{className:\"table-header\",children:/*#__PURE__*/_jsxs(\"h3\",{children:[/*#__PURE__*/_jsx(FiLayers,{}),\" Categories\"]})}),/*#__PURE__*/_jsx(\"div\",{className:\"table-container\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"dashboard-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Name\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Division\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Created\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:dashboardData.categories.slice(0,10).map(category=>{var _category$division;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:category.name}),/*#__PURE__*/_jsx(\"td\",{children:((_category$division=category.division)===null||_category$division===void 0?void 0:_category$division.name)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:new Date(category.createdAt).toLocaleDateString()})]},category.id);})})]})})]})]})]});};export default Dashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FiBarChart2", "<PERSON><PERSON><PERSON>", "FiLayers", "FiRefreshCw", "axios", "Bar", "Chart", "ChartJS", "CategoryScale", "LinearScale", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "jsx", "_jsx", "jsxs", "_jsxs", "register", "API_BASE_URL", "Dashboard", "dashboardData", "setDashboardData", "totalDivisions", "totalCategories", "totalSubCategories", "totalStates", "divisions", "categories", "subCategories", "states", "loading", "setLoading", "refreshing", "setRefreshing", "fetchDashboardData", "divisionsRes", "categoriesRes", "subCategoriesRes", "statesRes", "Promise", "all", "get", "data", "length", "error", "console", "getRandomColor", "colors", "Math", "floor", "random", "handleRefresh", "divisionsChartData", "labels", "map", "item", "name", "datasets", "label", "_", "index", "backgroundColor", "borderRadius", "categoriesChartData", "chartOptions", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "padding", "usePointStyle", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "onClick", "disabled", "title", "value", "icon", "color", "metric", "scale", "delay", "whileHover", "style", "size", "toLocaleString", "x", "options", "slice", "division", "Date", "createdAt", "toLocaleDateString", "id", "category", "_category$division"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport {\r\n  <PERSON><PERSON>ie<PERSON>hart,\r\n  FiBarChart2,\r\n  FiGrid,\r\n  FiLayers,\r\n  FiRefreshCw\r\n} from 'react-icons/fi';\r\nimport axios from 'axios';\r\nimport { Bar } from 'react-chartjs-2';\r\nimport {\r\n  Chart as ChartJS,\r\n  CategoryScale,\r\n  LinearScale,\r\n  BarElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n} from 'chart.js';\r\n\r\n// Register Chart.js components\r\nChartJS.register(\r\n  CategoryScale,\r\n  LinearScale,\r\n  BarElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend\r\n);\r\n\r\nconst API_BASE_URL = 'http://localhost:5000/api';\r\n\r\nconst Dashboard = () => {\r\n  const [dashboardData, setDashboardData] = useState({\r\n    totalDivisions: 0,\r\n    totalCategories: 0,\r\n    totalSubCategories: 0,\r\n    totalStates: 0,\r\n    divisions: [],\r\n    categories: [],\r\n    subCategories: [],\r\n    states: []\r\n  });\r\n\r\n  const [loading, setLoading] = useState(true);\r\n  const [refreshing, setRefreshing] = useState(false);\r\n\r\n  useEffect(() => {\r\n    fetchDashboardData();\r\n  }, []);\r\n\r\n  const fetchDashboardData = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const [\r\n        divisionsRes,\r\n        categoriesRes,\r\n        subCategoriesRes,\r\n        statesRes\r\n      ] = await Promise.all([\r\n        axios.get(`${API_BASE_URL}/divisions`),\r\n        axios.get(`${API_BASE_URL}/categories`),\r\n        axios.get(`${API_BASE_URL}/subcategories`),\r\n        axios.get(`${API_BASE_URL}/states`)\r\n      ]);\r\n\r\n      const divisions = divisionsRes.data;\r\n      const categories = categoriesRes.data;\r\n      const subCategories = subCategoriesRes.data;\r\n      const states = statesRes.data;\r\n\r\n      setDashboardData({\r\n        totalDivisions: divisions.length,\r\n        totalCategories: categories.length,\r\n        totalSubCategories: subCategories.length,\r\n        totalStates: states.length,\r\n        divisions,\r\n        categories,\r\n        subCategories,\r\n        states\r\n      });\r\n    } catch (error) {\r\n      console.error('Error fetching dashboard data:', error);\r\n      setDashboardData({\r\n        totalDivisions: 0,\r\n        totalCategories: 0,\r\n        totalSubCategories: 0,\r\n        totalStates: 0,\r\n        divisions: [],\r\n        categories: [],\r\n        subCategories: [],\r\n        states: []\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getRandomColor = () => {\r\n    const colors = [\r\n      '#667eea', '#764ba2', '#f093fb', '#f5576c',\r\n      '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',\r\n      '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'\r\n    ];\r\n    return colors[Math.floor(Math.random() * colors.length)];\r\n  };\r\n\r\n  const handleRefresh = async () => {\r\n    setRefreshing(true);\r\n    await fetchDashboardData();\r\n    setRefreshing(false);\r\n  };\r\n\r\n  // Chart configurations\r\n  const divisionsChartData = {\r\n    labels: dashboardData.divisions.map(item => item.name),\r\n    datasets: [\r\n      {\r\n        label: 'Divisions',\r\n        data: dashboardData.divisions.map((_, index) => index + 1),\r\n        backgroundColor: dashboardData.divisions.map(() => getRandomColor()),\r\n        borderRadius: 8\r\n      }\r\n    ]\r\n  };\r\n\r\n  const categoriesChartData = {\r\n    labels: dashboardData.categories.map(item => item.name),\r\n    datasets: [\r\n      {\r\n        label: 'Categories',\r\n        data: dashboardData.categories.map((_, index) => index + 1),\r\n        backgroundColor: dashboardData.categories.map(() => getRandomColor()),\r\n        borderRadius: 8\r\n      }\r\n    ]\r\n  };\r\n\r\n  const chartOptions = {\r\n    responsive: true,\r\n    maintainAspectRatio: false,\r\n    plugins: {\r\n      legend: {\r\n        position: 'bottom',\r\n        labels: {\r\n          padding: 20,\r\n          usePointStyle: true\r\n        }\r\n      }\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"dashboard-loading\">\r\n        <div className=\"loading-spinner-large\"></div>\r\n        <p>Loading dashboard...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"dashboard\">\r\n      {/* Dashboard Header */}\r\n      <motion.div\r\n        className=\"dashboard-header\"\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n      >\r\n        <div className=\"dashboard-title\">\r\n          <h1>📊 System Overview</h1>\r\n          <p>Overview of your CRM system configuration</p>\r\n        </div>\r\n\r\n        <div className=\"dashboard-controls\">\r\n          <button\r\n            className=\"btn btn-secondary dashboard-btn\"\r\n            onClick={handleRefresh}\r\n            disabled={refreshing}\r\n          >\r\n            <FiRefreshCw className={refreshing ? 'spinning' : ''} />\r\n            Refresh\r\n          </button>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Key Metrics Cards */}\r\n      <div className=\"metrics-grid\">\r\n        {[\r\n          { title: 'Divisions', value: dashboardData.totalDivisions, icon: FiGrid, color: '#22c55e' },\r\n          { title: 'Categories', value: dashboardData.totalCategories, icon: FiLayers, color: '#f59e0b' },\r\n          { title: 'Sub Categories', value: dashboardData.totalSubCategories, icon: FiBarChart2, color: '#8b5cf6' },\r\n          { title: 'States', value: dashboardData.totalStates, icon: FiPieChart, color: '#ef4444' }\r\n        ].map((metric, index) => (\r\n          <motion.div\r\n            key={metric.title}\r\n            className=\"metric-card\"\r\n            initial={{ opacity: 0, scale: 0.9 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 0.5, delay: index * 0.1 }}\r\n            whileHover={{ scale: 1.05 }}\r\n          >\r\n            <div className=\"metric-icon\" style={{ backgroundColor: metric.color }}>\r\n              <metric.icon size={24} />\r\n            </div>\r\n            <div className=\"metric-content\">\r\n              <h3>{metric.value.toLocaleString()}</h3>\r\n              <p>{metric.title}</p>\r\n            </div>\r\n          </motion.div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Charts Section */}\r\n      <div className=\"charts-grid\">\r\n        {/* Division Overview */}\r\n        <motion.div\r\n          className=\"chart-card\"\r\n          initial={{ opacity: 0, x: -20 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n        >\r\n          <div className=\"chart-header\">\r\n            <h3><FiGrid /> Divisions Overview</h3>\r\n          </div>\r\n          <div className=\"chart-container\">\r\n            <Bar data={divisionsChartData} options={chartOptions} />\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Categories Overview */}\r\n        <motion.div\r\n          className=\"chart-card\"\r\n          initial={{ opacity: 0, x: 20 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ duration: 0.6, delay: 0.1 }}\r\n        >\r\n          <div className=\"chart-header\">\r\n            <h3><FiLayers /> Categories Overview</h3>\r\n          </div>\r\n          <div className=\"chart-container\">\r\n            <Bar data={categoriesChartData} options={chartOptions} />\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* Data Tables Section */}\r\n      <div className=\"tables-grid\">\r\n        {/* Divisions List */}\r\n        <motion.div\r\n          className=\"table-card\"\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6, delay: 0.4 }}\r\n        >\r\n          <div className=\"table-header\">\r\n            <h3><FiGrid /> Divisions</h3>\r\n          </div>\r\n          <div className=\"table-container\">\r\n            <table className=\"dashboard-table\">\r\n              <thead>\r\n                <tr>\r\n                  <th>Name</th>\r\n                  <th>Created</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {dashboardData.divisions.slice(0, 10).map(division => (\r\n                  <tr key={division.id}>\r\n                    <td>{division.name}</td>\r\n                    <td>{new Date(division.createdAt).toLocaleDateString()}</td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Categories List */}\r\n        <motion.div\r\n          className=\"table-card\"\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6, delay: 0.5 }}\r\n        >\r\n          <div className=\"table-header\">\r\n            <h3><FiLayers /> Categories</h3>\r\n          </div>\r\n          <div className=\"table-container\">\r\n            <table className=\"dashboard-table\">\r\n              <thead>\r\n                <tr>\r\n                  <th>Name</th>\r\n                  <th>Division</th>\r\n                  <th>Created</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {dashboardData.categories.slice(0, 10).map(category => (\r\n                  <tr key={category.id}>\r\n                    <td>{category.name}</td>\r\n                    <td>{category.division?.name || '-'}</td>\r\n                    <td>{new Date(category.createdAt).toLocaleDateString()}</td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Dashboard;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,UAAU,CACVC,WAAW,CACXC,MAAM,CACNC,QAAQ,CACRC,WAAW,KACN,gBAAgB,CACvB,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,GAAG,KAAQ,iBAAiB,CACrC,OACEC,KAAK,GAAI,CAAAC,OAAO,CAChBC,aAAa,CACbC,WAAW,CACXC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,MAAM,KACD,UAAU,CAEjB;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACAV,OAAO,CAACW,QAAQ,CACdV,aAAa,CACbC,WAAW,CACXC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,MACF,CAAC,CAED,KAAM,CAAAM,YAAY,CAAG,2BAA2B,CAEhD,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG1B,QAAQ,CAAC,CACjD2B,cAAc,CAAE,CAAC,CACjBC,eAAe,CAAE,CAAC,CAClBC,kBAAkB,CAAE,CAAC,CACrBC,WAAW,CAAE,CAAC,CACdC,SAAS,CAAE,EAAE,CACbC,UAAU,CAAE,EAAE,CACdC,aAAa,CAAE,EAAE,CACjBC,MAAM,CAAE,EACV,CAAC,CAAC,CAEF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGpC,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACqC,UAAU,CAAEC,aAAa,CAAC,CAAGtC,QAAQ,CAAC,KAAK,CAAC,CAEnDC,SAAS,CAAC,IAAM,CACdsC,kBAAkB,CAAC,CAAC,CACtB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrCH,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CACJI,YAAY,CACZC,aAAa,CACbC,gBAAgB,CAChBC,SAAS,CACV,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CACpBrC,KAAK,CAACsC,GAAG,CAAC,GAAGvB,YAAY,YAAY,CAAC,CACtCf,KAAK,CAACsC,GAAG,CAAC,GAAGvB,YAAY,aAAa,CAAC,CACvCf,KAAK,CAACsC,GAAG,CAAC,GAAGvB,YAAY,gBAAgB,CAAC,CAC1Cf,KAAK,CAACsC,GAAG,CAAC,GAAGvB,YAAY,SAAS,CAAC,CACpC,CAAC,CAEF,KAAM,CAAAQ,SAAS,CAAGS,YAAY,CAACO,IAAI,CACnC,KAAM,CAAAf,UAAU,CAAGS,aAAa,CAACM,IAAI,CACrC,KAAM,CAAAd,aAAa,CAAGS,gBAAgB,CAACK,IAAI,CAC3C,KAAM,CAAAb,MAAM,CAAGS,SAAS,CAACI,IAAI,CAE7BrB,gBAAgB,CAAC,CACfC,cAAc,CAAEI,SAAS,CAACiB,MAAM,CAChCpB,eAAe,CAAEI,UAAU,CAACgB,MAAM,CAClCnB,kBAAkB,CAAEI,aAAa,CAACe,MAAM,CACxClB,WAAW,CAAEI,MAAM,CAACc,MAAM,CAC1BjB,SAAS,CACTC,UAAU,CACVC,aAAa,CACbC,MACF,CAAC,CAAC,CACJ,CAAE,MAAOe,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtDvB,gBAAgB,CAAC,CACfC,cAAc,CAAE,CAAC,CACjBC,eAAe,CAAE,CAAC,CAClBC,kBAAkB,CAAE,CAAC,CACrBC,WAAW,CAAE,CAAC,CACdC,SAAS,CAAE,EAAE,CACbC,UAAU,CAAE,EAAE,CACdC,aAAa,CAAE,EAAE,CACjBC,MAAM,CAAE,EACV,CAAC,CAAC,CACJ,CAAC,OAAS,CACRE,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAe,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,MAAM,CAAG,CACb,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAC1C,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAC1C,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAC3C,CACD,MAAO,CAAAA,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAGH,MAAM,CAACJ,MAAM,CAAC,CAAC,CAC1D,CAAC,CAED,KAAM,CAAAQ,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChClB,aAAa,CAAC,IAAI,CAAC,CACnB,KAAM,CAAAC,kBAAkB,CAAC,CAAC,CAC1BD,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAmB,kBAAkB,CAAG,CACzBC,MAAM,CAAEjC,aAAa,CAACM,SAAS,CAAC4B,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACC,IAAI,CAAC,CACtDC,QAAQ,CAAE,CACR,CACEC,KAAK,CAAE,WAAW,CAClBhB,IAAI,CAAEtB,aAAa,CAACM,SAAS,CAAC4B,GAAG,CAAC,CAACK,CAAC,CAAEC,KAAK,GAAKA,KAAK,CAAG,CAAC,CAAC,CAC1DC,eAAe,CAAEzC,aAAa,CAACM,SAAS,CAAC4B,GAAG,CAAC,IAAMR,cAAc,CAAC,CAAC,CAAC,CACpEgB,YAAY,CAAE,CAChB,CAAC,CAEL,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAG,CAC1BV,MAAM,CAAEjC,aAAa,CAACO,UAAU,CAAC2B,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACC,IAAI,CAAC,CACvDC,QAAQ,CAAE,CACR,CACEC,KAAK,CAAE,YAAY,CACnBhB,IAAI,CAAEtB,aAAa,CAACO,UAAU,CAAC2B,GAAG,CAAC,CAACK,CAAC,CAAEC,KAAK,GAAKA,KAAK,CAAG,CAAC,CAAC,CAC3DC,eAAe,CAAEzC,aAAa,CAACO,UAAU,CAAC2B,GAAG,CAAC,IAAMR,cAAc,CAAC,CAAC,CAAC,CACrEgB,YAAY,CAAE,CAChB,CAAC,CAEL,CAAC,CAED,KAAM,CAAAE,YAAY,CAAG,CACnBC,UAAU,CAAE,IAAI,CAChBC,mBAAmB,CAAE,KAAK,CAC1BC,OAAO,CAAE,CACPC,MAAM,CAAE,CACNC,QAAQ,CAAE,QAAQ,CAClBhB,MAAM,CAAE,CACNiB,OAAO,CAAE,EAAE,CACXC,aAAa,CAAE,IACjB,CACF,CACF,CACF,CAAC,CAED,GAAIzC,OAAO,CAAE,CACX,mBACEd,KAAA,QAAKwD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC3D,IAAA,QAAK0D,SAAS,CAAC,uBAAuB,CAAM,CAAC,cAC7C1D,IAAA,MAAA2D,QAAA,CAAG,sBAAoB,CAAG,CAAC,EACxB,CAAC,CAEV,CAEA,mBACEzD,KAAA,QAAKwD,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBzD,KAAA,CAACnB,MAAM,CAAC6E,GAAG,EACTF,SAAS,CAAC,kBAAkB,CAC5BG,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAP,QAAA,eAE9BzD,KAAA,QAAKwD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B3D,IAAA,OAAA2D,QAAA,CAAI,8BAAkB,CAAI,CAAC,cAC3B3D,IAAA,MAAA2D,QAAA,CAAG,2CAAyC,CAAG,CAAC,EAC7C,CAAC,cAEN3D,IAAA,QAAK0D,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCzD,KAAA,WACEwD,SAAS,CAAC,iCAAiC,CAC3CS,OAAO,CAAE9B,aAAc,CACvB+B,QAAQ,CAAElD,UAAW,CAAAyC,QAAA,eAErB3D,IAAA,CAACZ,WAAW,EAACsE,SAAS,CAAExC,UAAU,CAAG,UAAU,CAAG,EAAG,CAAE,CAAC,UAE1D,EAAQ,CAAC,CACN,CAAC,EACI,CAAC,cAGblB,IAAA,QAAK0D,SAAS,CAAC,cAAc,CAAAC,QAAA,CAC1B,CACC,CAAEU,KAAK,CAAE,WAAW,CAAEC,KAAK,CAAEhE,aAAa,CAACE,cAAc,CAAE+D,IAAI,CAAErF,MAAM,CAAEsF,KAAK,CAAE,SAAU,CAAC,CAC3F,CAAEH,KAAK,CAAE,YAAY,CAAEC,KAAK,CAAEhE,aAAa,CAACG,eAAe,CAAE8D,IAAI,CAAEpF,QAAQ,CAAEqF,KAAK,CAAE,SAAU,CAAC,CAC/F,CAAEH,KAAK,CAAE,gBAAgB,CAAEC,KAAK,CAAEhE,aAAa,CAACI,kBAAkB,CAAE6D,IAAI,CAAEtF,WAAW,CAAEuF,KAAK,CAAE,SAAU,CAAC,CACzG,CAAEH,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAEhE,aAAa,CAACK,WAAW,CAAE4D,IAAI,CAAEvF,UAAU,CAAEwF,KAAK,CAAE,SAAU,CAAC,CAC1F,CAAChC,GAAG,CAAC,CAACiC,MAAM,CAAE3B,KAAK,gBAClB5C,KAAA,CAACnB,MAAM,CAAC6E,GAAG,EAETF,SAAS,CAAC,aAAa,CACvBG,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEY,KAAK,CAAE,GAAI,CAAE,CACpCV,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEY,KAAK,CAAE,CAAE,CAAE,CAClCT,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAES,KAAK,CAAE7B,KAAK,CAAG,GAAI,CAAE,CAClD8B,UAAU,CAAE,CAAEF,KAAK,CAAE,IAAK,CAAE,CAAAf,QAAA,eAE5B3D,IAAA,QAAK0D,SAAS,CAAC,aAAa,CAACmB,KAAK,CAAE,CAAE9B,eAAe,CAAE0B,MAAM,CAACD,KAAM,CAAE,CAAAb,QAAA,cACpE3D,IAAA,CAACyE,MAAM,CAACF,IAAI,EAACO,IAAI,CAAE,EAAG,CAAE,CAAC,CACtB,CAAC,cACN5E,KAAA,QAAKwD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B3D,IAAA,OAAA2D,QAAA,CAAKc,MAAM,CAACH,KAAK,CAACS,cAAc,CAAC,CAAC,CAAK,CAAC,cACxC/E,IAAA,MAAA2D,QAAA,CAAIc,MAAM,CAACJ,KAAK,CAAI,CAAC,EAClB,CAAC,GAbDI,MAAM,CAACJ,KAcF,CACb,CAAC,CACC,CAAC,cAGNnE,KAAA,QAAKwD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAE1BzD,KAAA,CAACnB,MAAM,CAAC6E,GAAG,EACTF,SAAS,CAAC,YAAY,CACtBG,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEkB,CAAC,CAAE,CAAC,EAAG,CAAE,CAChChB,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEkB,CAAC,CAAE,CAAE,CAAE,CAC9Bf,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAP,QAAA,eAE9B3D,IAAA,QAAK0D,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BzD,KAAA,OAAAyD,QAAA,eAAI3D,IAAA,CAACd,MAAM,GAAE,CAAC,sBAAmB,EAAI,CAAC,CACnC,CAAC,cACNc,IAAA,QAAK0D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B3D,IAAA,CAACV,GAAG,EAACsC,IAAI,CAAEU,kBAAmB,CAAC2C,OAAO,CAAE/B,YAAa,CAAE,CAAC,CACrD,CAAC,EACI,CAAC,cAGbhD,KAAA,CAACnB,MAAM,CAAC6E,GAAG,EACTF,SAAS,CAAC,YAAY,CACtBG,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEkB,CAAC,CAAE,EAAG,CAAE,CAC/BhB,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEkB,CAAC,CAAE,CAAE,CAAE,CAC9Bf,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAES,KAAK,CAAE,GAAI,CAAE,CAAAhB,QAAA,eAE1C3D,IAAA,QAAK0D,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BzD,KAAA,OAAAyD,QAAA,eAAI3D,IAAA,CAACb,QAAQ,GAAE,CAAC,uBAAoB,EAAI,CAAC,CACtC,CAAC,cACNa,IAAA,QAAK0D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B3D,IAAA,CAACV,GAAG,EAACsC,IAAI,CAAEqB,mBAAoB,CAACgC,OAAO,CAAE/B,YAAa,CAAE,CAAC,CACtD,CAAC,EACI,CAAC,EACV,CAAC,cAGNhD,KAAA,QAAKwD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAE1BzD,KAAA,CAACnB,MAAM,CAAC6E,GAAG,EACTF,SAAS,CAAC,YAAY,CACtBG,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAES,KAAK,CAAE,GAAI,CAAE,CAAAhB,QAAA,eAE1C3D,IAAA,QAAK0D,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BzD,KAAA,OAAAyD,QAAA,eAAI3D,IAAA,CAACd,MAAM,GAAE,CAAC,aAAU,EAAI,CAAC,CAC1B,CAAC,cACNc,IAAA,QAAK0D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BzD,KAAA,UAAOwD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAChC3D,IAAA,UAAA2D,QAAA,cACEzD,KAAA,OAAAyD,QAAA,eACE3D,IAAA,OAAA2D,QAAA,CAAI,MAAI,CAAI,CAAC,cACb3D,IAAA,OAAA2D,QAAA,CAAI,SAAO,CAAI,CAAC,EACd,CAAC,CACA,CAAC,cACR3D,IAAA,UAAA2D,QAAA,CACGrD,aAAa,CAACM,SAAS,CAACsE,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAC1C,GAAG,CAAC2C,QAAQ,eAChDjF,KAAA,OAAAyD,QAAA,eACE3D,IAAA,OAAA2D,QAAA,CAAKwB,QAAQ,CAACzC,IAAI,CAAK,CAAC,cACxB1C,IAAA,OAAA2D,QAAA,CAAK,GAAI,CAAAyB,IAAI,CAACD,QAAQ,CAACE,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAAK,CAAC,GAFrDH,QAAQ,CAACI,EAGd,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACI,CAAC,cAGbrF,KAAA,CAACnB,MAAM,CAAC6E,GAAG,EACTF,SAAS,CAAC,YAAY,CACtBG,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAES,KAAK,CAAE,GAAI,CAAE,CAAAhB,QAAA,eAE1C3D,IAAA,QAAK0D,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BzD,KAAA,OAAAyD,QAAA,eAAI3D,IAAA,CAACb,QAAQ,GAAE,CAAC,cAAW,EAAI,CAAC,CAC7B,CAAC,cACNa,IAAA,QAAK0D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BzD,KAAA,UAAOwD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAChC3D,IAAA,UAAA2D,QAAA,cACEzD,KAAA,OAAAyD,QAAA,eACE3D,IAAA,OAAA2D,QAAA,CAAI,MAAI,CAAI,CAAC,cACb3D,IAAA,OAAA2D,QAAA,CAAI,UAAQ,CAAI,CAAC,cACjB3D,IAAA,OAAA2D,QAAA,CAAI,SAAO,CAAI,CAAC,EACd,CAAC,CACA,CAAC,cACR3D,IAAA,UAAA2D,QAAA,CACGrD,aAAa,CAACO,UAAU,CAACqE,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAC1C,GAAG,CAACgD,QAAQ,OAAAC,kBAAA,oBACjDvF,KAAA,OAAAyD,QAAA,eACE3D,IAAA,OAAA2D,QAAA,CAAK6B,QAAQ,CAAC9C,IAAI,CAAK,CAAC,cACxB1C,IAAA,OAAA2D,QAAA,CAAK,EAAA8B,kBAAA,CAAAD,QAAQ,CAACL,QAAQ,UAAAM,kBAAA,iBAAjBA,kBAAA,CAAmB/C,IAAI,GAAI,GAAG,CAAK,CAAC,cACzC1C,IAAA,OAAA2D,QAAA,CAAK,GAAI,CAAAyB,IAAI,CAACI,QAAQ,CAACH,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAAK,CAAC,GAHrDE,QAAQ,CAACD,EAId,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACI,CAAC,EACV,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}