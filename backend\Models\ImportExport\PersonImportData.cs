using CrmApi.Models.Person;

namespace CrmApi.Models.ImportExport
{
    public class PersonImportData
    {
        // Row Information
        public int RowNumber { get; set; }
        public bool IsValid { get; set; } = true;
        public List<ImportValidationError> ValidationErrors { get; set; } = new List<ImportValidationError>();

        // Required Fields
        public string? DivisionName { get; set; }
        public string? CategoryName { get; set; }
        public string? FirmNatureName { get; set; }
        public int? DivisionId { get; set; }
        public int? CategoryId { get; set; }
        public int? FirmNatureId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string MobileNumber { get; set; } = string.Empty;
        public string? Nature { get; set; }

        // Optional Fields
        public string? Gender { get; set; }
        public string? AlternateNumbers { get; set; }
        public string? PrimaryEmailId { get; set; }
        public string? AlternateEmailIds { get; set; }
        public string? Website { get; set; }
        public string? DateOfBirth { get; set; }
        public string? IsMarried { get; set; }
        public string? DateOfMarriage { get; set; }
        public string? WorkingState { get; set; }
        public string? DomesticState { get; set; }
        public string? District { get; set; }
        public string? Address { get; set; }
        public string? WorkingArea { get; set; }
        public string? HasAssociate { get; set; }
        public string? AssociateName { get; set; }
        public string? AssociateRelation { get; set; }
        public string? AssociateMobile { get; set; }
        public string? UsingWebsite { get; set; }
        public string? WebsiteLink { get; set; }
        public string? UsingCRMApp { get; set; }
        public string? CRMAppLink { get; set; }
        public string? TransactionValue { get; set; }
        public string? RERARegistrationNumber { get; set; }
        public string? WorkingProfiles { get; set; }
        public string? StarRating { get; set; }
        public string? Source { get; set; }
        public string? Remarks { get; set; }
        public string? FirmName { get; set; }
        public string? NumberOfOffices { get; set; }
        public string? NumberOfBranches { get; set; }
        public string? TotalEmployeeStrength { get; set; }
        public string? AuthorizedPersonName { get; set; }
        public string? AuthorizedPersonEmail { get; set; }
        public string? Designation { get; set; }
        public string? MarketingContact { get; set; }
        public string? MarketingDesignation { get; set; }
        public string? PlaceOfPosting { get; set; }
        public string? Department { get; set; }

        // Computed Properties
        public bool IsDuplicate { get; set; }
        public int? ExistingPersonId { get; set; }
        public ImportAction Action { get; set; } = ImportAction.Create;
    }

    public enum ImportAction
    {
        Create = 1,
        Update = 2,
        Skip = 3,
        Error = 4
    }

    public static class PersonImportColumns
    {
        public const string DivisionName = "Division Name";
        public const string CategoryName = "Category Name";
        public const string FirmNatureName = "Firm Nature Name";
        public const string Name = "Name";
        public const string MobileNumber = "Mobile Number";
        public const string Nature = "Nature";
        public const string Gender = "Gender";
        public const string AlternateNumbers = "Alternate Numbers";
        public const string PrimaryEmailId = "Primary Email";
        public const string AlternateEmailIds = "Alternate Emails";
        public const string Website = "Website";
        public const string DateOfBirth = "Date of Birth";
        public const string IsMarried = "Is Married";
        public const string DateOfMarriage = "Date of Marriage";
        public const string WorkingState = "Working State";
        public const string DomesticState = "Domestic State";
        public const string District = "District";
        public const string Address = "Address";
        public const string WorkingArea = "Working Area";
        public const string HasAssociate = "Has Associate";
        public const string AssociateName = "Associate Name";
        public const string AssociateRelation = "Associate Relation";
        public const string AssociateMobile = "Associate Mobile";
        public const string UsingWebsite = "Using Website";
        public const string WebsiteLink = "Website Link";
        public const string UsingCRMApp = "Using CRM App";
        public const string CRMAppLink = "CRM App Link";
        public const string TransactionValue = "Transaction Value";
        public const string RERARegistrationNumber = "RERA Registration Number";
        public const string WorkingProfiles = "Working Profiles";
        public const string StarRating = "Star Rating";
        public const string Source = "Source";
        public const string Remarks = "Remarks";
        public const string FirmName = "Firm Name";
        public const string NumberOfOffices = "Number of Offices";
        public const string NumberOfBranches = "Number of Branches";
        public const string TotalEmployeeStrength = "Total Employee Strength";
        public const string AuthorizedPersonName = "Authorized Person Name";
        public const string AuthorizedPersonEmail = "Authorized Person Email";
        public const string Designation = "Designation";
        public const string MarketingContact = "Marketing Contact";
        public const string MarketingDesignation = "Marketing Designation";
        public const string PlaceOfPosting = "Place of Posting";
        public const string Department = "Department";

        public static readonly List<string> RequiredColumns = new List<string>
        {
            DivisionName,
            CategoryName,
            Name,
            MobileNumber,
            Nature
        };

        public static readonly List<string> AllColumns = new List<string>
        {
            DivisionName, CategoryName, FirmNatureName, Name, MobileNumber, Nature, Gender,
            AlternateNumbers, PrimaryEmailId, AlternateEmailIds, Website, DateOfBirth,
            IsMarried, DateOfMarriage, WorkingState, DomesticState, District, Address,
            WorkingArea, HasAssociate, AssociateName, AssociateRelation, AssociateMobile,
            UsingWebsite, WebsiteLink, UsingCRMApp, CRMAppLink, TransactionValue,
            RERARegistrationNumber, WorkingProfiles, StarRating, Source, Remarks,
            FirmName, NumberOfOffices, NumberOfBranches, TotalEmployeeStrength,
            AuthorizedPersonName, AuthorizedPersonEmail, Designation, MarketingContact,
            MarketingDesignation, PlaceOfPosting, Department
        };
    }
}
