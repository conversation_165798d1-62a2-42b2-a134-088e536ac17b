{"ast": null, "code": "// Enums\nexport const PersonNature={Business:1,Corporate:2,Agriculture:3,Individual:4};export const PersonNatureLabels={[PersonNature.Business]:'Business',[PersonNature.Corporate]:'Corporate',[PersonNature.Agriculture]:'Agriculture',[PersonNature.Individual]:'Individual'};export const Gender={Male:1,Female:2,Other:3};export const GenderLabels={[Gender.Male]:'Male',[Gender.Female]:'Female',[Gender.Other]:'Other'};export const WorkingProfile={Business:1,Corporate:2,Agriculture:3};export const WorkingProfileLabels={[WorkingProfile.Business]:'Business',[WorkingProfile.Corporate]:'Corporate',[WorkingProfile.Agriculture]:'Agriculture'};// Field definitions for form builder\nexport const PersonFieldDefinitions={// Personal Information Section\npersonalInfo:{title:'Personal Information',fields:{name:{key:'name',label:'Full Name',type:'text',required:true,validation:{maxLength:255},section:'personalInfo'},gender:{key:'gender',label:'Gender',type:'select',required:false,options:Object.entries(GenderLabels).map(_ref=>{let[value,label]=_ref;return{value:parseInt(value),label};}),section:'personalInfo'},dateOfBirth:{key:'dateOfBirth',label:'Date of Birth',type:'date',required:false,section:'personalInfo'},isMarried:{key:'isMarried',label:'Is Married',type:'checkbox',required:false,section:'personalInfo'},dateOfMarriage:{key:'dateOfMarriage',label:'Date of Marriage',type:'date',required:false,conditional:{field:'isMarried',value:true},section:'personalInfo'}}},// Contact Information Section\ncontactInfo:{title:'Contact Information',fields:{mobileNumber:{key:'mobileNumber',label:'Mobile Number',type:'tel',required:true,validation:{pattern:'^(\\\\+91[\\\\-\\\\s]?)?[0]?(91)?[789]\\\\d{9}$'},section:'contactInfo'},alternateNumbers:{key:'alternateNumbers',label:'Alternate Numbers',type:'array',required:false,section:'contactInfo'},primaryEmailId:{key:'primaryEmailId',label:'Primary Email',type:'email',required:false,section:'contactInfo'},alternateEmailIds:{key:'alternateEmailIds',label:'Alternate Emails',type:'array',required:false,section:'contactInfo'},website:{key:'website',label:'Website',type:'url',required:false,section:'contactInfo'}}},// Location Information Section\nlocationInfo:{title:'Location Information',fields:{workingState:{key:'workingState',label:'Working State',type:'text',required:false,section:'locationInfo'},domesticState:{key:'domesticState',label:'Domestic State',type:'text',required:false,section:'locationInfo'},district:{key:'district',label:'District',type:'text',required:false,section:'locationInfo'},address:{key:'address',label:'Address',type:'textarea',required:false,section:'locationInfo'},workingArea:{key:'workingArea',label:'Working Area',type:'text',required:false,section:'locationInfo'}}},// Business Information Section\nbusinessInfo:{title:'Business Information',fields:{nature:{key:'nature',label:'Nature',type:'select',required:true,options:Object.entries(PersonNatureLabels).map(_ref2=>{let[value,label]=_ref2;return{value:parseInt(value),label};}),section:'businessInfo'},transactionValue:{key:'transactionValue',label:'Transaction Value',type:'number',required:false,section:'businessInfo'},reraRegistrationNumber:{key:'reraRegistrationNumber',label:'RERA Registration Number',type:'text',required:false,section:'businessInfo'},workingProfiles:{key:'workingProfiles',label:'Working Profiles',type:'multiselect',required:false,options:Object.entries(WorkingProfileLabels).map(_ref3=>{let[value,label]=_ref3;return{value:parseInt(value),label};}),section:'businessInfo'},starRating:{key:'starRating',label:'Star Rating',type:'number',required:false,validation:{min:1,max:5},section:'businessInfo'},source:{key:'source',label:'Source',type:'text',required:false,section:'businessInfo'},remarks:{key:'remarks',label:'Remarks',type:'textarea',required:false,section:'businessInfo'}}},// Associate Information Section\nassociateInfo:{title:'Associate Information',fields:{hasAssociate:{key:'hasAssociate',label:'Has Associate',type:'checkbox',required:false,section:'associateInfo'},associateName:{key:'associateName',label:'Associate Name',type:'text',required:false,conditional:{field:'hasAssociate',value:true},section:'associateInfo'},associateRelation:{key:'associateRelation',label:'Associate Relation',type:'text',required:false,conditional:{field:'hasAssociate',value:true},section:'associateInfo'},associateMobile:{key:'associateMobile',label:'Associate Mobile',type:'tel',required:false,conditional:{field:'hasAssociate',value:true},section:'associateInfo'}}},// Digital Presence Section\ndigitalPresence:{title:'Digital Presence',fields:{usingWebsite:{key:'usingWebsite',label:'Using Website',type:'checkbox',required:false,section:'digitalPresence'},websiteLink:{key:'websiteLink',label:'Website Link',type:'url',required:false,conditional:{field:'usingWebsite',value:true},section:'digitalPresence'},usingCRMApp:{key:'usingCRMApp',label:'Using CRM App',type:'checkbox',required:false,section:'digitalPresence'},crmAppLink:{key:'crmAppLink',label:'CRM App Link',type:'url',required:false,conditional:{field:'usingCRMApp',value:true},section:'digitalPresence'}}},// Company Information Section\ncompanyInfo:{title:'Company Information',fields:{firmName:{key:'firmName',label:'Firm Name',type:'text',required:false,section:'companyInfo'},numberOfOffices:{key:'numberOfOffices',label:'Number of Offices',type:'number',required:false,section:'companyInfo'},numberOfBranches:{key:'numberOfBranches',label:'Number of Branches',type:'number',required:false,section:'companyInfo'},totalEmployeeStrength:{key:'totalEmployeeStrength',label:'Total Employee Strength',type:'number',required:false,section:'companyInfo'}}},// Authorized Person Section\nauthorizedPerson:{title:'Authorized Person',fields:{authorizedPersonName:{key:'authorizedPersonName',label:'Authorized Person Name',type:'text',required:false,section:'authorizedPerson'},authorizedPersonEmail:{key:'authorizedPersonEmail',label:'Authorized Person Email',type:'email',required:false,section:'authorizedPerson'},designation:{key:'designation',label:'Designation',type:'text',required:false,section:'authorizedPerson'}}},// Marketing Information Section\nmarketingInfo:{title:'Marketing Information',fields:{marketingContact:{key:'marketingContact',label:'Marketing Contact',type:'text',required:false,section:'marketingInfo'},marketingDesignation:{key:'marketingDesignation',label:'Marketing Designation',type:'text',required:false,section:'marketingInfo'},placeOfPosting:{key:'placeOfPosting',label:'Place of Posting',type:'text',required:false,section:'marketingInfo'},department:{key:'department',label:'Department',type:'text',required:false,section:'marketingInfo'}}}};// Get all fields as a flat array\nexport const getAllPersonFields=()=>{const allFields=[];Object.values(PersonFieldDefinitions).forEach(section=>{Object.values(section.fields).forEach(field=>{allFields.push(field);});});return allFields;};// Get required fields\nexport const getRequiredFields=()=>{return getAllPersonFields().filter(field=>field.required);};// Debug function to check for duplicate field definitions\nexport const checkForDuplicateFields=()=>{const fieldsByKey=new Map();const duplicates=[];Object.entries(PersonFieldDefinitions).forEach(_ref4=>{let[sectionKey,section]=_ref4;Object.entries(section.fields).forEach(_ref5=>{let[fieldName,field]=_ref5;const key=field.key;if(fieldsByKey.has(key)){duplicates.push({key,sections:[fieldsByKey.get(key).section,sectionKey],fieldName1:fieldsByKey.get(key).fieldName,fieldName2:fieldName});}else{fieldsByKey.set(key,{section:sectionKey,fieldName});}});});if(duplicates.length>0){console.error('DUPLICATE FIELD DEFINITIONS FOUND:');duplicates.forEach(dup=>{console.error(`Field key \"${dup.key}\" is defined in multiple sections:`,dup.sections);});}else{console.log('No duplicate field definitions found.');}return duplicates;};// Default form data structure\nexport const getDefaultPersonData=()=>({divisionId:null,categoryId:null,subCategoryId:null,name:'',mobileNumber:'',nature:null,gender:null,alternateNumbers:[],primaryEmailId:null,// Changed from '' to null to avoid validation\nalternateEmailIds:[],website:null,// Changed from '' to null to avoid validation\ndateOfBirth:null,isMarried:false,dateOfMarriage:null,workingState:'',domesticState:'',district:'',address:'',workingArea:'',hasAssociate:false,associateName:'',associateRelation:'',associateMobile:'',usingWebsite:false,websiteLink:null,// Changed from '' to null to avoid validation\nusingCRMApp:false,crmAppLink:null,// Changed from '' to null to avoid validation\ntransactionValue:null,reraRegistrationNumber:'',workingProfiles:[],starRating:null,source:'',remarks:'',firmName:'',numberOfOffices:null,numberOfBranches:null,totalEmployeeStrength:null,authorizedPersonName:'',authorizedPersonEmail:null,// Changed from '' to null to avoid validation\ndesignation:'',marketingContact:'',marketingDesignation:'',placeOfPosting:'',department:''});", "map": {"version": 3, "names": ["PersonNature", "Business", "Corporate", "Agriculture", "Individual", "PersonNatureLabels", "Gender", "Male", "Female", "Other", "<PERSON><PERSON><PERSON><PERSON>", "WorkingProfile", "WorkingProfileLabels", "PersonFieldDefinitions", "personalInfo", "title", "fields", "name", "key", "label", "type", "required", "validation", "max<PERSON><PERSON><PERSON>", "section", "gender", "options", "Object", "entries", "map", "_ref", "value", "parseInt", "dateOfBirth", "<PERSON><PERSON><PERSON><PERSON>", "dateOfMarriage", "conditional", "field", "contactInfo", "mobileNumber", "pattern", "alternateNumbers", "primaryEmailId", "alternateEmailIds", "website", "locationInfo", "workingState", "domesticState", "district", "address", "workingArea", "businessInfo", "nature", "_ref2", "transactionValue", "reraRegistrationNumber", "workingProfiles", "_ref3", "starRating", "min", "max", "source", "remarks", "associateInfo", "hasAssociate", "associate<PERSON><PERSON>", "associateRelation", "associate<PERSON><PERSON><PERSON>", "digitalPresence", "usingWebsite", "websiteLink", "usingCRMApp", "crmAppLink", "companyInfo", "firmName", "numberOfOffices", "numberOfBranches", "totalEmployeeStrength", "<PERSON><PERSON><PERSON>", "authorizedPersonName", "authorizedPersonEmail", "designation", "marketingInfo", "marketingContact", "marketingDesignation", "placeOfPosting", "department", "<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>s", "allFields", "values", "for<PERSON>ach", "push", "getRequiredFields", "filter", "checkFor<PERSON>up<PERSON><PERSON><PERSON>ields", "fieldsByKey", "Map", "duplicates", "_ref4", "sectionKey", "_ref5", "fieldName", "has", "sections", "get", "fieldName1", "fieldName2", "set", "length", "console", "error", "dup", "log", "getDefaultPersonData", "divisionId", "categoryId", "subCategoryId"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/constants/personConstants.js"], "sourcesContent": ["// Enums\nexport const PersonNature = {\n  Business: 1,\n  Corporate: 2,\n  Agriculture: 3,\n  Individual: 4\n};\n\nexport const PersonNatureLabels = {\n  [PersonNature.Business]: 'Business',\n  [PersonNature.Corporate]: 'Corporate',\n  [PersonNature.Agriculture]: 'Agriculture',\n  [PersonNature.Individual]: 'Individual'\n};\n\nexport const Gender = {\n  Male: 1,\n  Female: 2,\n  Other: 3\n};\n\nexport const GenderLabels = {\n  [Gender.Male]: 'Male',\n  [Gender.Female]: 'Female',\n  [Gender.Other]: 'Other'\n};\n\nexport const WorkingProfile = {\n  Business: 1,\n  Corporate: 2,\n  Agriculture: 3\n};\n\nexport const WorkingProfileLabels = {\n  [WorkingProfile.Business]: 'Business',\n  [WorkingProfile.Corporate]: 'Corporate',\n  [WorkingProfile.Agriculture]: 'Agriculture'\n};\n\n// Field definitions for form builder\nexport const PersonFieldDefinitions = {\n  // Personal Information Section\n  personalInfo: {\n    title: 'Personal Information',\n    fields: {\n      name: {\n        key: 'name',\n        label: 'Full Name',\n        type: 'text',\n        required: true,\n        validation: { maxLength: 255 },\n        section: 'personalInfo'\n      },\n      gender: {\n        key: 'gender',\n        label: 'Gender',\n        type: 'select',\n        required: false,\n        options: Object.entries(GenderLabels).map(([value, label]) => ({ value: parseInt(value), label })),\n        section: 'personalInfo'\n      },\n      dateOfBirth: {\n        key: 'dateOfBirth',\n        label: 'Date of Birth',\n        type: 'date',\n        required: false,\n        section: 'personalInfo'\n      },\n      isMarried: {\n        key: 'isMarried',\n        label: 'Is Married',\n        type: 'checkbox',\n        required: false,\n        section: 'personalInfo'\n      },\n      dateOfMarriage: {\n        key: 'dateOfMarriage',\n        label: 'Date of Marriage',\n        type: 'date',\n        required: false,\n        conditional: { field: 'isMarried', value: true },\n        section: 'personalInfo'\n      }\n    }\n  },\n\n  // Contact Information Section\n  contactInfo: {\n    title: 'Contact Information',\n    fields: {\n      mobileNumber: {\n        key: 'mobileNumber',\n        label: 'Mobile Number',\n        type: 'tel',\n        required: true,\n        validation: { pattern: '^(\\\\+91[\\\\-\\\\s]?)?[0]?(91)?[789]\\\\d{9}$' },\n        section: 'contactInfo'\n      },\n      alternateNumbers: {\n        key: 'alternateNumbers',\n        label: 'Alternate Numbers',\n        type: 'array',\n        required: false,\n        section: 'contactInfo'\n      },\n      primaryEmailId: {\n        key: 'primaryEmailId',\n        label: 'Primary Email',\n        type: 'email',\n        required: false,\n        section: 'contactInfo'\n      },\n      alternateEmailIds: {\n        key: 'alternateEmailIds',\n        label: 'Alternate Emails',\n        type: 'array',\n        required: false,\n        section: 'contactInfo'\n      },\n      website: {\n        key: 'website',\n        label: 'Website',\n        type: 'url',\n        required: false,\n        section: 'contactInfo'\n      }\n    }\n  },\n\n  // Location Information Section\n  locationInfo: {\n    title: 'Location Information',\n    fields: {\n      workingState: {\n        key: 'workingState',\n        label: 'Working State',\n        type: 'text',\n        required: false,\n        section: 'locationInfo'\n      },\n      domesticState: {\n        key: 'domesticState',\n        label: 'Domestic State',\n        type: 'text',\n        required: false,\n        section: 'locationInfo'\n      },\n      district: {\n        key: 'district',\n        label: 'District',\n        type: 'text',\n        required: false,\n        section: 'locationInfo'\n      },\n      address: {\n        key: 'address',\n        label: 'Address',\n        type: 'textarea',\n        required: false,\n        section: 'locationInfo'\n      },\n      workingArea: {\n        key: 'workingArea',\n        label: 'Working Area',\n        type: 'text',\n        required: false,\n        section: 'locationInfo'\n      }\n    }\n  },\n\n  // Business Information Section\n  businessInfo: {\n    title: 'Business Information',\n    fields: {\n      nature: {\n        key: 'nature',\n        label: 'Nature',\n        type: 'select',\n        required: true,\n        options: Object.entries(PersonNatureLabels).map(([value, label]) => ({ value: parseInt(value), label })),\n        section: 'businessInfo'\n      },\n      transactionValue: {\n        key: 'transactionValue',\n        label: 'Transaction Value',\n        type: 'number',\n        required: false,\n        section: 'businessInfo'\n      },\n      reraRegistrationNumber: {\n        key: 'reraRegistrationNumber',\n        label: 'RERA Registration Number',\n        type: 'text',\n        required: false,\n        section: 'businessInfo'\n      },\n      workingProfiles: {\n        key: 'workingProfiles',\n        label: 'Working Profiles',\n        type: 'multiselect',\n        required: false,\n        options: Object.entries(WorkingProfileLabels).map(([value, label]) => ({ value: parseInt(value), label })),\n        section: 'businessInfo'\n      },\n      starRating: {\n        key: 'starRating',\n        label: 'Star Rating',\n        type: 'number',\n        required: false,\n        validation: { min: 1, max: 5 },\n        section: 'businessInfo'\n      },\n      source: {\n        key: 'source',\n        label: 'Source',\n        type: 'text',\n        required: false,\n        section: 'businessInfo'\n      },\n      remarks: {\n        key: 'remarks',\n        label: 'Remarks',\n        type: 'textarea',\n        required: false,\n        section: 'businessInfo'\n      }\n    }\n  },\n\n  // Associate Information Section\n  associateInfo: {\n    title: 'Associate Information',\n    fields: {\n      hasAssociate: {\n        key: 'hasAssociate',\n        label: 'Has Associate',\n        type: 'checkbox',\n        required: false,\n        section: 'associateInfo'\n      },\n      associateName: {\n        key: 'associateName',\n        label: 'Associate Name',\n        type: 'text',\n        required: false,\n        conditional: { field: 'hasAssociate', value: true },\n        section: 'associateInfo'\n      },\n      associateRelation: {\n        key: 'associateRelation',\n        label: 'Associate Relation',\n        type: 'text',\n        required: false,\n        conditional: { field: 'hasAssociate', value: true },\n        section: 'associateInfo'\n      },\n      associateMobile: {\n        key: 'associateMobile',\n        label: 'Associate Mobile',\n        type: 'tel',\n        required: false,\n        conditional: { field: 'hasAssociate', value: true },\n        section: 'associateInfo'\n      }\n    }\n  },\n\n  // Digital Presence Section\n  digitalPresence: {\n    title: 'Digital Presence',\n    fields: {\n      usingWebsite: {\n        key: 'usingWebsite',\n        label: 'Using Website',\n        type: 'checkbox',\n        required: false,\n        section: 'digitalPresence'\n      },\n      websiteLink: {\n        key: 'websiteLink',\n        label: 'Website Link',\n        type: 'url',\n        required: false,\n        conditional: { field: 'usingWebsite', value: true },\n        section: 'digitalPresence'\n      },\n      usingCRMApp: {\n        key: 'usingCRMApp',\n        label: 'Using CRM App',\n        type: 'checkbox',\n        required: false,\n        section: 'digitalPresence'\n      },\n      crmAppLink: {\n        key: 'crmAppLink',\n        label: 'CRM App Link',\n        type: 'url',\n        required: false,\n        conditional: { field: 'usingCRMApp', value: true },\n        section: 'digitalPresence'\n      }\n    }\n  },\n\n  // Company Information Section\n  companyInfo: {\n    title: 'Company Information',\n    fields: {\n      firmName: {\n        key: 'firmName',\n        label: 'Firm Name',\n        type: 'text',\n        required: false,\n        section: 'companyInfo'\n      },\n      numberOfOffices: {\n        key: 'numberOfOffices',\n        label: 'Number of Offices',\n        type: 'number',\n        required: false,\n        section: 'companyInfo'\n      },\n      numberOfBranches: {\n        key: 'numberOfBranches',\n        label: 'Number of Branches',\n        type: 'number',\n        required: false,\n        section: 'companyInfo'\n      },\n      totalEmployeeStrength: {\n        key: 'totalEmployeeStrength',\n        label: 'Total Employee Strength',\n        type: 'number',\n        required: false,\n        section: 'companyInfo'\n      }\n    }\n  },\n\n  // Authorized Person Section\n  authorizedPerson: {\n    title: 'Authorized Person',\n    fields: {\n      authorizedPersonName: {\n        key: 'authorizedPersonName',\n        label: 'Authorized Person Name',\n        type: 'text',\n        required: false,\n        section: 'authorizedPerson'\n      },\n      authorizedPersonEmail: {\n        key: 'authorizedPersonEmail',\n        label: 'Authorized Person Email',\n        type: 'email',\n        required: false,\n        section: 'authorizedPerson'\n      },\n      designation: {\n        key: 'designation',\n        label: 'Designation',\n        type: 'text',\n        required: false,\n        section: 'authorizedPerson'\n      }\n    }\n  },\n\n  // Marketing Information Section\n  marketingInfo: {\n    title: 'Marketing Information',\n    fields: {\n      marketingContact: {\n        key: 'marketingContact',\n        label: 'Marketing Contact',\n        type: 'text',\n        required: false,\n        section: 'marketingInfo'\n      },\n      marketingDesignation: {\n        key: 'marketingDesignation',\n        label: 'Marketing Designation',\n        type: 'text',\n        required: false,\n        section: 'marketingInfo'\n      },\n      placeOfPosting: {\n        key: 'placeOfPosting',\n        label: 'Place of Posting',\n        type: 'text',\n        required: false,\n        section: 'marketingInfo'\n      },\n      department: {\n        key: 'department',\n        label: 'Department',\n        type: 'text',\n        required: false,\n        section: 'marketingInfo'\n      }\n    }\n  }\n};\n\n// Get all fields as a flat array\nexport const getAllPersonFields = () => {\n  const allFields = [];\n  Object.values(PersonFieldDefinitions).forEach(section => {\n    Object.values(section.fields).forEach(field => {\n      allFields.push(field);\n    });\n  });\n  return allFields;\n};\n\n// Get required fields\nexport const getRequiredFields = () => {\n  return getAllPersonFields().filter(field => field.required);\n};\n\n// Debug function to check for duplicate field definitions\nexport const checkForDuplicateFields = () => {\n  const fieldsByKey = new Map();\n  const duplicates = [];\n\n  Object.entries(PersonFieldDefinitions).forEach(([sectionKey, section]) => {\n    Object.entries(section.fields).forEach(([fieldName, field]) => {\n      const key = field.key;\n\n      if (fieldsByKey.has(key)) {\n        duplicates.push({\n          key,\n          sections: [fieldsByKey.get(key).section, sectionKey],\n          fieldName1: fieldsByKey.get(key).fieldName,\n          fieldName2: fieldName\n        });\n      } else {\n        fieldsByKey.set(key, { section: sectionKey, fieldName });\n      }\n    });\n  });\n\n  if (duplicates.length > 0) {\n    console.error('DUPLICATE FIELD DEFINITIONS FOUND:');\n    duplicates.forEach(dup => {\n      console.error(`Field key \"${dup.key}\" is defined in multiple sections:`, dup.sections);\n    });\n  } else {\n    console.log('No duplicate field definitions found.');\n  }\n\n  return duplicates;\n};\n\n// Default form data structure\nexport const getDefaultPersonData = () => ({\n  divisionId: null,\n  categoryId: null,\n  subCategoryId: null,\n  name: '',\n  mobileNumber: '',\n  nature: null,\n  gender: null,\n  alternateNumbers: [],\n  primaryEmailId: null, // Changed from '' to null to avoid validation\n  alternateEmailIds: [],\n  website: null, // Changed from '' to null to avoid validation\n  dateOfBirth: null,\n  isMarried: false,\n  dateOfMarriage: null,\n  workingState: '',\n  domesticState: '',\n  district: '',\n  address: '',\n  workingArea: '',\n  hasAssociate: false,\n  associateName: '',\n  associateRelation: '',\n  associateMobile: '',\n  usingWebsite: false,\n  websiteLink: null, // Changed from '' to null to avoid validation\n  usingCRMApp: false,\n  crmAppLink: null, // Changed from '' to null to avoid validation\n  transactionValue: null,\n  reraRegistrationNumber: '',\n  workingProfiles: [],\n  starRating: null,\n  source: '',\n  remarks: '',\n  firmName: '',\n  numberOfOffices: null,\n  numberOfBranches: null,\n  totalEmployeeStrength: null,\n  authorizedPersonName: '',\n  authorizedPersonEmail: null, // Changed from '' to null to avoid validation\n  designation: '',\n  marketingContact: '',\n  marketingDesignation: '',\n  placeOfPosting: '',\n  department: ''\n});\n"], "mappings": "AAAA;AACA,MAAO,MAAM,CAAAA,YAAY,CAAG,CAC1BC,QAAQ,CAAE,CAAC,CACXC,SAAS,CAAE,CAAC,CACZC,WAAW,CAAE,CAAC,CACdC,UAAU,CAAE,CACd,CAAC,CAED,MAAO,MAAM,CAAAC,kBAAkB,CAAG,CAChC,CAACL,YAAY,CAACC,QAAQ,EAAG,UAAU,CACnC,CAACD,YAAY,CAACE,SAAS,EAAG,WAAW,CACrC,CAACF,YAAY,CAACG,WAAW,EAAG,aAAa,CACzC,CAACH,YAAY,CAACI,UAAU,EAAG,YAC7B,CAAC,CAED,MAAO,MAAM,CAAAE,MAAM,CAAG,CACpBC,IAAI,CAAE,CAAC,CACPC,MAAM,CAAE,CAAC,CACTC,KAAK,CAAE,CACT,CAAC,CAED,MAAO,MAAM,CAAAC,YAAY,CAAG,CAC1B,CAACJ,MAAM,CAACC,IAAI,EAAG,MAAM,CACrB,CAACD,MAAM,CAACE,MAAM,EAAG,QAAQ,CACzB,CAACF,MAAM,CAACG,KAAK,EAAG,OAClB,CAAC,CAED,MAAO,MAAM,CAAAE,cAAc,CAAG,CAC5BV,QAAQ,CAAE,CAAC,CACXC,SAAS,CAAE,CAAC,CACZC,WAAW,CAAE,CACf,CAAC,CAED,MAAO,MAAM,CAAAS,oBAAoB,CAAG,CAClC,CAACD,cAAc,CAACV,QAAQ,EAAG,UAAU,CACrC,CAACU,cAAc,CAACT,SAAS,EAAG,WAAW,CACvC,CAACS,cAAc,CAACR,WAAW,EAAG,aAChC,CAAC,CAED;AACA,MAAO,MAAM,CAAAU,sBAAsB,CAAG,CACpC;AACAC,YAAY,CAAE,CACZC,KAAK,CAAE,sBAAsB,CAC7BC,MAAM,CAAE,CACNC,IAAI,CAAE,CACJC,GAAG,CAAE,MAAM,CACXC,KAAK,CAAE,WAAW,CAClBC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,CAAEC,SAAS,CAAE,GAAI,CAAC,CAC9BC,OAAO,CAAE,cACX,CAAC,CACDC,MAAM,CAAE,CACNP,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,QAAQ,CACfC,IAAI,CAAE,QAAQ,CACdC,QAAQ,CAAE,KAAK,CACfK,OAAO,CAAEC,MAAM,CAACC,OAAO,CAAClB,YAAY,CAAC,CAACmB,GAAG,CAACC,IAAA,MAAC,CAACC,KAAK,CAAEZ,KAAK,CAAC,CAAAW,IAAA,OAAM,CAAEC,KAAK,CAAEC,QAAQ,CAACD,KAAK,CAAC,CAAEZ,KAAM,CAAC,EAAC,CAAC,CAClGK,OAAO,CAAE,cACX,CAAC,CACDS,WAAW,CAAE,CACXf,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,eAAe,CACtBC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,cACX,CAAC,CACDU,SAAS,CAAE,CACThB,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,YAAY,CACnBC,IAAI,CAAE,UAAU,CAChBC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,cACX,CAAC,CACDW,cAAc,CAAE,CACdjB,GAAG,CAAE,gBAAgB,CACrBC,KAAK,CAAE,kBAAkB,CACzBC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfe,WAAW,CAAE,CAAEC,KAAK,CAAE,WAAW,CAAEN,KAAK,CAAE,IAAK,CAAC,CAChDP,OAAO,CAAE,cACX,CACF,CACF,CAAC,CAED;AACAc,WAAW,CAAE,CACXvB,KAAK,CAAE,qBAAqB,CAC5BC,MAAM,CAAE,CACNuB,YAAY,CAAE,CACZrB,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,eAAe,CACtBC,IAAI,CAAE,KAAK,CACXC,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,CAAEkB,OAAO,CAAE,yCAA0C,CAAC,CAClEhB,OAAO,CAAE,aACX,CAAC,CACDiB,gBAAgB,CAAE,CAChBvB,GAAG,CAAE,kBAAkB,CACvBC,KAAK,CAAE,mBAAmB,CAC1BC,IAAI,CAAE,OAAO,CACbC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,aACX,CAAC,CACDkB,cAAc,CAAE,CACdxB,GAAG,CAAE,gBAAgB,CACrBC,KAAK,CAAE,eAAe,CACtBC,IAAI,CAAE,OAAO,CACbC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,aACX,CAAC,CACDmB,iBAAiB,CAAE,CACjBzB,GAAG,CAAE,mBAAmB,CACxBC,KAAK,CAAE,kBAAkB,CACzBC,IAAI,CAAE,OAAO,CACbC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,aACX,CAAC,CACDoB,OAAO,CAAE,CACP1B,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,KAAK,CACXC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,aACX,CACF,CACF,CAAC,CAED;AACAqB,YAAY,CAAE,CACZ9B,KAAK,CAAE,sBAAsB,CAC7BC,MAAM,CAAE,CACN8B,YAAY,CAAE,CACZ5B,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,eAAe,CACtBC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,cACX,CAAC,CACDuB,aAAa,CAAE,CACb7B,GAAG,CAAE,eAAe,CACpBC,KAAK,CAAE,gBAAgB,CACvBC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,cACX,CAAC,CACDwB,QAAQ,CAAE,CACR9B,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,UAAU,CACjBC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,cACX,CAAC,CACDyB,OAAO,CAAE,CACP/B,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,UAAU,CAChBC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,cACX,CAAC,CACD0B,WAAW,CAAE,CACXhC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,cAAc,CACrBC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,cACX,CACF,CACF,CAAC,CAED;AACA2B,YAAY,CAAE,CACZpC,KAAK,CAAE,sBAAsB,CAC7BC,MAAM,CAAE,CACNoC,MAAM,CAAE,CACNlC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,QAAQ,CACfC,IAAI,CAAE,QAAQ,CACdC,QAAQ,CAAE,IAAI,CACdK,OAAO,CAAEC,MAAM,CAACC,OAAO,CAACvB,kBAAkB,CAAC,CAACwB,GAAG,CAACwB,KAAA,MAAC,CAACtB,KAAK,CAAEZ,KAAK,CAAC,CAAAkC,KAAA,OAAM,CAAEtB,KAAK,CAAEC,QAAQ,CAACD,KAAK,CAAC,CAAEZ,KAAM,CAAC,EAAC,CAAC,CACxGK,OAAO,CAAE,cACX,CAAC,CACD8B,gBAAgB,CAAE,CAChBpC,GAAG,CAAE,kBAAkB,CACvBC,KAAK,CAAE,mBAAmB,CAC1BC,IAAI,CAAE,QAAQ,CACdC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,cACX,CAAC,CACD+B,sBAAsB,CAAE,CACtBrC,GAAG,CAAE,wBAAwB,CAC7BC,KAAK,CAAE,0BAA0B,CACjCC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,cACX,CAAC,CACDgC,eAAe,CAAE,CACftC,GAAG,CAAE,iBAAiB,CACtBC,KAAK,CAAE,kBAAkB,CACzBC,IAAI,CAAE,aAAa,CACnBC,QAAQ,CAAE,KAAK,CACfK,OAAO,CAAEC,MAAM,CAACC,OAAO,CAAChB,oBAAoB,CAAC,CAACiB,GAAG,CAAC4B,KAAA,MAAC,CAAC1B,KAAK,CAAEZ,KAAK,CAAC,CAAAsC,KAAA,OAAM,CAAE1B,KAAK,CAAEC,QAAQ,CAACD,KAAK,CAAC,CAAEZ,KAAM,CAAC,EAAC,CAAC,CAC1GK,OAAO,CAAE,cACX,CAAC,CACDkC,UAAU,CAAE,CACVxC,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,aAAa,CACpBC,IAAI,CAAE,QAAQ,CACdC,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,CAAEqC,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,CAAE,CAAC,CAC9BpC,OAAO,CAAE,cACX,CAAC,CACDqC,MAAM,CAAE,CACN3C,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,QAAQ,CACfC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,cACX,CAAC,CACDsC,OAAO,CAAE,CACP5C,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,UAAU,CAChBC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,cACX,CACF,CACF,CAAC,CAED;AACAuC,aAAa,CAAE,CACbhD,KAAK,CAAE,uBAAuB,CAC9BC,MAAM,CAAE,CACNgD,YAAY,CAAE,CACZ9C,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,eAAe,CACtBC,IAAI,CAAE,UAAU,CAChBC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,eACX,CAAC,CACDyC,aAAa,CAAE,CACb/C,GAAG,CAAE,eAAe,CACpBC,KAAK,CAAE,gBAAgB,CACvBC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfe,WAAW,CAAE,CAAEC,KAAK,CAAE,cAAc,CAAEN,KAAK,CAAE,IAAK,CAAC,CACnDP,OAAO,CAAE,eACX,CAAC,CACD0C,iBAAiB,CAAE,CACjBhD,GAAG,CAAE,mBAAmB,CACxBC,KAAK,CAAE,oBAAoB,CAC3BC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfe,WAAW,CAAE,CAAEC,KAAK,CAAE,cAAc,CAAEN,KAAK,CAAE,IAAK,CAAC,CACnDP,OAAO,CAAE,eACX,CAAC,CACD2C,eAAe,CAAE,CACfjD,GAAG,CAAE,iBAAiB,CACtBC,KAAK,CAAE,kBAAkB,CACzBC,IAAI,CAAE,KAAK,CACXC,QAAQ,CAAE,KAAK,CACfe,WAAW,CAAE,CAAEC,KAAK,CAAE,cAAc,CAAEN,KAAK,CAAE,IAAK,CAAC,CACnDP,OAAO,CAAE,eACX,CACF,CACF,CAAC,CAED;AACA4C,eAAe,CAAE,CACfrD,KAAK,CAAE,kBAAkB,CACzBC,MAAM,CAAE,CACNqD,YAAY,CAAE,CACZnD,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,eAAe,CACtBC,IAAI,CAAE,UAAU,CAChBC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,iBACX,CAAC,CACD8C,WAAW,CAAE,CACXpD,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,cAAc,CACrBC,IAAI,CAAE,KAAK,CACXC,QAAQ,CAAE,KAAK,CACfe,WAAW,CAAE,CAAEC,KAAK,CAAE,cAAc,CAAEN,KAAK,CAAE,IAAK,CAAC,CACnDP,OAAO,CAAE,iBACX,CAAC,CACD+C,WAAW,CAAE,CACXrD,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,eAAe,CACtBC,IAAI,CAAE,UAAU,CAChBC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,iBACX,CAAC,CACDgD,UAAU,CAAE,CACVtD,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,cAAc,CACrBC,IAAI,CAAE,KAAK,CACXC,QAAQ,CAAE,KAAK,CACfe,WAAW,CAAE,CAAEC,KAAK,CAAE,aAAa,CAAEN,KAAK,CAAE,IAAK,CAAC,CAClDP,OAAO,CAAE,iBACX,CACF,CACF,CAAC,CAED;AACAiD,WAAW,CAAE,CACX1D,KAAK,CAAE,qBAAqB,CAC5BC,MAAM,CAAE,CACN0D,QAAQ,CAAE,CACRxD,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,WAAW,CAClBC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,aACX,CAAC,CACDmD,eAAe,CAAE,CACfzD,GAAG,CAAE,iBAAiB,CACtBC,KAAK,CAAE,mBAAmB,CAC1BC,IAAI,CAAE,QAAQ,CACdC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,aACX,CAAC,CACDoD,gBAAgB,CAAE,CAChB1D,GAAG,CAAE,kBAAkB,CACvBC,KAAK,CAAE,oBAAoB,CAC3BC,IAAI,CAAE,QAAQ,CACdC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,aACX,CAAC,CACDqD,qBAAqB,CAAE,CACrB3D,GAAG,CAAE,uBAAuB,CAC5BC,KAAK,CAAE,yBAAyB,CAChCC,IAAI,CAAE,QAAQ,CACdC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,aACX,CACF,CACF,CAAC,CAED;AACAsD,gBAAgB,CAAE,CAChB/D,KAAK,CAAE,mBAAmB,CAC1BC,MAAM,CAAE,CACN+D,oBAAoB,CAAE,CACpB7D,GAAG,CAAE,sBAAsB,CAC3BC,KAAK,CAAE,wBAAwB,CAC/BC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,kBACX,CAAC,CACDwD,qBAAqB,CAAE,CACrB9D,GAAG,CAAE,uBAAuB,CAC5BC,KAAK,CAAE,yBAAyB,CAChCC,IAAI,CAAE,OAAO,CACbC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,kBACX,CAAC,CACDyD,WAAW,CAAE,CACX/D,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,aAAa,CACpBC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,kBACX,CACF,CACF,CAAC,CAED;AACA0D,aAAa,CAAE,CACbnE,KAAK,CAAE,uBAAuB,CAC9BC,MAAM,CAAE,CACNmE,gBAAgB,CAAE,CAChBjE,GAAG,CAAE,kBAAkB,CACvBC,KAAK,CAAE,mBAAmB,CAC1BC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,eACX,CAAC,CACD4D,oBAAoB,CAAE,CACpBlE,GAAG,CAAE,sBAAsB,CAC3BC,KAAK,CAAE,uBAAuB,CAC9BC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,eACX,CAAC,CACD6D,cAAc,CAAE,CACdnE,GAAG,CAAE,gBAAgB,CACrBC,KAAK,CAAE,kBAAkB,CACzBC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,eACX,CAAC,CACD8D,UAAU,CAAE,CACVpE,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,YAAY,CACnBC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfG,OAAO,CAAE,eACX,CACF,CACF,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAA+D,kBAAkB,CAAGA,CAAA,GAAM,CACtC,KAAM,CAAAC,SAAS,CAAG,EAAE,CACpB7D,MAAM,CAAC8D,MAAM,CAAC5E,sBAAsB,CAAC,CAAC6E,OAAO,CAAClE,OAAO,EAAI,CACvDG,MAAM,CAAC8D,MAAM,CAACjE,OAAO,CAACR,MAAM,CAAC,CAAC0E,OAAO,CAACrD,KAAK,EAAI,CAC7CmD,SAAS,CAACG,IAAI,CAACtD,KAAK,CAAC,CACvB,CAAC,CAAC,CACJ,CAAC,CAAC,CACF,MAAO,CAAAmD,SAAS,CAClB,CAAC,CAED;AACA,MAAO,MAAM,CAAAI,iBAAiB,CAAGA,CAAA,GAAM,CACrC,MAAO,CAAAL,kBAAkB,CAAC,CAAC,CAACM,MAAM,CAACxD,KAAK,EAAIA,KAAK,CAAChB,QAAQ,CAAC,CAC7D,CAAC,CAED;AACA,MAAO,MAAM,CAAAyE,uBAAuB,CAAGA,CAAA,GAAM,CAC3C,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAC7B,KAAM,CAAAC,UAAU,CAAG,EAAE,CAErBtE,MAAM,CAACC,OAAO,CAACf,sBAAsB,CAAC,CAAC6E,OAAO,CAACQ,KAAA,EAA2B,IAA1B,CAACC,UAAU,CAAE3E,OAAO,CAAC,CAAA0E,KAAA,CACnEvE,MAAM,CAACC,OAAO,CAACJ,OAAO,CAACR,MAAM,CAAC,CAAC0E,OAAO,CAACU,KAAA,EAAwB,IAAvB,CAACC,SAAS,CAAEhE,KAAK,CAAC,CAAA+D,KAAA,CACxD,KAAM,CAAAlF,GAAG,CAAGmB,KAAK,CAACnB,GAAG,CAErB,GAAI6E,WAAW,CAACO,GAAG,CAACpF,GAAG,CAAC,CAAE,CACxB+E,UAAU,CAACN,IAAI,CAAC,CACdzE,GAAG,CACHqF,QAAQ,CAAE,CAACR,WAAW,CAACS,GAAG,CAACtF,GAAG,CAAC,CAACM,OAAO,CAAE2E,UAAU,CAAC,CACpDM,UAAU,CAAEV,WAAW,CAACS,GAAG,CAACtF,GAAG,CAAC,CAACmF,SAAS,CAC1CK,UAAU,CAAEL,SACd,CAAC,CAAC,CACJ,CAAC,IAAM,CACLN,WAAW,CAACY,GAAG,CAACzF,GAAG,CAAE,CAAEM,OAAO,CAAE2E,UAAU,CAAEE,SAAU,CAAC,CAAC,CAC1D,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF,GAAIJ,UAAU,CAACW,MAAM,CAAG,CAAC,CAAE,CACzBC,OAAO,CAACC,KAAK,CAAC,oCAAoC,CAAC,CACnDb,UAAU,CAACP,OAAO,CAACqB,GAAG,EAAI,CACxBF,OAAO,CAACC,KAAK,CAAC,cAAcC,GAAG,CAAC7F,GAAG,oCAAoC,CAAE6F,GAAG,CAACR,QAAQ,CAAC,CACxF,CAAC,CAAC,CACJ,CAAC,IAAM,CACLM,OAAO,CAACG,GAAG,CAAC,uCAAuC,CAAC,CACtD,CAEA,MAAO,CAAAf,UAAU,CACnB,CAAC,CAED;AACA,MAAO,MAAM,CAAAgB,oBAAoB,CAAGA,CAAA,IAAO,CACzCC,UAAU,CAAE,IAAI,CAChBC,UAAU,CAAE,IAAI,CAChBC,aAAa,CAAE,IAAI,CACnBnG,IAAI,CAAE,EAAE,CACRsB,YAAY,CAAE,EAAE,CAChBa,MAAM,CAAE,IAAI,CACZ3B,MAAM,CAAE,IAAI,CACZgB,gBAAgB,CAAE,EAAE,CACpBC,cAAc,CAAE,IAAI,CAAE;AACtBC,iBAAiB,CAAE,EAAE,CACrBC,OAAO,CAAE,IAAI,CAAE;AACfX,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,KAAK,CAChBC,cAAc,CAAE,IAAI,CACpBW,YAAY,CAAE,EAAE,CAChBC,aAAa,CAAE,EAAE,CACjBC,QAAQ,CAAE,EAAE,CACZC,OAAO,CAAE,EAAE,CACXC,WAAW,CAAE,EAAE,CACfc,YAAY,CAAE,KAAK,CACnBC,aAAa,CAAE,EAAE,CACjBC,iBAAiB,CAAE,EAAE,CACrBC,eAAe,CAAE,EAAE,CACnBE,YAAY,CAAE,KAAK,CACnBC,WAAW,CAAE,IAAI,CAAE;AACnBC,WAAW,CAAE,KAAK,CAClBC,UAAU,CAAE,IAAI,CAAE;AAClBlB,gBAAgB,CAAE,IAAI,CACtBC,sBAAsB,CAAE,EAAE,CAC1BC,eAAe,CAAE,EAAE,CACnBE,UAAU,CAAE,IAAI,CAChBG,MAAM,CAAE,EAAE,CACVC,OAAO,CAAE,EAAE,CACXY,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,IAAI,CACrBC,gBAAgB,CAAE,IAAI,CACtBC,qBAAqB,CAAE,IAAI,CAC3BE,oBAAoB,CAAE,EAAE,CACxBC,qBAAqB,CAAE,IAAI,CAAE;AAC7BC,WAAW,CAAE,EAAE,CACfE,gBAAgB,CAAE,EAAE,CACpBC,oBAAoB,CAAE,EAAE,CACxBC,cAAc,CAAE,EAAE,CAClBC,UAAU,CAAE,EACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}