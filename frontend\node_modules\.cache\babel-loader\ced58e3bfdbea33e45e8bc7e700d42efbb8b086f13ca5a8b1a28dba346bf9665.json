{"ast": null, "code": "import { buildTransform } from './build-transform.mjs';\nimport { isCSSVariableName } from '../../dom/utils/is-css-variable.mjs';\nimport { transformProps } from './transform.mjs';\nimport { getValueAsType } from '../../dom/value-types/get-as-type.mjs';\nimport { numberValueTypes } from '../../dom/value-types/number.mjs';\nfunction buildHTMLStyles(state, latestValues, options, transformTemplate) {\n  const {\n    style,\n    vars,\n    transform,\n    transformOrigin\n  } = state;\n  // Track whether we encounter any transform or transformOrigin values.\n  let hasTransform = false;\n  let hasTransformOrigin = false;\n  // Does the calculated transform essentially equal \"none\"?\n  let transformIsNone = true;\n  /**\n   * Loop over all our latest animated values and decide whether to handle them\n   * as a style or CSS variable.\n   *\n   * Transforms and transform origins are kept seperately for further processing.\n   */\n  for (const key in latestValues) {\n    const value = latestValues[key];\n    /**\n     * If this is a CSS variable we don't do any further processing.\n     */\n    if (isCSSVariableName(key)) {\n      vars[key] = value;\n      continue;\n    }\n    // Convert the value to its default value type, ie 0 -> \"0px\"\n    const valueType = numberValueTypes[key];\n    const valueAsType = getValueAsType(value, valueType);\n    if (transformProps.has(key)) {\n      // If this is a transform, flag to enable further transform processing\n      hasTransform = true;\n      transform[key] = valueAsType;\n      // If we already know we have a non-default transform, early return\n      if (!transformIsNone) continue;\n      // Otherwise check to see if this is a default transform\n      if (value !== (valueType.default || 0)) transformIsNone = false;\n    } else if (key.startsWith(\"origin\")) {\n      // If this is a transform origin, flag and enable further transform-origin processing\n      hasTransformOrigin = true;\n      transformOrigin[key] = valueAsType;\n    } else {\n      style[key] = valueAsType;\n    }\n  }\n  if (!latestValues.transform) {\n    if (hasTransform || transformTemplate) {\n      style.transform = buildTransform(state.transform, options, transformIsNone, transformTemplate);\n    } else if (style.transform) {\n      /**\n       * If we have previously created a transform but currently don't have any,\n       * reset transform style to none.\n       */\n      style.transform = \"none\";\n    }\n  }\n  /**\n   * Build a transformOrigin style. Uses the same defaults as the browser for\n   * undefined origins.\n   */\n  if (hasTransformOrigin) {\n    const {\n      originX = \"50%\",\n      originY = \"50%\",\n      originZ = 0\n    } = transformOrigin;\n    style.transformOrigin = `${originX} ${originY} ${originZ}`;\n  }\n}\nexport { buildHTMLStyles };", "map": {"version": 3, "names": ["buildTransform", "isCSSVariableName", "transformProps", "getValueAsType", "numberValueTypes", "buildHTMLStyles", "state", "latestValues", "options", "transformTemplate", "style", "vars", "transform", "transform<PERSON><PERSON>in", "hasTransform", "hasTransformOrigin", "transformIsNone", "key", "value", "valueType", "valueAsType", "has", "default", "startsWith", "originX", "originY", "originZ"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs"], "sourcesContent": ["import { buildTransform } from './build-transform.mjs';\nimport { isCSSVariableName } from '../../dom/utils/is-css-variable.mjs';\nimport { transformProps } from './transform.mjs';\nimport { getValueAsType } from '../../dom/value-types/get-as-type.mjs';\nimport { numberValueTypes } from '../../dom/value-types/number.mjs';\n\nfunction buildHTMLStyles(state, latestValues, options, transformTemplate) {\n    const { style, vars, transform, transformOrigin } = state;\n    // Track whether we encounter any transform or transformOrigin values.\n    let hasTransform = false;\n    let hasTransformOrigin = false;\n    // Does the calculated transform essentially equal \"none\"?\n    let transformIsNone = true;\n    /**\n     * Loop over all our latest animated values and decide whether to handle them\n     * as a style or CSS variable.\n     *\n     * Transforms and transform origins are kept seperately for further processing.\n     */\n    for (const key in latestValues) {\n        const value = latestValues[key];\n        /**\n         * If this is a CSS variable we don't do any further processing.\n         */\n        if (isCSSVariableName(key)) {\n            vars[key] = value;\n            continue;\n        }\n        // Convert the value to its default value type, ie 0 -> \"0px\"\n        const valueType = numberValueTypes[key];\n        const valueAsType = getValueAsType(value, valueType);\n        if (transformProps.has(key)) {\n            // If this is a transform, flag to enable further transform processing\n            hasTransform = true;\n            transform[key] = valueAsType;\n            // If we already know we have a non-default transform, early return\n            if (!transformIsNone)\n                continue;\n            // Otherwise check to see if this is a default transform\n            if (value !== (valueType.default || 0))\n                transformIsNone = false;\n        }\n        else if (key.startsWith(\"origin\")) {\n            // If this is a transform origin, flag and enable further transform-origin processing\n            hasTransformOrigin = true;\n            transformOrigin[key] = valueAsType;\n        }\n        else {\n            style[key] = valueAsType;\n        }\n    }\n    if (!latestValues.transform) {\n        if (hasTransform || transformTemplate) {\n            style.transform = buildTransform(state.transform, options, transformIsNone, transformTemplate);\n        }\n        else if (style.transform) {\n            /**\n             * If we have previously created a transform but currently don't have any,\n             * reset transform style to none.\n             */\n            style.transform = \"none\";\n        }\n    }\n    /**\n     * Build a transformOrigin style. Uses the same defaults as the browser for\n     * undefined origins.\n     */\n    if (hasTransformOrigin) {\n        const { originX = \"50%\", originY = \"50%\", originZ = 0, } = transformOrigin;\n        style.transformOrigin = `${originX} ${originY} ${originZ}`;\n    }\n}\n\nexport { buildHTMLStyles };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,uBAAuB;AACtD,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,gBAAgB,QAAQ,kCAAkC;AAEnE,SAASC,eAAeA,CAACC,KAAK,EAAEC,YAAY,EAAEC,OAAO,EAAEC,iBAAiB,EAAE;EACtE,MAAM;IAAEC,KAAK;IAAEC,IAAI;IAAEC,SAAS;IAAEC;EAAgB,CAAC,GAAGP,KAAK;EACzD;EACA,IAAIQ,YAAY,GAAG,KAAK;EACxB,IAAIC,kBAAkB,GAAG,KAAK;EAC9B;EACA,IAAIC,eAAe,GAAG,IAAI;EAC1B;AACJ;AACA;AACA;AACA;AACA;EACI,KAAK,MAAMC,GAAG,IAAIV,YAAY,EAAE;IAC5B,MAAMW,KAAK,GAAGX,YAAY,CAACU,GAAG,CAAC;IAC/B;AACR;AACA;IACQ,IAAIhB,iBAAiB,CAACgB,GAAG,CAAC,EAAE;MACxBN,IAAI,CAACM,GAAG,CAAC,GAAGC,KAAK;MACjB;IACJ;IACA;IACA,MAAMC,SAAS,GAAGf,gBAAgB,CAACa,GAAG,CAAC;IACvC,MAAMG,WAAW,GAAGjB,cAAc,CAACe,KAAK,EAAEC,SAAS,CAAC;IACpD,IAAIjB,cAAc,CAACmB,GAAG,CAACJ,GAAG,CAAC,EAAE;MACzB;MACAH,YAAY,GAAG,IAAI;MACnBF,SAAS,CAACK,GAAG,CAAC,GAAGG,WAAW;MAC5B;MACA,IAAI,CAACJ,eAAe,EAChB;MACJ;MACA,IAAIE,KAAK,MAAMC,SAAS,CAACG,OAAO,IAAI,CAAC,CAAC,EAClCN,eAAe,GAAG,KAAK;IAC/B,CAAC,MACI,IAAIC,GAAG,CAACM,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC/B;MACAR,kBAAkB,GAAG,IAAI;MACzBF,eAAe,CAACI,GAAG,CAAC,GAAGG,WAAW;IACtC,CAAC,MACI;MACDV,KAAK,CAACO,GAAG,CAAC,GAAGG,WAAW;IAC5B;EACJ;EACA,IAAI,CAACb,YAAY,CAACK,SAAS,EAAE;IACzB,IAAIE,YAAY,IAAIL,iBAAiB,EAAE;MACnCC,KAAK,CAACE,SAAS,GAAGZ,cAAc,CAACM,KAAK,CAACM,SAAS,EAAEJ,OAAO,EAAEQ,eAAe,EAAEP,iBAAiB,CAAC;IAClG,CAAC,MACI,IAAIC,KAAK,CAACE,SAAS,EAAE;MACtB;AACZ;AACA;AACA;MACYF,KAAK,CAACE,SAAS,GAAG,MAAM;IAC5B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIG,kBAAkB,EAAE;IACpB,MAAM;MAAES,OAAO,GAAG,KAAK;MAAEC,OAAO,GAAG,KAAK;MAAEC,OAAO,GAAG;IAAG,CAAC,GAAGb,eAAe;IAC1EH,KAAK,CAACG,eAAe,GAAG,GAAGW,OAAO,IAAIC,OAAO,IAAIC,OAAO,EAAE;EAC9D;AACJ;AAEA,SAASrB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}