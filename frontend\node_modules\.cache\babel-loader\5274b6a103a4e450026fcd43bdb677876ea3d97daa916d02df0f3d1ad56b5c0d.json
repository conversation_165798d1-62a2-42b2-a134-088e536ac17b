{"ast": null, "code": "const API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:5000/api';class ApiService{constructor(){this.baseURL=API_BASE_URL;}async request(endpoint){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const url=`${this.baseURL}${endpoint}`;const config={headers:{'Content-Type':'application/json',...options.headers},...options};try{const response=await fetch(url,config);if(!response.ok){const errorData=await response.json().catch(()=>({}));throw new ApiError(response.status,errorData.message||'An error occurred',errorData);}// Handle blob responses (for file downloads)\nif(options.responseType==='blob'){return{data:await response.blob()};}const data=await response.json();return{data};// Wrap in data property for consistency\n}catch(error){if(error instanceof ApiError){throw error;}throw new ApiError(0,'Network error',{originalError:error.message});}}// Generic HTTP methods\nasync get(endpoint){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};return this.request(endpoint,{method:'GET',...options});}async post(endpoint){let data=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;let options=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};const config={method:'POST',...options};if(data){config.body=JSON.stringify(data);}return this.request(endpoint,config);}async put(endpoint){let data=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;let options=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};const config={method:'PUT',...options};if(data){config.body=JSON.stringify(data);}return this.request(endpoint,config);}async delete(endpoint){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};return this.request(endpoint,{method:'DELETE',...options});}// Division endpoints\nasync getDivisions(){return this.get('/divisions');}async getDivision(id){return this.get(`/divisions/${id}`);}// Category endpoints\nasync getCategories(){return this.get('/categories');}async getCategory(id){return this.get(`/categories/${id}`);}async getCategoriesByDivision(divisionId){return this.get(`/categories/division/${divisionId}`);}// FirmNature endpoints\nasync getFirmNatures(){return this.get('/firmnatures');}async getFirmNature(id){return this.get(`/firmnatures/${id}`);}async getFirmNaturesByCategory(categoryId){return this.get(`/firmnatures/category/${categoryId}`);}// States endpoints\nasync getStates(){return this.get('/states');}// Person endpoints\nasync getPersons(){let params=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};const queryString=new URLSearchParams(params).toString();return this.get(`/persons${queryString?`?${queryString}`:''}`);}async getPerson(id){return this.get(`/persons/${id}`);}async createPerson(personData){return this.post('/persons',personData);}async updatePerson(id,personData){return this.put(`/persons/${id}`,personData);}async deletePerson(id){return this.delete(`/persons/${id}`);}async searchPersons(searchRequest){return this.post('/persons/search',searchRequest);}async getPersonsByDivision(divisionId){return this.get(`/persons/division/${divisionId}`);}async getPersonsByCategory(categoryId){return this.get(`/persons/category/${categoryId}`);}async getPersonsByFirmNature(firmNatureId){return this.get(`/persons/firmnature/${firmNatureId}`);}async getPersonStatistics(){return this.get('/persons/statistics');}async getPersonEnums(){return this.get('/persons/enums');}// Bulk operations\nasync bulkCreatePersons(personsData){return this.post('/persons/bulk',personsData);}async bulkSoftDeletePersons(personIds){return this.post('/persons/bulk/soft-delete',personIds);}async bulkRestorePersons(personIds){return this.post('/persons/bulk/restore',personIds);}}class ApiError extends Error{constructor(status,message){let data=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};super(message);this.name='ApiError';this.status=status;this.data=data;}isValidationError(){return this.status===400&&this.data.errors;}isNotFoundError(){return this.status===404;}isServerError(){return this.status>=500;}getValidationErrors(){return this.data.errors||{};}}export{ApiService,ApiError};export default new ApiService();", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ApiService", "constructor", "baseURL", "request", "endpoint", "options", "arguments", "length", "undefined", "url", "config", "headers", "response", "fetch", "ok", "errorData", "json", "catch", "ApiError", "status", "message", "responseType", "data", "blob", "error", "originalError", "get", "method", "post", "body", "JSON", "stringify", "put", "delete", "getDivisions", "getDivision", "id", "getCategories", "getCategory", "getCategoriesByDivision", "divisionId", "getFirmNatures", "getFirmNature", "getFirmNaturesByCategory", "categoryId", "getStates", "<PERSON><PERSON><PERSON><PERSON>", "params", "queryString", "URLSearchParams", "toString", "<PERSON><PERSON><PERSON>", "create<PERSON>erson", "personData", "update<PERSON><PERSON>", "deletePerson", "search<PERSON><PERSON>s", "searchRequest", "getPersonsByDivision", "getPersonsByCategory", "getPersonsByFirmNature", "firmNatureId", "getPersonStatistics", "getPersonEnums", "bulkCreatePersons", "personsData", "bulkSoftDeletePersons", "personIds", "bulkRestorePersons", "Error", "name", "isValidationError", "errors", "isNotFoundError", "isServerError", "getValidationErrors"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/services/apiService.js"], "sourcesContent": ["const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\nclass ApiService {\n  constructor() {\n    this.baseURL = API_BASE_URL;\n  }\n\n  async request(endpoint, options = {}) {\n    const url = `${this.baseURL}${endpoint}`;\n    const config = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    try {\n      const response = await fetch(url, config);\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new ApiError(response.status, errorData.message || 'An error occurred', errorData);\n      }\n\n      // Handle blob responses (for file downloads)\n      if (options.responseType === 'blob') {\n        return { data: await response.blob() };\n      }\n\n      const data = await response.json();\n      return { data }; // Wrap in data property for consistency\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(0, 'Network error', { originalError: error.message });\n    }\n  }\n\n  // Generic HTTP methods\n  async get(endpoint, options = {}) {\n    return this.request(endpoint, { method: 'GET', ...options });\n  }\n\n  async post(endpoint, data = null, options = {}) {\n    const config = { method: 'POST', ...options };\n    if (data) {\n      config.body = JSON.stringify(data);\n    }\n    return this.request(endpoint, config);\n  }\n\n  async put(endpoint, data = null, options = {}) {\n    const config = { method: 'PUT', ...options };\n    if (data) {\n      config.body = JSON.stringify(data);\n    }\n    return this.request(endpoint, config);\n  }\n\n  async delete(endpoint, options = {}) {\n    return this.request(endpoint, { method: 'DELETE', ...options });\n  }\n\n  // Division endpoints\n  async getDivisions() {\n    return this.get('/divisions');\n  }\n\n  async getDivision(id) {\n    return this.get(`/divisions/${id}`);\n  }\n\n  // Category endpoints\n  async getCategories() {\n    return this.get('/categories');\n  }\n\n  async getCategory(id) {\n    return this.get(`/categories/${id}`);\n  }\n\n  async getCategoriesByDivision(divisionId) {\n    return this.get(`/categories/division/${divisionId}`);\n  }\n\n  // FirmNature endpoints\n  async getFirmNatures() {\n    return this.get('/firmnatures');\n  }\n\n  async getFirmNature(id) {\n    return this.get(`/firmnatures/${id}`);\n  }\n\n  async getFirmNaturesByCategory(categoryId) {\n    return this.get(`/firmnatures/category/${categoryId}`);\n  }\n\n  // States endpoints\n  async getStates() {\n    return this.get('/states');\n  }\n\n  // Person endpoints\n  async getPersons(params = {}) {\n    const queryString = new URLSearchParams(params).toString();\n    return this.get(`/persons${queryString ? `?${queryString}` : ''}`);\n  }\n\n  async getPerson(id) {\n    return this.get(`/persons/${id}`);\n  }\n\n  async createPerson(personData) {\n    return this.post('/persons', personData);\n  }\n\n  async updatePerson(id, personData) {\n    return this.put(`/persons/${id}`, personData);\n  }\n\n  async deletePerson(id) {\n    return this.delete(`/persons/${id}`);\n  }\n\n  async searchPersons(searchRequest) {\n    return this.post('/persons/search', searchRequest);\n  }\n\n  async getPersonsByDivision(divisionId) {\n    return this.get(`/persons/division/${divisionId}`);\n  }\n\n  async getPersonsByCategory(categoryId) {\n    return this.get(`/persons/category/${categoryId}`);\n  }\n\n  async getPersonsByFirmNature(firmNatureId) {\n    return this.get(`/persons/firmnature/${firmNatureId}`);\n  }\n\n  async getPersonStatistics() {\n    return this.get('/persons/statistics');\n  }\n\n  async getPersonEnums() {\n    return this.get('/persons/enums');\n  }\n\n  // Bulk operations\n  async bulkCreatePersons(personsData) {\n    return this.post('/persons/bulk', personsData);\n  }\n\n  async bulkSoftDeletePersons(personIds) {\n    return this.post('/persons/bulk/soft-delete', personIds);\n  }\n\n  async bulkRestorePersons(personIds) {\n    return this.post('/persons/bulk/restore', personIds);\n  }\n}\n\nclass ApiError extends Error {\n  constructor(status, message, data = {}) {\n    super(message);\n    this.name = 'ApiError';\n    this.status = status;\n    this.data = data;\n  }\n\n  isValidationError() {\n    return this.status === 400 && this.data.errors;\n  }\n\n  isNotFoundError() {\n    return this.status === 404;\n  }\n\n  isServerError() {\n    return this.status >= 500;\n  }\n\n  getValidationErrors() {\n    return this.data.errors || {};\n  }\n}\n\nexport { ApiService, ApiError };\nexport default new ApiService();\n"], "mappings": "AAAA,KAAM,CAAAA,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,2BAA2B,CAEjF,KAAM,CAAAC,UAAW,CACfC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,OAAO,CAAGN,YAAY,CAC7B,CAEA,KAAM,CAAAO,OAAOA,CAACC,QAAQ,CAAgB,IAAd,CAAAC,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAClC,KAAM,CAAAG,GAAG,CAAG,GAAG,IAAI,CAACP,OAAO,GAAGE,QAAQ,EAAE,CACxC,KAAM,CAAAM,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClC,GAAGN,OAAO,CAACM,OACb,CAAC,CACD,GAAGN,OACL,CAAC,CAED,GAAI,CACF,KAAM,CAAAO,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAACJ,GAAG,CAAEC,MAAM,CAAC,CAEzC,GAAI,CAACE,QAAQ,CAACE,EAAE,CAAE,CAChB,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAO,CAAC,CAAC,CAAC,CAAC,CACzD,KAAM,IAAI,CAAAC,QAAQ,CAACN,QAAQ,CAACO,MAAM,CAAEJ,SAAS,CAACK,OAAO,EAAI,mBAAmB,CAAEL,SAAS,CAAC,CAC1F,CAEA;AACA,GAAIV,OAAO,CAACgB,YAAY,GAAK,MAAM,CAAE,CACnC,MAAO,CAAEC,IAAI,CAAE,KAAM,CAAAV,QAAQ,CAACW,IAAI,CAAC,CAAE,CAAC,CACxC,CAEA,KAAM,CAAAD,IAAI,CAAG,KAAM,CAAAV,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClC,MAAO,CAAEM,IAAK,CAAC,CAAE;AACnB,CAAE,MAAOE,KAAK,CAAE,CACd,GAAIA,KAAK,WAAY,CAAAN,QAAQ,CAAE,CAC7B,KAAM,CAAAM,KAAK,CACb,CACA,KAAM,IAAI,CAAAN,QAAQ,CAAC,CAAC,CAAE,eAAe,CAAE,CAAEO,aAAa,CAAED,KAAK,CAACJ,OAAQ,CAAC,CAAC,CAC1E,CACF,CAEA;AACA,KAAM,CAAAM,GAAGA,CAACtB,QAAQ,CAAgB,IAAd,CAAAC,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAC9B,MAAO,KAAI,CAACH,OAAO,CAACC,QAAQ,CAAE,CAAEuB,MAAM,CAAE,KAAK,CAAE,GAAGtB,OAAQ,CAAC,CAAC,CAC9D,CAEA,KAAM,CAAAuB,IAAIA,CAACxB,QAAQ,CAA6B,IAA3B,CAAAkB,IAAI,CAAAhB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,IAAE,CAAAD,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAC5C,KAAM,CAAAI,MAAM,CAAG,CAAEiB,MAAM,CAAE,MAAM,CAAE,GAAGtB,OAAQ,CAAC,CAC7C,GAAIiB,IAAI,CAAE,CACRZ,MAAM,CAACmB,IAAI,CAAGC,IAAI,CAACC,SAAS,CAACT,IAAI,CAAC,CACpC,CACA,MAAO,KAAI,CAACnB,OAAO,CAACC,QAAQ,CAAEM,MAAM,CAAC,CACvC,CAEA,KAAM,CAAAsB,GAAGA,CAAC5B,QAAQ,CAA6B,IAA3B,CAAAkB,IAAI,CAAAhB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,IAAE,CAAAD,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAC3C,KAAM,CAAAI,MAAM,CAAG,CAAEiB,MAAM,CAAE,KAAK,CAAE,GAAGtB,OAAQ,CAAC,CAC5C,GAAIiB,IAAI,CAAE,CACRZ,MAAM,CAACmB,IAAI,CAAGC,IAAI,CAACC,SAAS,CAACT,IAAI,CAAC,CACpC,CACA,MAAO,KAAI,CAACnB,OAAO,CAACC,QAAQ,CAAEM,MAAM,CAAC,CACvC,CAEA,KAAM,CAAAuB,MAAMA,CAAC7B,QAAQ,CAAgB,IAAd,CAAAC,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACjC,MAAO,KAAI,CAACH,OAAO,CAACC,QAAQ,CAAE,CAAEuB,MAAM,CAAE,QAAQ,CAAE,GAAGtB,OAAQ,CAAC,CAAC,CACjE,CAEA;AACA,KAAM,CAAA6B,YAAYA,CAAA,CAAG,CACnB,MAAO,KAAI,CAACR,GAAG,CAAC,YAAY,CAAC,CAC/B,CAEA,KAAM,CAAAS,WAAWA,CAACC,EAAE,CAAE,CACpB,MAAO,KAAI,CAACV,GAAG,CAAC,cAAcU,EAAE,EAAE,CAAC,CACrC,CAEA;AACA,KAAM,CAAAC,aAAaA,CAAA,CAAG,CACpB,MAAO,KAAI,CAACX,GAAG,CAAC,aAAa,CAAC,CAChC,CAEA,KAAM,CAAAY,WAAWA,CAACF,EAAE,CAAE,CACpB,MAAO,KAAI,CAACV,GAAG,CAAC,eAAeU,EAAE,EAAE,CAAC,CACtC,CAEA,KAAM,CAAAG,uBAAuBA,CAACC,UAAU,CAAE,CACxC,MAAO,KAAI,CAACd,GAAG,CAAC,wBAAwBc,UAAU,EAAE,CAAC,CACvD,CAEA;AACA,KAAM,CAAAC,cAAcA,CAAA,CAAG,CACrB,MAAO,KAAI,CAACf,GAAG,CAAC,cAAc,CAAC,CACjC,CAEA,KAAM,CAAAgB,aAAaA,CAACN,EAAE,CAAE,CACtB,MAAO,KAAI,CAACV,GAAG,CAAC,gBAAgBU,EAAE,EAAE,CAAC,CACvC,CAEA,KAAM,CAAAO,wBAAwBA,CAACC,UAAU,CAAE,CACzC,MAAO,KAAI,CAAClB,GAAG,CAAC,yBAAyBkB,UAAU,EAAE,CAAC,CACxD,CAEA;AACA,KAAM,CAAAC,SAASA,CAAA,CAAG,CAChB,MAAO,KAAI,CAACnB,GAAG,CAAC,SAAS,CAAC,CAC5B,CAEA;AACA,KAAM,CAAAoB,UAAUA,CAAA,CAAc,IAAb,CAAAC,MAAM,CAAAzC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAC1B,KAAM,CAAA0C,WAAW,CAAG,GAAI,CAAAC,eAAe,CAACF,MAAM,CAAC,CAACG,QAAQ,CAAC,CAAC,CAC1D,MAAO,KAAI,CAACxB,GAAG,CAAC,WAAWsB,WAAW,CAAG,IAAIA,WAAW,EAAE,CAAG,EAAE,EAAE,CAAC,CACpE,CAEA,KAAM,CAAAG,SAASA,CAACf,EAAE,CAAE,CAClB,MAAO,KAAI,CAACV,GAAG,CAAC,YAAYU,EAAE,EAAE,CAAC,CACnC,CAEA,KAAM,CAAAgB,YAAYA,CAACC,UAAU,CAAE,CAC7B,MAAO,KAAI,CAACzB,IAAI,CAAC,UAAU,CAAEyB,UAAU,CAAC,CAC1C,CAEA,KAAM,CAAAC,YAAYA,CAAClB,EAAE,CAAEiB,UAAU,CAAE,CACjC,MAAO,KAAI,CAACrB,GAAG,CAAC,YAAYI,EAAE,EAAE,CAAEiB,UAAU,CAAC,CAC/C,CAEA,KAAM,CAAAE,YAAYA,CAACnB,EAAE,CAAE,CACrB,MAAO,KAAI,CAACH,MAAM,CAAC,YAAYG,EAAE,EAAE,CAAC,CACtC,CAEA,KAAM,CAAAoB,aAAaA,CAACC,aAAa,CAAE,CACjC,MAAO,KAAI,CAAC7B,IAAI,CAAC,iBAAiB,CAAE6B,aAAa,CAAC,CACpD,CAEA,KAAM,CAAAC,oBAAoBA,CAAClB,UAAU,CAAE,CACrC,MAAO,KAAI,CAACd,GAAG,CAAC,qBAAqBc,UAAU,EAAE,CAAC,CACpD,CAEA,KAAM,CAAAmB,oBAAoBA,CAACf,UAAU,CAAE,CACrC,MAAO,KAAI,CAAClB,GAAG,CAAC,qBAAqBkB,UAAU,EAAE,CAAC,CACpD,CAEA,KAAM,CAAAgB,sBAAsBA,CAACC,YAAY,CAAE,CACzC,MAAO,KAAI,CAACnC,GAAG,CAAC,uBAAuBmC,YAAY,EAAE,CAAC,CACxD,CAEA,KAAM,CAAAC,mBAAmBA,CAAA,CAAG,CAC1B,MAAO,KAAI,CAACpC,GAAG,CAAC,qBAAqB,CAAC,CACxC,CAEA,KAAM,CAAAqC,cAAcA,CAAA,CAAG,CACrB,MAAO,KAAI,CAACrC,GAAG,CAAC,gBAAgB,CAAC,CACnC,CAEA;AACA,KAAM,CAAAsC,iBAAiBA,CAACC,WAAW,CAAE,CACnC,MAAO,KAAI,CAACrC,IAAI,CAAC,eAAe,CAAEqC,WAAW,CAAC,CAChD,CAEA,KAAM,CAAAC,qBAAqBA,CAACC,SAAS,CAAE,CACrC,MAAO,KAAI,CAACvC,IAAI,CAAC,2BAA2B,CAAEuC,SAAS,CAAC,CAC1D,CAEA,KAAM,CAAAC,kBAAkBA,CAACD,SAAS,CAAE,CAClC,MAAO,KAAI,CAACvC,IAAI,CAAC,uBAAuB,CAAEuC,SAAS,CAAC,CACtD,CACF,CAEA,KAAM,CAAAjD,QAAQ,QAAS,CAAAmD,KAAM,CAC3BpE,WAAWA,CAACkB,MAAM,CAAEC,OAAO,CAAa,IAAX,CAAAE,IAAI,CAAAhB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACpC,KAAK,CAACc,OAAO,CAAC,CACd,IAAI,CAACkD,IAAI,CAAG,UAAU,CACtB,IAAI,CAACnD,MAAM,CAAGA,MAAM,CACpB,IAAI,CAACG,IAAI,CAAGA,IAAI,CAClB,CAEAiD,iBAAiBA,CAAA,CAAG,CAClB,MAAO,KAAI,CAACpD,MAAM,GAAK,GAAG,EAAI,IAAI,CAACG,IAAI,CAACkD,MAAM,CAChD,CAEAC,eAAeA,CAAA,CAAG,CAChB,MAAO,KAAI,CAACtD,MAAM,GAAK,GAAG,CAC5B,CAEAuD,aAAaA,CAAA,CAAG,CACd,MAAO,KAAI,CAACvD,MAAM,EAAI,GAAG,CAC3B,CAEAwD,mBAAmBA,CAAA,CAAG,CACpB,MAAO,KAAI,CAACrD,IAAI,CAACkD,MAAM,EAAI,CAAC,CAAC,CAC/B,CACF,CAEA,OAASxE,UAAU,CAAEkB,QAAQ,EAC7B,cAAe,IAAI,CAAAlB,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}