import { PersonFieldDefinitions, getAllPersonFields } from '../constants/personConstants';
import formConfigService from '../services/formConfigService';

/**
 * Script to create forms for all Real Estate division subcategories
 * Based on the actual structure provided by the user
 */

// Real Estate division structure as provided
const REAL_ESTATE_STRUCTURE = {
  divisionId: 1,
  divisionName: 'Real Estate',
  categories: [
    {
      id: 1,
      name: 'Normal Agents',
      firmNatures: [
        { id: 1, name: 'Proprietorship' },
        { id: 2, name: 'Company' },
        { id: 3, name: 'Individual' },
        { id: 4, name: 'Partnership' }
      ]
    },
    {
      id: 2,
      name: 'Rera.Reg. Agents',
      firmNatures: [
        { id: 5, name: 'proprietorship' },
        { id: 6, name: 'Company' },
        { id: 7, name: 'Individual' },
        { id: 8, name: 'Partnership' }
      ]
    },
    {
      id: 3,
      name: 'Rera.Reg. Promoters',
      firmNatures: [
        { id: 9, name: 'proprietorship' },
        { id: 10, name: 'Company' },
        { id: 11, name: 'Individual' },
        { id: 12, name: 'Partnership' }
      ]
    },
    {
      id: 4,
      name: 'Colonisers',
      subcategories: [
        { id: 13, name: 'Partnership' },
        { id: 14, name: 'proprietorship' },
        { id: 15, name: 'Company' },
        { id: 16, name: 'Individual' }
      ]
    },
    {
      id: 5,
      name: 'Others',
      subcategories: [] // No subcategories, will create category form
    }
  ]
};

/**
 * Create a comprehensive form configuration with all person fields
 */
function createComprehensiveFormConfig(name, description, hierarchy) {
  const allFields = getAllPersonFields();
  
  return {
    name: name,
    description: description,
    fields: allFields,
    sections: Object.keys(PersonFieldDefinitions),
    settings: {
      showSections: true,
      allowConditionalFields: true,
      validateOnChange: true,
      autoSave: true
    },
    hierarchy: hierarchy,
    metadata: {
      createdBy: 'Real Estate Forms Creation Script',
      purpose: 'Comprehensive form for Real Estate division',
      version: '1.0.0',
      createdAt: new Date().toISOString()
    }
  };
}

/**
 * Create forms for all firm natures in a category
 */
function createFirmNatureForms(category) {
  const results = [];
  
  category.firmNatures.forEach(firmNature => {
    try {
      const formName = `${REAL_ESTATE_STRUCTURE.divisionName} - ${category.name} - ${firmNature.name}`;
      const formDescription = `Comprehensive form for ${firmNature.name} in ${category.name} category of Real Estate division`;

      const hierarchy = {
        divisionId: REAL_ESTATE_STRUCTURE.divisionId,
        divisionName: REAL_ESTATE_STRUCTURE.divisionName,
        categoryId: category.id,
        categoryName: category.name,
        firmNatureId: firmNature.id,
        firmNatureName: firmNature.name
      };
      
      const formConfig = createComprehensiveFormConfig(formName, formDescription, hierarchy);
      
      // Save the form using the form config service
      const savedForm = formConfigService.saveFormConfig('firmnature', firmNature.id, formConfig);

      results.push({
        success: true,
        type: 'firmnature',
        id: firmNature.id,
        name: firmNature.name,
        category: category.name,
        formId: savedForm.id,
        message: `Form created successfully for ${firmNature.name}`
      });

      console.log(`✓ Created form for firm nature: ${category.name} - ${firmNature.name} (ID: ${firmNature.id})`);
      
    } catch (error) {
      results.push({
        success: false,
        type: 'firmnature',
        id: firmNature.id,
        name: firmNature.name,
        category: category.name,
        error: error.message,
        message: `Failed to create form for ${firmNature.name}`
      });

      console.error(`✗ Failed to create form for firm nature: ${category.name} - ${firmNature.name}`, error);
    }
  });
  
  return results;
}

/**
 * Create form for a category (when it has no firm natures)
 */
function createCategoryForm(category) {
  try {
    const formName = `${REAL_ESTATE_STRUCTURE.divisionName} - ${category.name}`;
    const formDescription = `Comprehensive form for ${category.name} category of Real Estate division`;
    
    const hierarchy = {
      divisionId: REAL_ESTATE_STRUCTURE.divisionId,
      divisionName: REAL_ESTATE_STRUCTURE.divisionName,
      categoryId: category.id,
      categoryName: category.name,
      firmNatureId: null,
      firmNatureName: null
    };
    
    const formConfig = createComprehensiveFormConfig(formName, formDescription, hierarchy);
    
    // Save the form using the form config service
    const savedForm = formConfigService.saveFormConfig('category', category.id, formConfig);
    
    console.log(`✓ Created form for category: ${category.name} (ID: ${category.id})`);
    
    return {
      success: true,
      type: 'category',
      id: category.id,
      name: category.name,
      formId: savedForm.id,
      message: `Form created successfully for ${category.name}`
    };
    
  } catch (error) {
    console.error(`✗ Failed to create form for category: ${category.name}`, error);
    
    return {
      success: false,
      type: 'category',
      id: category.id,
      name: category.name,
      error: error.message,
      message: `Failed to create form for ${category.name}`
    };
  }
}

/**
 * Main function to create all Real Estate forms
 */
export function createAllRealEstateForms() {
  console.log('🏢 Starting Real Estate Forms Creation...');
  console.log(`Creating forms for ${REAL_ESTATE_STRUCTURE.divisionName} division`);
  
  const results = [];
  
  REAL_ESTATE_STRUCTURE.categories.forEach(category => {
    console.log(`\n📁 Processing category: ${category.name}`);
    
    if (category.firmNatures.length > 0) {
      // Category has firm natures - create forms for each firm nature
      console.log(`  Found ${category.firmNatures.length} firm natures`);
      const firmNatureResults = createFirmNatureForms(category);
      results.push(...firmNatureResults);
    } else {
      // Category has no firm natures - create form for the category itself
      console.log(`  No firm natures found, creating category form`);
      const categoryResult = createCategoryForm(category);
      results.push(categoryResult);
    }
  });
  
  // Summary
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log('\n📊 Summary:');
  console.log(`✓ Successfully created: ${successful.length} forms`);
  console.log(`✗ Failed to create: ${failed.length} forms`);
  
  if (failed.length > 0) {
    console.log('\n❌ Failed forms:');
    failed.forEach(f => {
      console.log(`  - ${f.category} - ${f.name}: ${f.error}`);
    });
  }
  
  console.log('\n🎉 Real Estate Forms Creation Complete!');
  
  return {
    total: results.length,
    successful: successful.length,
    failed: failed.length,
    results: results
  };
}

/**
 * Verify all forms were created successfully
 */
export function verifyRealEstateForms() {
  console.log('🔍 Verifying Real Estate Forms...');

  const allForms = formConfigService.getAllFormConfigs();
  const realEstateForms = allForms.filter(form =>
    form.hierarchy?.divisionId === REAL_ESTATE_STRUCTURE.divisionId
  );

  console.log(`Found ${realEstateForms.length} Real Estate forms in storage`);

  const verification = {
    totalFound: realEstateForms.length,
    byType: {
      subcategory: realEstateForms.filter(f => f.type === 'subcategory').length,
      category: realEstateForms.filter(f => f.type === 'category').length
    },
    forms: realEstateForms.map(form => ({
      id: form.id,
      name: form.name,
      type: form.type,
      associatedId: form.associatedId,
      categoryName: form.hierarchy?.categoryName,
      subCategoryName: form.hierarchy?.subCategoryName,
      fieldCount: form.fields?.length || 0
    }))
  };

  console.log('📋 Verification Results:');
  console.log(`  Subcategory forms: ${verification.byType.subcategory}`);
  console.log(`  Category forms: ${verification.byType.category}`);

  verification.forms.forEach(form => {
    console.log(`  - ${form.name} (${form.fieldCount} fields)`);
  });

  return verification;
}

/**
 * Delete all Real Estate forms (for cleanup if needed)
 */
export function deleteAllRealEstateForms() {
  console.log('🗑️ Deleting all Real Estate forms...');

  const allForms = formConfigService.getAllFormConfigs();
  const realEstateForms = allForms.filter(form =>
    form.hierarchy?.divisionId === REAL_ESTATE_STRUCTURE.divisionId
  );

  let deletedCount = 0;
  realEstateForms.forEach(form => {
    try {
      formConfigService.deleteFormConfig(form.type, form.associatedId);
      deletedCount++;
      console.log(`✓ Deleted: ${form.name}`);
    } catch (error) {
      console.error(`✗ Failed to delete: ${form.name}`, error);
    }
  });

  console.log(`🗑️ Deleted ${deletedCount} Real Estate forms`);
  return deletedCount;
}

// Export the main functions
export default {
  createAllRealEstateForms,
  verifyRealEstateForms,
  deleteAllRealEstateForms,
  REAL_ESTATE_STRUCTURE
};
