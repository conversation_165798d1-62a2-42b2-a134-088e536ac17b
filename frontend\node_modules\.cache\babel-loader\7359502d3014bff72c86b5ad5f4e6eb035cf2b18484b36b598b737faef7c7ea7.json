{"ast": null, "code": "import React,{createContext,useContext,useState,useEffect}from'react';import axios from'axios';import{jsx as _jsx}from\"react/jsx-runtime\";const AuthContext=/*#__PURE__*/createContext();export const useAuth=()=>{const context=useContext(AuthContext);if(!context){throw new Error('useAuth must be used within an AuthProvider');}return context;};export const AuthProvider=_ref=>{let{children}=_ref;const[user,setUser]=useState(null);const[loading,setLoading]=useState(true);const[token,setToken]=useState(localStorage.getItem('token'));useEffect(()=>{if(token){axios.defaults.headers.common['Authorization']=`Bearer ${token}`;validateToken();}else{setLoading(false);}},[token]);const validateToken=async()=>{try{await axios.post('http://localhost:5000/api/auth/validate');const userData=JSON.parse(localStorage.getItem('user')||'{}');setUser(userData);}catch(error){logout();}finally{setLoading(false);}};const login=async(username,password)=>{try{const response=await axios.post('http://localhost:5000/api/auth/login',{username,password});const{token,...userData}=response.data;localStorage.setItem('token',token);localStorage.setItem('user',JSON.stringify(userData));setToken(token);setUser(userData);axios.defaults.headers.common['Authorization']=`Bearer ${token}`;return{success:true};}catch(error){var _error$response,_error$response$data;return{success:false,message:((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||'Login failed'};}};const logout=()=>{localStorage.removeItem('token');localStorage.removeItem('user');setToken(null);setUser(null);delete axios.defaults.headers.common['Authorization'];};const value={user,login,logout,loading,isAuthenticated:!!user};return/*#__PURE__*/_jsx(AuthContext.Provider,{value:value,children:children});};", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsx", "_jsx", "AuthContext", "useAuth", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "user", "setUser", "loading", "setLoading", "token", "setToken", "localStorage", "getItem", "defaults", "headers", "common", "validateToken", "post", "userData", "JSON", "parse", "error", "logout", "login", "username", "password", "response", "data", "setItem", "stringify", "success", "_error$response", "_error$response$data", "message", "removeItem", "value", "isAuthenticated", "Provider"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\n\r\nconst AuthContext = createContext();\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (!context) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const AuthProvider = ({ children }) => {\r\n  const [user, setUser] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [token, setToken] = useState(localStorage.getItem('token'));\r\n\r\n  useEffect(() => {\r\n    if (token) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n      validateToken();\r\n    } else {\r\n      setLoading(false);\r\n    }\r\n  }, [token]);\r\n\r\n  const validateToken = async () => {\r\n    try {\r\n      await axios.post('http://localhost:5000/api/auth/validate');\r\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n      setUser(userData);\r\n    } catch (error) {\r\n      logout();\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const login = async (username, password) => {\r\n    try {\r\n      const response = await axios.post('http://localhost:5000/api/auth/login', {\r\n        username,\r\n        password\r\n      });\r\n\r\n      const { token, ...userData } = response.data;\r\n      \r\n      localStorage.setItem('token', token);\r\n      localStorage.setItem('user', JSON.stringify(userData));\r\n      \r\n      setToken(token);\r\n      setUser(userData);\r\n      \r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n      \r\n      return { success: true };\r\n    } catch (error) {\r\n      return { \r\n        success: false, \r\n        message: error.response?.data?.message || 'Login failed' \r\n      };\r\n    }\r\n  };\r\n\r\n  const logout = () => {\r\n    localStorage.removeItem('token');\r\n    localStorage.removeItem('user');\r\n    setToken(null);\r\n    setUser(null);\r\n    delete axios.defaults.headers.common['Authorization'];\r\n  };\r\n\r\n  const value = {\r\n    user,\r\n    login,\r\n    logout,\r\n    loading,\r\n    isAuthenticated: !!user\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC7E,MAAO,CAAAC,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE1B,KAAM,CAAAC,WAAW,cAAGP,aAAa,CAAC,CAAC,CAEnC,MAAO,MAAM,CAAAQ,OAAO,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,OAAO,CAAGR,UAAU,CAACM,WAAW,CAAC,CACvC,GAAI,CAACE,OAAO,CAAE,CACZ,KAAM,IAAI,CAAAC,KAAK,CAAC,6CAA6C,CAAC,CAChE,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAED,MAAO,MAAM,CAAAE,YAAY,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACvC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAACc,OAAO,CAAEC,UAAU,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACgB,KAAK,CAAEC,QAAQ,CAAC,CAAGjB,QAAQ,CAACkB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC,CAEjElB,SAAS,CAAC,IAAM,CACd,GAAIe,KAAK,CAAE,CACTd,KAAK,CAACkB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAG,UAAUN,KAAK,EAAE,CAClEO,aAAa,CAAC,CAAC,CACjB,CAAC,IAAM,CACLR,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,CAACC,KAAK,CAAC,CAAC,CAEX,KAAM,CAAAO,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAArB,KAAK,CAACsB,IAAI,CAAC,yCAAyC,CAAC,CAC3D,KAAM,CAAAC,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAACT,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,EAAI,IAAI,CAAC,CACjEN,OAAO,CAACY,QAAQ,CAAC,CACnB,CAAE,MAAOG,KAAK,CAAE,CACdC,MAAM,CAAC,CAAC,CACV,CAAC,OAAS,CACRd,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAe,KAAK,CAAG,KAAAA,CAAOC,QAAQ,CAAEC,QAAQ,GAAK,CAC1C,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA/B,KAAK,CAACsB,IAAI,CAAC,sCAAsC,CAAE,CACxEO,QAAQ,CACRC,QACF,CAAC,CAAC,CAEF,KAAM,CAAEhB,KAAK,CAAE,GAAGS,QAAS,CAAC,CAAGQ,QAAQ,CAACC,IAAI,CAE5ChB,YAAY,CAACiB,OAAO,CAAC,OAAO,CAAEnB,KAAK,CAAC,CACpCE,YAAY,CAACiB,OAAO,CAAC,MAAM,CAAET,IAAI,CAACU,SAAS,CAACX,QAAQ,CAAC,CAAC,CAEtDR,QAAQ,CAACD,KAAK,CAAC,CACfH,OAAO,CAACY,QAAQ,CAAC,CAEjBvB,KAAK,CAACkB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAG,UAAUN,KAAK,EAAE,CAElE,MAAO,CAAEqB,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAE,MAAOT,KAAK,CAAE,KAAAU,eAAA,CAAAC,oBAAA,CACd,MAAO,CACLF,OAAO,CAAE,KAAK,CACdG,OAAO,CAAE,EAAAF,eAAA,CAAAV,KAAK,CAACK,QAAQ,UAAAK,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBJ,IAAI,UAAAK,oBAAA,iBAApBA,oBAAA,CAAsBC,OAAO,GAAI,cAC5C,CAAC,CACH,CACF,CAAC,CAED,KAAM,CAAAX,MAAM,CAAGA,CAAA,GAAM,CACnBX,YAAY,CAACuB,UAAU,CAAC,OAAO,CAAC,CAChCvB,YAAY,CAACuB,UAAU,CAAC,MAAM,CAAC,CAC/BxB,QAAQ,CAAC,IAAI,CAAC,CACdJ,OAAO,CAAC,IAAI,CAAC,CACb,MAAO,CAAAX,KAAK,CAACkB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CACvD,CAAC,CAED,KAAM,CAAAoB,KAAK,CAAG,CACZ9B,IAAI,CACJkB,KAAK,CACLD,MAAM,CACNf,OAAO,CACP6B,eAAe,CAAE,CAAC,CAAC/B,IACrB,CAAC,CAED,mBACER,IAAA,CAACC,WAAW,CAACuC,QAAQ,EAACF,KAAK,CAAEA,KAAM,CAAA/B,QAAA,CAChCA,QAAQ,CACW,CAAC,CAE3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}