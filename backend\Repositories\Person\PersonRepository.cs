using Microsoft.EntityFrameworkCore;
using CrmApi.Data;
using CrmApi.Models.Person;

namespace CrmApi.Repositories.Person
{
    public class PersonRepository : IPersonRepository
    {
        private readonly CrmDbContext _context;

        public PersonRepository(CrmDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Models.Person.Person>> GetAllAsync(bool includeDeleted = false)
        {
            var query = _context.Persons.AsQueryable();
            
            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            return await query.ToListAsync();
        }

        public async Task<Models.Person.Person?> GetByIdAsync(int id, bool includeDeleted = false)
        {
            var query = _context.Persons.AsQueryable();
            
            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            return await query.FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Models.Person.Person?> GetByIdWithRelationsAsync(int id, bool includeDeleted = false)
        {
            var query = _context.Persons
                .Include(p => p.Division)
                .Include(p => p.Category)
                .Include(p => p.FirmNature)
                .AsQueryable();
            
            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            return await query.FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Models.Person.Person> CreateAsync(Models.Person.Person person)
        {
            person.CreatedAt = DateTime.UtcNow;
            person.UpdatedAt = DateTime.UtcNow;
            person.IsDeleted = false;
            
            _context.Persons.Add(person);
            await _context.SaveChangesAsync();
            return person;
        }

        public async Task<Models.Person.Person> UpdateAsync(Models.Person.Person person)
        {
            person.UpdatedAt = DateTime.UtcNow;
            _context.Entry(person).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return person;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var person = await _context.Persons.FindAsync(id);
            if (person == null)
                return false;

            _context.Persons.Remove(person);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> SoftDeleteAsync(int id)
        {
            var person = await _context.Persons.FindAsync(id);
            if (person == null || person.IsDeleted)
                return false;

            person.IsDeleted = true;
            person.DeletedAt = DateTime.UtcNow;
            person.UpdatedAt = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RestoreAsync(int id)
        {
            var person = await _context.Persons.FindAsync(id);
            if (person == null || !person.IsDeleted)
                return false;

            person.IsDeleted = false;
            person.DeletedAt = null;
            person.UpdatedAt = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int id, bool includeDeleted = false)
        {
            var query = _context.Persons.AsQueryable();
            
            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            return await query.AnyAsync(p => p.Id == id);
        }

        public async Task<(IEnumerable<Models.Person.Person> persons, int totalCount)> SearchAsync(PersonSearchRequest request)
        {
            var query = _context.Persons.AsQueryable();

            // Apply partition filters first for optimal performance (most selective)
            if (request.DivisionId.HasValue)
                query = query.Where(p => p.DivisionId == request.DivisionId.Value);

            if (request.CategoryId.HasValue)
                query = query.Where(p => p.CategoryId == request.CategoryId.Value);

            if (request.FirmNatureId.HasValue)
                query = query.Where(p => p.FirmNatureId == request.FirmNatureId.Value);

            // Apply other filters
            query = query.Where(p => !p.IsDeleted);

            if (!string.IsNullOrEmpty(request.Name))
                query = query.Where(p => p.Name.Contains(request.Name));

            if (!string.IsNullOrEmpty(request.MobileNumber))
                query = query.Where(p => p.MobileNumber.Contains(request.MobileNumber));

            if (!string.IsNullOrEmpty(request.Email))
                query = query.Where(p => p.PrimaryEmailId.Contains(request.Email) ||
                                        p.AlternateEmailIds.Any(e => e.Contains(request.Email)));

            if (request.Nature.HasValue)
                query = query.Where(p => p.Nature == request.Nature.Value);

            if (request.Gender.HasValue)
                query = query.Where(p => p.Gender == request.Gender.Value);

            if (!string.IsNullOrEmpty(request.WorkingState))
                query = query.Where(p => p.WorkingState.Contains(request.WorkingState));

            if (!string.IsNullOrEmpty(request.District))
                query = query.Where(p => p.District.Contains(request.District));

            if (!string.IsNullOrEmpty(request.FirmName))
                query = query.Where(p => p.FirmName.Contains(request.FirmName));

            if (request.MinStarRating.HasValue)
                query = query.Where(p => p.StarRating >= request.MinStarRating.Value);

            if (request.MaxStarRating.HasValue)
                query = query.Where(p => p.StarRating <= request.MaxStarRating.Value);

            if (request.MinTransactionValue.HasValue)
                query = query.Where(p => p.TransactionValue >= request.MinTransactionValue.Value);

            if (request.MaxTransactionValue.HasValue)
                query = query.Where(p => p.TransactionValue <= request.MaxTransactionValue.Value);

            if (request.HasAssociate.HasValue)
                query = query.Where(p => p.HasAssociate == request.HasAssociate.Value);

            if (request.UsingWebsite.HasValue)
                query = query.Where(p => p.UsingWebsite == request.UsingWebsite.Value);

            if (request.UsingCRMApp.HasValue)
                query = query.Where(p => p.UsingCRMApp == request.UsingCRMApp.Value);

            if (request.CreatedAfter.HasValue)
                query = query.Where(p => p.CreatedAt >= request.CreatedAfter.Value);

            if (request.CreatedBefore.HasValue)
                query = query.Where(p => p.CreatedAt <= request.CreatedBefore.Value);

            // Include related data if requested
            if (request.IncludeDivision)
                query = query.Include(p => p.Division);

            if (request.IncludeCategory)
                query = query.Include(p => p.Category);

            if (request.IncludeFirmNature)
                query = query.Include(p => p.FirmNature);

            // Get total count before pagination
            var totalCount = await query.CountAsync();

            // Apply sorting
            query = request.SortBy.ToLower() switch
            {
                "name" => request.SortDirection.ToLower() == "desc" 
                    ? query.OrderByDescending(p => p.Name)
                    : query.OrderBy(p => p.Name),
                "mobilenumber" => request.SortDirection.ToLower() == "desc" 
                    ? query.OrderByDescending(p => p.MobileNumber)
                    : query.OrderBy(p => p.MobileNumber),
                "createdat" => request.SortDirection.ToLower() == "desc" 
                    ? query.OrderByDescending(p => p.CreatedAt)
                    : query.OrderBy(p => p.CreatedAt),
                "updatedat" => request.SortDirection.ToLower() == "desc" 
                    ? query.OrderByDescending(p => p.UpdatedAt)
                    : query.OrderBy(p => p.UpdatedAt),
                "starrating" => request.SortDirection.ToLower() == "desc" 
                    ? query.OrderByDescending(p => p.StarRating)
                    : query.OrderBy(p => p.StarRating),
                _ => query.OrderByDescending(p => p.CreatedAt)
            };

            // Apply pagination
            var persons = await query
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync();

            return (persons, totalCount);
        }

        public async Task<IEnumerable<Models.Person.Person>> GetByDivisionAsync(int divisionId, bool includeDeleted = false)
        {
            var query = _context.Persons.Where(p => p.DivisionId == divisionId);
            
            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            return await query.ToListAsync();
        }

        public async Task<IEnumerable<Models.Person.Person>> GetByCategoryAsync(int categoryId, bool includeDeleted = false)
        {
            var query = _context.Persons.Where(p => p.CategoryId == categoryId);
            
            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            return await query.ToListAsync();
        }

        public async Task<IEnumerable<Models.Person.Person>> GetByFirmNatureAsync(int firmNatureId, bool includeDeleted = false)
        {
            var query = _context.Persons.Where(p => p.FirmNatureId == firmNatureId);

            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            return await query.ToListAsync();
        }

        public async Task<IEnumerable<Models.Person.Person>> GetByMobileNumberAsync(string mobileNumber, bool includeDeleted = false)
        {
            var query = _context.Persons.Where(p => p.MobileNumber.Contains(mobileNumber));
            
            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            return await query.ToListAsync();
        }

        public async Task<Models.Person.Person?> GetByMobileNumberExactAsync(string mobileNumber, bool includeDeleted = false)
        {
            var query = _context.Persons.Where(p => p.MobileNumber == mobileNumber);
            
            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            return await query.FirstOrDefaultAsync();
        }

        public async Task<bool> MobileNumberExistsAsync(string mobileNumber, int? excludeId = null, bool includeDeleted = false)
        {
            var query = _context.Persons.Where(p => p.MobileNumber == mobileNumber);
            
            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            if (excludeId.HasValue)
                query = query.Where(p => p.Id != excludeId.Value);

            return await query.AnyAsync();
        }

        public async Task<bool> EmailExistsAsync(string email, int? excludeId = null, bool includeDeleted = false)
        {
            var query = _context.Persons.Where(p => p.PrimaryEmailId == email || 
                                                   p.AlternateEmailIds.Contains(email));
            
            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            if (excludeId.HasValue)
                query = query.Where(p => p.Id != excludeId.Value);

            return await query.AnyAsync();
        }

        public async Task<bool> MobileNumberExistsInDivisionCategoryAsync(string mobileNumber, int divisionId, int categoryId, int? excludeId = null, bool includeDeleted = false)
        {
            var query = _context.Persons.Where(p => p.MobileNumber == mobileNumber && 
                                                   p.DivisionId == divisionId && 
                                                   p.CategoryId == categoryId);
            
            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            if (excludeId.HasValue)
                query = query.Where(p => p.Id != excludeId.Value);

            return await query.AnyAsync();
        }

        public async Task<int> GetCountByDivisionAsync(int divisionId, bool includeDeleted = false)
        {
            var query = _context.Persons.Where(p => p.DivisionId == divisionId);
            
            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            return await query.CountAsync();
        }

        public async Task<int> GetCountByCategoryAsync(int categoryId, bool includeDeleted = false)
        {
            var query = _context.Persons.Where(p => p.CategoryId == categoryId);
            
            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            return await query.CountAsync();
        }

        public async Task<int> GetCountByNatureAsync(PersonNature nature, bool includeDeleted = false)
        {
            var query = _context.Persons.Where(p => p.Nature == nature);
            
            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            return await query.CountAsync();
        }

        public async Task<decimal> GetAverageStarRatingAsync(bool includeDeleted = false)
        {
            var query = _context.Persons.Where(p => p.StarRating.HasValue);

            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            var average = await query.AverageAsync(p => p.StarRating!.Value);
            return (decimal)average;
        }

        public async Task<decimal> GetTotalTransactionValueAsync(bool includeDeleted = false)
        {
            var query = _context.Persons.Where(p => p.TransactionValue.HasValue);
            
            if (!includeDeleted)
                query = query.Where(p => !p.IsDeleted);

            return await query.SumAsync(p => p.TransactionValue!.Value);
        }

        public async Task<IEnumerable<Models.Person.Person>> CreateBulkAsync(IEnumerable<Models.Person.Person> persons)
        {
            var personList = persons.ToList();
            var now = DateTime.UtcNow;
            
            foreach (var person in personList)
            {
                person.CreatedAt = now;
                person.UpdatedAt = now;
                person.IsDeleted = false;
            }
            
            _context.Persons.AddRange(personList);
            await _context.SaveChangesAsync();
            return personList;
        }

        public async Task<bool> SoftDeleteBulkAsync(IEnumerable<int> ids)
        {
            var persons = await _context.Persons
                .Where(p => ids.Contains(p.Id) && !p.IsDeleted)
                .ToListAsync();

            if (!persons.Any())
                return false;

            var now = DateTime.UtcNow;
            foreach (var person in persons)
            {
                person.IsDeleted = true;
                person.DeletedAt = now;
                person.UpdatedAt = now;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RestoreBulkAsync(IEnumerable<int> ids)
        {
            var persons = await _context.Persons
                .Where(p => ids.Contains(p.Id) && p.IsDeleted)
                .ToListAsync();

            if (!persons.Any())
                return false;

            var now = DateTime.UtcNow;
            foreach (var person in persons)
            {
                person.IsDeleted = false;
                person.DeletedAt = null;
                person.UpdatedAt = now;
            }

            await _context.SaveChangesAsync();
            return true;
        }
    }
}
